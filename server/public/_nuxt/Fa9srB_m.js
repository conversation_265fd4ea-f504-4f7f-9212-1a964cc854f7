import{E as f}from"./DVv83yww.js";import{_ as u}from"./BY8Moot3.js";import{b as _}from"./C9xud4Fy.js";/* empty css        */import{l as v,m as r,M as i,N as m,O as p,Z as e,a0 as n,ab as C,ac as b,u as s,a7 as h,V as x}from"./uahP8ofS.js";import g from"./CbzV_IL6.js";import w from"./DM8t36pk.js";import S from"./CIQec7Q0.js";import{_ as N}from"./DlAUqK2U.js";import"./Bhh0k9k9.js";import"./5nPgGenE.js";import"./9Ov0gw5P.js";import"./DCTLXrZ8.js";import"./_aEA_d5X.js";/* empty css        */import"./l0sNRNKZ.js";/* empty css        */import"./BrhttwqI.js";import"./CeULTHO7.js";import"./Dd_kbKZi.js";import"./C1JW1GZc.js";const $={class:"layout-aside h-full flex"},k={class:"h-full flex justify-between flex-col"},y={class:"h-full"},E=v({__name:"index",setup(M){const o=_(),a=r(()=>o.isMobile);return r({get(){return!o.isCollapsed&&a.value},set(t){o.toggleCollapsed(!t)}}),(t,l)=>{const c=f,d=u;return i(),m("div",$,[p("div",k,[e(c,{class:"w-[80px] el-scrollbar"},{default:n(()=>[e(S,{class:"mb-auto"})]),_:1}),e(w)]),C(p("div",y,[e(g,null,{default:n(()=>[x(t.$slots,"aside",{},void 0,!0)]),_:3})],512),[[b,!s(o).isCollapsed]]),t.$slots.panel&&!s(a)?(i(),m("div",{key:0,class:"panel-left-arrow",onClick:l[0]||(l[0]=V=>s(o).toggleCollapsed())},[e(d,{class:"mr-1",name:`el-icon-${s(o).isCollapsed?"CaretRight":"CaretLeft"}`},null,8,["name"])])):h("",!0)])}}}),X=N(E,[["__scopeId","data-v-e34fcc87"]]);export{X as default};
