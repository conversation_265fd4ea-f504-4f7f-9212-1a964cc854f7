import{_ as v}from"./B6IIPh85.js";import{_ as y}from"./D99FoGlZ.js";import{E as V,a as g}from"./D76T9XJY.js";import{E}from"./DH3BuQAR.js";import{l as w,h as B}from"./ClNUxNV9.js";/* empty css        */import"./l0sNRNKZ.js";/* empty css        *//* empty css        */import{l as S,M as o,N as p,O as t,Z as n,V as C,a1 as l,a4 as _,a0 as r,u as M,_ as N,aq as I,a7 as L}from"./Dp9aCaJ6.js";import{_ as P}from"./DlAUqK2U.js";const $={class:"setting-aside p-4"},O={class:"flex flex-col h-full bg-body w-[180px] rounded-[12px]"},T={class:"px-[15px] pt-[15px]"},q={class:"flex items-center cursor-pointer"},z={class:"text-xl flex-1 min-w-0 ml-[10px]"},D={class:"mb-[10px]"},F=S({__name:"index",props:{modelValue:{default:""},menuList:{default:()=>[]},backPath:{},title:{default:""}},emits:["update:modelValue"],setup(u,{emit:R}){const a=u,c=w(),i=B(a,"modelValue"),f=()=>{a.backPath?c.replace({path:a.backPath}):c.back()};return(s,m)=>{const d=v,x=y,b=V,h=g,k=E;return o(),p("div",$,[t("div",O,[t("div",T,[t("div",q,[t("div",{class:"flex bg-body p-[5px] text-bold rounded-[50%] text-primary shadow-light",onClick:f},[n(d,{name:"el-icon-Back",size:18})]),t("div",z,[C(s.$slots,"title",{},()=>[s.title?(o(),l(x,{key:0,content:s.title,teleported:!0,effect:"light"},null,8,["content"])):_("",!0)],!0)])])]),n(k,{class:"tab-lists w-full"},{default:r(()=>[t("div",D,[n(h,{"default-active":M(i),style:{border:"none"},router:!1,onSelect:m[0]||(m[0]=e=>i.value=e)},{default:r(()=>[(o(!0),p(N,null,I(s.menuList,e=>(o(),l(b,{key:e.key,index:e.key},{default:r(()=>[e.icon?(o(),l(d,{key:0,name:e.icon},null,8,["name"])):_("",!0),t("span",null,L(e.name),1)]),_:2},1032,["index"]))),128))]),_:1},8,["default-active"])])]),_:1})])])}}}),Y=P(F,[["__scopeId","data-v-80cd954f"]]);export{Y as _};
