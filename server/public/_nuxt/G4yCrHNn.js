import{_ as L}from"./BAooD3NP.js";import{b as M,E as Q}from"./C12kmceL.js";import{l as V,M as n,N as r,O as e,V as T,a2 as W,b as p,m as O,F as P,Z as l,_ as S,aq as D,u as a,a0 as w,a7 as U,r as q,X as G,a5 as B,ab as X,ac as Z,a3 as j,a6 as H,a4 as J}from"./uahP8ofS.js";import{_ as g}from"./DlAUqK2U.js";import{u as K}from"./BfGcwPP1.js";import{ImportTypeEnum as d}from"./BiHhwkbt.js";import R from"./CeykNNaO.js";import Y from"./BVNr9Vpn.js";import tt from"./PWDMcoaz.js";import et from"./gjAVCBFb.js";import{g as ot}from"./CzIVhTAp.js";import"./D0NTNmoy.js";import"./DH3GNTka.js";import"./Cr9cZ-Xs.js";import"./DHsrbrOc.js";import"./DL-C_KWg.js";/* empty css        */import"./CcdExHMo.js";import"./DWZt4P8F.js";import"./Cpg3PDWZ.js";import"./BTQ7_I24.js";import"./CBI7ecvA.js";import"./DCTLXrZ8.js";import"./DFv4Fkzk.js";import"./Cq-dlMe8.js";import"./HrsfEhzV.js";import"./CeHUJVAt.js";import"./B167I4fC.js";import"./D8Q6oBc7.js";import"./l0sNRNKZ.js";/* empty css        *//* empty css        *//* empty css        */import"./C4qLKnCc.js";import"./DQxdb33m.js";import"./DP2rzg_V.js";/* empty css        */const st={class:"footer-btns"},at=V({__name:"index",props:{fixed:{type:Boolean,default:!0}},setup(u){return(_,v)=>(n(),r("div",st,[e("div",{class:"footer-btns__content",style:W(u.fixed?"position: fixed":"")},[T(_.$slots,"default",{},void 0,!0)],4)]))}}),nt=g(at,[["__scopeId","data-v-4b8d4336"]]),rt={class:"h-full flex flex-col relative"},it={class:"pb-[20px] flex items-center font-bold cursor-pointer"},pt={class:"important-notice mb-4"},ct={class:"notice-header"},mt={class:"flex"},lt=["onClick"],dt={class:"text-info text-[14px]"},ut={class:"flex-1"},_t={key:0},ft=V({__name:"importData",props:{id:{type:Number,default:0},type:{type:String,default:""}},emits:["success","back"],setup(u,{emit:_}){const v=M(),E=_,x=u,c=p(0),F=p([]),N=p([]),A=p([]),I=p([]),i=O(()=>q([{name:"文档导入",type:d.DOC,describe:"选择文本文件，直接将其按分段进行处理",component:Y,show:!0,data:F},{name:"问答对导入",type:d.CVS,describe:"批量导入问答对，效果最佳",component:R,show:!0,data:N},{name:"自动拆分问答对",type:d.QASplit,describe:"选择文本文件，让大模型自动生成问答对",component:tt,show:!0,data:A},{name:"网页解析",type:d.WEB_PAGE,describe:"输入网页链接，快速导入内容",component:et,show:!0,data:I}]).filter(({show:t})=>t)),b=s=>{c.value=s},$=()=>{b(i.value[0].type)},{lockFn:z}=K(async()=>{const s=i.value.find(({type:y})=>y===c.value);console.log(s==null?void 0:s.data);const{data:t,type:m}=s,f={kb_id:x.id,method:m,documents:t};await ot({...f}),k()}),k=()=>{E("back")};return P(()=>{$()}),(s,t)=>{const m=L,f=Q,y=nt;return n(),r("div",rt,[e("div",it,[e("div",{onClick:k,class:"flex items-center"},[l(m,{name:"el-icon-Back",size:"16"}),t[0]||(t[0]=e("span",{class:"ml-2"},"文件导入",-1))])]),e("div",pt,[e("div",ct,[l(m,{name:"el-icon-Warning",color:"#ff9900",size:"18"}),t[1]||(t[1]=e("span",{class:"notice-title"},"重要提示",-1))]),t[2]||(t[2]=e("div",{class:"notice-content"}," 为保护您的信息安全，请勿录入如姓名、身份证号、电话等个人隐私信息及任何工作秘密、商业秘密。本平台在未获得您同意的情况下，不会使用您的数据进行模型训练。 ",-1))]),e("div",mt,[(n(!0),r(S,null,D(a(i),(o,h)=>(n(),r("div",{key:h,class:G(["unselect w-[290px] p-[16px] text-center rounded-md cursor-pointer mr-4",{isselect:o.type==a(c)}]),onClick:C=>b(o.type)},[e("div",null,B(o.name),1),e("div",dt,B(o.describe),1)],10,lt))),128))]),e("div",ut,[(n(!0),r(S,null,D(a(i),(o,h)=>X((n(),j(H(o.component),{key:o.type,modelValue:o.data,"onUpdate:modelValue":C=>o.data=C,type:x.type},null,8,["modelValue","onUpdate:modelValue","type"])),[[Z,o.type==a(c)]])),128))]),a(i).length>0?(n(),r("div",_t,[l(y,{fixed:!a(v).isMobile},{default:w(()=>[l(f,{type:"primary",onClick:a(z)},{default:w(()=>t[3]||(t[3]=[J(" 保存 ")])),_:1},8,["onClick"])]),_:1},8,["fixed"])])):U("",!0)])}}}),Yt=g(ft,[["__scopeId","data-v-a9546196"]]);export{Yt as default};
