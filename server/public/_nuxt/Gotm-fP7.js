import{_ as h}from"./PVV9uz0C.js";import{E as x,a as v}from"./Dz_s4yBT.js";import{a as E,_ as b,cm as g}from"./DGzblORL.js";/* empty css        */import"./l0sNRNKZ.js";/* empty css        */import{l as u,m as y,M as s,N as l,Z as e,a0 as o,_ as L,aq as N,a3 as d,O as m,a5 as $,u as k}from"./uahP8ofS.js";import{_ as B}from"./DlAUqK2U.js";import{E as P}from"./BFLg3Y4S.js";/* empty css        */import"./HP80a3uV.js";import"./Dt1L8u-1.js";import"./DCTLXrZ8.js";import"./CY5Ghzht.js";const S={class:"tab-list"},w={class:"menu-icon"},C={class:"mt-[10px] text-sm"},I=u({__name:"index",props:{navList:{}},setup(f){const t=E(),i=y(()=>{const a=t.path==="/"?t.path:t.path.replace(/\/$/,"");return t.meta.activePath||a});return(a,p)=>{const _=h,c=x,r=v;return s(),l("div",S,[e(r,{"default-active":k(i),router:"",style:{border:"none"}},{default:o(()=>[(s(!0),l(L,null,N(a.navList,n=>(s(),d(c,{key:n.path,index:n.path},{default:o(()=>[m("span",w,[e(_,{size:20,name:n.icon},null,8,["name"])]),m("span",C,$(n.name),1)]),_:2},1032,["index"]))),128))]),_:1},8,["default-active"])])}}}),M=B(I,[["__scopeId","data-v-317921ce"]]),V={class:"bg-white flex flex-col h-full w-[150px] text-tx-primary tab-list"},T=u({__name:"aside",setup(f){const t=[{name:"视频合成",icon:"el-icon-VideoCamera",path:"/digital_human/aside/video_compositing"}];return(i,a)=>{const p=M,_=P,c=g,r=b;return s(),d(r,{name:"default"},{panel:o(()=>[m("div",V,[e(_,null,{default:o(()=>[e(p,{"nav-list":t})]),_:1})])]),default:o(()=>[e(c)]),_:1})}}});export{T as default};
