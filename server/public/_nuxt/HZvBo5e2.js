import{E as B,a as D}from"./LvTTyycE.js";import{E as K}from"./BnGNjiBA.js";import{_ as M}from"./BQBYw5b-.js";import{b as T,j as F,_ as A,f as k,p as P,E as X}from"./DkNxoV1Z.js";import{E as Z}from"./DZB2bx3q.js";import{E as H}from"./BaKauqEV.js";import{E as J}from"./Bs_6fr1m.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        *//* empty css        *//* empty css        */import{u as Q}from"./DKxmiVxP.js";import{u as W}from"./BfGcwPP1.js";import{a as Y,p as ee}from"./B5TkE_dZ.js";import{defaultDurationValue as le,defaultCameraValue as oe,defaultModel as te,ChannelToken as _,ModeOptions as ae,CameraOptions as re,DurationOptions as ne}from"./CDaGe7dH.js";import{_ as se}from"./8mX4kwR4.js";import{_ as me}from"./9A5B8LIG.js";import ie from"./D4i1Ahms.js";import{_ as pe}from"./CPx89DMb.js";import ue from"./B9LuHcF2.js";import de from"./fF9GB5_Q.js";import{_ as f}from"./D6rEAyu8.js";import{e as ce}from"./iL4oR4RZ.js";import{l as _e,j as fe,r as ye,m as g,c as Ve,M as a,N as i,Z as r,a0 as n,u as e,O as p,a3 as u,_ as y,aq as v,a7 as d,a5 as E}from"./uahP8ofS.js";import"./C1DmYkVe.js";import"./DCTLXrZ8.js";import"./UB3oSb9l.js";import"./HrsfEhzV.js";import"./9mpg2Psx.js";import"./D45YP0Si.js";import"./CWpusvr2.js";import"./CZqEt49j.js";import"./DlAUqK2U.js";import"./Txs22M_p.js";import"./FN4AbcWJ.js";import"./C-HK5oo_.js";import"./DP2rzg_V.js";import"./C8VUL651.js";import"./BX3NQxO5.js";import"./BZTlN-U1.js";import"./xixvWuCN.js";import"./Bw8I6-LI.js";import"./Bjl3F-BN.js";import"./4RF45SSE.js";import"./Cpg3PDWZ.js";import"./C_YLeEJc.js";import"./DNxQvvN3.js";import"./BD9OgGux.js";import"./DiLesGdS.js";import"./l0sNRNKZ.js";/* empty css        */import"./DDvTJbu4.js";import"./BJeOdEyS.js";import"./CmOb-s4M.js";import"./Bk100RBx.js";import"./DjwCd26w.js";import"./Dh9d3v4F.js";import"./BGIqOOM2.js";/* empty css        */const ge={key:0,class:"h-full p-[16px] flex"},ve={class:"bg-body w-[355px] h-full rounded-[12px] flex flex-col"},xe={class:"p-4"},he={class:"flex-1 min-h-0"},be={class:"p-4"},ke={key:0,class:"text-sm ml-[4px]"},Ee={key:1,class:"text-sm ml-[4px]"},we={class:"ml-4 flex-1 min-w-0 h-full"},Ue={key:1,class:"h-full flex-1 flex p-4 justify-center items-center"},Ol=_e({__name:"index",setup(Le){const x=T(),w=F(),h=fe(),l=ye({type:1,prompt:"",scale:"1:1",image:"",style_id:[],channel:"",model_name:"",negative_prompt:"",duration:le,camera_control:{type:oe},mode:te}),{data:s,refresh:U}=Q(()=>Y(),{default(){return{channel:"",model:{},style:[],example:{}}},lazy:!0},"$xAqLrLrbXd"),b=g(()=>(console.log(l.channel,s.value),l.channel||(l.channel=s.value.channel),s.value.model[l.channel]||{}));g(()=>{var m;return(m=Object.entries(s.value.model))==null?void 0:m.map(([t,c])=>({label:c.name,value:c.channel}))});const L=g(()=>{var t;const m=((t=s.value.model[l.channel])==null?void 0:t.models)||[l.channel];return l.model_name=m[0],m}),{lockFn:C,isLock:N}=W(async()=>{var m;try{if(!l.prompt){k.msgError(`请输入${l.type===1?"视频场景":"描述词"}`);return}if(l.type===2&&!l.image)return k.msgError("请上传上传参考图");await ee({...l}),l.prompt="",l.style_id=[],(m=h.value)==null||m.refresh(),w.getUser(),U()}catch{}});Ve(()=>{l.channel=s.value.channel},s);const R=m=>{Object.assign(l,m)};return(m,t)=>{const c=B,V=D,S=K,G=M,z=P,I=Z,O=X,q=H,$=J,j=A;return a(),i("div",null,[r(j,{name:"default"},{default:n(()=>[e(x).config.switch.video_status?(a(),i("div",ge,[p("div",ve,[p("div",xe,[r(me,{modelValue:e(l).type,"onUpdate:modelValue":t[0]||(t[0]=o=>e(l).type=o)},null,8,["modelValue"])]),p("div",he,[r(I,null,{default:n(()=>[r(z,{class:"px-4",ref:"formRef",model:e(l),"label-position":"top","show-message":!1},{default:n(()=>[e(_).K_LING===e(l).channel?(a(),u(f,{key:0,label:"模型",required:""},{default:n(()=>[r(V,{modelValue:e(l).model_name,"onUpdate:modelValue":t[1]||(t[1]=o=>e(l).model_name=o),placeholder:"请选择模型",size:"large"},{default:n(()=>[(a(!0),i(y,null,v(e(L),o=>(a(),u(c,{key:o,label:o,value:o},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):d("",!0),e(_).K_LING===e(l).channel?(a(),u(f,{key:1,label:"模式",required:""},{default:n(()=>[r(S,{modelValue:e(l).mode,"onUpdate:modelValue":t[2]||(t[2]=o=>e(l).mode=o),class:"w-full",size:"large",options:e(ae)},null,8,["modelValue","options"])]),_:1})):d("",!0),e(l).type===2?(a(),u(ue,{key:2,modelValue:e(l).image,"onUpdate:modelValue":t[3]||(t[3]=o=>e(l).image=o)},null,8,["modelValue"])):d("",!0),r(se,{type:e(l).type,modelValue:e(l).prompt,"onUpdate:modelValue":t[4]||(t[4]=o=>e(l).prompt=o),config:e(s).example,showTranslate:!!e(s).translate_switch},null,8,["type","modelValue","config","showTranslate"]),e(_).K_LING===e(l).channel?(a(),u(f,{key:3,label:"负面描述"},{default:n(()=>[r(G,{modelValue:e(l).negative_prompt,"onUpdate:modelValue":t[5]||(t[5]=o=>e(l).negative_prompt=o),placeholder:"当需要表达负面情绪时，我们可以使用一些负面提示词用来描述低质量的绘画、畸形的形象等","content-style":{height:"120px"}},null,8,["modelValue"])]),_:1})):d("",!0),r(ie,{modelValue:e(l).scale,"onUpdate:modelValue":t[6]||(t[6]=o=>e(l).scale=o),filters:e(_).K_LING===e(l).channel?["1:1","9:16","16:9"]:["1:1","3:4","4:3","9:16","16:9"]},null,8,["modelValue","filters"]),e(s).style.length?(a(),u(pe,{key:4,"style-list":e(s).style,modelValue:e(l).style_id,"onUpdate:modelValue":t[7]||(t[7]=o=>e(l).style_id=o)},null,8,["style-list","modelValue"])):d("",!0),e(_).K_LING===e(l).channel?(a(),i(y,{key:5},[r(f,{label:"镜头",required:""},{default:n(()=>[r(V,{modelValue:e(l).camera_control.type,"onUpdate:modelValue":t[8]||(t[8]=o=>e(l).camera_control.type=o),placeholder:"请选择镜头",size:"large"},{default:n(()=>[(a(!0),i(y,null,v(e(re),o=>(a(),u(c,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),r(f,{label:"时长",required:""},{default:n(()=>[r(V,{modelValue:e(l).duration,"onUpdate:modelValue":t[9]||(t[9]=o=>e(l).duration=o),placeholder:"请选择时长",size:"large"},{default:n(()=>[(a(!0),i(y,null,v(e(ne),o=>(a(),u(c,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})],64)):d("",!0)]),_:1},8,["model"])]),_:1})]),p("div",be,[r(O,{size:"large",class:"w-full",type:"primary",loading:e(N),onClick:e(C)},{default:n(()=>[p("div",null,[t[10]||(t[10]=p("span",{class:"text-base font-bold"},"立即生成",-1)),e(s).is_member?(a(),i("span",ke,"会员免费")):e(b).price>0?(a(),i("span",Ee,"消耗 "+E(e(b).price)+E(e(x).getTokenUnit),1)):d("",!0)])]),_:1},8,["loading","onClick"])])]),p("div",we,[r(de,{ref_key:"videoResultRef",ref:h,onRegenerate:R},null,512)])])):(a(),i("div",Ue,[r($,null,{icon:n(()=>[r(q,{class:"w-[150px] dark:opacity-60",src:e(ce)},null,8,["src"])]),title:n(()=>t[11]||(t[11]=[p("div",{class:"text-info"},"功能暂未开启",-1)])),_:1})]))]),_:1})])}}});export{Ol as default};
