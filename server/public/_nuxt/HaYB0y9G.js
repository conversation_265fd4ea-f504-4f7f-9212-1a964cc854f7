import{E as d}from"./BZXCTEAI.js";import"./Ct33iMSA.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import{l as m,m as c,M as f,N as z,Z as h,W as S,u as n}from"./Dp9aCaJ6.js";const P={class:"pagination"},v=m({__name:"index",props:{modelValue:{default:()=>({})},pageSizes:{default:()=>[15,20,30,40]},layout:{default:"total, sizes, prev, pager, next, jumper"},hideOnSinglePage:{type:Boolean,default:!1},background:{type:Boolean,default:!1}},emits:["change","update:modelValue"],setup(g,{emit:i}){const s=g,t=i,e=c({get(){return s.modelValue},set(a){t("update:modelValue",a)}}),r=()=>{e.value.page=1,t("change")},l=()=>{t("change")};return(a,o)=>{const u=d;return f(),z("div",P,[h(u,S(s,{background:a.background,currentPage:n(e).page,"onUpdate:currentPage":o[0]||(o[0]=p=>n(e).page=p),pageSize:n(e).size,"onUpdate:pageSize":o[1]||(o[1]=p=>n(e).size=p),"pager-count":5,"page-sizes":a.pageSizes,layout:a.layout,total:n(e).count,"hide-on-single-page":a.hideOnSinglePage,onSizeChange:r,onCurrentChange:l}),null,16,["background","currentPage","pageSize","page-sizes","layout","total","hide-on-single-page"])])}}});export{v as _};
