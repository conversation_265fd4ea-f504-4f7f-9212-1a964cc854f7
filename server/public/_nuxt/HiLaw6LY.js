import{E as l}from"./DH3BuQAR.js";import{b as c,_}from"./ClNUxNV9.js";/* empty css        */import u from"./CjFbGOre.js";import{l as d,M as t,N as r,Z as p,a0 as m,O as i,_ as f,aq as x,u as e,a1 as h,a3 as k,a4 as v}from"./Dp9aCaJ6.js";import"./D0Om6V3E.js";import"./hx-0JZAY.js";import"./CNgDMrD1.js";import"./zRTrVFrw.js";import"./D4cQUBDp.js";import"./CLpaSNIL.js";import"./BNsSO7uz.js";import"./CraaJdZW.js";/* empty css        */import"./DlAUqK2U.js";import"./BLlzqjN8.js";import"./DeXaFite.js";import"./Cj5ixK27.js";import"./CdFAZ5DU.js";import"./D76T9XJY.js";import"./DMVjPTRc.js";import"./Tedtu6ac.js";import"./DCTLXrZ8.js";import"./DkqMgBWM.js";/* empty css        */import"./l0sNRNKZ.js";/* empty css        */import"./CBW9YMWX.js";import"./BzESl7MG.js";import"./CN8DIg3d.js";import"./B6IIPh85.js";import"./-wubw-0v.js";/* empty css        */import"./D0vVg3b3.js";import"./C6UmJVkT.js";import"./D6j_GvJh.js";import"./BYYVzU6A.js";import"./9Bti1uB6.js";/* empty css        */import"./B2UFrjaV.js";import"./BnLJcfTV.js";import"./D5r7ryMI.js";import"./H3w5L7FX.js";import"./B_j7Mcw4.js";import"./BE7GAo-z.js";import"./CwYwhzCk.js";import"./CJh-I_8R.js";import"./C0R3uvOr.js";/* empty css        */import"./DP2rzg_V.js";import"./CcPlX2kz.js";import"./Dm4nStW0.js";import"./Cv1u9LLW.js";import"./5uRRW6AF.js";const N={class:"h-full"},S={class:"index"},Eo=d({__name:"index",setup(g){const a=c();return(y,B)=>{const n=l,s=_;return t(),r("div",null,[p(s,{name:"single-row"},{default:m(()=>[i("div",N,[p(n,null,{default:m(()=>[i("div",S,[(t(!0),r(f,null,x(e(a).pageIndex,o=>(t(),r("div",{key:o.id},[o.isShow?(t(),h(k(e(u)[o.name]),{key:0,prop:o.prop},null,8,["prop"])):v("",!0)]))),128))])]),_:1})])]),_:1})])}}});export{Eo as default};
