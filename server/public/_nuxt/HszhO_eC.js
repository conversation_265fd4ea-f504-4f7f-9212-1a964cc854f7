import{E as N,a as V}from"./C0aqmoaB.js";import{_ as W}from"./DxKRx1wF.js";import{b as $,j as w,d3 as x,cW as B,E as P}from"./Ct33iMSA.js";import{_ as R}from"./BdCguodY.js";import{_ as A}from"./CD40zxwt.js";import{_ as I}from"./B93za888.js";import{l as F,b as O,m as p,M as a,N as _,O as c,Z as u,a0 as m,_ as j,aq as q,u as t,a6 as g,a4 as r,a1 as l}from"./Dp9aCaJ6.js";import{_ as G}from"./DlAUqK2U.js";import"./Cv6HhfEG.js";import"./DWZQK6lH.js";import"./3BjIQFFf.js";import"./iAKPM1CD.js";import"./DCTLXrZ8.js";import"./B4i2sXD1.js";import"./DXT6IpgZ.js";import"./DDmMBU6l.js";import"./t4HvZ20D.js";import"./VtiWddsO.js";import"./CxsMjoDo.js";import"./DejgKSRh.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./DP2rzg_V.js";/* empty css        */import"./CcPlX2kz.js";import"./DvrbA4QQ.js";import"./BE7GAo-z.js";import"./BPaXy7Em.js";import"./BCfv4qMP.js";import"./BHaz_wmF.js";const M={class:"flex flex-col h-full"},U={class:"flex-1 pt-[20px] px-[30px] min-h-0"},Y={key:0,class:"bg-[#f4f4f4] px-[20px] py-[15px] flex dark:bg-[#333]"},Z={key:0,class:"flex-1 text-tx-secondary"},z=["href"],D=["href"],H=F({__name:"index",setup(J){const s=$(),b=w(),k=[{name:"微信登录",type:"3"},{name:"手机号登录",type:"1"},{name:"邮箱登录",type:"2"}],i=O(1),C=p(()=>k.filter(e=>L.value.includes(e.type))),L=p(()=>{var e;return((e=s.getLoginConfig)==null?void 0:e.login_way)||[]}),f=p(()=>{var e;return((e=s.getLoginConfig)==null?void 0:e.register_way)||[]}),y=p(()=>s.getLoginConfig.is_agreement===1),E=p(()=>(i.value=s.getLoginConfig.default_login_way.toString(),s.getLoginConfig.default_login_way.toString())),v=e=>{i.value=e};return(e,n)=>{const h=V,S=N,d=W;return a(),_("div",M,[c("div",U,[u(S,{"model-value":t(E),onTabChange:v},{default:m(()=>[(a(!0),_(j,null,q(t(C),(o,T)=>(a(),l(h,{key:T,label:o.name,name:o.type},{default:m(()=>[o.type==="3"&&t(i)==="3"?(a(),l(I,{key:0})):r("",!0),o.type==="1"?(a(),l(R,{key:1})):r("",!0),o.type==="2"?(a(),l(A,{key:2})):r("",!0)]),_:2},1032,["label","name"]))),128))]),_:1},8,["model-value"])]),t(f).length&&t(i)!="3"||t(y)?(a(),_("div",Y,[t(y)?(a(),_("div",Z,[n[1]||(n[1]=g(" 您登录即同意 ")),u(d,{to:`/policy/${t(x).SERVICE}`,custom:""},{default:m(({href:o})=>[c("a",{class:"text-tx-primary",href:o,target:"_blank"}," 用户协议 ",8,z)]),_:1},8,["to"]),n[2]||(n[2]=g(" 和 ")),u(d,{class:"text-tx-primary",to:`/policy/${t(x).PRIVACY}`,custom:""},{default:m(({href:o})=>[c("a",{class:"text-tx-primary",href:o,target:"_blank"}," 隐私政策 ",8,D)]),_:1},8,["to"])])):r("",!0),t(f).length&&t(i)!="3"?(a(),l(t(P),{key:1,type:"primary",link:"",onClick:n[0]||(n[0]=o=>t(b).setLoginPopupType(t(B).REGISTER))},{default:m(()=>n[3]||(n[3]=[g(" 注册新账号 ")])),_:1})):r("",!0)])):r("",!0)])}}}),Vt=G(H,[["__scopeId","data-v-b05048cb"]]);export{Vt as default};
