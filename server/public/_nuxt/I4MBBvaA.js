import{E as b,a as k,b as w,d as E,c as C}from"./MjSzvUfH.js";import{b as M,j as z,cW as A,bw as B,cx as D,dE as c}from"./D726nzJl.js";import F from"./UG5xTrDA.js";import H from"./_zIDlIFo.js";import{_ as L}from"./Ds9JMEz3.js";import{_ as N}from"./CwpHHuea.js";import V from"./Y2ZCZKx9.js";import{_ as I}from"./DvIUxaSC.js";import{l as P,m as R,F as T,ar as W,M as m,a1 as a,a0 as e,Z as t,V as p,a4 as s,as as j,X as U,u as n,a2 as X}from"./Dp9aCaJ6.js";import{_ as Z}from"./DlAUqK2U.js";import"./LAbtQoPB.js";import"./DU8ualjj.js";import"./DXe7oepa.js";import"./CN8DIg3d.js";import"./BpYIl71c.js";import"./vDgPRvwV.js";/* empty css        */import"./Cu4TKUNO.js";import"./CoHZZg7R.js";import"./B58jTiS9.js";import"./GbXQDMM-.js";import"./DCTLXrZ8.js";import"./DcfReECr.js";import"./DxoS9eIh.js";import"./9Bti1uB6.js";/* empty css        *//* empty css        */import"./BXMCmyua.js";import"./CEZIOyk0.js";import"./DAK40OfZ.js";import"./t2TIE93p.js";import"./DnE_5Yhr.js";import"./CBTxQWUq.js";import"./DcsXcc99.js";import"./D12SYOqE.js";/* empty css        */import"./BOaJ3oTb.js";import"./BE7GAo-z.js";import"./D3CLxE2I.js";import"./BDcuJK2h.js";import"./D7NF1x92.js";/* empty css        */import"./DP2rzg_V.js";import"./CcPlX2kz.js";/* empty css        */import"./Bn8r6RG7.js";import"./CkAK066m.js";import"./BrUhOgZ5.js";import"./DyqYHYmh.js";import"./CjE8iZ8I.js";/* empty css        */import"./l0sNRNKZ.js";import"./Dafp_Twg.js";import"./CYo-PpF0.js";import"./CVfmg84P.js";import"./CAxJ_37f.js";import"./BA2tMv9Z.js";import"./BwHhKyiz.js";import"./Cv6HhfEG.js";import"./BxHAV5ix.js";import"./CL_qrTVc.js";import"./B0taQFTv.js";import"./CJW7H0Ln.js";import"./CmJNjzrM.js";import"./DzhE9Pcm.js";import"./DUPlCeo8.js";/* empty css        *//* empty css        *//* empty css        */import"./Jlj4NWKR.js";import"./Cwbucawk.js";import"./DqRQRM8o.js";import"./DMFF9nMU.js";import"./bYvTTKjr.js";import"./CLm7wway.js";import"./CQkEtUSn.js";import"./W37O7FSh.js";import"./DsM4SyY3.js";const q=P({__name:"default",setup(x){const f=M(),h=z(),d=A(),g=B();R(()=>f.isMobile?{"--header-height":"50px","--main-padding":"12px"}:{"--main-padding":"15px"});const{height:l}=D(),u=()=>{g.value=d.isDark,d.setTheme()};return T(()=>{u()}),W(()=>{u()}),(o,G)=>{const y=b,v=k,S=w,$=E,i=C;return m(),a(i,{class:"bg-body h-full layout-default",style:X([{height:`${n(l)=="Infinity"?"100vh":n(l)+"px"}`}])},{default:e(()=>[t(y,{height:"var(--header-height)",style:{padding:"0"}},{default:e(()=>[t(F,null,{default:e(()=>{var r;return[(r=o.$slots)!=null&&r.header?p(o.$slots,"header",{key:0},void 0,!0):s("",!0)]}),_:3})]),_:3}),t(i,{class:"min-h-0"},{default:e(()=>{var r;return[t(v,{width:"auto",class:"!overflow-visible"},{default:e(()=>{var _;return[t(H,null,j({_:2},[(_=o.$slots)!=null&&_.aside?{name:"aside",fn:e(()=>[p(o.$slots,"aside",{},void 0,!0)]),key:"0"}:void 0]),1024)]}),_:3}),t(i,{class:U(["overflow-hidden layout-bg rounded-[12px]",{"":(r=o.$slots)==null?void 0:r.aside,"!rounded-none ":(o._.provides[c]||o.$route).meta.hiddenRounded}])},{default:e(()=>[t(S,{class:"scrollbar",style:{padding:"0"}},{default:e(()=>[p(o.$slots,"default",{},void 0,!0)]),_:3}),(o._.provides[c]||o.$route).meta.hiddenFooter?s("",!0):(m(),a($,{key:0,height:"auto"},{default:e(()=>[t(L)]),_:1}))]),_:3},8,["class"])]}),_:3}),n(h).showLogin?(m(),a(N,{key:0})):s("",!0),t(V),t(I)]),_:3},8,["style"])}}}),St=Z(q,[["__scopeId","data-v-1a51c74d"]]);export{St as default};
