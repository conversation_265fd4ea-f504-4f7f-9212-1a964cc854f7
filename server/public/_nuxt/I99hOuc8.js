import{l as ne,j as z,r as j,b as ie,ak as M,M as i,N as l,Z as n,a0 as d,_ as le,aq as ce,u as r,O as a,aa as D,a1 as P,a4 as _,a6 as R,a7 as h,X as S,a9 as $,ab as de,aG as ue,aH as _e}from"./Dp9aCaJ6.js";import{E as pe}from"./BPaXy7Em.js";import{_ as me}from"./DvrbA4QQ.js";import{_ as fe}from"./DxKRx1wF.js";import{E as ge}from"./cZpipWaD.js";import{E as ye}from"./iAKPM1CD.js";import{W as xe}from"./BDmMpw_0.js";import{l as we,j as he,cy as ve,cg as be,cS as ke,J as Ce,E as Ee,f as Pe}from"./Ct33iMSA.js";import{u as Se,_ as Be}from"./DDbp6ww0.js";import{E as qe}from"./CLVsM8Qg.js";/* empty css        *//* empty css        */import"./l0sNRNKZ.js";/* empty css        */import{u as F}from"./DuO6be6_.js";import{b as Ve}from"./DmGK8OfV.js";import{b as Ie,m as Le}from"./CJgd20ip.js";import{a as ze}from"./DjwCd26w.js";import{e as je}from"./LwQ_Zv-K.js";import{_ as Me}from"./DlAUqK2U.js";import"./BCfv4qMP.js";import"./t4HvZ20D.js";import"./BHaz_wmF.js";import"./DCTLXrZ8.js";import"./BHQFQfA3.js";import"./BDl3LJB7.js";import"./DlKZEFPo.js";import"./Bu_nKEGp.js";const De={class:"flex-1 min-h-0 mx-[16px] relative"},Re=["onClick"],$e={class:"flex-1 min-h-[70vh] overflow-hidden mx-auto",style:{"padding-bottom":"100px"}},Fe=["infinite-scroll-disabled"],Te=["id","onClick"],Ne={class:"w-[100px] h-[100px] flex items-center justify-center flex-none relative"},Ue={key:1,class:"text-tx-secondary"},Ae={key:2,class:"absolute inset-0 flex items-center justify-center text-white"},Oe={key:3,class:"absolute inset-0 flex items-center justify-center text-white"},We={class:"flex-1 ml-[20px]"},Ye={key:0,class:"mt-[12px] text-tx-secondary"},Ge={class:"flex justify-between mt-[12px]"},He={key:0,class:"flex items-center"},Je={class:"text-[#BBBBBB] ml-[6px] w-[100px] truncate"},Xe={class:"flex items-center mt-[4px] text-tx-secondary"},Ze={class:"flex items-center"},Ke=["onClick"],Qe=["onClick"],et={key:1,class:"flex justify-center items-center mt-[50px]"},tt={class:"flex flex-col justify-center items-center w-full h-[60vh]"},st={class:"fixed pb-[32px] bottom-0 left-[112px] right-[34px] bg-page"},ot=ne({__name:"music",props:{keyword:{}},async setup(T){let p,v;const N=T,U=we(),b=he(),A=z(null),{playing:B,currentId:y,setCurrentId:O,togglePlay:W,setMusic:Y}=Se(),m=j({page_no:0,page_size:20,keyword:"",category_id:""}),G={4e3:{rowPerView:4},2e3:{rowPerView:3},1800:{rowPerView:3},1600:{rowPerView:3},1440:{rowPerView:2},1360:{rowPerView:2},1280:{rowPerView:2},1024:{rowPerView:2}},s=j({first:!0,more:!0,count:0,loading:!1,lists:[]}),q=ie(0),{data:V}=([p,v]=M(()=>F(()=>ze({type:ke.MUSIC}),{default(){return[]},transform(t){return[{id:"",name:"全部"}].concat(t)},lazy:!0},"$ydYDo0Bg16")),p=await p,v(),p);[p,v]=M(()=>F(()=>C(),{lazy:!0},"$Fywk2foRYg")),await p,v();const C=async()=>{if(!s.loading){if(s.more)m.page_no+=1;else return;s.loading=!0;try{const t=await Ie(m),{lists:o,page_no:c,page_size:x,count:f}=t;if(c*x>f&&(s.more=!1),c==1?s.lists=o:s.lists=[...s.lists,...o],o.length){const g=s.lists.map(u=>(u.square_id=u.id,u.id=u.records_id,u));Y(g),y.value=s.lists[0].records_id}}finally{setTimeout(()=>s.loading=!1,200)}}},k=()=>{m.page_no=0,s.more=!0,C()},H=t=>{U.push({path:"/music/player",query:{id:t.square_id}})},J=t=>{if(t.records_id==y.value){W();return}O(t.records_id)},X=async t=>{if(!b.isLogin){b.toggleShowLogin(!0);return}await Le({records_id:t.square_id,status:t.is_collect?0:1}),m.category_id===0?k():t.is_collect=t.is_collect?0:1},Z=async(t,o)=>{if(!b.isLogin){b.toggleShowLogin(!0);return}try{const c=await $request.get({url:t,responseType:"blob",baseURL:""},{isReturnDefaultResponse:!0,apiPrefix:""});console.log(c);const x=new Blob([c._data],{type:c.headers.get("Content-Type")}),f=window.URL.createObjectURL(x);Ve(f,o)}catch{Pe.msgError("文件下载失败")}},K=z(),Q=t=>{K.value=t,console.log(t)},I=t=>{var o;q.value=t,m.category_id=(o=V.value[t])==null?void 0:o.id,k()};return I(0),ve(()=>N.keyword,t=>{m.keyword=t,k()},{debounce:500}),(t,o)=>{const c=_e,x=ue,f=pe,g=me,u=fe,ee=ge,L=ye,te=xe,se=Ce,oe=Ee,re=Be,ae=qe;return i(),l("div",De,[n(x,{slidesPerView:"auto",spaceBetween:16,class:"category-lists",onSwiper:Q,style:{padding:"10px 0"}},{default:d(()=>[(i(!0),l(le,null,ce(r(V),(e,w)=>(i(),P(c,{key:e.id,style:{width:"auto","margin-right":"12px"}},{default:d(()=>[Object.keys(e).includes("name")?(i(),l("div",{key:0,class:S(["category-item bg-white",{"is-active":r(q)===w}]),onClick:E=>I(w)},h(e.name),11,Re)):_("",!0)]),_:2},1024))),128))]),_:1}),a("div",$e,[D((i(),l("div",{class:"model-lists mb-[10px] mx-[0px]","infinite-scroll-distance":"50","infinite-scroll-delay":200,"infinite-scroll-disabled":!r(s).more},[r(s).lists.length?(i(),P(te,{key:0,ref_key:"waterFull",ref:A,delay:100,list:r(s).lists,width:315,gutter:20,animationDelay:0,animationDuration:0,backgroundColor:"none",animationPrefix:"none",animated:"none",animationEffect:"none",breakpoints:G},{item:d(({item:e})=>{var w;return[a("div",{class:"flex bg-body p-[20px] rounded-[12px] hover:bg-[#EEF2FF]",id:`music-item-${e.id}`,onClick:E=>J(e)},[a("div",Ne,[e.image_url?(i(),P(f,{key:0,src:e.image_url,class:"w-full h-full rounded-[12px]"},null,8,["src"])):(i(),l("div",Ue,[n(g,{name:"local-icon-music1",size:45})])),r(y)==e.records_id&&r(B)?(i(),l("div",Ae,[n(g,{name:"local-icon-pause1",size:20})])):_("",!0),r(y)==e.records_id&&!r(B)?(i(),l("div",Oe,[n(g,{name:"local-icon-play",size:20})])):_("",!0)]),a("div",We,[n(u,{class:S(["text-[16px] font-bold",{"!text-primary":r(y)===e.records_id}]),to:{path:"/music/player",query:{id:e.square_id}}},{default:d(()=>[R(h(e.title),1)]),_:2},1032,["class","to"]),e.tags?(i(),l("div",Ye,h(e.tags),1)):_("",!0),a("div",Ge,[e.user_info?(i(),l("div",He,[n(ee,{size:28,src:(w=e==null?void 0:e.user_info)==null?void 0:w.image},null,8,["src"]),a("p",Je,h(e.user_info.name),1)])):_("",!0),a("div",Xe,h(e.duration),1),a("div",Ze,[n(L,{effect:"dark",content:"收藏 / 取消收藏",placement:"bottom"},{default:d(()=>[a("div",{class:"image-praise relative dark:hover:bg-[rgba(255,255,255,0.05)] hover:bg-[rgba(0,0,0,0.05)] rounded-md p-1 box-content",onClick:$(E=>X(e),["stop"])},[a("div",{class:S(["praise-animate",e.is_collect?"praise-entry":"praise-leave"])},null,2)],8,Ke)]),_:2},1024),n(L,{effect:"dark",content:"下载音乐",placement:"bottom"},{default:d(()=>[a("div",{onClick:$(E=>Z(e.audio_url,e.title),["stop"])},[n(g,{class:"cursor-pointer dark:hover:bg-[rgba(255,255,255,0.05)] hover:bg-[rgba(0,0,0,0.05)] rounded-md p-1 box-content",name:"el-icon-Download",size:"24",color:"#556477"})],8,Qe)]),_:2},1024)])])])],8,Te)]}),_:1},8,["list"])):_("",!0),r(s).loading?(i(),l("div",et,[n(se,{size:"25",class:"is-loading"},{default:d(()=>[n(r(be))]),_:1}),o[0]||(o[0]=a("span",{class:"mt-[4px] ml-[10px] text-[#999999]"},"加载中...",-1))])):_("",!0),D(a("div",tt,[n(f,{class:"w-[200px] h-[200px]",src:r(je)},null,8,["src"]),o[2]||(o[2]=a("div",{class:"text-tx-regular mb-4"},"当前选择暂无音乐～",-1)),n(oe,{type:"primary",onClick:k},{default:d(()=>o[1]||(o[1]=[R(" 点击刷新 ")])),_:1})],512),[[de,!r(s).lists.length&&!r(s).loading]])],8,Fe)),[[ae,C]])]),a("div",st,[n(re,{ref:"musicPlayerRef",class:"bg-body rounded-[12px]",onTitle:H},null,512)])])}}}),Lt=Me(ot,[["__scopeId","data-v-6410fed0"]]);export{Lt as default};
