import{E as b,a as k,b as w,c as E,d as C}from"./CNwj5hPh.js";import{b as M,j as B,cW as z,bw as A,cx as D,dB as c}from"./C9xud4Fy.js";import F from"./UP_BT-6h.js";import H from"./Fa9srB_m.js";import{_ as L}from"./By0c9TrJ.js";import{_ as N}from"./Dszb7j0a.js";import V from"./DgV65ahi.js";import{_ as I}from"./Du_Y1vcu.js";import{l as P,m as R,F as T,ar as W,M as m,a3 as a,a0 as e,a2 as j,u as p,Z as t,V as s,a7 as n,as as U,X}from"./uahP8ofS.js";import{_ as Z}from"./DlAUqK2U.js";import"./I8A8leGL.js";import"./Dd_kbKZi.js";import"./Cp6jQqWb.js";import"./DzplivRi.js";import"./BY8Moot3.js";import"./BaIF4soF.js";/* empty css        */import"./D2641Ura.js";import"./1ctTGChF.js";import"./0Fi_NQ6B.js";import"./9Ov0gw5P.js";import"./DCTLXrZ8.js";import"./QDYWwj7a.js";import"./DVv83yww.js";import"./BD9OgGux.js";/* empty css        *//* empty css        */import"./CJotcpX_.js";import"./2e9ulp1P.js";import"./q7CWkKxM.js";import"./CPGHyRs8.js";import"./CVlyBbl9.js";import"./CC6TKTZR.js";import"./DWsmRjYd.js";import"./B7tOnmRj.js";/* empty css        */import"./B7Pa_RBU.js";import"./CgMcgcz-.js";import"./Cwf6Ab7n.js";import"./D73adq_4.js";import"./ABiOVlWA.js";/* empty css        */import"./DP2rzg_V.js";import"./BfGcwPP1.js";/* empty css        */import"./CbzV_IL6.js";import"./DM8t36pk.js";import"./Bhh0k9k9.js";import"./5nPgGenE.js";import"./_aEA_d5X.js";/* empty css        */import"./l0sNRNKZ.js";import"./BrhttwqI.js";import"./CeULTHO7.js";import"./C1JW1GZc.js";import"./CIQec7Q0.js";import"./BrGL3vyS.js";import"./DOypabm-.js";import"./HrsfEhzV.js";import"./DIKhZqcM.js";import"./Dtsan6Dn.js";import"./BNvvS5EC.js";import"./CrYRPBPt.js";import"./DSHcXcee.js";import"./Bj8bqPxM.js";import"./C-1i1j4b.js";/* empty css        *//* empty css        *//* empty css        */import"./DDwvOinG.js";import"./K4QbyyJ1.js";import"./Bfxj2hZ2.js";import"./vDjtOR7K.js";import"./Bt2oq4Ot.js";import"./B72S-Fto.js";import"./BejnEexj.js";import"./CBCkSvLI.js";import"./DEUj6QS6.js";const q=P({__name:"default",setup(x){const f=M(),h=B(),d=z(),g=A();R(()=>f.isMobile?{"--header-height":"50px","--main-padding":"12px"}:{"--main-padding":"15px"});const{height:l}=D(),u=()=>{g.value=d.isDark,d.setTheme()};return T(()=>{u()}),W(()=>{u()}),(o,G)=>{const y=k,v=w,S=E,$=C,i=b;return m(),a(i,{class:"bg-body h-full layout-default",style:j([{height:`${p(l)=="Infinity"?"100vh":p(l)+"px"}`}])},{default:e(()=>[t(y,{height:"var(--header-height)",style:{padding:"0"}},{default:e(()=>[t(F,null,{default:e(()=>{var r;return[(r=o.$slots)!=null&&r.header?s(o.$slots,"header",{key:0},void 0,!0):n("",!0)]}),_:3})]),_:3}),t(i,{class:"min-h-0"},{default:e(()=>{var r;return[t(v,{width:"auto",class:"!overflow-visible"},{default:e(()=>{var _;return[t(H,null,U({_:2},[(_=o.$slots)!=null&&_.aside?{name:"aside",fn:e(()=>[s(o.$slots,"aside",{},void 0,!0)]),key:"0"}:void 0]),1024)]}),_:3}),t(i,{class:X(["overflow-hidden layout-bg rounded-[12px]",{"":(r=o.$slots)==null?void 0:r.aside,"!rounded-none ":(o._.provides[c]||o.$route).meta.hiddenRounded}])},{default:e(()=>[t(S,{class:"scrollbar",style:{padding:"0"}},{default:e(()=>[s(o.$slots,"default",{},void 0,!0)]),_:3}),(o._.provides[c]||o.$route).meta.hiddenFooter?n("",!0):(m(),a($,{key:0,height:"auto"},{default:e(()=>[t(L)]),_:1}))]),_:3},8,["class"])]}),_:3}),p(h).showLogin?(m(),a(N,{key:0})):n("",!0),t(V),t(I)]),_:3},8,["style"])}}}),St=Z(q,[["__scopeId","data-v-1a51c74d"]]);export{St as default};
