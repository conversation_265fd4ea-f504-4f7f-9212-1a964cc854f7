import{_ as q}from"./DvrbA4QQ.js";import{_ as C}from"./DxKRx1wF.js";import{E}from"./BPaXy7Em.js";import{E as B}from"./B4i2sXD1.js";import{_ as R}from"./BZet8wu2.js";import{a as L,_ as z}from"./Ct33iMSA.js";/* empty css        *//* empty css        */import{u as I}from"./DuO6be6_.js";import{G as S}from"./Bo3PTL3c.js";import{l as V,ak as A,m as u,M as i,N as x,Z as e,a0 as a,O as t,_ as D,aq as F,u as r,a1 as f,a4 as O,X as h,a7 as b}from"./Dp9aCaJ6.js";import"./DlAUqK2U.js";import"./BCfv4qMP.js";import"./t4HvZ20D.js";import"./BHaz_wmF.js";import"./CSt9LTZT.js";import"./C9U8o6T_.js";import"./DXT6IpgZ.js";/* empty css        */import"./D2VNpxWB.js";import"./PIZLygzx.js";import"./BFbcoQee.js";import"./Cpg3PDWZ.js";import"./SbrOwfyf.js";import"./9Bti1uB6.js";import"./DCTLXrZ8.js";/* empty css        */import"./BLxRo6nR.js";import"./Bb2-23m7.js";import"./D16flgaF.js";import"./DwFObZc_.js";import"./B_1915px.js";import"./DmGK8OfV.js";import"./Bg2e09vA.js";import"./COW3kRc3.js";import"./5L1sP24H.js";import"./BuZ1OskF.js";import"./CL661lr-.js";import"./DDmMBU6l.js";import"./PnfXFZ_1.js";import"./DOBoXv-W.js";import"./gTlcmlrF.js";import"./DDaEm-_F.js";import"./CNI-6NF5.js";import"./B77oJvju.js";import"./kzeq9hfp.js";import"./CKri9zTF.js";import"./DfI44KPu.js";const X={class:"h-full flex"},Z={class:"p-4 h-full"},$={class:"w-[300px] h-full flex flex-col bg-body rounded-lg"},G={class:"p-[15px]"},M={class:"flex items-center"},T={class:"flex-1 min-h-0"},j={class:"px-[15px]"},H={class:"flex-1 min-w-0 ml-[15px]"},J={class:"line-clamp-1 text-xl font-medium"},K={class:"flex-1 min-w-0 pr-4 py-4"},P={class:"bg-body rounded-[10px] h-full"},jt=V({__name:"chat",async setup(Q){let s,p;const y=L(),{data:l}=([s,p]=A(()=>I(()=>S(),{default(){return[]},lazy:!0},"$X7Lic9ZOFl")),s=await s,p(),s),m=u(()=>y.query.id),n=u(()=>l.value.find(c=>c.id===Number(m.value))||{});return(c,d)=>{const v=q,_=C,g=E,w=B,k=R,N=z;return i(),x("div",null,[e(N,{name:"default"},{default:a(()=>[t("div",X,[t("div",Z,[t("div",$,[t("div",G,[t("div",M,[e(_,{class:"flex bg-body p-[5px] text-bold rounded-[50%] text-primary shadow-light",to:"/robot_square",replace:!0},{default:a(()=>[e(v,{name:"el-icon-Back",size:18})]),_:1}),d[0]||(d[0]=t("div",{class:"text-xl flex-1 min-w-0 ml-[10px]"}," 智能体广场 ",-1))])]),t("div",T,[e(w,null,{default:a(()=>[t("div",j,[(i(!0),x(D,null,F(r(l),o=>(i(),f(_,{key:o.id,to:{path:"",query:{id:o.id}},class:h(["flex mb-[15px] rounded-[10px] px-[15px] py-[10px] items-center border border-br-light bg-body",{"text-white !border-primary !bg-primary":r(m)==o.id}]),replace:!0},{default:a(()=>[e(g,{class:"w-[50px] h-[50px] rounded-[50%]",src:o.image,alt:""},null,8,["src"]),t("div",H,[t("div",J,b(o.name),1),t("div",{class:h(["line-clamp-1 mt-[4px] text-tx-secondary",{"!text-white":r(m)==o.id}])},b(o.intro),3)])]),_:2},1032,["to","class"]))),128))])]),_:1})])])]),t("div",K,[t("div",P,[r(n).id?(i(),f(k,{key:0,"robot-id":r(n).robot_id,"square-id":r(n).id},null,8,["robot-id","square-id"])):O("",!0)])])])]),_:1})])}}});export{jt as default};
