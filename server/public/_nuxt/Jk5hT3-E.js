import{E as X}from"./DH3BuQAR.js";import{_ as Z}from"./B6IIPh85.js";import{E as w,e as G,s as H,f as J}from"./ClNUxNV9.js";/* empty css        */import{u as K}from"./67xbGseh.js";import{u as Q}from"./CcPlX2kz.js";import{l as R,b as h,r as Y,M as r,N as c,Z as i,a0 as a,O as s,u as o,a6 as E,_ as I,aq as M,a4 as D,a7 as g,X as ee,a9 as _}from"./Dp9aCaJ6.js";import{g as se,p as te}from"./DwFObZc_.js";import{E as le}from"./BQ2TCOuy.js";import{E as oe}from"./C0R3uvOr.js";import{E as y}from"./DggqxWTi.js";import{E as N}from"./-wubw-0v.js";import{E as ie}from"./D6j_GvJh.js";import{_ as ae}from"./DlAUqK2U.js";import"./9Bti1uB6.js";import"./DCTLXrZ8.js";import"./Cr7puf4F.js";import"./Tedtu6ac.js";import"./BYYVzU6A.js";const ne={class:"locality-draw-popup"},de={class:"w-1/2"},re={class:"px-4"},ce={class:"flex items-center"},ue={class:"flex flex-col"},pe={key:0,style:{height:"500px"},class:"flex justify-center items-center text-tx-placeholder"},ve={key:1,class:"mt-4"},xe=["onClick"],fe={class:"ml-2 text-xs"},me={key:0,class:"text-tx-placeholder"},_e={class:"flex flex-col px-4"},ke={class:"text-base"},he={class:"flex items-center"},ge={class:"ml-2 text-tx-regular text-xs"},ye={class:"flex items-center"},Ce={class:"cursor-pointer"},we=["onClick"],Ee=["onClick"],Ve=["onClick"],Pe=["onClick"],$e={class:"text-xs"},Se=["onClick"],ze={class:"flex justify-end"},Ie=R({__name:"add-user",props:{id:{type:Number,default:0}},emits:["success"],setup(U,{expose:b,emit:A}){const B={1:"可管理",2:"可编辑",3:"可查看"},L=A,V=U,x=h(!1),C=h(!1),P=h(-1),f=Y({keyword:"",kb_id:V.id,page_type:0}),n=h([]),{pager:d,getLists:T,resetPage:$,resetParams:Me}=K({fetchFun:se,params:f}),F=()=>{if(!f.keyword.trim()){J.msgWarning("请输入完整的用户ID进行检索"),d.lists=[],d.count=0;return}$(),T()},S=(l,e)=>{if(l.is_added)return;const m=n.value.findIndex(k=>k.id===l.id);m!==-1?(l.permission=0,n.value.splice(m,1)):l.is_added||(l.permission=3,n.value.push(l)),e==="box"?l.isSelected=l.isSelected?0:1:l.isSelected=l.isSelected?1:0},j=l=>{n.value=n.value.filter(e=>e.id!==l.id),d.lists.find(e=>e.id===l.id).isSelected=!1},q=l=>{P.value=l,C.value=!0},z=()=>{C.value=!1},p=(l,e)=>{l.permission=e,z()},{lockFn:O,isLock:W}=Q(async()=>{const l={};n.value.forEach(e=>{l[e.sn]=e.permission}),await te({kb_id:V.id,users:l}),L("success"),x.value=!1});return b({show:()=>{x.value=!0,f.keyword="",d.lists=[],d.count=0,$(),n.value=[]}}),(l,e)=>{const m=X,k=Z;return r(),c("div",ne,[i(o(oe),{modelValue:x.value,"onUpdate:modelValue":e[2]||(e[2]=t=>x.value=t),width:"980px",class:"!rounded-[12px]",center:!0,draggable:!0,"destroy-on-close":!0,"close-on-click-modal":!1},{header:a(()=>e[3]||(e[3]=[s("div",{class:"w-full text-left"},[s("div",{class:"text-lg font-medium"},"添加成员")],-1)])),footer:a(()=>[s("div",ze,[i(o(w),{onClick:e[1]||(e[1]=t=>x.value=!1)},{default:a(()=>e[8]||(e[8]=[E("取消")])),_:1}),i(o(w),{type:"primary",loading:o(W),onClick:o(O)},{default:a(()=>e[9]||(e[9]=[E(" 确认 ")])),_:1},8,["loading","onClick"])])]),default:a(()=>[s("div",{class:"flex",onClick:z},[s("div",de,[s("div",re,[s("div",ce,[i(o(G),{modelValue:f.keyword,"onUpdate:modelValue":e[0]||(e[0]=t=>f.keyword=t),style:{width:"100%"},size:"large",placeholder:"请输入完整的用户ID进行检索","prefix-icon":o(H)},null,8,["modelValue","prefix-icon"]),i(o(w),{type:"primary",class:"ml-2",onClick:F},{default:a(()=>e[4]||(e[4]=[E(" 搜索 ")])),_:1})])]),i(m,{height:"500px"},{default:a(()=>[s("div",ue,[o(d).lists.length===0?(r(),c("div",pe," 请输入完整的用户ID进行检索 ")):(r(),c("div",ve,[o(d).lists.length!==0?(r(!0),c(I,{key:0},M(o(d).lists,t=>(r(),c("div",{class:ee(["my-4 mr-4 py-2 px-4 flex items-center cursor-pointer hover:bg-primary-light-9 rounded-[12px]",{"!cursor-not-allowed":t.is_added}]),key:t.id,onClick:v=>S(t,"box")},[i(o(y),{modelValue:t.isSelected,"onUpdate:modelValue":v=>t.isSelected=v,"true-value":1,"false-value":0,label:"",size:"large",disabled:t.is_added,onClick:_(v=>S(t,"checkbox"),["stop"])},null,8,["modelValue","onUpdate:modelValue","disabled","onClick"]),i(o(N),{src:t.avatar,size:26,class:"flex-none ml-2"},null,8,["src"]),s("div",fe,[s("span",null,g(t.nickname),1),t.is_added?(r(),c("div",me," 已添加 ")):D("",!0)])],10,xe))),128)):D("",!0)]))])]),_:1})]),i(o(le),{direction:"vertical",style:{height:"500px"}}),i(m,{height:"500px",class:"w-1/2"},{default:a(()=>[s("div",_e,[s("div",ke," 已选择："+g(n.value.length)+" 个 ",1),(r(!0),c(I,null,M(n.value,(t,v)=>(r(),c("div",{class:"mt-4 py-2 px-4 flex items-center justify-between cursor-pointer hover:bg-primary-light-9 rounded-[12px]",key:t.id},[s("div",he,[i(o(N),{src:t.avatar,size:26,class:"flex-none ml-2"},null,8,["src"]),s("div",ge,g(t.nickname),1)]),s("div",ye,[i(o(ie),{placement:"bottom-end",width:380,trigger:"click","show-arrow":!1,transition:"custom-popover",visible:C.value&&P.value===v},{reference:a(()=>[s("div",{class:"flex items-center cursor-pointer",onClick:_(u=>q(v),["stop"])},[s("span",$e,g(B[t.permission]),1),i(k,{name:"el-icon-ArrowDown"})],8,Pe)]),default:a(()=>[s("div",Ce,[s("div",{class:"flex items-center p-4 hover:bg-page rounded-xl",onClick:u=>p(t,2)},[e[5]||(e[5]=s("div",{style:{width:"320px"}},[s("div",{class:"text-base text-tx-primary"}," 可编辑 "),s("div",{class:"text-xs text-tx-placeholder mt-2"}," 只能操作数据学习，增删改查自己的数据，不能修改他人 ")],-1)),i(o(y),{"model-value":t.permission,"true-value":2,label:"",size:"large",onClick:_(u=>p(t,2),["stop"])},null,8,["model-value","true-value","onClick"])],8,we),s("div",{class:"flex items-center p-4 hover:bg-page rounded-xl",onClick:u=>p(t,3)},[e[6]||(e[6]=s("div",{style:{width:"320px"}},[s("div",{class:"text-base text-tx-primary"}," 可查看 "),s("div",{class:"text-xs text-tx-placeholder mt-2"}," 查看知识库所有数据 ")],-1)),i(o(y),{"model-value":t.permission,"true-value":3,label:"",size:"large",onClick:_(u=>p(t,3),["stop"])},null,8,["model-value","true-value","onClick"])],8,Ee),s("div",{class:"flex items-center p-4 hover:bg-page rounded-xl",onClick:u=>p(t,1)},[e[7]||(e[7]=s("div",{style:{width:"320px"}},[s("div",{class:"text-base text-tx-primary"}," 可管理 "),s("div",{class:"text-xs text-tx-placeholder mt-2"}," 管理整个知识库数据和信息 ")],-1)),i(o(y),{"model-value":t.permission,"true-value":1,label:"",size:"large",onClick:_(u=>p(t,1),["stop"])},null,8,["model-value","true-value","onClick"])],8,Ve)])]),_:2},1032,["visible"]),s("div",{class:"flex items-center ml-6",onClick:u=>j(t)},[i(k,{name:"el-icon-CloseBold"})],8,Se)])]))),128))])]),_:1})])]),_:1},8,["modelValue"])])}}}),Qe=ae(Ie,[["__scopeId","data-v-d910c929"]]);export{Qe as default};
