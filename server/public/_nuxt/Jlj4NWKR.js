import{j as K,b as O,dz as T,cX as V,dp as P,dm as U,e as j,o as M,E as q,p as A,dA as D}from"./D726nzJl.js";import{_ as G}from"./DUPlCeo8.js";import"./DP2rzg_V.js";/* empty css        */import{l as $,j as B,r as z,m as F,M as i,N as X,O as u,Z as l,a0 as n,u as o,ai as c,a1 as f,a4 as y,a6 as _}from"./Dp9aCaJ6.js";import{u as W}from"./CcPlX2kz.js";const Z={class:"pt-[10px]"},H={class:"flex justify-center leading-5 w-[90px] pl-2.5 border-l border-br"},J={class:"flex justify-end"},Q={class:"flex-1 flex"},le=$({__name:"mailbox-login",setup(Y){const r=K(),I=O(),g=B(),S={email:[{required:!0,message:"请输入邮箱账号"},{type:"email",message:"请输入正确的邮箱账号"}],password:[{required:!0,message:"请输入密码"}],code:[{required:!0,message:"请输入验证码"}]},t=z({code:"",email:"",password:"",scene:4,terminal:T}),w=F(()=>t.scene===4),v=F(()=>t.scene===2),E=s=>{t.scene=s},C=B(),b=async()=>{var s,e;await((s=g.value)==null?void 0:s.validateField(["email"])),await P({scene:U.LOGIN,email:t.email}),(e=C.value)==null||e.start()},{lockFn:x,isLock:L}=W(async()=>{var e;await((e=g.value)==null?void 0:e.validate());const s=await D(t);!s.mobile&&I.getLoginConfig.coerce_mobile?(r.temToken=s.token,r.setLoginPopupType(V.BIND_MOBILE)):(r.login(s.token),r.setUser(s),r.toggleShowLogin(!1),location.reload())}),d=()=>{L.value||x()};return(s,e)=>{const k=j,m=M,N=G,p=q,R=A;return i(),X("div",Z,[u("div",null,[l(R,{ref_key:"formRef",ref:g,size:"large",model:o(t),rules:S,onKeyup:c(d,["enter"])},{default:n(()=>[l(m,{prop:"email"},{default:n(()=>[l(k,{modelValue:o(t).email,"onUpdate:modelValue":e[0]||(e[0]=a=>o(t).email=a),placeholder:"请输入邮箱账号",onKeyup:c(d,["enter"])},null,8,["modelValue"])]),_:1}),o(v)?(i(),f(m,{key:0,prop:"password"},{default:n(()=>[l(k,{modelValue:o(t).password,"onUpdate:modelValue":e[1]||(e[1]=a=>o(t).password=a),type:"password","show-password":"",placeholder:"请输入密码",onKeyup:c(d,["enter"])},null,8,["modelValue"])]),_:1})):y("",!0),o(w)?(i(),f(m,{key:1,prop:"code"},{default:n(()=>[l(k,{modelValue:o(t).code,"onUpdate:modelValue":e[2]||(e[2]=a=>o(t).code=a),placeholder:"请输入验证码",onKeyup:c(d,["enter"])},{suffix:n(()=>[u("div",H,[l(N,{ref_key:"verificationCodeRef",ref:C,onClickGet:b},null,512)])]),_:1},8,["modelValue"])]),_:1})):y("",!0),u("div",J,[u("div",Q,[o(v)?(i(),f(p,{key:0,type:"primary",link:"",onClick:e[3]||(e[3]=a=>E(4))},{default:n(()=>e[6]||(e[6]=[_(" 邮箱验证码登录 ")])),_:1})):y("",!0),o(w)?(i(),f(p,{key:1,type:"primary",link:"",onClick:e[4]||(e[4]=a=>E(2))},{default:n(()=>e[7]||(e[7]=[_(" 邮箱密码登录 ")])),_:1})):y("",!0)]),l(p,{link:"",onClick:e[5]||(e[5]=a=>o(r).setLoginPopupType(o(V).FORGOT_PWD_MAILBOX))},{default:n(()=>e[8]||(e[8]=[_(" 忘记密码？ ")])),_:1})]),l(m,{class:"my-[30px]"},{default:n(()=>[l(p,{class:"w-full",type:"primary",loading:o(L),onClick:o(x)},{default:n(()=>e[9]||(e[9]=[_(" 登录 ")])),_:1},8,["loading","onClick"])]),_:1})]),_:1},8,["model"])])])}}});export{le as _};
