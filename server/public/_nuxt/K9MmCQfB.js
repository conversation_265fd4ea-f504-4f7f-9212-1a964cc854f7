import{E as k}from"./2KyQdWL7.js";import{l as E,h as C,E as V}from"./C12kmceL.js";import{E as B}from"./BnhkBVfO.js";/* empty css        *//* empty css        */import{u as N}from"./loTdDtpJ.js";import{r as D}from"./01DN-8Sp.js";import{a as j}from"./DxfcmQ5j.js";import{l as I,m as M,M as u,a3 as $,a0 as a,u as l,y as F,O as t,Z as i,a4 as p,a5 as o,N as v,a7 as _}from"./uahP8ofS.js";const O={class:"flex text-tx-primary"},R={class:"flex w-[510px] justify-center items-center"},S={class:"w-[350px] ml-[20px] flex flex-col"},z={class:"bg-page p-[20px] rounded-[15px] flex-1 min-h-0"},H={class:"mb-[15px]"},J={class:"flex items-center mb-[15px]"},P={class:"bg-body rounded-[10px] h-[110px] overflow-y-auto"},T={class:"p-[10px]"},U={key:0,class:"mb-[15px]"},Z={class:"flex items-center mb-[15px]"},q={class:"bg-body rounded-[10px] h-[110px] overflow-y-auto"},A={class:"p-[10px]"},G={class:"mb-[15px]"},K={class:"bg-body rounded-[10px] p-[10px]"},L={class:"flex mb-[15px]"},Q={class:"ml-[20px] text-right"},W={class:"flex mb-[15px]"},X={class:"ml-[20px] text-right"},Y={class:"flex mb-[15px]"},ss={class:"ml-[20px] text-right"},ts={key:0,class:"flex mb-[15px]"},es={class:"ml-[20px] text-right"},os={class:"flex mb-[15px]"},as={class:"ml-[20px] text-right"},ls={class:"flex mb-[15px]"},is={class:"ml-[20px] text-right"},ds={class:"flex justify-end mt-[20px]"},ms={class:"flex-1 mr-[20px]"},ns={class:"flex-1"},ys=I({__name:"image-preview",props:{show:{type:Boolean},data:{default:()=>({})}},emits:["update:show"],setup(c,{emit:g}){const x=E(),{copy:f}=N(),d=c,m=C(d,"show",g),w=M(()=>d.data.complex_params?JSON.parse(d.data.complex_params):{}),y=()=>{const e=d.data,s={draw_model:e.engine,image_mask:e.image_base,negative_prompt:e.negative_prompt,prompt:e.prompt,size:e.scale,draw_loras:e.loras};e.image_base&&(s.draw_type="img2img"),D(s),m.value=!1,e.model==="dalle3"?x.push("/draw/dalle"):e.model==="sd"?x.push("/draw/sd"):x.push("/draw/mj")};return(e,s)=>{const b=k,n=V,h=B;return u(),$(h,{modelValue:l(m),"onUpdate:modelValue":s[3]||(s[3]=r=>F(m)?m.value=r:null),width:"900px"},{default:a(()=>[t("div",O,[t("div",R,[i(b,{class:"w-full rounded-[12px]",src:e.data.image,"preview-src-list":[e.data.image],"hide-on-click-modal":""},null,8,["src","preview-src-list"])]),t("div",S,[t("div",z,[t("div",H,[t("div",J,[s[5]||(s[5]=t("div",{class:"font-medium mr-auto"},"描述词",-1)),i(n,{link:"",type:"primary",onClick:s[0]||(s[0]=r=>l(f)(e.data.prompt))},{default:a(()=>s[4]||(s[4]=[p(" 复制 ")])),_:1})]),t("div",P,[t("div",T,o(e.data.prompt),1)])]),e.data.negative_prompt?(u(),v("div",U,[t("div",Z,[s[7]||(s[7]=t("div",{class:"font-medium mr-auto"},"反向词",-1)),i(n,{link:"",type:"primary",onClick:s[1]||(s[1]=r=>l(f)(e.data.negative_prompt))},{default:a(()=>s[6]||(s[6]=[p(" 复制 ")])),_:1})]),t("div",q,[t("div",A,o(e.data.negative_prompt),1)])])):_("",!0),t("div",G,[s[14]||(s[14]=t("div",{class:"flex items-center mb-[15px]"},[t("div",{class:"font-medium mr-auto"},"创作信息")],-1)),t("div",K,[t("div",L,[s[8]||(s[8]=t("div",{class:"mr-auto flex-none"},"生成时间",-1)),t("div",Q,o(e.data.create_time),1)]),t("div",W,[s[9]||(s[9]=t("div",{class:"mr-auto flex-none"},"绘画类型",-1)),t("div",X,o(e.data.draw_type==="txt2img"?"文生图":"图生图"),1)]),t("div",Y,[s[10]||(s[10]=t("div",{class:"mr-auto flex-none"},"绘画模型",-1)),t("div",ss,o(e.data.engine),1)]),e.data.loras.length?(u(),v("div",ts,[s[11]||(s[11]=t("div",{class:"mr-auto flex-none"},"微调模型",-1)),t("div",es,o(e.data.loras),1)])):_("",!0),t("div",os,[s[12]||(s[12]=t("div",{class:"mr-auto flex-none"},"图片尺寸",-1)),t("div",as,o(e.data.scale),1)]),t("div",ls,[s[13]||(s[13]=t("div",{class:"mr-auto flex-none"},"绘画步数",-1)),t("div",is,o(l(w).step),1)])])])]),t("div",ds,[t("div",ms,[i(n,{class:"w-full",type:"primary",onClick:y},{default:a(()=>s[15]||(s[15]=[p(" 画同款 ")])),_:1})]),t("div",ns,[i(n,{class:"w-full",plain:"",type:"primary",onClick:s[2]||(s[2]=r=>l(j)(e.data.image))},{default:a(()=>s[16]||(s[16]=[p(" 下载图片 ")])),_:1})])])])])]),_:1},8,["modelValue"])}}});export{ys as _};
