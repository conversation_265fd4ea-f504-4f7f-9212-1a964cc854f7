import{_ as m}from"./D5KDMvDa.js";import{b as p,a as f}from"./C3HqF-ve.js";import{l as _,b as v,m as h,M as s,N as b,O as t,V as k,a1 as w,aa as B,ab as C,u as a,Z as O,at as S}from"./Dp9aCaJ6.js";import{_ as $}from"./DlAUqK2U.js";const N={class:"chat-container"},V=["src"],g=_({__name:"online",setup(x){const n=p(),r=f(),e=v(!1),i=h(()=>n.getOnlineKf),c=()=>{setTimeout(()=>{r.path=="/"&&(e.value=!0)},5e3)};return(l,o)=>{const d=m;return s(),b("div",null,[t("div",{onClick:o[0]||(o[0]=u=>e.value=!0)},[k(l.$slots,"default",{},void 0,!0)]),(s(),w(S,{to:"body"},[B(t("div",N,[t("div",{class:"close-icon",onClick:o[1]||(o[1]=u=>e.value=!1)},[O(d,{name:"el-icon-Close",size:18})]),t("iframe",{width:"100%",height:"100%",border:"none",src:a(i).link,onLoad:c},null,40,V)],512),[[C,a(e)]])]))])}}}),z=$(g,[["__scopeId","data-v-009bd42f"]]);export{z as default};
