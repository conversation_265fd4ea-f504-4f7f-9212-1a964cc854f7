import{_ as O}from"./BpYIl71c.js";import{a as j,E as L}from"./B0taQFTv.js";import{E as M,a as A}from"./PVsoX9id.js";import{e as S,E as T}from"./D726nzJl.js";import{_ as W}from"./CJ8CVceb.js";import{_ as Z}from"./CpSR-rXf.js";import{P as G}from"./DaBGfXF7.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import{u as H}from"./CcPlX2kz.js";import{n as J,o as K,p as Q,q as X}from"./eyo1vffA.js";import{l as Y,j as ee,b as d,m as te,c as oe,M as p,a1 as _,a0 as m,O as l,Z as n,u as s,y,N as E,aq as g,_ as C,a4 as f}from"./Dp9aCaJ6.js";import{_ as ae}from"./DlAUqK2U.js";import"./GbXQDMM-.js";import"./DCTLXrZ8.js";import"./DxoS9eIh.js";import"./CJW7H0Ln.js";import"./Cv6HhfEG.js";import"./CmJNjzrM.js";import"./DcsXcc99.js";import"./DzhE9Pcm.js";import"./CjE8iZ8I.js";import"./BpD5EEWf.js";import"./DHCaImEx.js";import"./qffJON56.js";import"./D7NF1x92.js";import"./9Bti1uB6.js";/* empty css        *//* empty css        *//* empty css        */import"./DVPRiAuL.js";import"./xQb50RO9.js";import"./Cpg3PDWZ.js";const le={class:"important-notice mb-4"},se={class:"notice-header"},ne={class:"example-tip mb-4"},ie={class:"tip-header"},ce={class:"mb-4"},re={class:"grid grid-cols-2 gap-x-[20px]"};const de=Y({__name:"editPop",emits:["success","close"],setup(me,{expose:B,emit:N}){const U=N,v=ee(),h=d(""),a=d({kb_id:"",fd_id:"",question:"",answer:"",files:[],images:[],video:[],uuid:""}),i=d(""),r=d(""),x=d([]),u=d([]),D=te(()=>{if(!i.value)return[];const t=u.value.find(e=>e.id===i.value);return t?t.examples:[]}),P=()=>{r.value=""},$=()=>{const t=u.value.find(e=>e.id===i.value);if(t){const e=t.examples.find(c=>c.id===r.value);e&&(a.value.question=e.question,a.value.answer=e.answer)}},I=async()=>{try{const t=await J();u.value=t,x.value=t.map(e=>({id:e.id,name:e.name}))}catch(t){console.error("获取示例库数据失败:",t)}};oe(h,t=>{a.value.video=[{url:t,name:""}]});const{lockFn:q}=H(async()=>{a.value.uuid?await K({...a.value}):await Q({...a.value}),U("success")}),F=async()=>{var e;const t=await X({uuid:a.value.uuid});Object.keys(a.value).map(c=>{a.value[c]=t[c]}),h.value=((e=t.video[0])==null?void 0:e.url)||""};return B({open:t=>{v.value.open(),i.value="",r.value="",[a.value.kb_id,a.value.fd_id,a.value.uuid]=[t.kb_id,t.fd_id,t.uuid||""],t.hasOwnProperty("uuid")&&F(),I()}}),(t,e)=>{const c=O,V=L,w=j,b=M,R=A,k=S,ue=W,_e=Z,fe=T,z=G;return p(),_(z,{ref_key:"popRef",ref:v,title:"录入数据",width:"800px",async:"",onConfirm:s(q),onClose:e[7]||(e[7]=o=>t.$emit("close"))},{default:m(()=>[l("div",null,[l("div",le,[l("div",se,[n(c,{name:"el-icon-Warning",color:"#e6a23c",size:"18"}),e[8]||(e[8]=l("span",{class:"notice-title"},"⚠️ 重要提示",-1))]),e[9]||(e[9]=l("div",{class:"notice-content"}," 为保护您的信息安全，请勿录入如姓名、身份证号、电话等个人隐私信息及任何工作秘密、商业秘密。本平台在未获得您同意的情况下，不会使用您的数据进行模型训练。 ",-1))]),l("div",ne,[l("div",ie,[n(c,{name:"el-icon-Lightbulb",color:"#409EFF",size:"18"}),e[10]||(e[10]=l("span",{class:"tip-title"},"💡 小贴士",-1))]),e[11]||(e[11]=l("div",{class:"tip-content"}," 可以试试直接选择示例，快速掌握知识库使用方法，成为AI达人 ",-1))]),l("div",ce,[n(R,{gutter:20},{default:m(()=>[n(b,{span:12},{default:m(()=>[n(w,{modelValue:s(i),"onUpdate:modelValue":e[0]||(e[0]=o=>y(i)?i.value=o:null),placeholder:"请选择示例类别",clearable:"",class:"w-full",onChange:P},{default:m(()=>[(p(!0),E(C,null,g(s(x),o=>(p(),_(V,{key:o.id,label:o.name,value:o.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),n(b,{span:12},{default:m(()=>[n(w,{modelValue:s(r),"onUpdate:modelValue":e[1]||(e[1]=o=>y(r)?r.value=o:null),placeholder:"请选择具体示例",clearable:"",class:"w-full",disabled:!s(i),onChange:$,filterable:""},{default:m(()=>[(p(!0),E(C,null,g(s(D),o=>(p(),_(V,{key:o.id,label:o.title,value:o.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),l("div",re,[n(k,{modelValue:s(a).question,"onUpdate:modelValue":e[2]||(e[2]=o=>s(a).question=o),type:"textarea",placeholder:"请输入文档内容，你可以理解为提问的问题（必填）",rows:"10"},null,8,["modelValue"]),n(k,{modelValue:s(a).answer,"onUpdate:modelValue":e[3]||(e[3]=o=>s(a).answer=o),type:"textarea",placeholder:"请填入补充内容，你可以理解为问题的答案",rows:"10"},null,8,["modelValue"])]),f("",!0),f("",!0),f("",!0)])]),_:1},8,["onConfirm"])}}}),Qe=ae(de,[["__scopeId","data-v-ba361af2"]]);export{Qe as default};
