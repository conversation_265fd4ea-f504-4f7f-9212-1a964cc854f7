import{_ as ge}from"./BNsSO7uz.js";import{_ as he}from"./DzWd4x2z.js";import{c as ye,a as ve,_ as xe,b as we}from"./CVgoAVKM.js";import{_ as Se}from"./B6IIPh85.js";import{i as Ce,b as be,j as ke,l as Le,bw as Ee,bx as Ie,by as Re,f as D,_ as Te,E as Ae}from"./ClNUxNV9.js";import{E as $e,a as De}from"./CQXeYJFv.js";import{_ as Fe}from"./CN8DIg3d.js";import{E as Ue}from"./DH3BuQAR.js";import{_ as Ve}from"./DIFvMdL6.js";import{E as Ne}from"./DiZwe6ND.js";/* empty css        */import{l as Me,j as X,b as F,ak as Z,r as Be,c as ze,F as Oe,n as G,k as je,M as d,N as h,Z as u,a0 as p,O as _,u as e,_ as B,aq as z,as as Pe,a1 as L,a4 as U,a6 as qe,a9 as He,a7 as K,X as We}from"./Dp9aCaJ6.js";import{u as Je}from"./B2UFrjaV.js";import{u as Q}from"./BnLJcfTV.js";import{u as Xe}from"./CcPlX2kz.js";import Ze from"./BLlVAs2h.js";import{_ as Ge}from"./_E68iMkU.js";import{h as Ke,i as Qe,j as Ye,k as et,l as tt,e as ot,b as Y,c as st}from"./B_1915px.js";import{useSessionFiles as nt}from"./DgjIqVqm.js";import{_ as at}from"./DlAUqK2U.js";import"./B1qK8f0i.js";/* empty css        */import"./DTSvt-82.js";import"./hx-0JZAY.js";import"./CNgDMrD1.js";import"./zRTrVFrw.js";import"./D4cQUBDp.js";import"./scKBn7ss.js";/* empty css        */import"./mW1R_rAi.js";import"./Cga6GjQY.js";import"./Cpg3PDWZ.js";import"./C0R3uvOr.js";import"./9Bti1uB6.js";import"./DCTLXrZ8.js";/* empty css        */import"./BviKld3d.js";import"./Bb2-23m7.js";import"./DwFObZc_.js";import"./D0cj6m41.js";import"./Bg2e09vA.js";import"./CraaJdZW.js";import"./CqanTtdS.js";import"./CxSV922q.js";import"./CeZA--ML.js";import"./Cr7puf4F.js";import"./DMVjPTRc.js";import"./Cd8UtlNR.js";import"./Tedtu6ac.js";import"./Cv6HhfEG.js";import"./BbPTMigZ.js";import"./DkqMgBWM.js";/* empty css        *//* empty css        */import"./qQcR97T8.js";import"./DCJ0qFa1.js";import"./NEhbE7vl.js";import"./DZESUSa0.js";import"./C5YZ-9ot.js";const it=Ce({id:"dialogueSate",state:()=>({sessionId:"",sessionLists:[]}),getters:{getCurrentSession:r=>r.sessionLists.find(x=>String(x.id)===String(r.sessionId))||{}},actions:{setSessionId(r=""){this.sessionId=String(r)},setSessionSelect(r){r||([r]=this.sessionLists),this.setSessionId((r==null?void 0:r.id)||"")},async getSessionLists(){const r=await Ke({page_type:0});return this.sessionLists=r.lists||[],this.setSessionSelect(),this.sessionLists},async sessionAdd(){await Qe({}),await this.getSessionLists(),this.setSessionSelect()},async sessionEdit(r){await Ye({...r}),await this.getSessionLists(),this.setSessionSelect(r)},async sessionClear(){await et(),await this.getSessionLists()},async sessionDelete(r){await tt({id:r}),await this.getSessionLists()}}}),rt={class:"h-full flex"},lt={class:"p-[16px]"},ct={class:"flex items-center justify-around text-xl font-medium px-[16px] pt-[16px] cursor-pointer"},pt={class:"flex-1 min-w-0 pr-4 py-4"},dt={class:"h-full flex flex-col bg-body rounded-[12px]"},ut={class:"flex-1 min-h-0"},mt={class:"my-[5px]"},ft={key:0,class:"flex flex-col",style:{"margin-left":"52px"}},_t=["onClick"],gt={class:"mr-2 text-tx-primary"},ht={key:1,class:"max-w-[1200px] mx-auto"},yt={class:"mb-[10px] px-[30px]"},vt={class:"flex gap-3 mr-3"},xt={class:"flex items-center h-12 px-3 bg-page rounded-lg max-w-xs line-clamp-1 overflow-hidden"},wt=Me({__name:"chat",async setup(r){var J;let x,V;const y=be(),w=ke(),N=X(),a=it(),ee=Le(),O=F(""),{copy:te}=Je(),C=nt();[x,V]=Z(()=>Q(()=>a.getSessionLists(),{lazy:!0},"$SUo5FcMjp8")),await x,V();const oe=Ee(),{data:c,refresh:j}=([x,V]=Z(()=>Q(()=>ot({type:1,category_id:a.sessionId,page_type:0}),{transform(i){return i.lists||[]},default(){return[]},lazy:!0},"$5oItIRu1UV")),x=await x,V(),x);(J=y.getChatConfig)!=null&&J.is_reopen&&(a.sessionAdd(),y.getChatConfig.is_reopen=0);const se=async()=>{if(!w.isLogin)return w.toggleShowLogin();a.sessionId&&(await D.confirm("确定清空记录？"),await Y({category_id:a.sessionId,type:1}),j())},M=F(-1),{lockFn:ne}=Xe(async()=>{const i=c.value[c.value.length-1],t=c.value.find(({id:v})=>v===i.id);t&&(M.value=i.id,c.value.splice(c.value.length-2,2),I(t.content))}),f=Be({show:!1,data:{url:"",name:"",type:"10"}}),ae=i=>{f.show=!!i.support_image,f.show||(f.data.url="")},ie=()=>{var i;if(!w.isLogin)return(i=N.value)==null||i.blur(),w.toggleShowLogin();R()};let s=null;const S=F(!1);let P=!1;const E=F([]),I=async(i,t="input")=>{var T;if(!w.isLogin)return w.toggleShowLogin();if(!i)return D.msgError("请输入问题");if(S.value)return;const v=Date.now();E.value=[],S.value=!0;const b=C.files.value.map(n=>({name:n.name,type:"30",url:n.url}));f.data.url&&b.push({...f.data}),c.value.push({type:1,content:i,files_plugin:b}),c.value.push({type:2,typing:!0,content:[""],reasoning:"",key:v}),(T=N.value)==null||T.setInputValue();const l=c.value.find(n=>n.key===v);a.sessionId||(P=!0,await a.sessionAdd(),P=!1),s=st({type:1,other_id:a.sessionId,question:i,model:O.value,annex:f.data.url?[{type:f.data.type,name:f.data.name,url:f.data.url}]:[],files:C.files.value.map(n=>({...n,type:"30"}))}),s.addEventListener("reasoning",({data:n})=>{const{data:m,index:g}=n;l.reasoning||(l.reasoning=""),l.reasoning+=m}),s.addEventListener("chat",({data:n})=>{const{data:m,index:g}=n;l.content[g]||(l.content[g]=""),l.content[g]+=m}),s.addEventListener("finish",({data:n})=>{const{data:m,index:g}=n;m&&(l.content[g]+=m),f.data.url=""}),s.addEventListener("question",({data:n})=>{E.value=JSON.parse(n.data)}),s.addEventListener("close",async()=>{M.value!==-1&&l.content[0].length&&(await Y({type:1,id:M.value}),M.value=-1),await w.getUser(),a.getCurrentSession.name==="新的会话"&&await a.sessionEdit({id:a.sessionId,name:i}),S.value=!1,l.typing=!1;const n=l.content[0]&&l.content[0].length>0,m=l.reasoning&&l.reasoning.length>0;setTimeout(async()=>{await j(),await G(),R()},n||m?3e3:1e3)}),s.addEventListener("error",async n=>{var m,g;if(t==="input"&&((m=N.value)==null||m.setInputValue(i)),((g=n.data)==null?void 0:g.code)===1100){y.getIsShowRecharge?(await D.confirm(`${y.getTokenUnit}数量已用完，请前往充值`),ee.push("/user/recharge")):D.msgError(`${y.getTokenUnit}数量已用完。请联系客服增加`);return}n.errorType==="connectError"&&D.msgError("请求失败，请重试"),["connectError","responseError"].includes(n.errorType)&&c.value.splice(c.value.length-2,2),l.typing=!1,setTimeout(()=>{S.value=!1},200)})},q=X(),H=F(),R=async()=>{var t,v,b;const i=(v=(t=q.value)==null?void 0:t.wrapRef)==null?void 0:v.scrollHeight;(b=q.value)==null||b.setScrollTop(i)},{height:re}=Ie(H);Re(re,()=>{S.value&&R()},{immediate:!0});const W=()=>{s==null||s.removeEventListener("chat"),s==null||s.removeEventListener("close"),s==null||s.removeEventListener("error"),s==null||s.removeEventListener("finish"),s==null||s.abort(),S.value=!1,E.value=[]};return ze(()=>a.sessionId,async(i,t)=>{!P&&i!=t&&(W(),await j(),R())}),Oe(async()=>{await G(),c.value.length&&R()}),je(()=>{W()}),(i,t)=>{const v=ge,b=he,l=ve,T=Se,n=Ae,m=xe,g=$e,le=De,ce=Fe,pe=Ue,de=Ve,ue=we,me=ye,fe=Ne,_e=Te;return d(),h("div",null,[u(_e,{name:"default"},{default:p(()=>[_("div",rt,[_("div",lt,[u(b,{modelValue:e(a).sessionId,"onUpdate:modelValue":t[0]||(t[0]=o=>e(a).sessionId=o),data:e(a).sessionLists,onAdd:e(a).sessionAdd,onEdit:e(a).sessionEdit,onDelete:e(a).sessionDelete,onClear:e(a).sessionClear,onClickItem:e(a).setSessionSelect},{top:p(()=>[_("div",ct,[t[7]||(t[7]=_("div",{class:"pb-[6px] text-primary border-solid border-b-[2px] border-primary"}," 问答助手 ",-1)),u(v,{to:"/dialogue/role"},{default:p(()=>t[6]||(t[6]=[_("div",{class:"pb-[8px]"},"角色助手",-1)])),_:1})])]),_:1},8,["modelValue","data","onAdd","onEdit","onDelete","onClear","onClickItem"])]),_("div",pt,[u(fe,{class:"h-full",content:e(y).getChatConfig.watermark,font:{color:e(oe)?"rgba(256,256,256,0.08)":"rgba(0,0,0,0.06)",fontSize:12}},{default:p(()=>[_("div",dt,[_("div",ut,[u(pe,{ref_key:"scrollbarRef",ref:q},{default:p(()=>[u(ce,null,{default:p(()=>[e(c).length?(d(),h("div",{key:0,ref_key:"innerRef",ref:H,class:"px-8"},[(d(!0),h(B,null,z(e(c),(o,k)=>(d(),h("div",{key:o.id+""+k,class:"mt-4 sm:pb-[20px]"},[o.type==1?(d(),L(m,{key:0,type:"right",avatar:e(w).userInfo.avatar,color:"white"},{actions:p(()=>[_("div",mt,[u(n,{link:"",type:"info",onClick:A=>e(te)(o.content)},{icon:p(()=>[u(T,{name:"el-icon-CopyDocument"})]),default:p(()=>[t[8]||(t[8]=qe(" 复制 "))]),_:2},1032,["onClick"])])]),default:p(()=>[u(l,{content:o.content,"files-plugin":o.files_plugin},null,8,["content","files-plugin"])]),_:2},1032,["avatar"])):U("",!0),o.type==2?(d(),L(m,{key:1,type:"left",avatar:e(y).getChatConfig.chat_logo,time:o.create_time,bg:"var(--el-bg-color-page)",modelName:o.model},{outer_actions:p(()=>[k===e(c).length-1&&!S.value?(d(),h("div",ft,[(d(!0),h(B,null,z(E.value.length?E.value:o.correlation,(A,$)=>(d(),h("div",{key:$,class:"inline-flex items-center rounded-[12px] bg-page cursor-pointer mt-[10px] hover:bg-primary-light-9",style:{padding:"8px 12px",width:"fit-content"},onClick:He(St=>I(A,"input"),["stop"])},[_("span",gt,K(A),1),u(T,{name:"el-icon-Right",color:"#999",size:"20"})],8,_t))),128))])):U("",!0)]),default:p(()=>[o.reasoning?(d(),L(le,{key:0,"model-value":"the-chat-msg-collapse",class:"mb-2 the-chat-msg-collapse"},{default:p(()=>[u(g,{title:"深度思考",name:"the-chat-msg-collapse"},{default:p(()=>[u(l,{content:o.reasoning,class:"text-tx-secondary px-3 border-l-[3px] border-br-light"},null,8,["content"])]),_:2},1024)]),_:2},1024)):U("",!0),(d(!0),h(B,null,z(o.content,(A,$)=>(d(),L(l,{key:$,content:A,type:"html",typing:o.typing,"line-numbers":!e(y).isMobile,"show-rewrite":k===e(c).length-1,"show-copy":"","show-voice":e(y).getIsVoiceOpen,class:We(["mb-[15px] last-of-type:mb-0",{"pt-[15px] border-t border-solid border-br-light":$>0}]),"show-poster":"","record-list":e(c),index:$,"record-id":o.id,onRewrite:e(ne)},null,8,["content","typing","line-numbers","show-rewrite","show-voice","class","record-list","index","record-id","onRewrite"]))),128))]),_:2},1032,["avatar","time","modelName"])):U("",!0)]))),128))],512)):(d(),h("div",ht,[u(Ze,{onClickItem:t[1]||(t[1]=o=>I(o,"sample"))})]))]),_:1})]),_:1},512)]),_("div",yt,[u(me,{ref_key:"chatActionRef",ref:N,loading:S.value,"file-plugin":e(f).data,"onUpdate:filePlugin":t[3]||(t[3]=o=>e(f).data=o),onEnter:I,onClear:se,onPause:t[4]||(t[4]=o=>{var k;return(k=e(s))==null?void 0:k.abort()}),onFocus:ie,"show-continue":e(c).length,"show-file-upload":e(f).show,onContinue:t[5]||(t[5]=o=>I("继续","btn"))},Pe({btn:p(()=>[_("div",vt,[u(de,{class:"min-w-[280px] select-class",sub_id:O.value,"onUpdate:sub_id":t[2]||(t[2]=o=>O.value=o),"onUpdate:modelConfig":ae},null,8,["sub_id"]),e(C).isSupportFile?(d(),L(Ge,{key:0,type:"file","is-parse-content":!0,onOnSuccess:e(C).addFile},null,8,["onOnSuccess"])):U("",!0)])]),_:2},[e(C).files.value.length?{name:"file-list",fn:p(()=>[(d(!0),h(B,null,z(e(C).files.value,o=>(d(),L(ue,{key:o.id,onClose:k=>e(C).removeFile(o)},{default:p(()=>[_("div",xt,K(o.name),1)]),_:2},1032,["onClose"]))),128))]),key:"0"}:void 0]),1032,["loading","file-plugin","show-continue","show-file-upload"])])])]),_:1},8,["content","font"])])])]),_:1})])}}}),Ro=at(wt,[["__scopeId","data-v-43862358"]]);export{Ro as default};
