import{E as W}from"./BTEYzK2M.js";import{_ as j}from"./Ag4elq8V.js";import{l as F,b as q,j as z,bA as M,dq as O,f as w,cW as X,dr as Z,ds as G,o as H,e as J,E as K,p as Q}from"./DkNxoV1Z.js";import{E as Y}from"./DZB2bx3q.js";/* empty css        */import"./DP2rzg_V.js";/* empty css        *//* empty css        */import{_ as h}from"./B6kIumE-.js";import{_ as ee}from"./uVTQQAsm.js";import{l as te,b as k,j as g,u as l,M as r,N as E,Z as n,a0 as o,a3 as m,a7 as _,O as t,y as oe,a5 as i,a4 as u,n as C}from"./uahP8ofS.js";import{_ as se}from"./DlAUqK2U.js";import"./C8VUL651.js";import"./BX3NQxO5.js";import"./BZTlN-U1.js";import"./9mpg2Psx.js";import"./BGIqOOM2.js";import"./BD9OgGux.js";import"./DCTLXrZ8.js";import"./LvTTyycE.js";import"./C1DmYkVe.js";import"./UB3oSb9l.js";import"./HrsfEhzV.js";import"./D45YP0Si.js";import"./CWpusvr2.js";import"./CZqEt49j.js";import"./B_qKDp2q.js";import"./Dh9d3v4F.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./BfGcwPP1.js";const le={class:"w-full h-full bg-body rounded-[12px]"},ne={class:"p-[20px]"},ae={class:"mt-[30px]"},re={class:""},ie={class:"flex items-center"},pe={class:"mr-4"},me={class:"flex items-center"},ue={class:"flex items-center"},de={class:"flex items-center"},fe={class:"flex items-center"},ce={key:0},_e=te({__name:"center",setup(ve){const S=F(),v=k(!1),x=g(),b=k(!1),I=g(),P=q(),s=z(),d=k(l(s.userInfo.nickname)),B=async()=>{v.value=!0,await C(),x.value.open()},N=async()=>{b.value=!0,await C(),I.value.open()},U=async(f,e)=>{try{await O({value:f,field:e}),s.getUser()}catch(y){w.msgError(y)}},R=()=>{s.setLoginPopupType(X.BIND_WEIXIN),s.toggleShowLogin(!0)},V=async()=>{await Z({field:"nickname",value:d.value}),s.getUser()},A=async()=>{await w.confirm("确认注销账号吗？注销后将无法登录！"),await G(),S.push("/"),s.logout()};return(f,e)=>{const y=W,T=j,a=H,$=J,p=K,D=Q,L=Y;return r(),E("div",le,[n(L,null,{default:o(()=>[t("div",ne,[e[14]||(e[14]=t("div",{class:"title font-medium text-xl"},"个人信息",-1)),t("div",ae,[n(D,{"label-width":"90px","label-position":"left"},{default:o(()=>[n(a,{label:"","label-position":"right"},{label:o(()=>e[5]||(e[5]=[t("div",{class:"w-[55px] text-right"},"头像",-1)])),default:o(()=>[t("div",re,[n(T,{onChange:e[0]||(e[0]=c=>U(c,"avatar"))},{default:o(()=>[n(y,{size:60,src:l(s).userInfo.avatar},null,8,["src"])]),_:1}),e[6]||(e[6]=t("div",{class:"text-tx-secondary mt-2 text-sm"}," 建议尺寸：240*240px ",-1))])]),_:1}),n(a,{label:"用户昵称"},{default:o(()=>[t("div",null,[n($,{modelValue:l(d),"onUpdate:modelValue":e[1]||(e[1]=c=>oe(d)?d.value=c:null),class:"!w-[300px]"},null,8,["modelValue"])])]),_:1}),n(a,{label:"用户ID"},{default:o(()=>[t("div",ie,[t("span",pe,i(l(s).userInfo.sn),1),n(p,{type:"primary",link:!0,onClick:e[2]||(e[2]=c=>("copy"in f?f.copy:l(M))(l(s).userInfo.sn))},{default:o(()=>e[7]||(e[7]=[u(" 复制 ")])),_:1})])]),_:1}),n(a,{label:"上级邀请人"},{default:o(()=>[t("div",null,i(l(s).userInfo.leader_nickname||"系统"),1)]),_:1}),n(a,{label:"注册时间"},{default:o(()=>[t("div",null,i(l(s).userInfo.create_time),1)]),_:1}),l(s).userInfo.email?(r(),m(a,{key:0,label:"邮箱号码"},{default:o(()=>[t("div",me,[t("span",null,i(l(s).userInfo.email),1),e[8]||(e[8]=t("span",{class:"ml-2 text-tx-placeholder"}," （如需修改，请前往移动端修改） ",-1))])]),_:1})):_("",!0),n(a,{label:"手机号码"},{default:o(()=>[t("div",ue,[t("span",null,i(l(s).userInfo.mobile),1),n(p,{class:"ml-4",type:"primary",link:"",onClick:B},{default:o(()=>[u(i(l(s).userInfo.mobile?"点击更改":"立即绑定"),1)]),_:1})])]),_:1}),n(a,{label:"登录密码"},{default:o(()=>[t("div",de,[n(p,{type:"primary",link:"",onClick:N},{default:o(()=>e[9]||(e[9]=[u("点击设置")])),_:1})])]),_:1}),n(a,{label:"绑定微信"},{default:o(()=>[t("div",fe,[l(s).userInfo.is_auth?(r(),E("span",ce,"已绑定")):(r(),m(p,{key:1,type:"primary",link:!0,onClick:R},{default:o(()=>e[10]||(e[10]=[u(" 点击绑定 ")])),_:1}))])]),_:1}),n(a,{"label-width":"0"},{default:o(()=>[n(p,{type:"primary",class:"save-btn",onClick:V},{default:o(()=>e[11]||(e[11]=[u(" 保存 ")])),_:1})]),_:1}),e[13]||(e[13]=t("div",{class:"line mb-[30px]"},null,-1)),l(P).getAccountCancelledStatus?(r(),m(a,{key:1,label:"注销账号"},{default:o(()=>[t("div",null,[t("div",{class:"text-primary cursor-pointer",onClick:A}," 立即注销 > "),e[12]||(e[12]=t("div",{class:"form-tips"}," 注销你的账户后，你将无法使用我们的任何服务，并且与你的账户相关的所有数据都将永久丢失。此操作不可逆！ ",-1))])]),_:1})):_("",!0)]),_:1})])])]),_:1}),l(v)?(r(),m(h,{key:0,ref_key:"bindPopRef",ref:x,onClose:e[3]||(e[3]=()=>{v.value=!1,l(s).getUser()})},null,512)):_("",!0),l(b)?(r(),m(ee,{key:1,ref_key:"pwdPopRef",ref:I,mobile:l(s).userInfo.mobile,email:l(s).userInfo.email,onClose:e[4]||(e[4]=()=>{b.value=!1})},null,8,["mobile","email"])):_("",!0)])}}}),Ke=se(_e,[["__scopeId","data-v-ef279538"]]);export{Ke as default};
