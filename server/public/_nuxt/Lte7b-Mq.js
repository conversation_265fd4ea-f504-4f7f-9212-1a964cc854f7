import{_ as w}from"./BNsATNM9.js";import{b as S}from"./DGzblORL.js";import{l as k,m as u,M as s,N as o,O as t,X as I,u as c,a5 as r,a7 as m,_ as g,aq as h,a2 as C,Z as x,a0 as f}from"./uahP8ofS.js";import{_ as Q}from"./DlAUqK2U.js";const q={class:"flex flex-col items-center mx-auto max-w-[1200px]"},L={key:0,class:"flex justify-center flex-col mt-4 sm:mt-0"},M={class:"flex items-center"},N=["src"],U={class:"text-[20px] font-medium ml-2"},j={class:"flex sm:mt-3 sm:flex-col"},z={class:"mt-5 sm:mb-5"},B={class:"grid grid-cols-2 mt-10 sm:mt-0"},D={class:"mt-4 text-center"},O={class:"text-[18px] font-bold"},T={class:"mt-2 text-[14px] text-[#666]"},V={class:"mt-[15px] cursor-pointer"},A={class:"mt-4 text-center"},$={class:"text-[18px] font-bold"},E={class:"mt-2 text-[14px] text-[#666]"},F={class:"mt-[15px] cursor-pointer"},P={class:"flex items-center sm:m-0 m-5 sm:mt-[25px] mt-[35px]"},W={key:0,class:"mr-10 text-center sm:mr-12"},X=["src"],Z={class:"mt-3 text-[#666]"},G={key:1,class:"text-center"},H=["src"],J={class:"mt-3 text-[#666]"},K=k({__name:"guide",props:{prop:{}},setup(v){const d=v,i=S(),y=u(()=>d.prop.columnMenu1.filter(e=>e.isShow)),b=u(()=>d.prop.columnMenu2.filter(e=>e.isShow));return(e,Y)=>{const _=w;return s(),o("div",{class:"bg-center bg-cover",style:C({backgroundImage:`url(${c(i).getImageUrl(e.prop.bgImage)})`})},[t("div",q,[t("div",{class:I(["grid grid-cols-1 lg:grid-cols-3 xl:gap-x-10 sm:py-10 lg:max-w-[1150px] lg:mx-auto",{"lg:grid-cols-2":!e.prop.isShowLeft}])},[e.prop.isShowLeft?(s(),o("div",L,[t("div",M,[t("img",{class:"w-[34px] h-[34px]",src:c(i).getImageUrl(e.prop.logoImage),alt:""},null,8,N),t("span",U,r(c(i).getWebsiteConfig.pc_name),1)]),t("div",j,[t("div",z,r(e.prop.content),1)])])):m("",!0),t("div",B,[t("div",D,[t("div",O,r(e.prop.column1),1),t("div",T,[(s(!0),o(g,null,h(c(y),(l,p)=>{var a,n;return s(),o("ul",{key:p},[x(_,{to:{path:(a=l.link)==null?void 0:a.path,query:(n=l.link)==null?void 0:n.query}},{default:f(()=>[t("li",V,r(l.title),1)]),_:2},1032,["to"])])}),128))])]),t("div",A,[t("div",$,r(e.prop.column2),1),t("div",E,[(s(!0),o(g,null,h(c(b),(l,p)=>{var a,n;return s(),o("ul",{key:p},[x(_,{to:{path:(a=l.link)==null?void 0:a.path,query:(n=l.link)==null?void 0:n.query}},{default:f(()=>[t("li",F,r(l.title),1)]),_:2},1032,["to"])])}),128))])])]),t("div",P,[e.prop.rightQrcodeShow1?(s(),o("div",W,[t("img",{class:"w-[120px] h-[120px]",src:c(i).getImageUrl(e.prop.rightQrcode1),alt:"码多多"},null,8,X),t("div",Z,r(e.prop.rightQrcodeTitle1),1)])):m("",!0),e.prop.rightQrcodeShow2?(s(),o("div",G,[t("img",{class:"w-[120px] h-[120px]",src:c(i).getImageUrl(e.prop.rightQrcode2),alt:"码多多"},null,8,H),t("div",J,r(e.prop.rightQrcodeTitle2),1)])):m("",!0)])],2)])],4)}}}),R=Q(K,[["__scopeId","data-v-8bcf370f"]]),rt=Object.freeze(Object.defineProperty({__proto__:null,default:R},Symbol.toStringTag,{value:"Module"}));export{rt as _};
