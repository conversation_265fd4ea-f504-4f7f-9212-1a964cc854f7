import{i as me,m as i,J as Q,A as tt,c as q,a as Ve,l as se,u as Oe,r as ge,C as lt,o as Qt,n as H,ab as le,ac as Ee,M as m,N as V,V as k,O as R,a5 as z,X as u,a9 as B,b as I,F as Te,a7 as $,a2 as ve,U as J,I as L,w as Jt,aw as we,H as Xt,q as nt,ah as K,ax as Zt,Z as A,a0 as M,_ as Ye,aq as xe,a3 as N,ai as te,ay as Yt,a6 as Ie}from"./uahP8ofS.js";import{b1 as G,O as he,M as ne,ar as st,a0 as U,b2 as xt,a5 as _t,b3 as el,aO as tl,aP as ll,b4 as nl,b5 as sl,S as ol,aR as al,W as il,aT as rl,Z as ul,ap as W,af as dl,b6 as cl,aM as ot,K as pl,aL as fl,X as fe,al as _e,b7 as vl,aU as ml,b8 as bl,Y as gl,e as hl,J as yl,b9 as Sl,b0 as Cl,P as Ol,Q as at}from"./DkNxoV1Z.js";import{a as wl,b as Il,E as Vl}from"./C1DmYkVe.js";import{E as El}from"./DZB2bx3q.js";import{t as Tl,E as $l}from"./UB3oSb9l.js";import{e as Rl}from"./HrsfEhzV.js";import{i as be}from"./9mpg2Psx.js";import{d as Ml}from"./D45YP0Si.js";import{b as kl}from"./CWpusvr2.js";import{C as Dl}from"./CZqEt49j.js";function Bl(e,n,o,h){e.length;for(var r=o+1;r--;)if(n(e[r],r,e))return r;return-1}function Ll(e,n,o){var h=e==null?0:e.length;if(!h)return-1;var r=h-1;return Bl(e,kl(n),r)}const it=Symbol("ElSelectGroup"),ye=Symbol("ElSelect");function Fl(e,n){const o=me(ye),h=me(it,{disabled:!1}),r=i(()=>o.props.multiple?v(o.props.modelValue,e.value):v([o.props.modelValue],e.value)),y=i(()=>{if(o.props.multiple){const d=o.props.modelValue||[];return!r.value&&d.length>=o.props.multipleLimit&&o.props.multipleLimit>0}else return!1}),l=i(()=>e.label||(Q(e.value)?"":e.value)),b=i(()=>e.value||e.label||""),p=i(()=>e.disabled||n.groupDisabled||y.value),g=Ve(),v=(d=[],f)=>{if(Q(e.value)){const a=o.props.valueKey;return d&&d.some(E=>tt(G(E,a))===G(f,a))}else return d&&d.includes(f)},C=()=>{!e.disabled&&!h.disabled&&(o.states.hoveringIndex=o.optionsArray.indexOf(g.proxy))},O=d=>{const f=new RegExp(Rl(d),"i");n.visible=f.test(l.value)||e.created};return q(()=>l.value,()=>{!e.created&&!o.props.remote&&o.setSelected()}),q(()=>e.value,(d,f)=>{const{remote:a,valueKey:E}=o.props;if(be(d,f)||(o.onOptionDestroy(f,g.proxy),o.onOptionCreate(g.proxy)),!e.created&&!a){if(E&&Q(d)&&Q(f)&&d[E]===f[E])return;o.setSelected()}}),q(()=>h.disabled,()=>{n.groupDisabled=h.disabled},{immediate:!0}),{select:o,currentLabel:l,currentValue:b,itemSelected:r,isDisabled:p,hoverItem:C,updateOption:O}}const Nl=se({name:"ElOption",componentName:"ElOption",props:{value:{required:!0,type:[String,Number,Boolean,Object]},label:[String,Number],created:Boolean,disabled:Boolean},setup(e){const n=ne("select"),o=st(),h=i(()=>[n.be("dropdown","item"),n.is("disabled",Oe(b)),n.is("selected",Oe(l)),n.is("hovering",Oe(O))]),r=ge({index:-1,groupDisabled:!1,visible:!0,hover:!1}),{currentLabel:y,itemSelected:l,isDisabled:b,select:p,hoverItem:g,updateOption:v}=Fl(e,r),{visible:C,hover:O}=lt(r),d=Ve().proxy;p.onOptionCreate(d),Qt(()=>{const a=d.value,{selected:E}=p.states,oe=(p.props.multiple?E:[E]).some(ae=>ae.value===d.value);H(()=>{p.states.cachedOptions.get(a)===d&&!oe&&p.states.cachedOptions.delete(a)}),p.onOptionDestroy(a,d)});function f(){e.disabled!==!0&&r.groupDisabled!==!0&&p.handleOptionSelect(d)}return{ns:n,id:o,containerKls:h,currentLabel:y,itemSelected:l,isDisabled:b,select:p,hoverItem:g,updateOption:v,visible:C,hover:O,selectOptionClick:f,states:r}}}),Wl=["id","aria-disabled","aria-selected"];function zl(e,n,o,h,r,y){return le((m(),V("li",{id:e.id,class:u(e.containerKls),role:"option","aria-disabled":e.isDisabled||void 0,"aria-selected":e.itemSelected,onMouseenter:n[0]||(n[0]=(...l)=>e.hoverItem&&e.hoverItem(...l)),onClick:n[1]||(n[1]=B((...l)=>e.selectOptionClick&&e.selectOptionClick(...l),["stop"]))},[k(e.$slots,"default",{},()=>[R("span",null,z(e.currentLabel),1)])],42,Wl)),[[Ee,e.visible]])}var $e=he(Nl,[["render",zl],["__file","option.vue"]]);const Pl=se({name:"ElSelectDropdown",componentName:"ElSelectDropdown",setup(){const e=me(ye),n=ne("select"),o=i(()=>e.props.popperClass),h=i(()=>e.props.multiple),r=i(()=>e.props.fitInputWidth),y=I("");function l(){var b;y.value=`${(b=e.selectRef)==null?void 0:b.offsetWidth}px`}return Te(()=>{l(),U(e.selectRef,l)}),{ns:n,minWidth:y,popperClass:o,isMultiple:h,isFitInputWidth:r}}});function Kl(e,n,o,h,r,y){return m(),V("div",{class:u([e.ns.b("dropdown"),e.ns.is("multiple",e.isMultiple),e.popperClass]),style:ve({[e.isFitInputWidth?"width":"minWidth"]:e.minWidth})},[e.$slots.header?(m(),V("div",{key:0,class:u(e.ns.be("dropdown","header"))},[k(e.$slots,"header")],2)):$("v-if",!0),k(e.$slots,"default"),e.$slots.footer?(m(),V("div",{key:1,class:u(e.ns.be("dropdown","footer"))},[k(e.$slots,"footer")],2)):$("v-if",!0)],6)}var Al=he(Pl,[["render",Kl],["__file","select-dropdown.vue"]]);function Hl(e){const n=I(!1);return{handleCompositionStart:()=>{n.value=!0},handleCompositionUpdate:y=>{const l=y.target.value,b=l[l.length-1]||"";n.value=!xt(b)},handleCompositionEnd:y=>{n.value&&(n.value=!1,J(e)&&e(y))}}}const Ul=11,Gl=(e,n)=>{const{t:o}=_t(),h=st(),r=ne("select"),y=ne("input"),l=ge({inputValue:"",options:new Map,cachedOptions:new Map,disabledOptions:new Map,optionValues:[],selected:e.multiple?[]:{},selectionWidth:0,calculatorWidth:0,collapseItemWidth:0,selectedLabel:"",hoveringIndex:-1,previousQuery:null,inputHovering:!1,menuVisibleOnFocus:!1,isBeforeHide:!1}),b=I(null),p=I(null),g=I(null),v=I(null),C=I(null),O=I(null),d=I(null),f=I(null),a=I(null),E=I(null),X=I(null),oe=I(null),{wrapperRef:ae,isFocused:Re,handleFocus:ut,handleBlur:Me}=el(C,{afterFocus(){e.automaticDropdown&&!w.value&&(w.value=!0,l.menuVisibleOnFocus=!0)},beforeBlur(t){var s,c;return((s=g.value)==null?void 0:s.isFocusInsideContent(t))||((c=v.value)==null?void 0:c.isFocusInsideContent(t))},afterBlur(){w.value=!1,l.menuVisibleOnFocus=!1}}),w=I(!1),Z=I(),{form:ke,formItem:Y}=tl(),{inputId:dt}=ll(e,{formItemContext:Y}),{valueOnClear:ct,isEmptyValue:pt}=nl(e),ie=i(()=>e.disabled||(ke==null?void 0:ke.disabled)),Se=i(()=>e.multiple?L(e.modelValue)&&e.modelValue.length>0:!pt(e.modelValue)),ft=i(()=>e.clearable&&!ie.value&&l.inputHovering&&Se.value),De=i(()=>e.remote&&e.filterable&&!e.remoteShowSuffix?"":e.suffixIcon),vt=i(()=>r.is("reverse",De.value&&w.value)),Be=i(()=>(Y==null?void 0:Y.validateState)||""),mt=i(()=>sl[Be.value]),bt=i(()=>e.remote?300:0),Le=i(()=>e.loading?e.loadingText||o("el.select.loading"):e.remote&&!l.inputValue&&l.options.size===0?!1:e.filterable&&l.inputValue&&l.options.size>0&&x.value===0?e.noMatchText||o("el.select.noMatch"):l.options.size===0?e.noDataText||o("el.select.noData"):null),x=i(()=>T.value.filter(t=>t.visible).length),T=i(()=>{const t=Array.from(l.options.values()),s=[];return l.optionValues.forEach(c=>{const S=t.findIndex(F=>F.value===c);S>-1&&s.push(t[S])}),s.length>=t.length?s:t}),gt=i(()=>Array.from(l.cachedOptions.values())),ht=i(()=>{const t=T.value.filter(s=>!s.created).some(s=>s.currentLabel===l.inputValue);return e.filterable&&e.allowCreate&&l.inputValue!==""&&!t}),Fe=()=>{e.filterable&&J(e.filterMethod)||e.filterable&&e.remote&&J(e.remoteMethod)||T.value.forEach(t=>{var s;(s=t.updateOption)==null||s.call(t,l.inputValue)})},Ne=ol(),yt=i(()=>["small"].includes(Ne.value)?"small":"default"),St=i({get(){return w.value&&Le.value!==!1},set(t){w.value=t}}),Ct=i(()=>L(e.modelValue)?e.modelValue.length===0&&!l.inputValue:e.filterable?!l.inputValue:!0),Ot=i(()=>{var t;const s=(t=e.placeholder)!=null?t:o("el.select.placeholder");return e.multiple||!Se.value?s:l.selectedLabel});q(()=>e.modelValue,(t,s)=>{e.multiple&&e.filterable&&!e.reserveKeyword&&(l.inputValue="",re("")),ue(),!be(t,s)&&e.validateEvent&&(Y==null||Y.validate("change").catch(c=>al()))},{flush:"post",deep:!0}),q(()=>w.value,t=>{t?re(l.inputValue):(l.inputValue="",l.previousQuery=null,l.isBeforeHide=!0),n("visible-change",t)}),q(()=>l.options.entries(),()=>{var t;if(!il)return;const s=((t=b.value)==null?void 0:t.querySelectorAll("input"))||[];(!e.filterable&&!e.defaultFirstOption&&!rl(e.modelValue)||!Array.from(s).includes(document.activeElement))&&ue(),e.defaultFirstOption&&(e.filterable||e.remote)&&x.value&&We()},{flush:"post"}),q(()=>l.hoveringIndex,t=>{ul(t)&&t>-1?Z.value=T.value[t]||{}:Z.value={},T.value.forEach(s=>{s.hover=Z.value===s})}),Jt(()=>{l.isBeforeHide||Fe()});const re=t=>{l.previousQuery!==t&&(l.previousQuery=t,e.filterable&&J(e.filterMethod)?e.filterMethod(t):e.filterable&&e.remote&&J(e.remoteMethod)&&e.remoteMethod(t),e.defaultFirstOption&&(e.filterable||e.remote)&&x.value?H(We):H(wt))},We=()=>{const t=T.value.filter(S=>S.visible&&!S.disabled&&!S.states.groupDisabled),s=t.find(S=>S.created),c=t[0];l.hoveringIndex=je(T.value,s||c)},ue=()=>{if(e.multiple)l.selectedLabel="";else{const s=ze(e.modelValue);l.selectedLabel=s.currentLabel,l.selected=s;return}const t=[];L(e.modelValue)&&e.modelValue.forEach(s=>{t.push(ze(s))}),l.selected=t},ze=t=>{let s;const c=we(t).toLowerCase()==="object",S=we(t).toLowerCase()==="null",F=we(t).toLowerCase()==="undefined";for(let P=l.cachedOptions.size-1;P>=0;P--){const D=gt.value[P];if(c?G(D.value,e.valueKey)===G(t,e.valueKey):D.value===t){s={value:t,currentLabel:D.currentLabel,get isDisabled(){return D.isDisabled}};break}}if(s)return s;const j=c?t.label:!S&&!F?t:"";return{value:t,currentLabel:j}},wt=()=>{e.multiple?l.hoveringIndex=T.value.findIndex(t=>l.selected.some(s=>ee(s)===ee(t))):l.hoveringIndex=T.value.findIndex(t=>ee(t)===ee(l.selected))},It=()=>{l.selectionWidth=p.value.getBoundingClientRect().width},Pe=()=>{l.calculatorWidth=O.value.getBoundingClientRect().width},Vt=()=>{l.collapseItemWidth=X.value.getBoundingClientRect().width},Ce=()=>{var t,s;(s=(t=g.value)==null?void 0:t.updatePopper)==null||s.call(t)},Ke=()=>{var t,s;(s=(t=v.value)==null?void 0:t.updatePopper)==null||s.call(t)},Ae=()=>{l.inputValue.length>0&&!w.value&&(w.value=!0),re(l.inputValue)},He=t=>{if(l.inputValue=t.target.value,e.remote)Ue();else return Ae()},Ue=Ml(()=>{Ae()},bt.value),_=t=>{be(e.modelValue,t)||n(ot,t)},Et=t=>Ll(t,s=>!l.disabledOptions.has(s)),Tt=t=>{if(e.multiple&&t.code!==dl.delete&&t.target.value.length<=0){const s=e.modelValue.slice(),c=Et(s);if(c<0)return;s.splice(c,1),n(W,s),_(s)}},$t=(t,s)=>{const c=l.selected.indexOf(s);if(c>-1&&!ie.value){const S=e.modelValue.slice();S.splice(c,1),n(W,S),_(S),n("remove-tag",s.value)}t.stopPropagation(),ce()},Ge=t=>{t.stopPropagation();const s=e.multiple?[]:ct.value;if(e.multiple)for(const c of l.selected)c.isDisabled&&s.push(c.value);n(W,s),_(s),l.hoveringIndex=-1,w.value=!1,n("clear"),ce()},qe=t=>{if(e.multiple){const s=(e.modelValue||[]).slice(),c=je(s,t.value);c>-1?s.splice(c,1):(e.multipleLimit<=0||s.length<e.multipleLimit)&&s.push(t.value),n(W,s),_(s),t.created&&re(""),e.filterable&&!e.reserveKeyword&&(l.inputValue="")}else n(W,t.value),_(t.value),w.value=!1;ce(),!w.value&&H(()=>{de(t)})},je=(t=[],s)=>{if(!Q(s))return t.indexOf(s);const c=e.valueKey;let S=-1;return t.some((F,j)=>tt(G(F,c))===G(s,c)?(S=j,!0):!1),S},de=t=>{var s,c,S,F,j;const pe=L(t)?t[0]:t;let P=null;if(pe!=null&&pe.value){const D=T.value.filter(Ze=>Ze.value===pe.value);D.length>0&&(P=D[0].$el)}if(g.value&&P){const D=(F=(S=(c=(s=g.value)==null?void 0:s.popperRef)==null?void 0:c.contentRef)==null?void 0:S.querySelector)==null?void 0:F.call(S,`.${r.be("dropdown","wrap")}`);D&&cl(D,P)}(j=oe.value)==null||j.handleScroll()},Rt=t=>{l.options.set(t.value,t),l.cachedOptions.set(t.value,t),t.disabled&&l.disabledOptions.set(t.value,t)},Mt=(t,s)=>{l.options.get(t)===s&&l.options.delete(t)},{handleCompositionStart:kt,handleCompositionUpdate:Dt,handleCompositionEnd:Bt}=Hl(t=>He(t)),Lt=i(()=>{var t,s;return(s=(t=g.value)==null?void 0:t.popperRef)==null?void 0:s.contentRef}),Ft=()=>{l.isBeforeHide=!1,H(()=>de(l.selected))},ce=()=>{var t;(t=C.value)==null||t.focus()},Nt=()=>{Qe()},Wt=t=>{Ge(t)},Qe=t=>{if(w.value=!1,Re.value){const s=new FocusEvent("focus",t);H(()=>Me(s))}},zt=()=>{l.inputValue.length>0?l.inputValue="":w.value=!1},Je=()=>{ie.value||(l.menuVisibleOnFocus?l.menuVisibleOnFocus=!1:w.value=!w.value)},Pt=()=>{w.value?T.value[l.hoveringIndex]&&qe(T.value[l.hoveringIndex]):Je()},ee=t=>Q(t.value)?G(t.value,e.valueKey):t.value,Kt=i(()=>T.value.filter(t=>t.visible).every(t=>t.disabled)),At=i(()=>e.multiple?e.collapseTags?l.selected.slice(0,e.maxCollapseTags):l.selected:[]),Ht=i(()=>e.multiple?e.collapseTags?l.selected.slice(e.maxCollapseTags):[]:[]),Xe=t=>{if(!w.value){w.value=!0;return}if(!(l.options.size===0||x.value===0)&&!Kt.value){t==="next"?(l.hoveringIndex++,l.hoveringIndex===l.options.size&&(l.hoveringIndex=0)):t==="prev"&&(l.hoveringIndex--,l.hoveringIndex<0&&(l.hoveringIndex=l.options.size-1));const s=T.value[l.hoveringIndex];(s.disabled===!0||s.states.groupDisabled===!0||!s.visible)&&Xe(t),H(()=>de(Z.value))}},Ut=()=>{if(!p.value)return 0;const t=window.getComputedStyle(p.value);return Number.parseFloat(t.gap||"6px")},Gt=i(()=>{const t=Ut();return{maxWidth:`${X.value&&e.maxCollapseTags===1?l.selectionWidth-l.collapseItemWidth-t:l.selectionWidth}px`}}),qt=i(()=>({maxWidth:`${l.selectionWidth}px`})),jt=i(()=>({width:`${Math.max(l.calculatorWidth,Ul)}px`}));return e.multiple&&!L(e.modelValue)&&n(W,[]),!e.multiple&&L(e.modelValue)&&n(W,""),U(p,It),U(O,Pe),U(a,Ce),U(ae,Ce),U(E,Ke),U(X,Vt),Te(()=>{ue()}),{inputId:dt,contentId:h,nsSelect:r,nsInput:y,states:l,isFocused:Re,expanded:w,optionsArray:T,hoverOption:Z,selectSize:Ne,filteredOptionsCount:x,resetCalculatorWidth:Pe,updateTooltip:Ce,updateTagTooltip:Ke,debouncedOnInputChange:Ue,onInput:He,deletePrevTag:Tt,deleteTag:$t,deleteSelected:Ge,handleOptionSelect:qe,scrollToOption:de,hasModelValue:Se,shouldShowPlaceholder:Ct,currentPlaceholder:Ot,showClose:ft,iconComponent:De,iconReverse:vt,validateState:Be,validateIcon:mt,showNewOption:ht,updateOptions:Fe,collapseTagSize:yt,setSelected:ue,selectDisabled:ie,emptyText:Le,handleCompositionStart:kt,handleCompositionUpdate:Dt,handleCompositionEnd:Bt,onOptionCreate:Rt,onOptionDestroy:Mt,handleMenuEnter:Ft,handleFocus:ut,focus:ce,blur:Nt,handleBlur:Me,handleClearClick:Wt,handleClickOutside:Qe,handleEsc:zt,toggleMenu:Je,selectOption:Pt,getValueKey:ee,navigateOptions:Xe,dropdownMenuVisible:St,showTagList:At,collapseTagList:Ht,tagStyle:Gt,collapseTagStyle:qt,inputStyle:jt,popperRef:Lt,inputRef:C,tooltipRef:g,tagTooltipRef:v,calculatorRef:O,prefixRef:d,suffixRef:f,selectRef:b,wrapperRef:ae,selectionRef:p,scrollbarRef:oe,menuRef:a,tagMenuRef:E,collapseItemRef:X}};var ql=se({name:"ElOptions",setup(e,{slots:n}){const o=me(ye);let h=[];return()=>{var r,y;const l=(r=n.default)==null?void 0:r.call(n),b=[];function p(g){L(g)&&g.forEach(v=>{var C,O,d,f;const a=(C=(v==null?void 0:v.type)||{})==null?void 0:C.name;a==="ElOptionGroup"?p(!Xt(v.children)&&!L(v.children)&&J((O=v.children)==null?void 0:O.default)?(d=v.children)==null?void 0:d.default():v.children):a==="ElOption"?b.push((f=v.props)==null?void 0:f.value):L(v.children)&&p(v.children)})}return l.length&&p((y=l[0])==null?void 0:y.children),be(b,h)||(h=b,o&&(o.states.optionValues=b)),l}}});const jl=pl({name:String,id:String,modelValue:{type:[Array,String,Number,Boolean,Object],default:void 0},autocomplete:{type:String,default:"off"},automaticDropdown:Boolean,size:fl,effect:{type:fe(String),default:"light"},disabled:Boolean,clearable:Boolean,filterable:Boolean,allowCreate:Boolean,loading:Boolean,popperClass:{type:String,default:""},popperOptions:{type:fe(Object),default:()=>({})},remote:Boolean,loadingText:String,noMatchText:String,noDataText:String,remoteMethod:Function,filterMethod:Function,multiple:Boolean,multipleLimit:{type:Number,default:0},placeholder:{type:String},defaultFirstOption:Boolean,reserveKeyword:{type:Boolean,default:!0},valueKey:{type:String,default:"value"},collapseTags:Boolean,collapseTagsTooltip:Boolean,maxCollapseTags:{type:Number,default:1},teleported:wl.teleported,persistent:{type:Boolean,default:!0},clearIcon:{type:_e,default:vl},fitInputWidth:Boolean,suffixIcon:{type:_e,default:ml},tagType:{...Tl.type,default:"info"},validateEvent:{type:Boolean,default:!0},remoteShowSuffix:Boolean,placement:{type:fe(String),values:Il,default:"bottom-start"},fallbackPlacements:{type:fe(Array),default:["bottom-start","top-start","right","left"]},...bl,...gl(["ariaLabel"])}),et="ElSelect",Ql=se({name:et,componentName:et,components:{ElInput:hl,ElSelectMenu:Al,ElOption:$e,ElOptions:ql,ElTag:$l,ElScrollbar:El,ElTooltip:Vl,ElIcon:yl},directives:{ClickOutside:Dl},props:jl,emits:[W,ot,"remove-tag","clear","visible-change","focus","blur"],setup(e,{emit:n}){const o=Gl(e,n);return nt(ye,ge({props:e,states:o.states,optionsArray:o.optionsArray,handleOptionSelect:o.handleOptionSelect,onOptionCreate:o.onOptionCreate,onOptionDestroy:o.onOptionDestroy,selectRef:o.selectRef,setSelected:o.setSelected})),{...o}}}),Jl=["id","disabled","autocomplete","readonly","aria-activedescendant","aria-controls","aria-expanded","aria-label"],Xl=["textContent"];function Zl(e,n,o,h,r,y){const l=K("el-tag"),b=K("el-tooltip"),p=K("el-icon"),g=K("el-option"),v=K("el-options"),C=K("el-scrollbar"),O=K("el-select-menu"),d=Zt("click-outside");return le((m(),V("div",{ref:"selectRef",class:u([e.nsSelect.b(),e.nsSelect.m(e.selectSize)]),onMouseenter:n[16]||(n[16]=f=>e.states.inputHovering=!0),onMouseleave:n[17]||(n[17]=f=>e.states.inputHovering=!1),onClick:n[18]||(n[18]=B((...f)=>e.toggleMenu&&e.toggleMenu(...f),["prevent","stop"]))},[A(b,{ref:"tooltipRef",visible:e.dropdownMenuVisible,placement:e.placement,teleported:e.teleported,"popper-class":[e.nsSelect.e("popper"),e.popperClass],"popper-options":e.popperOptions,"fallback-placements":e.fallbackPlacements,effect:e.effect,pure:"",trigger:"click",transition:`${e.nsSelect.namespace.value}-zoom-in-top`,"stop-popper-mouse-event":!1,"gpu-acceleration":!1,persistent:e.persistent,onBeforeShow:e.handleMenuEnter,onHide:n[15]||(n[15]=f=>e.states.isBeforeHide=!1)},{default:M(()=>{var f;return[R("div",{ref:"wrapperRef",class:u([e.nsSelect.e("wrapper"),e.nsSelect.is("focused",e.isFocused),e.nsSelect.is("hovering",e.states.inputHovering),e.nsSelect.is("filterable",e.filterable),e.nsSelect.is("disabled",e.selectDisabled)])},[e.$slots.prefix?(m(),V("div",{key:0,ref:"prefixRef",class:u(e.nsSelect.e("prefix"))},[k(e.$slots,"prefix")],2)):$("v-if",!0),R("div",{ref:"selectionRef",class:u([e.nsSelect.e("selection"),e.nsSelect.is("near",e.multiple&&!e.$slots.prefix&&!!e.states.selected.length)])},[e.multiple?k(e.$slots,"tag",{key:0},()=>[(m(!0),V(Ye,null,xe(e.showTagList,a=>(m(),V("div",{key:e.getValueKey(a),class:u(e.nsSelect.e("selected-item"))},[A(l,{closable:!e.selectDisabled&&!a.isDisabled,size:e.collapseTagSize,type:e.tagType,"disable-transitions":"",style:ve(e.tagStyle),onClose:E=>e.deleteTag(E,a)},{default:M(()=>[R("span",{class:u(e.nsSelect.e("tags-text"))},z(a.currentLabel),3)]),_:2},1032,["closable","size","type","style","onClose"])],2))),128)),e.collapseTags&&e.states.selected.length>e.maxCollapseTags?(m(),N(b,{key:0,ref:"tagTooltipRef",disabled:e.dropdownMenuVisible||!e.collapseTagsTooltip,"fallback-placements":["bottom","top","right","left"],effect:e.effect,placement:"bottom",teleported:e.teleported},{default:M(()=>[R("div",{ref:"collapseItemRef",class:u(e.nsSelect.e("selected-item"))},[A(l,{closable:!1,size:e.collapseTagSize,type:e.tagType,"disable-transitions":"",style:ve(e.collapseTagStyle)},{default:M(()=>[R("span",{class:u(e.nsSelect.e("tags-text"))}," + "+z(e.states.selected.length-e.maxCollapseTags),3)]),_:1},8,["size","type","style"])],2)]),content:M(()=>[R("div",{ref:"tagMenuRef",class:u(e.nsSelect.e("selection"))},[(m(!0),V(Ye,null,xe(e.collapseTagList,a=>(m(),V("div",{key:e.getValueKey(a),class:u(e.nsSelect.e("selected-item"))},[A(l,{class:"in-tooltip",closable:!e.selectDisabled&&!a.isDisabled,size:e.collapseTagSize,type:e.tagType,"disable-transitions":"",onClose:E=>e.deleteTag(E,a)},{default:M(()=>[R("span",{class:u(e.nsSelect.e("tags-text"))},z(a.currentLabel),3)]),_:2},1032,["closable","size","type","onClose"])],2))),128))],2)]),_:1},8,["disabled","effect","teleported"])):$("v-if",!0)]):$("v-if",!0),e.selectDisabled?$("v-if",!0):(m(),V("div",{key:1,class:u([e.nsSelect.e("selected-item"),e.nsSelect.e("input-wrapper"),e.nsSelect.is("hidden",!e.filterable)])},[le(R("input",{id:e.inputId,ref:"inputRef","onUpdate:modelValue":n[0]||(n[0]=a=>e.states.inputValue=a),type:"text",class:u([e.nsSelect.e("input"),e.nsSelect.is(e.selectSize)]),disabled:e.selectDisabled,autocomplete:e.autocomplete,style:ve(e.inputStyle),role:"combobox",readonly:!e.filterable,spellcheck:"false","aria-activedescendant":((f=e.hoverOption)==null?void 0:f.id)||"","aria-controls":e.contentId,"aria-expanded":e.dropdownMenuVisible,"aria-label":e.ariaLabel,"aria-autocomplete":"none","aria-haspopup":"listbox",onFocus:n[1]||(n[1]=(...a)=>e.handleFocus&&e.handleFocus(...a)),onBlur:n[2]||(n[2]=(...a)=>e.handleBlur&&e.handleBlur(...a)),onKeydown:[n[3]||(n[3]=te(B(a=>e.navigateOptions("next"),["stop","prevent"]),["down"])),n[4]||(n[4]=te(B(a=>e.navigateOptions("prev"),["stop","prevent"]),["up"])),n[5]||(n[5]=te(B((...a)=>e.handleEsc&&e.handleEsc(...a),["stop","prevent"]),["esc"])),n[6]||(n[6]=te(B((...a)=>e.selectOption&&e.selectOption(...a),["stop","prevent"]),["enter"])),n[7]||(n[7]=te(B((...a)=>e.deletePrevTag&&e.deletePrevTag(...a),["stop"]),["delete"]))],onCompositionstart:n[8]||(n[8]=(...a)=>e.handleCompositionStart&&e.handleCompositionStart(...a)),onCompositionupdate:n[9]||(n[9]=(...a)=>e.handleCompositionUpdate&&e.handleCompositionUpdate(...a)),onCompositionend:n[10]||(n[10]=(...a)=>e.handleCompositionEnd&&e.handleCompositionEnd(...a)),onInput:n[11]||(n[11]=(...a)=>e.onInput&&e.onInput(...a)),onClick:n[12]||(n[12]=B((...a)=>e.toggleMenu&&e.toggleMenu(...a),["stop"]))},null,46,Jl),[[Yt,e.states.inputValue]]),e.filterable?(m(),V("span",{key:0,ref:"calculatorRef","aria-hidden":"true",class:u(e.nsSelect.e("input-calculator")),textContent:z(e.states.inputValue)},null,10,Xl)):$("v-if",!0)],2)),e.shouldShowPlaceholder?(m(),V("div",{key:2,class:u([e.nsSelect.e("selected-item"),e.nsSelect.e("placeholder"),e.nsSelect.is("transparent",!e.hasModelValue||e.expanded&&!e.states.inputValue)])},[R("span",null,z(e.currentPlaceholder),1)],2)):$("v-if",!0)],2),R("div",{ref:"suffixRef",class:u(e.nsSelect.e("suffix"))},[e.iconComponent&&!e.showClose?(m(),N(p,{key:0,class:u([e.nsSelect.e("caret"),e.nsSelect.e("icon"),e.iconReverse])},{default:M(()=>[(m(),N(Ie(e.iconComponent)))]),_:1},8,["class"])):$("v-if",!0),e.showClose&&e.clearIcon?(m(),N(p,{key:1,class:u([e.nsSelect.e("caret"),e.nsSelect.e("icon")]),onClick:e.handleClearClick},{default:M(()=>[(m(),N(Ie(e.clearIcon)))]),_:1},8,["class","onClick"])):$("v-if",!0),e.validateState&&e.validateIcon?(m(),N(p,{key:2,class:u([e.nsInput.e("icon"),e.nsInput.e("validateIcon")])},{default:M(()=>[(m(),N(Ie(e.validateIcon)))]),_:1},8,["class"])):$("v-if",!0)],2)],2)]}),content:M(()=>[A(O,{ref:"menuRef"},{default:M(()=>[e.$slots.header?(m(),V("div",{key:0,class:u(e.nsSelect.be("dropdown","header")),onClick:n[13]||(n[13]=B(()=>{},["stop"]))},[k(e.$slots,"header")],2)):$("v-if",!0),le(A(C,{id:e.contentId,ref:"scrollbarRef",tag:"ul","wrap-class":e.nsSelect.be("dropdown","wrap"),"view-class":e.nsSelect.be("dropdown","list"),class:u([e.nsSelect.is("empty",e.filteredOptionsCount===0)]),role:"listbox","aria-label":e.ariaLabel,"aria-orientation":"vertical"},{default:M(()=>[e.showNewOption?(m(),N(g,{key:0,value:e.states.inputValue,created:!0},null,8,["value"])):$("v-if",!0),A(v,null,{default:M(()=>[k(e.$slots,"default")]),_:3})]),_:3},8,["id","wrap-class","view-class","class","aria-label"]),[[Ee,e.states.options.size>0&&!e.loading]]),e.$slots.loading&&e.loading?(m(),V("div",{key:1,class:u(e.nsSelect.be("dropdown","loading"))},[k(e.$slots,"loading")],2)):e.loading||e.filteredOptionsCount===0?(m(),V("div",{key:2,class:u(e.nsSelect.be("dropdown","empty"))},[k(e.$slots,"empty",{},()=>[R("span",null,z(e.emptyText),1)])],2)):$("v-if",!0),e.$slots.footer?(m(),V("div",{key:3,class:u(e.nsSelect.be("dropdown","footer")),onClick:n[14]||(n[14]=B(()=>{},["stop"]))},[k(e.$slots,"footer")],2)):$("v-if",!0)]),_:3},512)]),_:3},8,["visible","placement","teleported","popper-class","popper-options","fallback-placements","effect","transition","persistent","onBeforeShow"])],34)),[[d,e.handleClickOutside,e.popperRef]])}var Yl=he(Ql,[["render",Zl],["__file","select.vue"]]);const xl=se({name:"ElOptionGroup",componentName:"ElOptionGroup",props:{label:String,disabled:Boolean},setup(e){const n=ne("select"),o=I(null),h=Ve(),r=I([]);nt(it,ge({...lt(e)}));const y=i(()=>r.value.some(g=>g.visible===!0)),l=g=>{var v,C;return((v=g.type)==null?void 0:v.name)==="ElOption"&&!!((C=g.component)!=null&&C.proxy)},b=g=>{const v=Cl(g),C=[];return v.forEach(O=>{var d,f;l(O)?C.push(O.component.proxy):(d=O.children)!=null&&d.length?C.push(...b(O.children)):(f=O.component)!=null&&f.subTree&&C.push(...b(O.component.subTree))}),C},p=()=>{r.value=b(h.subTree)};return Te(()=>{p()}),Sl(o,p,{attributes:!0,subtree:!0,childList:!0}),{groupRef:o,visible:y,ns:n}}});function _l(e,n,o,h,r,y){return le((m(),V("ul",{ref:"groupRef",class:u(e.ns.be("group","wrap"))},[R("li",{class:u(e.ns.be("group","title"))},z(e.label),3),R("li",null,[R("ul",{class:u(e.ns.b("group"))},[k(e.$slots,"default")],2)])],2)),[[Ee,e.visible]])}var rt=he(xl,[["render",_l],["__file","option-group.vue"]]);const cn=Ol(Yl,{Option:$e,OptionGroup:rt}),pn=at($e);at(rt);export{pn as E,cn as a};
