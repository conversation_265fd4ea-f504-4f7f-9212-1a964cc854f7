import{a as d}from"./C3HqF-ve.js";import{_ as m}from"./DdrcFgms.js";import{_ as u}from"./BGoJabyF.js";import{l as _,r as f,x as i,b as k,m as x,F as y,M as e,N as o,O as p,_ as v,aq as h,u as n,a1 as g,a3 as C,a4 as b,X as B,a7 as w}from"./Dp9aCaJ6.js";const N={class:"p-main h-full flex flex-col"},$={class:"flex"},q={class:"flex bg-page p-[5px] rounded-[10px]"},D=["onClick"],F={key:0,class:"flex-1 min-h-0"},K=_({__name:"index",props:{appId:{}},setup(I){const l=d(),r=f([{name:"对话数据",key:"data",component:i(m)},{name:"对话记录",key:"record",component:i(u)}]),a=k("data"),c=x(()=>r.find(t=>t.key==a.value));return y(()=>{l.query.dialogue&&(a.value="record")}),(t,L)=>(e(),o("div",N,[p("div",$,[p("div",q,[(e(!0),o(v,null,h(n(r),s=>(e(),o("div",{class:B(["leading-8 w-[120px] text-center rounded-[10px] cursor-pointer",{"bg-primary text-white":n(a)==s.key}]),key:s.key,onClick:M=>a.value=s.key},w(s.name),11,D))),128))])]),n(c)?(e(),o("div",F,[(e(),g(C(n(c).component),{"app-id":t.appId},null,8,["app-id"]))])):b("",!0)]))}});export{K as _};
