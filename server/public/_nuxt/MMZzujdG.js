import{E as V}from"./DxoS9eIh.js";import{_ as B}from"./DouuYAvM.js";import{_ as D}from"./BpYIl71c.js";import{E as L}from"./B58jTiS9.js";import{_ as T}from"./Bu0xx6JL.js";import{_ as N}from"./CN8DIg3d.js";import{a as M,l as O,b as P}from"./D726nzJl.js";/* empty css        *//* empty css        *//* empty css        */import{u as U}from"./CEZIOyk0.js";import z from"./B3wvkObF.js";import{_ as F}from"./B00vCeEj.js";import{_ as J}from"./NuUEQ6l2.js";import{g as Z}from"./DVdTSIWt.js";import{u as j}from"./DrXje_wD.js";import{l as G,b as d,ak as H,c as K,M as a,N as f,Z as m,a0 as s,O as e,u as o,y as w,_ as Q,aq as W,a1 as h,a4 as y,a7 as X}from"./Dp9aCaJ6.js";import{_ as Y}from"./DlAUqK2U.js";import"./GbXQDMM-.js";import"./DCTLXrZ8.js";import"./l0sNRNKZ.js";import"./DcfReECr.js";import"./9Bti1uB6.js";import"./BrUhOgZ5.js";import"./DyqYHYmh.js";import"./CjE8iZ8I.js";/* empty css        */import"./BwHhKyiz.js";import"./Cv6HhfEG.js";import"./BxHAV5ix.js";/* empty css        */import"./BPAOgr35.js";import"./BehqZJmW.js";import"./BpD5EEWf.js";import"./DHCaImEx.js";import"./qffJON56.js";import"./CmJNjzrM.js";/* empty css        *//* empty css        */import"./B0taQFTv.js";import"./CJW7H0Ln.js";import"./DcsXcc99.js";import"./DzhE9Pcm.js";import"./DsYfyJ0C.js";import"./nw0ThIMP.js";import"./C-BkwxIh.js";import"./DlKZEFPo.js";import"./DP2rzg_V.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./eyo1vffA.js";import"./BZaheE2V.js";import"./DH8JVbik.js";import"./BHChID2I.js";import"./DaBGfXF7.js";import"./D7NF1x92.js";/* empty css        */import"./D8hXTSyI.js";import"./Du5U7mLs.js";import"./CPCSqcOI.js";import"./DzDNkoYQ.js";import"./8HH1nzeK.js";import"./t5rt8HsH.js";/* empty css        *//* empty css        */import"./lXeGxUYW.js";import"./CJ8CVceb.js";import"./CQjYFccv.js";import"./DuMn-nmf.js";import"./DU8ualjj.js";import"./DnE_5Yhr.js";import"./CBTxQWUq.js";import"./D12SYOqE.js";/* empty css        */import"./C6_W4ts7.js";import"./oBCQ0nzs.js";import"./EetTZpAI.js";import"./BDC1j6IH.js";import"./BAJML0AH.js";import"./5pgbG-V6.js";import"./tBqpKipO.js";import"./KdfGSRx1.js";/* empty css        *//* empty css        */import"./BXMCmyua.js";import"./67xbGseh.js";import"./CBoEMgoX.js";import"./TLLNgt7W.js";import"./m-TlP05i.js";import"./tPkTvqK4.js";import"./CcPlX2kz.js";import"./Dp1MVQvw.js";import"./CgZh9ypM.js";import"./DDft7FHP.js";import"./BZIrwsET.js";import"./B6eARkLB.js";import"./rC0-t7zl.js";import"./DXCjCoQg.js";import"./9bfZRiBU.js";import"./V5W_64NZ.js";import"./D1aGBOrf.js";import"./wuEPx4OP.js";import"./CvGD_p3k.js";import"./7nP0LNCu.js";import"./Cej2IOgd.js";import"./D7z9IRkW.js";import"./CpSR-rXf.js";import"./DVPRiAuL.js";import"./xQb50RO9.js";import"./Cpg3PDWZ.js";const tt={class:"h-full flex"},ot=["onClick"],rt=["src"],et={class:"ml-[8px] line-clamp-1"},it={class:"flex items-center cursor-pointer"},pt={class:"text-xl flex-1 min-w-0"},mt={class:"sm:h-full py-[16px] pr-[16px] flex flex-col sm:flex-row flex-1 min-w-0"},nt={class:"sm:h-full flex-1 min-w-0 min-h-0 bg-body rounded-[12px]"},at=G({__name:"setting",async setup(st){let c,x;const i=M(),u=O();P();const v=j();v.getRobot();const _=d(!1),l=d(i.query.id),{data:b,refresh:k}=([c,x]=H(()=>U(()=>Z({id:l.value}),{transform(t){return(t==null?void 0:t.category_id)===0&&(t.category_id=""),t},default(){return{}},lazy:!0},"$3nIwi7TB6J")),c=await c,x(),c),p=d("edit"),q=[{name:"智能体设置",icon:"el-icon-Setting",key:"edit"},{name:"发布智能体",key:"release",icon:"el-icon-Position"},{name:"对话数据",key:"dialogue",icon:"el-icon-ChatDotRound"},{name:"立即对话",key:"chat",icon:"el-icon-ChatLineRound"}],S=t=>{switch(t){case"chat":u.push({path:"/application/chat",query:{id:l.value}});break;default:u.replace({path:i.path,query:{...i.query,currentTab:t}})}},C=async t=>{_.value=!1,t!=i.query.id&&(l.value=t,await k(),u.replace({path:i.path,query:{...i.query,id:t}}))};return K(()=>i.query,t=>{p.value=t.currentTab||"edit"},{immediate:!0}),(t,n)=>{const g=V,R=B,A=D,E=L,$=T,I=N;return a(),f("div",tt,[m($,{modelValue:o(p),"onUpdate:modelValue":[n[1]||(n[1]=r=>w(p)?p.value=r:null),S],"menu-list":q,"back-path":"/application/layout/robot"},{title:s(()=>[e("div",null,[m(E,{placement:"bottom",width:180,trigger:"click","show-arrow":!1,transition:"custom-popover",visible:o(_),"onUpdate:visible":n[0]||(n[0]=r=>w(_)?_.value=r:null)},{reference:s(()=>[e("div",it,[e("div",pt,[m(R,{content:o(b).name,teleported:!0,effect:"light"},null,8,["content"])]),m(A,{name:"el-icon-ArrowDown"})])]),default:s(()=>[e("div",null,[m(g,{style:{height:"250px"}},{default:s(()=>[(a(!0),f(Q,null,W(o(v).robotLists,r=>(a(),f("div",{class:"flex items-center leading-10 cursor-pointer hover:bg-primary-light-9 px-[10px] my-[5px] rounded-[12px] hover:text-primary",key:r.id,onClick:lt=>C(r.id)},[e("img",{class:"rounded-[50%] w-[28px] h-[28px] flex-none",src:r.image,alt:""},null,8,rt),e("div",et,X(r.name),1)],8,ot))),128))]),_:1})])]),_:1},8,["visible"])])]),_:1},8,["modelValue"]),e("div",mt,[e("div",nt,[o(p)==="edit"?(a(),h(z,{key:0,"model-value":o(b),onSuccess:n[2]||(n[2]=r=>o(u).push("/application/layout/robot"))},null,8,["model-value"])):y("",!0),m(I,null,{default:s(()=>[o(p)==="release"?(a(),h(g,{key:0},{default:s(()=>[m(F,{"app-id":o(l)},null,8,["app-id"])]),_:1})):y("",!0)]),_:1}),o(p)==="dialogue"?(a(),h(J,{key:1,"app-id":o(l)},null,8,["app-id"])):y("",!0)])])])}}}),yr=Y(at,[["__scopeId","data-v-21d9b61a"]]);export{yr as default};
