import{M as _,ar as h,O as u,K as g,a5 as k,$,P as v}from"./C12kmceL.js";import{l as c,M as n,N as r,O as t,u as l,m,V as d,X as i,a2 as C,a5 as N,a7 as B,Z as V}from"./uahP8ofS.js";const w={viewBox:"0 0 79 86",version:"1.1",xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink"},x=["id"],E=["stop-color"],R=["stop-color"],G=["id"],S=["stop-color"],b=["stop-color"],I=["id"],z={id:"Illustrations",stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},M={id:"B-type",transform:"translate(-1268.000000, -535.000000)"},O={id:"Group-2",transform:"translate(1268.000000, 535.000000)"},P=["fill"],D=["fill"],Z={id:"Group-Copy",transform:"translate(34.500000, 31.500000) scale(-1, 1) rotate(-25.000000) translate(-34.500000, -31.500000) translate(7.000000, 10.000000)"},K=["fill"],L=["fill"],T=["fill"],U=["fill"],X=["fill"],j={id:"Rectangle-Copy-17",transform:"translate(53.000000, 45.000000)"},q=["fill","xlink:href"],A=["fill","mask"],F=["fill"],H=c({name:"ImgEmpty"}),J=c({...H,setup(p){const s=_("empty"),o=h();return(a,f)=>(n(),r("svg",w,[t("defs",null,[t("linearGradient",{id:`linearGradient-1-${l(o)}`,x1:"38.8503086%",y1:"0%",x2:"61.1496914%",y2:"100%"},[t("stop",{"stop-color":`var(${l(s).cssVarBlockName("fill-color-1")})`,offset:"0%"},null,8,E),t("stop",{"stop-color":`var(${l(s).cssVarBlockName("fill-color-4")})`,offset:"100%"},null,8,R)],8,x),t("linearGradient",{id:`linearGradient-2-${l(o)}`,x1:"0%",y1:"9.5%",x2:"100%",y2:"90.5%"},[t("stop",{"stop-color":`var(${l(s).cssVarBlockName("fill-color-1")})`,offset:"0%"},null,8,S),t("stop",{"stop-color":`var(${l(s).cssVarBlockName("fill-color-6")})`,offset:"100%"},null,8,b)],8,G),t("rect",{id:`path-3-${l(o)}`,x:"0",y:"0",width:"17",height:"36"},null,8,I)]),t("g",z,[t("g",M,[t("g",O,[t("path",{id:"Oval-Copy-2",d:"M39.5,86 C61.3152476,86 79,83.9106622 79,81.3333333 C79,78.7560045 57.3152476,78 35.5,78 C13.6847524,78 0,78.7560045 0,81.3333333 C0,83.9106622 17.6847524,86 39.5,86 Z",fill:`var(${l(s).cssVarBlockName("fill-color-3")})`},null,8,P),t("polygon",{id:"Rectangle-Copy-14",fill:`var(${l(s).cssVarBlockName("fill-color-7")})`,transform:"translate(27.500000, 51.500000) scale(1, -1) translate(-27.500000, -51.500000) ",points:"13 58 53 58 42 45 2 45"},null,8,D),t("g",Z,[t("polygon",{id:"Rectangle-Copy-10",fill:`var(${l(s).cssVarBlockName("fill-color-7")})`,transform:"translate(11.500000, 5.000000) scale(1, -1) translate(-11.500000, -5.000000) ",points:"2.84078316e-14 3 18 3 23 7 5 7"},null,8,K),t("polygon",{id:"Rectangle-Copy-11",fill:`var(${l(s).cssVarBlockName("fill-color-5")})`,points:"-3.69149156e-15 7 38 7 38 43 -3.69149156e-15 43"},null,8,L),t("rect",{id:"Rectangle-Copy-12",fill:`url(#linearGradient-1-${l(o)})`,transform:"translate(46.500000, 25.000000) scale(-1, 1) translate(-46.500000, -25.000000) ",x:"38",y:"7",width:"17",height:"36"},null,8,T),t("polygon",{id:"Rectangle-Copy-13",fill:`var(${l(s).cssVarBlockName("fill-color-2")})`,transform:"translate(39.500000, 3.500000) scale(-1, 1) translate(-39.500000, -3.500000) ",points:"24 7 41 7 55 -3.63806207e-12 38 -3.63806207e-12"},null,8,U)]),t("rect",{id:"Rectangle-Copy-15",fill:`url(#linearGradient-2-${l(o)})`,x:"13",y:"45",width:"40",height:"36"},null,8,X),t("g",j,[t("use",{id:"Mask",fill:`var(${l(s).cssVarBlockName("fill-color-8")})`,transform:"translate(8.500000, 18.000000) scale(-1, 1) translate(-8.500000, -18.000000) ","xlink:href":`#path-3-${l(o)}`},null,8,q),t("polygon",{id:"Rectangle-Copy",fill:`var(${l(s).cssVarBlockName("fill-color-9")})`,mask:`url(#mask-4-${l(o)})`,transform:"translate(12.000000, 9.000000) scale(-1, 1) translate(-12.000000, -9.000000) ",points:"7 0 24 0 20 18 7 16.5"},null,8,A)]),t("polygon",{id:"Rectangle-Copy-18",fill:`var(${l(s).cssVarBlockName("fill-color-2")})`,transform:"translate(66.000000, 51.500000) scale(-1, 1) translate(-66.000000, -51.500000) ",points:"62 45 79 45 70 58 53 58"},null,8,F)])])])]))}});var Q=u(J,[["__file","img-empty.vue"]]);const W=g({image:{type:String,default:""},imageSize:Number,description:{type:String,default:""}}),Y=["src"],t0={key:1},l0=c({name:"ElEmpty"}),s0=c({...l0,props:W,setup(p){const s=p,{t:o}=k(),a=_("empty"),f=m(()=>s.description||o("el.table.emptyText")),y=m(()=>({width:$(s.imageSize)}));return(e,e0)=>(n(),r("div",{class:i(l(a).b())},[t("div",{class:i(l(a).e("image")),style:C(l(y))},[e.image?(n(),r("img",{key:0,src:e.image,ondragstart:"return false"},null,8,Y)):d(e.$slots,"image",{key:1},()=>[V(Q)])],6),t("div",{class:i(l(a).e("description"))},[e.$slots.description?d(e.$slots,"description",{key:0}):(n(),r("p",t0,N(l(f)),1))],2),e.$slots.default?(n(),r("div",{key:0,class:i(l(a).e("bottom"))},[d(e.$slots,"default")],2)):B("v-if",!0)],2))}});var o0=u(s0,[["__file","empty.vue"]]);const r0=v(o0);export{r0 as E};
