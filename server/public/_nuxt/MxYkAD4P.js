import{K as oe,X as le,M as ne,O as re,P as ie,b as ce,cy as de,s as pe,m as ue,e as me,E as fe,_ as _e}from"./Ct33iMSA.js";import{_ as xe}from"./DvrbA4QQ.js";import{l as C,M as r,N as d,X as f,u as t,V as v,a4 as u,O as a,a2 as T,a6 as k,a7 as m,b as S,r as ge,ak as O,m as he,j as ye,Z as c,a0 as p,y as we,aG as ve,_ as Se,aq as be,aa as A,a9 as ke,a1 as b,ab as Ce,aH as Ne}from"./Dp9aCaJ6.js";import{_ as Ve}from"./DxKRx1wF.js";import{W as Ie}from"./BDmMpw_0.js";import{E as Pe}from"./BPaXy7Em.js";import{E as ze}from"./CLVsM8Qg.js";/* empty css        */import{u as M}from"./DuO6be6_.js";import{g as $e,a as Ee,c as Be}from"./BdjxQtGL.js";import{e as De}from"./LwQ_Zv-K.js";import{_ as Le}from"./DlAUqK2U.js";import"./BCfv4qMP.js";import"./t4HvZ20D.js";import"./BHaz_wmF.js";const je=oe({header:{type:String,default:""},footer:{type:String,default:""},bodyStyle:{type:le([String,Object,Array]),default:""},bodyClass:String,shadow:{type:String,values:["always","hover","never"],default:"always"}}),Fe=C({name:"ElCard"}),Oe=C({...Fe,props:je,setup(W){const n=ne("card");return(o,N)=>(r(),d("div",{class:f([t(n).b(),t(n).is(`${o.shadow}-shadow`)])},[o.$slots.header||o.header?(r(),d("div",{key:0,class:f(t(n).e("header"))},[v(o.$slots,"header",{},()=>[k(m(o.header),1)])],2)):u("v-if",!0),a("div",{class:f([t(n).e("body"),o.bodyClass]),style:T(o.bodyStyle)},[v(o.$slots,"default")],6),o.$slots.footer||o.footer?(r(),d("div",{key:1,class:f(t(n).e("footer"))},[v(o.$slots,"footer",{},()=>[k(m(o.footer),1)])],2)):u("v-if",!0)],2))}});var Ae=re(Oe,[["__file","card.vue"]]);const Me=ie(Ae),Te={class:"h-full flex flex-col create 4xl:w-[2000px] mx-auto"},We={class:"2xl:max-w-[880px] xl:max-w-[780px] lg:max-w-[680px] max-w-[680px] search w-full mt-4"},qe={class:"flex-1 min-h-0 mx-[16px]"},Ge={class:"h-full flex flex-col"},Ke=["onClick"],Re={class:"flex-1 min-h-0"},Ue={key:0,"infinite-scroll-distance":"50","infinite-scroll-immediate":"false"},Xe={class:"flex flex-col min-h-0 h-full"},He={class:"flex items-center"},Je=["src"],Ze={class:"text-lg font-medium line-clamp-1"},Qe={class:"h-[36px]"},Ye={key:0,class:"text-xs text-tx-secondary mt-[10px] line-clamp-2 flex-1"},et={class:"flex items-center mt-[16px]"},tt={class:"text-tx-secondary mr-[30px] flex items-center text-sm"},at={class:"ml-1"},st=["onClick"],ot={class:"flex flex-col justify-center items-center w-full h-[60vh]"},lt=C({__name:"index",async setup(W){let n,o;const N=ce(),V=S(0),_=S(""),x=S(0),e=ge({pageNo:1,count:0,loading:!0,pageSize:10,lists:[]}),q={4e3:{rowPerView:7},2e3:{rowPerView:6},1800:{rowPerView:5},1600:{rowPerView:5},1440:{rowPerView:4},1360:{rowPerView:4},1280:{rowPerView:4},1024:{rowPerView:3}},G=async()=>{const s=await $e();return P(0),s},{data:I,refresh:nt}=([n,o]=O(()=>M(G,{default(){return[]},lazy:!0},"$gS1lmh76Gl")),n=await n,o(),n),{data:g}=([n,o]=O(()=>M(()=>ue({id:5}),{transform:s=>JSON.parse(s.data),default(){return[]},lazy:!0},"$Dd0tiMcnfk")),n=await n,o(),n),K=he(()=>s=>{switch(s){case 1:return"text-black";case 2:return"text-white";case 3:return"text-primary"}}),R=ye(),U=s=>{R.value=s,console.log(s)},P=s=>{var i;V.value=s,x.value=(i=I.value[s])==null?void 0:i.id,$()},h=async()=>{e.loading=!0;try{const s=await Ee({category_id:x.value,keyword:_.value,page_no:e.pageNo,page_size:e.pageSize});e.pageNo===1&&(e.lists=[]),e.count=s.count,e.lists.push(...s.lists)}finally{setTimeout(()=>e.loading=!1,200)}},X=()=>{e.count>=e.pageNo*e.pageSize&&(e.pageNo++,h())},z=async()=>{e.pageSize=e.pageNo*e.pageSize,e.pageNo=1,await h()},$=async()=>{e.loading=!0,e.pageSize=15,e.pageNo=1,await h()},H=async s=>{await Be({id:s}),z()};return de(_,s=>{$()},{debounce:500}),(s,i)=>{const J=me,y=xe,Z=Me,Q=Ve,Y=Ie,ee=Pe,te=fe,ae=_e,se=ze;return r(),d("div",null,[c(ae,{name:"default"},{default:p(()=>{var E,B,D,L,j,F;return[a("div",Te,[a("header",{class:"creation-header flex flex-col justify-center items-center px-[16px] m-[16px] rounded-[12px] overflow-hidden",style:T({"background-image":`url(${t(N).getImageUrl((B=(E=t(g)[0])==null?void 0:E.prop)==null?void 0:B.banner_bg)})`})},[a("div",{class:f(["font-medium 2xl:text-[50px] xl:text-[40px] lg:text-[36px] text-[36px]",t(K)((L=(D=t(g)[0])==null?void 0:D.prop)==null?void 0:L.title_color)])},m((F=(j=t(g)[0])==null?void 0:j.prop)==null?void 0:F.title),3),a("div",We,[c(J,{size:"large",class:"2xl:h-[54px] xl:h-[48px] lg:h-[44px] rounded-[7px]",style:{"--el-border-color":"transparent"},modelValue:t(_),"onUpdate:modelValue":i[0]||(i[0]=l=>we(_)?_.value=l:null),"prefix-icon":t(pe),placeholder:"请输入关键词搜索"},null,8,["modelValue","prefix-icon"])])],4),a("div",qe,[a("div",Ge,[c(t(ve),{slidesPerView:"auto",spaceBetween:16,class:"category-lists w-full",onSwiper:U,style:{padding:"10px 0","margin-left":"0"}},{default:p(()=>[(r(!0),d(Se,null,be(t(I),(l,w)=>(r(),b(t(Ne),{key:l.id,style:{width:"auto","margin-right":"12px"}},{default:p(()=>[Object.keys(l).includes("name")?(r(),d("div",{key:0,class:f(["category-item bg-white",{"is-active":t(V)===w}]),onClick:rt=>P(w)},m(l.name),11,Ke)):u("",!0)]),_:2},1024))),128))]),_:1}),a("div",Re,[t(e).lists.length?A((r(),d("div",Ue,[c(Y,{ref:"waterFull",delay:100,list:t(e).lists,width:305,gutter:20,animationDelay:0,animationDuration:0,backgroundColor:"none",animationPrefix:"none",animated:"none",animationEffect:"none",breakpoints:q},{item:p(({item:l})=>[c(Q,{to:{path:"/creation/produce",query:{cateId:t(x),modelId:l.id}},class:"h-full"},{default:p(()=>[c(Z,{class:"!border-none h-full rounded-[12px] relative cardItem shadow-light",shadow:"never",style:{"border-radius":"12px"},"body-style":"padding: 20px;height: 100%"},{default:p(()=>[a("div",Xe,[a("div",He,[a("img",{class:"w-[34px] h-[34px] mr-[10px]",src:l.image,alt:""},null,8,Je),a("div",Ze,m(l.name),1)]),a("div",Qe,[l.tips?(r(),d("div",Ye,m(l.tips),1)):u("",!0)]),a("div",et,[a("div",tt,[c(y,{name:"local-icon-yonghu"}),a("div",at,m(l.use_num)+"人使用过 ",1)]),a("div",{class:"flex collection absolute top-[10px] right-[10px]",onClick:ke(w=>H(l.id),["prevent"])},[l.is_collect?u("",!0):(r(),b(y,{key:0,size:20,name:"el-icon-Star",color:"#999"})),l.is_collect?(r(),b(y,{key:1,style:{transform:`scale(
                                                                        1.2
                                                                    )`},size:20,name:"el-icon-StarFilled",color:"#FFB529"})):u("",!0)],8,st)])])]),_:2},1024)]),_:2},1032,["to"])]),_:1},8,["list"])])),[[se,X]]):u("",!0),A(a("div",ot,[c(ee,{class:"w-[200px] h-[200px]",src:t(De)},null,8,["src"]),i[2]||(i[2]=a("div",{class:"text-tx-regular mb-4"}," 当前选择暂无创作～ ",-1)),c(te,{type:"primary",onClick:z},{default:p(()=>i[1]||(i[1]=[k(" 点击刷新 ")])),_:1})],512),[[Ce,!t(e).lists.length&&!t(e).loading]])])])])])]}),_:1})])}}}),bt=Le(lt,[["__scopeId","data-v-031e5857"]]);export{bt as default};
