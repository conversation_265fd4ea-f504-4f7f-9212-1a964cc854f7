import{_ as g}from"./C-HK5oo_.js";import{E as u}from"./BaKauqEV.js";import{E as x}from"./CND8oIbO.js";import"./DkNxoV1Z.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        *//* empty css        */import{useSearch as f}from"./DZbNtavR.js";import{l as h,r as z,m as v,M as r,N as i,O as s,_ as y,aq as k,u as a,Z as p,a5 as n,a0 as b}from"./uahP8ofS.js";const E=["href"],N={class:"line-clamp-2"},U={class:"text-primary"},w={class:"font-bold text-sm"},B={class:"text-xs text-tx-regular line-clamp-3"},M={class:"flex justify-end items-center mt-2"},S={class:"flex text-primary"},C={class:"text-xs text-tx-secondary ml-1"},J=h({__name:"doc",setup(I){const{options:L,result:l}=f(),t=z({size:5,page:1}),c=v(()=>l.value.search.slice((t.page-1)*t.size,t.page*t.size));return(P,o)=>{const m=g,_=u,d=x;return r(),i("div",null,[s("div",null,[(r(!0),i(y,null,k(a(c),(e,V)=>(r(),i("a",{class:"bg-page p-4 mb-[12px] rounded-[12px] block hover:text-primary",key:e.seeMoreUrl,href:e.seeMoreUrl,target:"_blank"},[s("div",N,[s("span",U,"【"+n(e.index)+"】",1),s("span",w,n(e.title),1)]),s("div",B,n(e.snippet),1),s("div",M,[p(_,{src:e.image,alt:"",class:"w-[12px] h-[12px]",fit:"contain"},{error:b(()=>[s("span",S,[p(m,{size:12,name:"el-icon-Link"})])]),_:2},1032,["src"]),s("div",C,n(e.showName),1)])],8,E))),128))]),p(d,{size:"small",background:"","hide-on-single-page":"","page-size":a(t).size,"onUpdate:pageSize":o[0]||(o[0]=e=>a(t).size=e),"current-page":a(t).page,"onUpdate:currentPage":o[1]||(o[1]=e=>a(t).page=e),layout:"pager",total:a(l).search.length,class:"py-4"},null,8,["page-size","current-page","total"])])}}});export{J as _};
