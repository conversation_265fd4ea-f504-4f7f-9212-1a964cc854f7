import{b as E,l as I,e as R,o as A,p as F}from"./C9xud4Fy.js";import{_ as z}from"./BY8Moot3.js";import{E as D}from"./0Fi_NQ6B.js";import{_ as K}from"./_btPu22R.js";import{_ as j}from"./sBOlCGQa.js";import{P as B}from"./CWneeePX.js";import"./DP2rzg_V.js";/* empty css        *//* empty css        *//* empty css        */import{k as C,a as M,b as O}from"./CzIVhTAp.js";import{l as Q,j as g,b as $,r as b,M as N,a3 as S,a0 as d,u as s,Z as t,O as m}from"./uahP8ofS.js";const Z={class:"flex items-center cursor-pointer text-[#666]"},G={class:"flex items-center cursor-pointer text-[#666]"},ne=Q({__name:"addPop",emits:["close","success"],setup(H,{expose:w,emit:v}){E();const x=v,u=g(),_=g(),n=$(-1),o=b({name:"",image:"",intro:"",documents_model_id:"",documents_model_sub_id:"",embedding_model_id:""}),k=b({name:[{required:!0,message:"请输入库的名称",trigger:"change"}],image:[{required:!0,message:"请选择封面图标",trigger:"change"}],type:[{required:!0,message:"Please select Activity zone",trigger:"change"}],embedding_model_id:[{required:!0,message:"请选择向量模型"}],documents_model_id:[{required:!0,message:"请选择文件处理通道"}],documents_model_sub_id:[{required:!0,message:"请选择文件处理模型"}],sort:[{required:!0,message:"请输入排序",trigger:"change"}],is_enable:[{required:!0,message:"请选择库的状态",trigger:"change"}]}),y=I(),V=async()=>{var a;if(await((a=_.value)==null?void 0:a.validate()),n.value!=-1)await C({id:n.value,...o});else{const{id:e}=await M({...o});y.push({path:"/application/kb/detail",query:{id:e}})}x("success"),u.value.close()},q=async a=>{const e=await O({id:a});Object.keys(e).map(r=>{o[r]=e[r]})};return w({open:async a=>{var e;if((e=_.value)==null||e.resetFields(),n.value=-1,!(a!=null&&a.id)){const i=`${window.location.origin}/resource/image/adminapi/default/zhishiku.png`;Object.assign(o,{name:"",image:i,intro:"",documents_model_id:"",documents_model_sub_id:"",embedding_model_id:""})}u.value.open(),a!=null&&a.id&&(n.value=a.id,await q(n.value))}}),(a,e)=>{const r=R,i=A,p=z,c=D,f=K,U=j,h=F,P=B;return N(),S(P,{ref_key:"popRef",ref:u,title:`${s(n)!=-1?"编辑":"新增"}知识库`,width:"500px",async:"",onConfirm:V},{default:d(()=>[t(h,{ref_key:"formRef",ref:_,"label-width":"130px",model:s(o),rules:s(k)},{default:d(()=>[t(i,{label:"知识库名称",prop:"name"},{default:d(()=>[t(r,{modelValue:s(o).name,"onUpdate:modelValue":e[0]||(e[0]=l=>s(o).name=l),placeholder:"请输入知识库名称",class:"w-[240px]"},null,8,["modelValue"])]),_:1}),t(i,{label:"知识库简介"},{default:d(()=>[t(r,{type:"textarea",modelValue:s(o).intro,"onUpdate:modelValue":e[1]||(e[1]=l=>s(o).intro=l),placeholder:"请用一句话描述知识库",class:"w-[240px]",rows:3},null,8,["modelValue"])]),_:1}),t(i,{label:"向量模型",prop:"embedding_model_id"},{label:d(()=>[t(c,{placement:"right",width:300,"show-arrow":!1,transition:"custom-popover",trigger:"hover",content:"向量模型可以将自然语言转成向量(即数据训练), 用于进行语义检索, 注意: 不同向量模型无法一起使用, 选择完后将无法修改。"},{reference:d(()=>[m("div",Z,[e[6]||(e[6]=m("span",{class:"mr-1"},"向量模型",-1)),t(p,{name:"el-icon-QuestionFilled",size:14})])]),_:1})]),default:d(()=>[t(f,{class:"flex-1",id:s(o).embedding_model_id,"onUpdate:id":e[2]||(e[2]=l=>s(o).embedding_model_id=l),"set-default":!1,type:"vectorModels",disabled:s(n)!=-1},null,8,["id","disabled"])]),_:1}),t(i,{label:"文件处理模型",prop:"documents_model_sub_id"},{label:d(()=>[t(c,{placement:"right",width:300,"show-arrow":!1,transition:"custom-popover",trigger:"hover",content:"文件模型用于QA拆分功能(导入数据->自动拆分问答对), 利用该AI模型对导入的文本进行处理，最终拆分成一问一答的数据形式。"},{reference:d(()=>[m("div",G,[e[7]||(e[7]=m("span",{class:"mr-1"},"文件处理模型",-1)),t(p,{name:"el-icon-QuestionFilled",size:14})])]),_:1})]),default:d(()=>[t(f,{class:"flex-1",id:s(o).documents_model_id,"onUpdate:id":e[3]||(e[3]=l=>s(o).documents_model_id=l),sub_id:s(o).documents_model_sub_id,"onUpdate:sub_id":e[4]||(e[4]=l=>s(o).documents_model_sub_id=l),"set-default":!1,disabled:""},null,8,["id","sub_id"])]),_:1}),t(i,{label:"封面",prop:"image"},{default:d(()=>[m("div",null,[t(U,{modelValue:s(o).image,"onUpdate:modelValue":e[5]||(e[5]=l=>s(o).image=l)},null,8,["modelValue"]),e[8]||(e[8]=m("div",{class:"form-tips"},"建议尺寸：200*160px",-1))])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title"])}}});export{ne as _};
