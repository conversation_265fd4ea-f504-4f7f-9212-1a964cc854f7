import{l as oe,j as P,r as D,b as L,ak as j,M as i,N as d,Z as r,a0 as m,O as s,ab as q,u as n,a3 as b,a7 as _,ac as se,aG as ae,_ as A,aq as ne,X as R,a5 as E,a9 as re,a4 as ie,aH as le}from"./uahP8ofS.js";import{_ as ce}from"./BY8Moot3.js";import{E as pe}from"./9Ov0gw5P.js";import{E as de}from"./BaIF4soF.js";import{W as me}from"./B-POUppf.js";import{l as ue,j as _e,cz as fe,cT as ge,J as we,E as ye,bA as N,ch as ve}from"./C9xud4Fy.js";import{E as he,a as xe}from"./CVlyBbl9.js";import{E as ke}from"./CKSvwWkv.js";import"./l0sNRNKZ.js";/* empty css        *//* empty css        *//* empty css        */import{u as T}from"./2e9ulp1P.js";import{a as Ce,d as Pe}from"./CJgd20ip.js";import{a as be}from"./DjwCd26w.js";import{e as Ee}from"./BWWzPGxX.js";import{I as Se}from"./D2bQaoVW.js";import Ve from"./DTASKxtZ.js";import{_ as Be}from"./DlAUqK2U.js";import"./DCTLXrZ8.js";import"./CC6TKTZR.js";import"./DWsmRjYd.js";import"./B7tOnmRj.js";import"./DrImGXqy.js";import"./DVv83yww.js";import"./CWneeePX.js";import"./ABiOVlWA.js";import"./BD9OgGux.js";/* empty css        *//* empty css        *//* empty css        */import"./CJotcpX_.js";import"./WcH50Ugn.js";import"./RSfPJsXe.js";const Ie={class:"flex-1 min-h-0 mx-[16px] relative"},$e=["onClick"],ze={class:"flex-1 min-h-[70vh] overflow-hidden mx-auto"},De=["infinite-scroll-disabled"],Le={class:"image-payload h-full w-full relative text-sm"},je={class:"image-bg"},qe=["onClick"],Ae=["onClick"],Re={class:"text-center leading-[38px]"},Ne={class:"image-content"},Te=["onClick"],We={class:"flex justify-between mt-[10px]"},Oe={class:"flex items-center"},Fe={class:"text-[#BBBBBB] ml-[6px] w-[80px] truncate"},Me=["onClick"],Ge={key:1,class:"flex justify-center items-center mt-[50px]"},He={class:"flex flex-col justify-center items-center w-full h-[60vh]"},Je=oe({__name:"draw",props:{keyword:{}},async setup(W){let l,f;const O=W;ue();const g=_e(),S=P(null),V=P(null),c=D({page_no:0,page_size:20,keyword:"",category_id:""}),F={4e3:{rowPerView:7},2e3:{rowPerView:6},1800:{rowPerView:5},1600:{rowPerView:5},1440:{rowPerView:4},1360:{rowPerView:4},1280:{rowPerView:4},1024:{rowPerView:3}},a=D({more:!0,count:0,loading:!1,lists:[]}),B=L(0),w=L([]),{data:I}=([l,f]=j(()=>T(()=>be({type:ge.DRAW}),{default(){return[]},transform(t){return[{id:"",name:"全部"}].concat(t)},lazy:!0},"$SQjM9OqPT4")),l=await l,f(),l);[l,f]=j(()=>T(()=>x(),{lazy:!0},"$E2KxCY8C7W")),await l,f();const x=async()=>{if(!a.loading){if(a.more)c.page_no+=1;else return;a.loading=!0;try{const t=await Ce(c),{lists:o,page_no:v,page_size:k,count:h}=t;v*k>h&&(a.more=!1),v==1?a.lists=o:a.lists=[...a.lists,...o]}finally{setTimeout(()=>a.loading=!1,200)}}},y=()=>{c.page_no=0,a.more=!0,x()},M=async t=>{if(!g.isLogin){g.toggleShowLogin(!0);return}await Pe({records_id:t.id,status:t.is_collect?0:1}),c.category_id===0?y():t.is_collect=t.is_collect?0:1},G=t=>{if(!g.isLogin){g.toggleShowLogin(!0);return}V.value.open(t)},H=P(),J=t=>{H.value=t,console.log(t)},$=t=>{var o;B.value=t,c.category_id=(o=I.value[t])==null?void 0:o.id,y()};return $(0),fe(()=>O.keyword,t=>{c.keyword=t,y()},{debounce:500}),(t,o)=>{const v=le,k=ae,h=ce,K=pe,Q=de,U=me,X=we,Y=he,Z=ye,ee=xe,te=ke;return i(),d("div",Ie,[r(k,{slidesPerView:"auto",spaceBetween:16,class:"category-lists",onSwiper:J,style:{padding:"10px 0"}},{default:m(()=>[(i(!0),d(A,null,ne(n(I),(e,u)=>(i(),b(v,{key:e.id,style:{width:"auto","margin-right":"12px"}},{default:m(()=>[Object.keys(e).includes("name")?(i(),d("div",{key:0,class:R(["category-item bg-white",{"is-active":n(B)===u}]),onClick:C=>$(u)},E(e.name),11,$e)):_("",!0)]),_:2},1024))),128))]),_:1}),s("div",ze,[q((i(),d("div",{class:"model-lists mb-[10px] mx-[0px]","infinite-scroll-distance":"50","infinite-scroll-delay":200,"infinite-scroll-disabled":!n(a).more},[n(a).lists.length?(i(),b(U,{key:0,ref_key:"waterFull",ref:S,delay:100,list:n(a).lists,width:305,gutter:20,animationDelay:0,animationDuration:0,backgroundColor:"none",animationPrefix:"none",animated:"none",animationEffect:"none",breakpoints:F},{item:m(({item:e})=>{var u,C;return[s("div",Le,[s("div",je,[r(Se,{thumbnail:e.thumbnail,image:(e==null?void 0:e.image)||(e==null?void 0:e.image_url),onRefresh:o[0]||(o[0]=p=>{var z;return(z=n(S))==null?void 0:z.renderer()}),onOnClick:o[1]||(o[1]=p=>w.value=p)},null,8,["thumbnail","image"])]),s("div",{class:"image-praise relative",onClick:p=>M(e)},[s("div",{class:R(["praise-animate",e.is_collect?"praise-entry":"praise-leave"])},null,2)],8,qe),r(K,{effect:"dark",content:"生成海报",placement:"top"},{default:m(()=>[s("div",{class:"image-poster relative",onClick:re(p=>G(e),["stop"])},[s("div",Re,[r(h,{size:"16px",color:"#ffffff",name:"el-icon-Picture"})])],8,Ae)]),_:2},1024),s("div",Ne,[s("p",{class:"text-white line-clamp-2",onClick:p=>("copy"in t?t.copy:n(N))(e.prompt)},E((e==null?void 0:e.prompts_cn)||(e==null?void 0:e.original_prompts.prompt)),9,Te),s("div",We,[s("div",Oe,[(u=e==null?void 0:e.user_info)!=null&&u.image?(i(),d(A,{key:0},[r(Q,{size:28,src:(C=e==null?void 0:e.user_info)==null?void 0:C.image},null,8,["src"]),s("p",Fe,E(e.user_info.name),1)],64)):_("",!0)]),s("div",{class:"flex items-center",onClick:p=>("copy"in t?t.copy:n(N))(e.prompts)},[r(h,{name:"el-icon-Copy"}),o[3]||(o[3]=s("p",{class:"text-white ml-[6px]"}," 复制 ",-1))],8,Me)])])])]}),_:1},8,["list"])):_("",!0),n(a).loading?(i(),d("div",Ge,[r(X,{size:"25",class:"is-loading"},{default:m(()=>[r(n(ve))]),_:1}),o[4]||(o[4]=s("span",{class:"mt-[4px] ml-[10px] text-[#999999]"},"加载中...",-1))])):_("",!0),q(s("div",He,[r(Y,{class:"w-[200px] h-[200px]",src:n(Ee)},null,8,["src"]),o[6]||(o[6]=s("div",{class:"text-tx-regular mb-4"},"当前选择暂无绘画～",-1)),r(Z,{type:"primary",onClick:y},{default:m(()=>o[5]||(o[5]=[ie(" 点击刷新")])),_:1})],512),[[se,!n(a).lists.length&&!n(a).loading]])],8,De)),[[te,x]]),r(Ve,{ref_key:"posterPopupRef",ref:V},null,512),n(w).length?(i(),b(ee,{key:0,"url-list":n(w),"hide-on-click-modal":!0,onClose:o[2]||(o[2]=e=>w.value=[])},null,8,["url-list"])):_("",!0)])])}}}),Vt=Be(Je,[["__scopeId","data-v-c481d2de"]]);export{Vt as default};
