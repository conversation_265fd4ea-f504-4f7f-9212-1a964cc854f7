import{E as c}from"./DH3BuQAR.js";import{E as g}from"./hx-0JZAY.js";import{E as x}from"./LS8qfqy3.js";import{b as V,v as w}from"./ClNUxNV9.js";/* empty css        *//* empty css        */import{r as y,x as D,y as b,f as e,e as k}from"./CTLQW8rw.js";import{DrawModeEnum as p,DrawTypeEnum as v}from"./tONJIxwY.js";import{_ as O}from"./Bf4wcbq-.js";import U from"./C-awW5NS.js";import E from"./DZ4Tudnw.js";import B from"./DjOw00ea.js";import{_ as A}from"./BCkmLKcS.js";import{D as S}from"./5cU5lAEb.js";import z from"./DIcXiZT6.js";import{_ as N}from"./Ds_DxAAb.js";import C from"./DwQwtixK.js";import{l as L,F as M,u as o,M as a,N as l,Z as r,a0 as i,O as s,a1 as P,a4 as $,aa as F}from"./Dp9aCaJ6.js";import{_ as I}from"./DlAUqK2U.js";import"./CNgDMrD1.js";import"./zRTrVFrw.js";import"./D4cQUBDp.js";import"./67xbGseh.js";import"./BE7GAo-z.js";import"./CWzXXCLi.js";import"./B6IIPh85.js";import"./Tedtu6ac.js";import"./DCTLXrZ8.js";import"./l0sNRNKZ.js";/* empty css        */import"./Dr3yaPao.js";import"./D6j_GvJh.js";import"./BYYVzU6A.js";import"./9Bti1uB6.js";/* empty css        */import"./Rdahxiqq.js";import"./C0R3uvOr.js";/* empty css        */import"./CqanTtdS.js";import"./CxSV922q.js";import"./CeZA--ML.js";import"./Cr7puf4F.js";import"./xixvWuCN.js";import"./B1qK8f0i.js";import"./B2RmFVzS.js";import"./mW1R_rAi.js";import"./Cga6GjQY.js";import"./Cpg3PDWZ.js";import"./C_z8SI9s.js";import"./CkTU2NpD.js";import"./Cd8UtlNR.js";import"./Cv6HhfEG.js";import"./BbPTMigZ.js";import"./DkqMgBWM.js";/* empty css        *//* empty css        */import"./1sgdir_0.js";import"./D0cj6m41.js";import"./DiqTVJuo.js";import"./DjwCd26w.js";import"./qQcR97T8.js";import"./CcPlX2kz.js";import"./C47JPtkS.js";import"./BVqQ2B7o.js";import"./BWdDF8rn.js";import"./1tHUlKgH.js";import"./C1aykdW0.js";import"./CqNjbiLO.js";import"./DlKZEFPo.js";import"./CQXeYJFv.js";import"./DMVjPTRc.js";const R={key:0,class:"h-full flex-1 flex p-4 gap-4 draw_layout"},T={class:"bg-body w-[355px] p-4 flex flex-col gap-4 relative"},j={key:1,class:"h-full flex-1 flex p-4 gap-4 draw_layout justify-center items-center"},Z=L({__name:"doubao",setup(q){const n=V();return M(()=>{y({draw_api:p.DOUBAO,draw_model:p.DOUBAO,action:"generate",prompt:"",engine:"high_aes_general_v20_L",negative_prompt:"",size:"512x512",complex_params:{seed:"",ddim_steps:20}}),D.model=p.DOUBAO,b()}),(G,t)=>{const d=c,u=g,_=x,f=w;return o(n).config.switch.doubao_status?(a(),l("div",R,[r(d,{class:"rounded-[12px] pb-[72px] bg-body"},{default:i(()=>[s("div",T,[r(O,{modelValue:o(e).draw_type,"onUpdate:modelValue":t[0]||(t[0]=m=>o(e).draw_type=m)},null,8,["modelValue"]),r(U,{modelValue:o(e).prompt,"onUpdate:modelValue":t[1]||(t[1]=m=>o(e).prompt=m),model:o(p).DOUBAO},null,8,["modelValue","model"]),o(e).draw_type===o(v).img2img?(a(),P(E,{key:0,modelValue:o(e).image_mask,"onUpdate:modelValue":t[2]||(t[2]=m=>o(e).image_mask=m),type:"image"},null,8,["modelValue"])):$("",!0),r(z,{modelValue:o(e).size,"onUpdate:modelValue":t[3]||(t[3]=m=>o(e).size=m)},null,8,["modelValue"]),r(N,{modelValue:o(e).engine,"onUpdate:modelValue":t[4]||(t[4]=m=>o(e).engine=m),draw_type:o(e).draw_type},null,8,["modelValue","draw_type"]),r(C,{modelValue:o(e).complex_params,"onUpdate:modelValue":t[5]||(t[5]=m=>o(e).complex_params=m)},null,8,["modelValue"])]),r(A)]),_:1}),F(r(B,{"element-loading-text":"正在加载数据..."},null,512),[[f,o(k)]])])):(a(),l("div",j,[r(_,null,{icon:i(()=>[r(u,{class:"w-[100px] dark:opacity-60",src:o(S)},null,8,["src"])]),title:i(()=>t[6]||(t[6]=[s("div",{class:"text-info"},"绘画功能暂未开启",-1)])),_:1})]))}}}),nt=I(Z,[["__scopeId","data-v-a401c716"]]);export{nt as default};
