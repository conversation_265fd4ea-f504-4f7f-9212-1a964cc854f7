import{_ as d}from"./CfCOJdKf.js";import{_}from"./DLBZzw-4.js";import{a as c,_ as u}from"./C12kmceL.js";import{u as f}from"./CyuOY8IG.js";import{l as x,m as h,M as C,N as b,Z as i,a0 as v,O as e,u as o}from"./uahP8ofS.js";import"./DL-C_KWg.js";import"./BAooD3NP.js";import"./DlAUqK2U.js";/* empty css        */import"./DmVakCHI.js";import"./DP4-20kY.js";import"./oOqAltFp.js";import"./Cq-dlMe8.js";/* empty css        */import"./2KyQdWL7.js";import"./B5_1O_mx.js";import"./CeHUJVAt.js";import"./i9Efl4hL.js";import"./sUoOAzN4.js";/* empty css        */import"./BJBjrpCs.js";import"./DWZt4P8F.js";import"./Cpg3PDWZ.js";import"./BnhkBVfO.js";import"./BD9OgGux.js";import"./DCTLXrZ8.js";/* empty css        */import"./Cj1QjIv2.js";import"./-Ctiqued.js";import"./loTdDtpJ.js";import"./DwFObZc_.js";import"./DQUFgXGm.js";import"./DxfcmQ5j.js";import"./RSfPJsXe.js";import"./CWOFGv9Q.js";import"./D0NTNmoy.js";import"./DH3GNTka.js";import"./Cr9cZ-Xs.js";import"./DHsrbrOc.js";import"./BxhsAhDB.js";import"./CcLFL5SG.js";import"./BWh_1kQN.js";import"./BQBYw5b-.js";import"./CM_SPFYb.js";import"./DUerO_fc.js";import"./BXWYK723.js";import"./ic_lduNt.js";import"./Ddd8PmxP.js";import"./CcdExHMo.js";const I={class:"h-full flex"},S={class:"flex h-full p-[16px]"},V={class:"h-full pr-[16px] py-[16px] flex-1 min-w-0"},k={class:"h-full flex flex-col bg-body rounded-lg"},Vo=x({__name:"chat",setup(y){const t=f(),m=c(),s=h(()=>m.query.id);return(E,r)=>{const p=d,n=_,l=u;return C(),b("div",null,[i(l,{name:"default"},{default:v(()=>[e("div",I,[e("div",S,[i(p,{modelValue:o(t).sessionId,"onUpdate:modelValue":r[0]||(r[0]=a=>o(t).sessionId=a),data:o(t).sessionLists,onAdd:o(t).sessionAdd,onEdit:o(t).sessionEdit,onDelete:o(t).sessionDelete,onClear:o(t).sessionClear,onClickItem:o(t).setSessionSelect},null,8,["modelValue","data","onAdd","onEdit","onDelete","onClear","onClickItem"])]),e("div",V,[e("div",k,[i(n,{"robot-id":o(s)},null,8,["robot-id"])])])])]),_:1})])}}});export{Vo as default};
