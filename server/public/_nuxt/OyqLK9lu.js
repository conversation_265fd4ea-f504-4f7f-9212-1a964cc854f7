import{_ as x}from"./BpYIl71c.js";import{E as h,a as y}from"./BwHhKyiz.js";import"./D726nzJl.js";import{u as $,a as w}from"./BgogZcDO.js";import{l as T,m as k,b as B,c as D,M as r,N as p,O as m,Z as l,a0 as c,_ as M,aq as S,u as e,a4 as E,a1 as f,a7 as N,aa as z,a3 as L,ab as V}from"./Dp9aCaJ6.js";import{_ as j}from"./B4BR8E8H.js";import{_ as A}from"./DfYajCPS.js";import{_ as I}from"./BxvSN_8b.js";import O from"./Cj4m0-2m.js";import P from"./B8dduKkR.js";import{_ as q}from"./Cpo-A_gS.js";import F from"./CGx2Szfa.js";import{_ as R}from"./BvuZyEef.js";import{_ as Z}from"./DlAUqK2U.js";import"./Cv6HhfEG.js";import"./BxHAV5ix.js";import"./Bb2-23m7.js";import"./CiYvFM4x.js";import"./xQb50RO9.js";import"./DBUUZxnF.js";import"./DnE_5Yhr.js";import"./CBTxQWUq.js";import"./DcsXcc99.js";import"./D12SYOqE.js";import"./BOaJ3oTb.js";import"./DxoS9eIh.js";/* empty css        *//* empty css        */import"./CEZIOyk0.js";import"./BHRQ9O7Q.js";import"./DsYfyJ0C.js";import"./Cb-sKF8G.js";/* empty css        */import"./D-aJub5c.js";/* empty css        */import"./BxN_wELh.js";import"./B0taQFTv.js";import"./GbXQDMM-.js";import"./DCTLXrZ8.js";import"./CJW7H0Ln.js";import"./CmJNjzrM.js";import"./DzhE9Pcm.js";import"./CjE8iZ8I.js";import"./C-BkwxIh.js";import"./DlKZEFPo.js";import"./Yd_pVd2e.js";/* empty css        *//* empty css        *//* empty css        */import"./DP2rzg_V.js";/* empty css        */import"./CygH4-hK.js";import"./D8hXTSyI.js";import"./qffJON56.js";import"./DuMn-nmf.js";const G={class:"h-full relative z-20 bg-white"},H={class:"flex flex-col items-center justify-center"},J={class:"text-lg mt-[6px]"},K={class:"w-[360px] h-full"},Q=T({__name:"index",setup(U){const u={Avatar:j,Dub:A,Music:I,Background:O,Text:P,Captions:q,Maps:F,Prospect:R},d=$(),{tabsState:a,initTabs:v,changeTabs:b}=w(),s=k({get(){return a.value.isCollapsed},set(i){a.value.isCollapsed=i}}),n=B(!1);return v(),D(()=>a.value.current,async i=>{d.setActiveObjectByType(i)}),(i,o)=>{const _=x,C=y,g=h;return r(),p("div",{class:"control-panel h-full",onMouseenter:o[2]||(o[2]=t=>n.value=!0),onMouseleave:o[3]||(o[3]=t=>n.value=!1)},[m("div",G,[l(g,{"tab-position":"left",class:"h-full",type:"card","model-value":e(a).current,onTabChange:o[0]||(o[0]=t=>e(b)(t))},{default:c(()=>[(r(!0),p(M,null,S(e(a).tabs,t=>(r(),f(C,{key:t.id,name:t.id,lazy:""},{label:c(()=>[m("div",H,[l(_,{name:t.icon,size:22},null,8,["name"]),m("span",J,N(t.label),1)])]),default:c(()=>[z(m("div",K,[(r(),f(L(u[t.component])))],512),[[V,!e(s)]])]),_:2},1032,["name"]))),128))]),_:1},8,["model-value"])]),e(s)||e(n)?(r(),p("div",{key:0,class:"panel-left-arrow",onClick:o[1]||(o[1]=t=>s.value=!e(s))},[l(_,{class:"mr-1",name:`el-icon-${e(s)?"CaretRight":"CaretLeft"}`},null,8,["name"])])):E("",!0)],32)}}}),Xt=Z(Q,[["__scopeId","data-v-8d50fa1f"]]);export{Xt as default};
