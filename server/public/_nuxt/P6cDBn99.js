import{E as y}from"./BCaukYSI.js";import{P as g}from"./BbazWion.js";import"./C3HqF-ve.js";import{u as c}from"./BG9of9uB.js";import{l as _,b as k,j as w,m as C,M as R,a1 as b,a0 as l,O as s,a6 as e,Z as u,u as n,a7 as d}from"./Dp9aCaJ6.js";const B=""+new URL("wxoa_config_menu.DpJ4F-gE.png",import.meta.url).href,N=""+new URL("wxoa_config_autoreply.CBOfNUld.png",import.meta.url).href,$={class:"mt-4"},E={class:"text-[#999] mt-2"},L={class:"flex items-center"},U={class:"mt-6"},V={class:"flex items-center mt-2"},D={class:"mt-4"},I={class:"text-[#999] mt-2"},M={class:"mt-6"},O={class:"flex items-center mt-2"},q=_({__name:"oa-config",setup(P,{expose:f}){const i=k(""),{copy:r}=c(),p=w(),o=C(()=>`${location.origin}/chat/${i.value}`);return f({open:a=>{var t;i.value=a,(t=p.value)==null||t.open()}}),(a,t)=>{const m=y,x=g;return R(),b(x,{ref_key:"popupRef",ref:p,title:"公众号配置",async:!0,width:"600px","confirm-button-text":"","cancel-button-text":""},{default:l(()=>[t[13]||(t[13]=s("div",{class:"text-xl text-tx-primary font-medium"},"添加菜单",-1)),s("div",$,[t[5]||(t[5]=s("div",null,[e("1.进入微信"),s("span",{class:"text-success"},"公众号后台")],-1)),s("div",E,[t[4]||(t[4]=s("div",null,"请确保您的公众号已过微信认证",-1)),s("div",L,[t[3]||(t[3]=s("span",null,"路径：内容与互动 > 自定义菜单 > 添加菜单",-1)),u(m,{href:n(B),target:"_blank",type:"primary",class:"ml-2"},{default:l(()=>t[2]||(t[2]=[e(" 查看填写示例 ")])),_:1},8,["href"])])])]),s("div",U,[t[6]||(t[6]=s("div",null,"2.创建菜单",-1)),t[7]||(t[7]=s("div",{class:"text-[#999] mt-2"},[s("div",null,"填写菜单名称，将以下链接或二维码，配置到菜单里")],-1)),s("div",V,[s("span",null,d(n(o)),1),s("span",{class:"ml-2 text-primary cursor-pointer",onClick:t[0]||(t[0]=v=>n(r)(n(o)))},"复制链接")])]),t[14]||(t[14]=s("div",{class:"text-xl font-medium text-tx-primary mt-[16px]"},"自动回复",-1)),s("div",D,[t[10]||(t[10]=s("div",null,[e("1.进入微信"),s("span",{class:"text-success"},"公众号后台")],-1)),s("div",I,[s("div",null,[t[9]||(t[9]=s("span",null,"路径：内容与互动 > 自动回复 > 收到消息回复",-1)),u(m,{href:n(N),target:"_blank",type:"primary",class:"ml-2"},{default:l(()=>t[8]||(t[8]=[e(" 查看填写示例 ")])),_:1},8,["href"])])])]),s("div",M,[t[11]||(t[11]=s("div",null,"2.创建自动回复",-1)),t[12]||(t[12]=s("div",{class:"text-[#999] mt-2"},[s("div",null,"选择自动回复类型，将以下链接或二维码，配置到回复里")],-1)),s("div",O,[s("span",null,d(n(o)),1),s("span",{class:"ml-2 text-primary cursor-pointer",onClick:t[1]||(t[1]=v=>n(r)(n(o)))},"复制链接")])])]),_:1},512)}}});export{q as _};
