import{K as G,al as re,a5 as se,M as H,O as Y,X as le,J as A,P as ne,Q as ie,db as ue,bs as de,d as f,E as j,dc as ce,dd as pe,p as me,o as k,e as O,aX as fe,bN as be}from"./D726nzJl.js";import{l as $,b as v,q as _e,F as W,M as i,N as _,V as K,X as P,u as e,i as ve,O as h,a1 as S,a0 as r,a3 as ge,a7 as U,a as ye,m as q,c as he,Z as o,a6 as T,a4 as D,r as we,_ as z,aq as X}from"./Dp9aCaJ6.js";import{RobotPermissionManager as N}from"./BzdN9Xy0.js";import{E as ke}from"./CJW7H0Ln.js";import{_ as ee}from"./DlAUqK2U.js";import{g as Ee,u as Ie}from"./DVdTSIWt.js";import{v as Ve}from"./eyo1vffA.js";import{E as Ce}from"./BpD5EEWf.js";import{a as J,E as Q}from"./B0taQFTv.js";import{a as Be,E as Z}from"./PVsoX9id.js";import{E as xe}from"./nw0ThIMP.js";import"./DHCaImEx.js";import"./qffJON56.js";import"./CmJNjzrM.js";import"./GbXQDMM-.js";import"./DCTLXrZ8.js";import"./DxoS9eIh.js";import"./Cv6HhfEG.js";import"./DcsXcc99.js";import"./DzhE9Pcm.js";import"./CjE8iZ8I.js";import"./C-BkwxIh.js";import"./DlKZEFPo.js";const ae=Symbol("breadcrumbKey"),Pe=G({separator:{type:String,default:"/"},separatorIcon:{type:re}}),Se=["aria-label"],$e=$({name:"ElBreadcrumb"}),Re=$({...$e,props:Pe,setup(C){const g=C,{t:w}=se(),s=H("breadcrumb"),u=v();return _e(ae,g),W(()=>{const d=u.value.querySelectorAll(`.${s.e("item")}`);d.length&&d[d.length-1].setAttribute("aria-current","page")}),(d,y)=>(i(),_("div",{ref_key:"breadcrumb",ref:u,class:P(e(s).b()),"aria-label":e(w)("el.breadcrumb.label"),role:"navigation"},[K(d.$slots,"default")],10,Se))}});var De=Y(Re,[["__file","breadcrumb.vue"]]);const Ne=G({to:{type:le([String,Object]),default:""},replace:{type:Boolean,default:!1}}),Ue=$({name:"ElBreadcrumbItem"}),Te=$({...Ue,props:Ne,setup(C){const g=C,w=ye(),s=ve(ae,void 0),u=H("breadcrumb"),d=w.appContext.config.globalProperties.$router,y=v(),p=()=>{!g.to||!d||(g.replace?d.replace(g.to):d.push(g.to))};return(a,b)=>{var E,B;return i(),_("span",{class:P(e(u).e("item"))},[h("span",{ref_key:"link",ref:y,class:P([e(u).e("inner"),e(u).is("link",!!a.to)]),role:"link",onClick:p},[K(a.$slots,"default")],2),(E=e(s))!=null&&E.separatorIcon?(i(),S(e(A),{key:0,class:P(e(u).e("separator"))},{default:r(()=>[(i(),S(ge(e(s).separatorIcon)))]),_:1},8,["class"])):(i(),_("span",{key:1,class:P(e(u).e("separator")),role:"presentation"},U((B=e(s))==null?void 0:B.separator),3))],2)}}});var te=Y(Te,[["__file","breadcrumb-item.vue"]]);const qe=ne(De,{BreadcrumbItem:te}),Ae=ie(te),Ke={class:"robot-edit-permission-wrapper"},Le={key:0,class:"permission-status-bar"},Me={class:"status-info"},Fe={class:"status-description"},Oe={key:0,class:"permission-warning"},We={key:1,class:"modification-warning"},je={key:1,class:"action-buttons"},ze=$({__name:"RobotEditPermissionWrapper",props:{robotId:{},kbId:{},type:{},showPermissionStatus:{type:Boolean,default:!0},showActionButtons:{type:Boolean,default:!0},autoCheck:{type:Boolean,default:!0}},emits:["save","cancel","permission-checked"],setup(C,{expose:g,emit:w}){const s=C,u=w,d=v(!1),y=v(!1),p=v(!0),a=v({}),b=q(()=>a.value.warning_type&&p.value===!1),E=q(()=>s.type==="robot"&&a.value.share_status?N.getRobotStatusTag(a.value.share_status):p.value?{type:"success",text:"可编辑"}:b.value?{type:"warning",text:"需确认"}:{type:"danger",text:"禁止编辑"}),B=q(()=>s.type==="robot"&&a.value.share_status?N.canShowEditButton(a.value.share_status):!0),L=q(()=>b.value?s.type==="robot"?"确认修改并下架":"确认修改":"保存"),R=async()=>{if(s.autoCheck){d.value=!0;try{let c;if(s.type==="robot"&&s.robotId)c=await N.checkRobotPermission(s.robotId);else if(s.type==="knowledge"&&s.kbId)c=await N.checkKnowledgePermission(s.kbId);else return;p.value=c.canModify,a.value=c.permissionInfo,u("permission-checked",c)}catch(c){console.error("权限检查失败:",c),f.error("权限检查失败")}finally{d.value=!1}}},M=async()=>{if(!(b.value&&!await N.showPermissionConfirmDialog(s.type,a.value))){if(!p.value&&!b.value){f.error(a.value.reason||"无权限进行此操作");return}y.value=!0;try{u("save",{forceModify:b.value,permissionInfo:a.value})}finally{y.value=!1}}},F=()=>{u("cancel")};return W(()=>{R()}),he([()=>s.robotId,()=>s.kbId],()=>{R()}),g({checkPermission:R,canModify:p,permissionInfo:a}),(c,x)=>(i(),_("div",Ke,[c.showPermissionStatus?(i(),_("div",Le,[h("div",Me,[o(e(ke),{type:E.value.type,size:"small",class:"status-tag"},{default:r(()=>[T(U(E.value.text),1)]),_:1},8,["type"]),h("span",Fe,U(a.value.reason),1)]),!p.value&&!b.value?(i(),_("div",Oe,[o(e(A),{class:"warning-icon"},{default:r(()=>[o(e(ue))]),_:1}),h("span",null,U(a.value.reason),1)])):D("",!0),b.value?(i(),_("div",We,[o(e(A),{class:"warning-icon"},{default:r(()=>[o(e(de))]),_:1}),x[0]||(x[0]=h("span",null,"修改后将自动下架，需要重新审核",-1))])):D("",!0)])):D("",!0),h("div",{class:P(["edit-content",{disabled:!p.value&&!b.value}])},[K(c.$slots,"default",{canModify:p.value,permissionInfo:a.value},void 0,!0)],2),c.showActionButtons?(i(),_("div",je,[K(c.$slots,"actions",{canModify:p.value,permissionInfo:a.value},()=>[B.value?(i(),S(e(j),{key:0,type:"primary",disabled:!p.value&&!b.value,loading:y.value,onClick:M},{default:r(()=>[T(U(L.value),1)]),_:1},8,["disabled","loading"])):D("",!0),o(e(j),{onClick:F},{default:r(()=>x[1]||(x[1]=[T(" 取消 ")])),_:1})],!0)])):D("",!0)]))}}),Xe=ee(ze,[["__scopeId","data-v-1ada24ce"]]),Je={class:"robot-edit-page"},Qe={class:"page-header"},Ze={class:"page-content"},Ge=["src"],He=$({__name:"edit-example",setup(C){const g=ce(),w=pe(),s=v(Number(g.params.id)),u=v(),d=v(!1),y=v([]),p=v([]),a=we({name:"",intro:"",icons:"",roles_prompt:"",kb_ids:[],model_id:"",temperature:.7}),b={name:[{required:!0,message:"请输入智能体名称",trigger:"blur"},{min:2,max:50,message:"名称长度在 2 到 50 个字符",trigger:"blur"}],intro:[{required:!0,message:"请输入智能体简介",trigger:"blur"},{max:200,message:"简介不能超过 200 个字符",trigger:"blur"}],roles_prompt:[{required:!0,message:"请输入角色提示词",trigger:"blur"}]},E=async()=>{try{d.value=!0;const l=await Ee({id:s.value});l.code===1?Object.assign(a,l.data):f.error(l.msg||"获取智能体详情失败")}catch(l){console.error("获取智能体详情失败:",l),f.error("获取智能体详情失败")}finally{d.value=!1}},B=async()=>{try{const l=await Ve();l.code===1&&(y.value=l.data)}catch(l){console.error("获取知识库列表失败:",l)}},L=l=>{console.log("权限检查结果:",l)},R=async l=>{try{await u.value.validate();const t={...a,id:s.value};l.forceModify&&(t.force_modify=1);const m=await Ie(t);m.code===1?(f.success("保存成功"),w.push("/robot/list")):m.data&&m.data.need_confirm?f.error(m.msg):f.error(m.msg||"保存失败")}catch(t){t.name==="ValidationError"?f.error("请检查表单填写是否正确"):(console.error("保存失败:",t),f.error("保存失败"))}},M=()=>{w.push("/robot/list")},F=l=>{console.log("选中的知识库:",l)},c=l=>{const t=l.type.startsWith("image/"),m=l.size/1024/1024<2;return t?m?!0:(f.error("图片大小不能超过 2MB!"),!1):(f.error("只能上传图片文件!"),!1)},x=async l=>{try{const t=new FormData;t.append("file",l.file);const m=await be(t);m.code===1?(t.icons=m.data.url,f.success("头像上传成功")):f.error(m.msg||"头像上传失败")}catch(t){console.error("头像上传失败:",t),f.error("头像上传失败")}};return W(()=>{E(),B()}),(l,t)=>{const m=Ae,oe=qe;return i(),_("div",Je,[h("div",Qe,[t[8]||(t[8]=h("h2",null,"编辑智能体",-1)),o(oe,{separator:"/"},{default:r(()=>[o(m,null,{default:r(()=>t[6]||(t[6]=[T("智能体管理")])),_:1}),o(m,null,{default:r(()=>t[7]||(t[7]=[T("编辑智能体")])),_:1})]),_:1})]),h("div",Ze,[o(Xe,{"robot-id":s.value,type:"robot",onSave:R,onCancel:M,onPermissionChecked:L},{default:r(({canModify:I,permissionInfo:V})=>[o(e(me),{ref_key:"formRef",ref:u,model:a,rules:b,"label-width":"120px",class:"robot-form"},{default:r(()=>[o(e(k),{label:"智能体名称",prop:"name"},{default:r(()=>[o(e(O),{modelValue:a.name,"onUpdate:modelValue":t[0]||(t[0]=n=>a.name=n),placeholder:"请输入智能体名称",disabled:!I&&!V.warning_type},null,8,["modelValue","disabled"])]),_:2},1024),o(e(k),{label:"智能体简介",prop:"intro"},{default:r(()=>[o(e(O),{modelValue:a.intro,"onUpdate:modelValue":t[1]||(t[1]=n=>a.intro=n),type:"textarea",rows:3,placeholder:"请输入智能体简介",disabled:!I&&!V.warning_type},null,8,["modelValue","disabled"])]),_:2},1024),o(e(k),{label:"智能体头像",prop:"icons"},{default:r(()=>[o(e(Ce),{class:"avatar-uploader","show-file-list":!1,"before-upload":c,"http-request":x,disabled:!I&&!V.warning_type},{default:r(()=>[a.icons?(i(),_("img",{key:0,src:a.icons,class:"avatar"},null,8,Ge)):(i(),S(e(A),{key:1,class:"avatar-uploader-icon"},{default:r(()=>[o(e(fe))]),_:1}))]),_:2},1032,["disabled"])]),_:2},1024),o(e(k),{label:"角色提示词",prop:"roles_prompt"},{default:r(()=>[o(e(O),{modelValue:a.roles_prompt,"onUpdate:modelValue":t[2]||(t[2]=n=>a.roles_prompt=n),type:"textarea",rows:5,placeholder:"请输入角色提示词",disabled:!I&&!V.warning_type},null,8,["modelValue","disabled"])]),_:2},1024),o(e(k),{label:"关联知识库",prop:"kb_ids"},{default:r(()=>[o(e(J),{modelValue:a.kb_ids,"onUpdate:modelValue":t[3]||(t[3]=n=>a.kb_ids=n),multiple:"",placeholder:"请选择关联知识库",style:{width:"100%"},disabled:!I&&!V.warning_type,onChange:F},{default:r(()=>[(i(!0),_(z,null,X(y.value,n=>(i(),S(e(Q),{key:n.id,label:n.name,value:n.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","disabled"])]),_:2},1024),o(e(k),{label:"模型配置"},{default:r(()=>[o(e(Be),{gutter:16},{default:r(()=>[o(e(Z),{span:12},{default:r(()=>[o(e(k),{prop:"model_id"},{default:r(()=>[o(e(J),{modelValue:a.model_id,"onUpdate:modelValue":t[4]||(t[4]=n=>a.model_id=n),placeholder:"请选择主模型",style:{width:"100%"},disabled:!I&&!V.warning_type},{default:r(()=>[(i(!0),_(z,null,X(p.value,n=>(i(),S(e(Q),{key:n.id,label:n.name,value:n.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","disabled"])]),_:2},1024)]),_:2},1024),o(e(Z),{span:12},{default:r(()=>[o(e(k),{prop:"temperature"},{default:r(()=>[o(e(xe),{modelValue:a.temperature,"onUpdate:modelValue":t[5]||(t[5]=n=>a.temperature=n),min:0,max:1,step:.1,"show-input":"",disabled:!I&&!V.warning_type},null,8,["modelValue","disabled"])]),_:2},1024)]),_:2},1024)]),_:2},1024)]),_:2},1024)]),_:2},1032,["model"])]),_:1},8,["robot-id"])])])}}}),ka=ee(He,[["__scopeId","data-v-5a1768d4"]]);export{ka as default};
