import{H as o,I as p,J as f,L as _}from"./DGzblORL.js";import{l as m,m as i,M as a,N as s,O as y,a2 as h,u as r,a3 as l,a0 as g,a6 as I,al as c,W as C,a7 as u,Z as N,am as S,_ as $}from"./uahP8ofS.js";import{_ as k}from"./DlAUqK2U.js";const x=m({props:{name:{type:String,required:!0},size:{type:[Number,String],default:16},color:{type:String,default:"inherit"}},setup(e){const t=i(()=>`#${e.name}`),n=i(()=>({width:o(e.size),height:o(e.size),color:e.color}));return{symbolId:t,styles:n}}}),z=["xlink:href"];function E(e,t,n,d,v,B){return a(),s("svg",{"aria-hidden":"true",style:h(e.styles)},[y("use",{"xlink:href":e.symbolId,fill:"currentColor"},null,8,z)],4)}const P=k(x,[["render",E]]),b={key:1,class:"local-icon"},R=m({__name:"index",props:{name:{type:String,default:""},size:{type:[String,Number],default:"14px"},color:{type:String,default:"inherit"}},setup(e){const t=e;return(n,d)=>(a(),s($,null,[e.name.includes(r(p))?(a(),l(r(f),c(C({key:0},{...t,...n.$attrs})),{default:g(()=>[(a(),l(I(e.name)))]),_:1},16)):u("",!0),e.name.includes(r(_))?(a(),s("span",b,[N(P,c(S(t)),null,16)])):u("",!0)],64))}});export{R as _};
