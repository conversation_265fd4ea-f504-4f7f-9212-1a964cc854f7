import{_ as B}from"./BAooD3NP.js";import{E as R}from"./D0NTNmoy.js";import{E as T}from"./DL-C_KWg.js";import{h as M,f as O,v as Q}from"./C12kmceL.js";import"./DH3GNTka.js";/* empty css        */import{isSameFile as P}from"./BiHhwkbt.js";import{s as W}from"./C4qLKnCc.js";import{_ as X}from"./DQxdb33m.js";import{r as E,a as Z,b as G}from"./CcdExHMo.js";import{l as H,b as g,j as J,r as K,i as Y,M as c,N as r,O as a,Z as d,ab as ee,u as s,a0 as V,_ as F,aq as I,a5 as w,a7 as te,a4 as ae,X as oe}from"./uahP8ofS.js";import{_ as se}from"./DlAUqK2U.js";import"./Cr9cZ-Xs.js";import"./DHsrbrOc.js";import"./DWZt4P8F.js";import"./Cpg3PDWZ.js";const ne={class:"important-notice"},le={class:"notice-header"},ie={class:"h-full flex flex-col"},ce={class:"py-[16px]"},re={class:"el-upload__text"},de={class:"el-upload__text"},pe={key:0,class:"grid grid-cols-2 gap-4 flex-1 min-h-[500px]"},me={style:{"border-right":"1px solid #eeeeee"}},ue={class:"mt-4 max-w-[500px]"},_e=["onClick"],fe={class:"ml-2"},he={class:"closeIcon ml-auto opacity-0 transition duration-300 flex items-center"},ve={class:"flex flex-col"},xe={class:"text-lg"},ge={class:"flex-auto mt-2 h-[100px]"},we=H({__name:"QASplit",props:{modelValue:{}},emits:["update:modelValue"],setup(L,{emit:S}){const n=M(L,"modelValue",S),k=[".txt",".docx",".pdf",".md"],h=k.join(", "),u=g([]),b=J(),v=g(!1),p=g(-1),$=K([]),q=Y("knowDetail"),N=async({raw:e})=>{var t,o;try{if(e){const l="."+((t=e.name.split(".").pop())==null?void 0:t.toLowerCase());if(!k.includes(l))throw`不支持的文件类型，请上传 ${h} 格式的文件`;v.value=!0,await P(e,u.value);const _=await A(e);if(!_)throw"解析结果为空，已自动忽略";const x=U(_);n.value.push({name:e.name,path:"",data:x}),u.value.push(e),C(u.value.length-1)}}catch(l){O.msgError(l)}finally{v.value=!1,(o=b.value)==null||o.clearFiles()}},A=async e=>{const t=e.name.substring(e.name.lastIndexOf(".")+1);let o="";switch(t){case"md":case"txt":o=await E(e);break;case"pdf":o=await G(e);break;case"doc":case"docx":o=await Z(e);break;default:o=await E(e);break}return o},C=e=>{p.value=e,$.length=0},U=e=>{const t=[];return W({text:e,chunkLen:q.qa_length}).forEach(l=>{t.push({q:l,a:""})}),t},j=async e=>{n.value[p.value].data.splice(e,1)},z=e=>{n.value.splice(e,1),u.value.splice(e,1)};return(e,t)=>{var y;const o=B,l=R,_=T,x=Q;return c(),r("div",null,[a("div",ne,[a("div",le,[d(o,{name:"el-icon-Warning",color:"#ff9900",size:"18"}),t[0]||(t[0]=a("span",{class:"notice-title"},"重要提示",-1))]),t[1]||(t[1]=a("div",{class:"notice-content"}," 为保护您的信息安全，请勿录入如姓名、身份证号、电话等个人隐私信息及任何工作秘密、商业秘密。本平台在未获得您同意的情况下，不会使用您的数据进行模型训练。 ",-1))]),a("div",ie,[ee((c(),r("div",ce,[d(l,{ref_key:"uploadRef",ref:b,drag:"","on-change":N,"auto-upload":!1,"show-file-list":!1,accept:s(h),multiple:!0,limit:50},{default:V(()=>[a("div",re,[d(o,{name:"el-icon-Upload"}),t[2]||(t[2]=ae(" 拖拽文件至此，或点击")),t[3]||(t[3]=a("em",null," 选择文件 ",-1))]),a("div",de,"支持 "+w(s(h))+" 文件",1)]),_:1},8,["accept"])])),[[x,s(v)]]),s(n).length>0?(c(),r("div",pe,[a("div",me,[a("div",ue,[(c(!0),r(F,null,I(s(n),(f,i)=>(c(),r("div",{key:i,class:oe(["fileItem flex items-center p-2 rounded-lg mt-1 hover:cursor-pointer hover:bg-page transition duration-300",{"bg-page":s(p)==i}]),onClick:m=>C(i)},[d(o,{name:"el-icon-Folder",size:16,color:"#ffc94d"}),a("div",fe,w(f.name),1),a("div",he,[d(o,{name:"el-icon-DeleteFilled",onClick:m=>z(i)},null,8,["onClick"])])],10,_e))),128))])]),a("div",ve,[a("div",xe," 分段预览（"+w((y=s(n)[s(p)])==null?void 0:y.data.length)+"组） ",1),a("div",ge,[d(_,{height:"100%"},{default:V(()=>{var f;return[(c(!0),r(F,null,I((f=s(n)[s(p)])==null?void 0:f.data,(i,m)=>(c(),r("div",{class:"bg-page rounded p-[10px] mt-2",key:m},[d(X,{index:m,name:s(n)[s(p)].name,data:i.q,"onUpdate:data":D=>i.q=D,onDelete:D=>j(m)},null,8,["index","name","data","onUpdate:data","onDelete"])]))),128))]}),_:1})])])])):te("",!0)])])}}}),Be=se(we,[["__scopeId","data-v-9ef33b78"]]);export{Be as default};
