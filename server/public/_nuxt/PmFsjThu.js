import{cn as x,e as v}from"./C3HqF-ve.js";import{_ as h}from"./D5KDMvDa.js";import{_ as w}from"./Xb_dJkX7.js";import{_ as V}from"./DwWhvIwJ.js";import{l as k,M as C,N as I,O as n,Z as s,u as m,y as l,a0 as M,a7 as $}from"./Dp9aCaJ6.js";import{_ as g}from"./DlAUqK2U.js";import"./BPWdBU3q.js";import"./DfZUM0y5.js";import"./DCTLXrZ8.js";import"./Ded2KV7I.js";import"./Dg2XwvBU.js";import"./9Bti1uB6.js";/* empty css        *//* empty css        */import"./BLV0QRdm.js";import"./ByNVYBgC.js";import"./Cq2NhlyP.js";import"./DOw7ocGw.js";const B={class:"bg-page rounded-[15px] overflow-hidden p-[10px] search-input"},D={class:"flex items-center"},E={class:"mr-auto"},N={class:"flex items-center px-[8px]"},R={class:"px-[6px]"},z=k({__name:"search-input",props:{mode:{},model:{},type:{},input:{}},emits:["update:type","update:input","search"],setup(d,{emit:u}){const c=d,p=u,{type:a,input:i}=x(c,p),_=e=>{if(!(e.shiftKey&&e.keyCode===13)&&e.keyCode===13)return p("search"),e.preventDefault()};return(e,t)=>{const f=v,r=h;return C(),I("div",B,[n("div",null,[s(f,{modelValue:m(i),"onUpdate:modelValue":t[0]||(t[0]=o=>l(i)?i.value=o:null),autosize:{minRows:2,maxRows:4},type:"textarea",placeholder:"输入你想搜索的问题",resize:"none",onKeydown:_},null,8,["modelValue"])]),n("div",D,[n("div",E,[s(w,{type:m(a),"onUpdate:type":t[1]||(t[1]=o=>l(a)?a.value=o:null),model:e.model},{item:M(({icon:o,label:y})=>[n("div",N,[s(r,{name:o},null,8,["name"]),n("span",R,$(y),1),s(r,{name:"el-icon-ArrowDown"})])]),_:1},8,["type","model"])]),n("div",null,[s(V,{onClick:t[2]||(t[2]=o=>p("search"))})])])])}}}),X=g(z,[["__scopeId","data-v-68cad505"]]);export{X as default};
