import{_ as k}from"./FhwEPQ4g.js";import{_ as w}from"./CN8DIg3d.js";import{a as D,l as b}from"./ClNUxNV9.js";import{_ as h}from"./BJkx99dh.js";import g from"./BqC-9iXG.js";import q from"./y0-xZlw0.js";import S from"./BPCiZmko.js";import{b as U}from"./BNnETjxs.js";import{l as V,b as c,r as B,c as C,F as M,q as N,M as u,N as I,Z as d,u as r,y as O,O as n,a0 as R,a1 as $,a3 as T}from"./Dp9aCaJ6.js";import{_ as j}from"./DlAUqK2U.js";import"./B6IIPh85.js";import"./D99FoGlZ.js";import"./Tedtu6ac.js";import"./DCTLXrZ8.js";import"./l0sNRNKZ.js";/* empty css        */import"./D76T9XJY.js";import"./DMVjPTRc.js";import"./DkqMgBWM.js";import"./DH3BuQAR.js";/* empty css        *//* empty css        */import"./UvOa_tuW.js";import"./hx-0JZAY.js";import"./CNgDMrD1.js";import"./zRTrVFrw.js";import"./D4cQUBDp.js";import"./B1qK8f0i.js";import"./DrS9ZDPg.js";import"./BbPTMigZ.js";import"./Cr7puf4F.js";import"./DggqxWTi.js";import"./C_z8SI9s.js";import"./CkTU2NpD.js";import"./Cd8UtlNR.js";import"./Cv6HhfEG.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./67xbGseh.js";import"./D-p_iaQf.js";import"./qQcR97T8.js";import"./C0R3uvOr.js";import"./9Bti1uB6.js";/* empty css        */import"./DP2rzg_V.js";/* empty css        */import"./BCKWAVV6.js";import"./CcPlX2kz.js";import"./BiHhwkbt.js";import"./CMXd0SVN.js";import"./CqanTtdS.js";import"./CxSV922q.js";import"./CeZA--ML.js";import"./C5YZ-9ot.js";import"./Cga6GjQY.js";import"./Cpg3PDWZ.js";import"./DTKTzERV.js";import"./Bs71q4x0.js";import"./C4qLKnCc.js";import"./BILmIRKF.js";import"./CsLzEdbI.js";import"./DS13yg-4.js";import"./DNEU5M_f.js";import"./CCKuheBh.js";/* empty css        */import"./19zTobXl.js";import"./DaDeVoIA.js";import"./COZg6ecR.js";import"./mW1R_rAi.js";import"./BE7GAo-z.js";import"./NEhbE7vl.js";import"./DZESUSa0.js";import"./DAKepvAG.js";import"./D6j_GvJh.js";import"./BYYVzU6A.js";import"./DIFvMdL6.js";import"./CQXeYJFv.js";import"./DCJ0qFa1.js";import"./D9R3MaZj.js";/* empty css        */import"./-wubw-0v.js";/* empty css        */import"./DwFObZc_.js";import"./Jk5hT3-E.js";import"./BQ2TCOuy.js";import"./i_f560_N.js";const E={class:"h-full flex"},F={class:"flex-1 min-w-0 overflow-auto pr-[16px] py-[16px]"},K={class:"bg-body h-full rounded-2xl"},L={class:"import-data h-full"},Z=V({__name:"index",setup(z){const m=D(),_=b(),s=c(1),p=m.query.id,f={dataStudy:h,testData:g,teamData:S,setUp:q},i=B({type:"",name:"",image:"",intro:"",owned:1,power:1,qa_length:""}),o=c("dataStudy"),y=[{name:"数据学习",icon:"el-icon-Document",key:"dataStudy"},{name:"搜索测试",key:"testData",icon:"el-icon-Search"},{name:"团队成员",key:"teamData",icon:"el-icon-User"},{name:"知识库设置",key:"setUp",icon:"el-icon-Setting"}],l=async()=>{const e=await U({id:p});Object.keys(i).map(t=>{i[t]=e[t]}),m.query.type&&(o.value=m.query.type)};return C(()=>o.value,e=>{_.replace({path:"",query:{id:p,type:e}})}),M(()=>{l()}),N("knowDetail",i),(e,t)=>{const v=k,x=w;return u(),I("div",E,[d(v,{modelValue:r(o),"onUpdate:modelValue":t[0]||(t[0]=a=>O(o)?o.value=a:null),"menu-list":y,title:r(i).name,"back-path":"/application/layout/kb"},null,8,["modelValue","title"]),n("div",F,[d(x,null,{default:R(()=>[n("div",K,[n("div",L,[(u(),$(T(f[r(o)]),{id:r(p),type:r(i).type,onToImport:t[1]||(t[1]=a=>s.value=2),onSuccess:t[2]||(t[2]=a=>s.value=1),onUpdate:l},null,40,["id","type"]))])])]),_:1})])])}}}),Uo=j(Z,[["__scopeId","data-v-3affaafe"]]);export{Uo as default};
