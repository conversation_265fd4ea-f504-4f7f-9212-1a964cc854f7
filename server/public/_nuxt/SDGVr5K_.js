import{E as A}from"./hx-0JZAY.js";import{_ as M}from"./B6IIPh85.js";import{E as R}from"./DH3BuQAR.js";import{h as F,E as j,v as q}from"./ClNUxNV9.js";import{E as O}from"./C0R3uvOr.js";/* empty css        *//* empty css        *//* empty css        */import{u as U}from"./CcPlX2kz.js";import{c as X}from"./R2n930gq.js";import{l as Z,r as G,b as H,m as J,c as C,M as a,a1 as E,a0 as v,aa as K,u as t,N as i,O as e,Z as c,_,aq as f,a6 as Q,y as W,X as V,a7 as Y,a2 as ee,a4 as le}from"./Dp9aCaJ6.js";const oe={class:"flex text-tx-primary"},te={class:"flex-[3] h-full mr-[26px]"},se={class:"flex-[1.8] flex flex-col"},ae={class:"flex text-xs"},re={class:"flex flex-wrap"},ne=["onClick"],ie={class:"flex text-xs"},ce={class:"flex flex-wrap mx-[-6px]"},pe=["onClick"],de={class:"flex-1 min-h-0"},me={class:"overflow-hidden"},ue={class:"flex flex-wrap mx-[-6px] py-[12px]"},ve=["onClick"],xe={class:"px-[6px]"},_e={class:"flex ml-[10px]"},Ie=Z({__name:"select-template",props:{visible:{type:Boolean},coverId:{},prompt:{default:""}},emits:["update:visible","update:coverId","confirm"],setup(I,{emit:B}){const r=I,x=B,p=F(r,"visible",x),d=F(r,"coverId",x),P=["科技","商务","小清新","极简","中国风","可爱卡通"],S=[{label:"红色",value:"#D7000F"},{label:"橙色",value:"#FF7A00"},{label:"黄色",value:"#FFC700"},{label:"绿色",value:"#39D819"},{label:"青色",value:"#3f9097"},{label:"蓝色",value:"#0385FF"},{label:"紫色",value:"#C73AFF"},{label:"粉色",value:"#FF3AA0"}],n=G({color:"红色",style:"科技"});let b="";const m=H([]),g=()=>{const[s]=m.value;s&&s.cover_id&&(d.value=s.cover_id)},{lockFn:y,isLock:T}=U(async()=>{b=r.prompt,m.value=await X({prompt:r.prompt,...n}),g()}),D=J(()=>m.value.find(s=>s.cover_id===r.coverId)||{});return C(n,()=>{y()},{deep:!0}),C(p,s=>{if(s){if(b===r.prompt){!d.value&&g();return}y()}}),(s,o)=>{const k=A,h=M,L=R,N=j,$=O,z=q;return a(),E($,{modelValue:t(p),"onUpdate:modelValue":o[1]||(o[1]=l=>W(p)?p.value=l:null),width:"1100"},{default:v(()=>[K((a(),i("div",oe,[e("div",te,[c(k,{class:"w-full rounded-[10px] h-[350px]",src:t(D).cover_image},{error:v(()=>o[2]||(o[2]=[e("div",{class:"el-image__error"},"选中右侧模板以预览",-1)])),_:1},8,["src"])]),e("div",se,[o[6]||(o[6]=e("div",{class:"font-bold mb-[15px]"},"选择PPT模板",-1)),e("div",ae,[o[3]||(o[3]=e("div",{class:"flex-none mt-[5px] text-tx-regular mr-[8px]"}," 模板风格: ",-1)),e("div",re,[(a(),i(_,null,f(P,(l,u)=>e("div",{key:u,class:V(["mx-[1px] px-[8px] py-[5px] rounded cursor-pointer hover:bg-page mb-[8px]",{"!bg-page":t(n).style===l}]),onClick:w=>t(n).style=l},Y(l),11,ne)),64))])]),e("div",ie,[o[4]||(o[4]=e("div",{class:"flex-none text-tx-regular mr-[8px]"}," 主题颜色: ",-1)),e("div",ce,[(a(),i(_,null,f(S,(l,u)=>e("div",{key:u,class:"w-[18px] h-[18px] text-white mx-[6px] mb-[6px] rounded-[50%] flex items-center justify-center cursor-pointer",style:ee({background:l.value}),onClick:w=>t(n).color=l.label},[t(n).color===l.label?(a(),E(h,{key:0,name:"el-icon-Select",size:12})):le("",!0)],12,pe)),64))])]),e("div",de,[c(L,null,{default:v(()=>[e("div",me,[e("div",ue,[(a(!0),i(_,null,f(t(m),(l,u)=>(a(),i("div",{class:"w-[50%]",key:l.cover_id,onClick:w=>d.value=l.cover_id},[e("div",xe,[c(k,{class:V(["w-full h-[100px] rounded border-2 cursor-pointer border-solid border-[transparent]",{"border-primary":t(d)===l.cover_id}]),src:l.cover_image},null,8,["class","src"])])],8,ve))),128))])])]),_:1})]),e("div",null,[c(N,{class:"w-full",type:"primary",size:"large",onClick:o[0]||(o[0]=l=>x("confirm"))},{default:v(()=>[o[5]||(o[5]=Q(" 生成PPT ")),e("span",_e,[c(h,{name:"el-icon-Right"})])]),_:1})])])])),[[z,t(T)]])]),_:1},8,["modelValue"])}}});export{Ie as _};
