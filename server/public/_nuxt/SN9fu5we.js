import{_ as l}from"./BY8Moot3.js";import{b as u,E as f}from"./C9xud4Fy.js";import{useSearch as d}from"./Ch8gf-1S.js";import{l as x,M as a,a3 as k,a0 as n,Z as y,a4 as o,O as g,u as e,N as r,_ as c,a5 as p,a7 as B}from"./uahP8ofS.js";const N={class:"text-xs ml-1"},F=x({__name:"search-btn",setup(S){const{config:t}=d(),i=u();return(V,s)=>{const m=l,_=f;return a(),k(_,{type:"primary",style:{padding:"8px"}},{icon:n(()=>[y(m,{name:"el-icon-Search"})]),default:n(()=>[s[0]||(s[0]=o(" 搜索 ")),g("span",N,[e(t).isVipFree?(a(),r(c,{key:0},[o(" 会员免费 ")],64)):e(t).price>0?(a(),r(c,{key:1},[o(" -"+p(e(t).price)+p(e(i).getTokenUnit),1)],64)):B("",!0)])]),_:1})}}});export{F as _};
