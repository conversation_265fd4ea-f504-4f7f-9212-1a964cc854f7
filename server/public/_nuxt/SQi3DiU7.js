import{l as g,$ as M,m as b,b as E,M as o,a1 as V,Z as l,a0 as f,aa as q,ab as H,u as e,O as A,X as t,N as i,V as c,a4 as r,W as U,a9 as J,ac as K,at as W,a7 as X}from"./Dp9aCaJ6.js";import{K as Z,aw as j,M as G,a5 as Q,ax as Y,ay as x,J as ee,T as ae,O as se,$ as te,P as oe}from"./ClNUxNV9.js";import{d as re,a as le,u as ie}from"./C0R3uvOr.js";const de=Z({...re,direction:{type:String,default:"rtl",values:["ltr","rtl","ttb","btt"]},size:{type:[String,Number],default:"30%"},withHeader:{type:Boolean,default:!0},modalFade:{type:Boolean,default:!0},headerAriaLevel:{type:String,default:"2"}}),ne=le,ue=["aria-label","aria-labelledby","aria-describedby"],fe=["id","aria-level"],ce=["aria-label"],pe=["id"],ve=g({name:"ElDrawer",inheritAttrs:!1}),me=g({...ve,props:de,emits:ne,setup(R,{expose:S}){const d=R,F=M();j({scope:"el-drawer",from:"the title slot",replacement:"the header slot",version:"3.0.0",ref:"https://element-plus.org/en-US/component/drawer.html#slots"},b(()=>!!F.title));const p=E(),y=E(),s=G("drawer"),{t:L}=Q(),{afterEnter:h,afterLeave:w,beforeLeave:$,visible:v,rendered:B,titleId:m,bodyId:k,zIndex:D,onModalClick:P,onOpenAutoFocus:T,onCloseAutoFocus:z,onFocusoutPrevented:I,onCloseRequested:N,handleClose:n}=ie(d,p),O=b(()=>d.direction==="rtl"||d.direction==="ltr"),C=b(()=>te(d.size));return S({handleClose:n,afterEnter:h,afterLeave:w}),(a,u)=>(o(),V(W,{to:"body",disabled:!a.appendToBody},[l(K,{name:e(s).b("fade"),onAfterEnter:e(h),onAfterLeave:e(w),onBeforeLeave:e($),persisted:""},{default:f(()=>[q(l(e(Y),{mask:a.modal,"overlay-class":a.modalClass,"z-index":e(D),onClick:e(P)},{default:f(()=>[l(e(x),{loop:"",trapped:e(v),"focus-trap-el":p.value,"focus-start-el":y.value,onFocusAfterTrapped:e(T),onFocusAfterReleased:e(z),onFocusoutPrevented:e(I),onReleaseRequested:e(N)},{default:f(()=>[A("div",U({ref_key:"drawerRef",ref:p,"aria-modal":"true","aria-label":a.title||void 0,"aria-labelledby":a.title?void 0:e(m),"aria-describedby":e(k)},a.$attrs,{class:[e(s).b(),a.direction,e(v)&&"open"],style:e(O)?"width: "+e(C):"height: "+e(C),role:"dialog",onClick:u[1]||(u[1]=J(()=>{},["stop"]))}),[A("span",{ref_key:"focusStartRef",ref:y,class:t(e(s).e("sr-focus")),tabindex:"-1"},null,2),a.withHeader?(o(),i("header",{key:0,class:t(e(s).e("header"))},[a.$slots.title?c(a.$slots,"title",{key:1},()=>[r(" DEPRECATED SLOT ")]):c(a.$slots,"header",{key:0,close:e(n),titleId:e(m),titleClass:e(s).e("title")},()=>[a.$slots.title?r("v-if",!0):(o(),i("span",{key:0,id:e(m),role:"heading","aria-level":a.headerAriaLevel,class:t(e(s).e("title"))},X(a.title),11,fe))]),a.showClose?(o(),i("button",{key:2,"aria-label":e(L)("el.drawer.close"),class:t(e(s).e("close-btn")),type:"button",onClick:u[0]||(u[0]=(..._)=>e(n)&&e(n)(..._))},[l(e(ee),{class:t(e(s).e("close"))},{default:f(()=>[l(e(ae))]),_:1},8,["class"])],10,ce)):r("v-if",!0)],2)):r("v-if",!0),e(B)?(o(),i("div",{key:1,id:e(k),class:t(e(s).e("body"))},[c(a.$slots,"default")],10,pe)):r("v-if",!0),a.$slots.footer?(o(),i("div",{key:2,class:t(e(s).e("footer"))},[c(a.$slots,"footer")],2)):r("v-if",!0)],16,ue)]),_:3},8,["trapped","focus-trap-el","focus-start-el","onFocusAfterTrapped","onFocusAfterReleased","onFocusoutPrevented","onReleaseRequested"])]),_:3},8,["mask","overlay-class","z-index","onClick"]),[[H,e(v)]])]),_:3},8,["name","onAfterEnter","onAfterLeave","onBeforeLeave"])],8,["disabled"]))}});var be=se(me,[["__file","drawer.vue"]]);const ke=oe(be);export{ke as E};
