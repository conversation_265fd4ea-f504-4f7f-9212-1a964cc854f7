import{a as T,l as U,b as $,j,s as E,m as F,e as O,_ as H}from"./Ct33iMSA.js";import{u as J}from"./DuO6be6_.js";import I from"./qvCcBo_D.js";import X from"./CZ9xKNx2.js";import Z from"./I99hOuc8.js";import G from"./DbAd14Tb.js";import{l as K,b as p,ak as P,m as Q,F as W,M as l,a1 as N,a0 as Y,O as r,a2 as tt,u as e,X as S,a7 as V,N as A,aq as et,_ as ot,Z as rt,y as at,a3 as st,a4 as pt,n as lt}from"./Dp9aCaJ6.js";import{_ as nt}from"./DlAUqK2U.js";import"./cZpipWaD.js";import"./BDmMpw_0.js";import"./BPaXy7Em.js";import"./BCfv4qMP.js";import"./t4HvZ20D.js";import"./BHaz_wmF.js";import"./CLVsM8Qg.js";/* empty css        *//* empty css        */import"./Bo3PTL3c.js";import"./LwQ_Zv-K.js";import"./DvrbA4QQ.js";import"./iAKPM1CD.js";import"./DCTLXrZ8.js";import"./l0sNRNKZ.js";/* empty css        */import"./CJgd20ip.js";import"./DjwCd26w.js";import"./NobvHViT.js";import"./C4ZGlHhy.js";import"./DJ8m2TZq.js";import"./B4i2sXD1.js";import"./kzeq9hfp.js";import"./SbrOwfyf.js";import"./9Bti1uB6.js";/* empty css        *//* empty css        *//* empty css        */import"./D16flgaF.js";import"./DmGK8OfV.js";import"./Bg2e09vA.js";import"./DxKRx1wF.js";import"./DDbp6ww0.js";import"./BHQFQfA3.js";import"./BDl3LJB7.js";import"./DlKZEFPo.js";import"./Bu_nKEGp.js";import"./Dl86xJuo.js";import"./PIZLygzx.js";import"./BFbcoQee.js";import"./Cpg3PDWZ.js";const it={class:"h-full flex flex-col 4xl:w-[2000px] mx-auto"},mt={class:"tabs-list grid grid-cols-4 gap-4 mt-4"},ct=["onClick"],ut={class:"2xl:max-w-[880px] xl:max-w-[780px] lg:max-w-[680px] max-w-[680px] search w-full mt-4"},xt={key:0,class:"flex-1 min-h-0 mx-[16px] relative"},dt=K({__name:"index",async setup(_t){let s,x;const z=T(),D=U(),R=$();j();const n=[{label:"智能体",value:0,component:I},{label:"AI绘画",value:1,component:X},{label:"AI音乐",value:2,component:Z},{label:"AI视频",value:3,component:G}],o=p(0),a=p(""),i=p(I),m=p(!0),{data:c}=([s,x]=P(()=>J(()=>F({id:6}),{lazy:!0,default(){return[]},transform:t=>JSON.parse(t.data)},"$mrRHbfubzc")),s=await s,x(),s),q=Q(()=>t=>{switch(t){case 1:return"text-black";case 2:return"text-white";case 3:return"text-primary"}}),B=async t=>{o.value!==t&&(a.value="",o.value=t,m.value=!1,i.value=n[o.value].component,await lt(),m.value=!0,D.replace({path:"",query:{type:t}}))};return W(async()=>{const t=z.query.type;t&&(o.value=Number(t),i.value=n[o.value].component)}),(t,d)=>{const L=O,M=H;return l(),N(M,{name:"default"},{default:Y(()=>{var _,f,v,b,y,h,w,g,k;return[r("div",it,[r("header",{class:"robot-square-header flex flex-col justify-center items-center px-[16px] m-[16px] rounded-[12px] overflow-hidden",style:tt({"background-image":`url(${e(R).getImageUrl((v=(f=(_=e(c))==null?void 0:_[0])==null?void 0:f.prop)==null?void 0:v.banner_bg)})`})},[r("div",{class:S(["font-medium 2xl:text-[50px] xl:text-[40px] lg:text-[36px] text-[36px]",e(q)((h=(y=(b=e(c))==null?void 0:b[0])==null?void 0:y.prop)==null?void 0:h.title_color)])},V((k=(g=(w=e(c))==null?void 0:w[0])==null?void 0:g.prop)==null?void 0:k.title),3),r("div",mt,[(l(),A(ot,null,et(n,(u,C)=>r("div",{class:S(["tabs-item bg-white",{"is-active":e(o)===C}]),onClick:ft=>B(C)},V(u.label),11,ct)),64))]),r("div",ut,[rt(L,{size:"large",class:"2xl:h-[54px] xl:h-[48px] lg:h-[44px] rounded-[12px]",style:{"--el-border-color":"transparent"},modelValue:e(a),"onUpdate:modelValue":d[0]||(d[0]=u=>at(a)?a.value=u:null),clearable:!0,"prefix-icon":e(E),placeholder:"请输入关键词搜索"},null,8,["modelValue","prefix-icon"])])],4),e(m)?(l(),A("div",xt,[(l(),N(st(e(i)),{keyword:e(a)},null,8,["keyword"]))])):pt("",!0)])]}),_:1})}}}),ue=nt(dt,[["__scopeId","data-v-411df7fd"]]);export{ue as default};
