import{E as h}from"./BMjy7v9n.js";import{_ as y}from"./CDtCQ8p9.js";import{b as f,c$ as k}from"./C12kmceL.js";import{l as N,m as b,M as t,N as o,Z as p,a0 as m,u as s,a5 as w,a7 as r}from"./uahP8ofS.js";import{_ as I}from"./DlAUqK2U.js";import"./CcLFL5SG.js";import"./CBI7ecvA.js";import"./DCTLXrZ8.js";import"./D8Q6oBc7.js";import"./DmVakCHI.js";import"./CWOFGv9Q.js";const S={key:0,class:"mb-[6px]"},g=["src"],B=["src"],$={key:0,class:"text-sm"},q=N({__name:"menu-item",props:{item:{},path:{},showName:{type:Boolean},isShowIcon:{type:[Number,Boolean]},isActive:{type:Boolean}},setup(u){const l=u,{getImageUrl:a}=f(),i=b(()=>{const e=l.item.link.query;try{const n=JSON.parse(e);return k(n)}catch{return e}});return(e,n)=>{const d=h,_=y;return t(),o("div",null,[p(_,{to:`${e.path}${s(i)?`?${s(i)}`:""}`},{default:m(()=>[p(d,{index:e.path},{title:m(()=>{var c;return[(c=e.item)!=null&&c.showName||e.showName?(t(),o("span",$,w(e.item.name),1)):r("",!0)]}),default:m(()=>[e.isShowIcon?(t(),o("span",S,[e.isActive&&e.item.selected?(t(),o("img",{key:0,class:"menu-item-icon",src:s(a)(e.item.selected)},null,8,g)):e.item.unselected?(t(),o("img",{key:1,class:"menu-item-icon",src:s(a)(e.item.unselected)},null,8,B)):r("",!0)])):r("",!0)]),_:1},8,["index"])]),_:1},8,["to"])])}}}),T=I(q,[["__scopeId","data-v-236bb885"]]);export{T as default};
