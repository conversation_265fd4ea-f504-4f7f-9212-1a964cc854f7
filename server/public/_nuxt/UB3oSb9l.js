import{l as v,m as T,M as n,N as M,O as t,V as f,X as o,u as a,a3 as c,a0 as r,a9 as k,a7 as g,a2 as y,aa as N,Z as C}from"./uahP8ofS.js";import{K as w,R as V,S as P,M as $,T as b,J as h,O as I,P as K}from"./DkNxoV1Z.js";const O=w({type:{type:String,values:["primary","success","info","warning","danger"],default:"primary"},closable:Boolean,disableTransitions:<PERSON><PERSON><PERSON>,hit:Boolean,color:String,size:{type:String,values:V},effect:{type:String,values:["dark","light","plain"],default:"light"},round:Boolean}),F={close:l=>l instanceof MouseEvent,click:l=>l instanceof MouseEvent},J=v({name:"ElTag"}),R=v({...J,props:O,emits:F,setup(l,{emit:i}){const S=l,_=P(),s=$("tag"),u=T(()=>{const{type:e,hit:m,effect:B,closable:E,round:z}=S;return[s.b(),s.is("closable",E),s.m(e||"primary"),s.m(_.value),s.m(B),s.is("hit",m),s.is("round",z)]}),p=e=>{i("close",e)},d=e=>{i("click",e)};return(e,m)=>e.disableTransitions?(n(),M("span",{key:0,class:o(a(u)),style:y({backgroundColor:e.color}),onClick:d},[t("span",{class:o(a(s).e("content"))},[f(e.$slots,"default")],2),e.closable?(n(),c(a(h),{key:0,class:o(a(s).e("close")),onClick:k(p,["stop"])},{default:r(()=>[C(a(b))]),_:1},8,["class","onClick"])):g("v-if",!0)],6)):(n(),c(N,{key:1,name:`${a(s).namespace.value}-zoom-in-center`,appear:""},{default:r(()=>[t("span",{class:o(a(u)),style:y({backgroundColor:e.color}),onClick:d},[t("span",{class:o(a(s).e("content"))},[f(e.$slots,"default")],2),e.closable?(n(),c(a(h),{key:0,class:o(a(s).e("close")),onClick:k(p,["stop"])},{default:r(()=>[C(a(b))]),_:1},8,["class","onClick"])):g("v-if",!0)],6)]),_:3},8,["name"]))}});var X=I(R,[["__file","tag.vue"]]);const q=K(X);export{q as E,O as t};
