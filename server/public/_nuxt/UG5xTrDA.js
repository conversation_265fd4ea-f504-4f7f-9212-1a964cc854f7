import{_ as m}from"./LAbtQoPB.js";import{_ as p}from"./DXe7oepa.js";import{b as s}from"./D726nzJl.js";import{l as a,M as l,N as n,Z as t,X as c,u as r,O as i,V as _}from"./Dp9aCaJ6.js";import{_ as d}from"./DlAUqK2U.js";import"./DU8ualjj.js";import"./CN8DIg3d.js";import"./BpYIl71c.js";import"./vDgPRvwV.js";/* empty css        */import"./Cu4TKUNO.js";import"./CoHZZg7R.js";import"./B58jTiS9.js";import"./GbXQDMM-.js";import"./DCTLXrZ8.js";import"./DcfReECr.js";import"./DxoS9eIh.js";import"./9Bti1uB6.js";/* empty css        *//* empty css        */import"./BXMCmyua.js";import"./CEZIOyk0.js";import"./DAK40OfZ.js";import"./t2TIE93p.js";import"./DnE_5Yhr.js";import"./CBTxQWUq.js";import"./DcsXcc99.js";import"./D12SYOqE.js";/* empty css        */import"./BOaJ3oTb.js";import"./BE7GAo-z.js";import"./D3CLxE2I.js";import"./BDcuJK2h.js";import"./D7NF1x92.js";/* empty css        */import"./DP2rzg_V.js";import"./CcPlX2kz.js";const f={class:"layout-header h-full flex items-center"},u={class:"flex-1 min-w-0"},g={class:""},h=a({__name:"index",setup(x){const o=s();return(e,v)=>(l(),n("div",f,[t(m,{class:c("mr-[50px]"),logo:r(o).getWebsiteConfig.pc_logo,title:r(o).getWebsiteConfig.pc_name},null,8,["logo","title"]),i("div",u,[i("div",g,[_(e.$slots,"default",{},void 0,!0)])]),t(p,{class:"ml-auto"})]))}}),io=d(h,[["__scopeId","data-v-a4fd1a58"]]);export{io as default};
