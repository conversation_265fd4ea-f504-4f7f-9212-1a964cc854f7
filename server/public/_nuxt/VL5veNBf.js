import{_ as C}from"./BUEdeWPj.js";import{h as I,b as M,o as A,e as S}from"./DGzblORL.js";import{E as D}from"./5x-QNCbo.js";import{E as z,a as F}from"./CfUZcPRR.js";import"./DP2rzg_V.js";import"./B3glShCv.js";import"./l0sNRNKZ.js";/* empty css        *//* empty css        */import{l as G,b as O,M as p,N as P,Z as s,a0 as d,u as l,a3 as f,a7 as V,O as a,y as T,a5 as E,a4 as r}from"./uahP8ofS.js";const Z={class:"pt-[10px]"},j={class:"w-80"},H={class:"flex-1 min-w-0"},J={class:"w-full flex"},K={class:"flex-1 max-w-[320px]"},L={class:"flex-1 min-w-0"},Q={class:"w-full flex"},W={class:"flex-1 max-w-[320px]"},X={class:"w-80"},Y={class:"form-tips"},$={class:"w-80"},c={class:"w-80"},h={class:"w-80"},ee={class:"max-w-[320px]"},le={class:"w-80"},Ve=G({__name:"search-config",props:{modelValue:{}},emits:["update:modelValue"],setup(N,{emit:R}){const t=I(N,"modelValue",R),n=O({});return M(),(se,e)=>{const x=C,i=A,u=D,m=z,_=F,q=S;return p(),P("div",Z,[s(i,{label:"AI模型",prop:"model_id"},{default:d(()=>[a("div",j,[s(x,{class:"flex-1",id:l(t).model_id,"onUpdate:id":e[0]||(e[0]=o=>l(t).model_id=o),sub_id:l(t).model_sub_id,"onUpdate:sub_id":e[1]||(e[1]=o=>l(t).model_sub_id=o),configs:l(n),"onUpdate:configs":e[2]||(e[2]=o=>T(n)?n.value=o:null),"set-default":!1,disabled:""},null,8,["id","sub_id","configs"])])]),_:1}),s(i,{label:"相似度",required:"",prop:"search_similarity"},{default:d(()=>[a("div",H,[a("div",J,[a("div",K,[s(u,{min:0,max:1,step:.001,modelValue:l(t).search_similarity,"onUpdate:modelValue":e[3]||(e[3]=o=>l(t).search_similarity=o)},null,8,["modelValue"])])]),e[13]||(e[13]=a("div",{class:"form-tips"}," 输入0-1之间的数值，支持3位小数点；高相似度推荐设置0.8以上 ",-1))])]),_:1}),s(i,{label:"单次搜索数量",required:"",prop:"search_limits"},{default:d(()=>[a("div",L,[a("div",Q,[a("div",W,[s(u,{min:0,max:20,modelValue:l(t).search_limits,"onUpdate:modelValue":e[4]||(e[4]=o=>l(t).search_limits=o)},null,8,["modelValue"])])]),e[14]||(e[14]=a("div",{class:"form-tips"},"默认设置为5，请输入0-20之间的整数数值",-1))])]),_:1}),s(i,{label:"温度属性",required:"",prop:"temperature"},{default:d(()=>{var o,b,v,g,w,y,k,U;return[a("div",X,[s(u,{modelValue:l(t).temperature,"onUpdate:modelValue":e[5]||(e[5]=B=>l(t).temperature=B),min:(b=(o=l(n))==null?void 0:o.range)==null?void 0:b[0],max:(g=(v=l(n))==null?void 0:v.range)==null?void 0:g[1],step:.1},null,8,["modelValue","min","max"]),a("div",Y," 输入"+E((y=(w=l(n))==null?void 0:w.range)==null?void 0:y[0])+"-"+E((U=(k=l(n))==null?void 0:k.range)==null?void 0:U[1])+"之间的数值，支持1位小数点；",1)])]}),_:1}),s(i,{label:"空搜索回复"},{default:d(()=>[s(_,{modelValue:l(t).search_empty_type,"onUpdate:modelValue":e[6]||(e[6]=o=>l(t).search_empty_type=o)},{default:d(()=>[s(m,{label:1},{default:d(()=>e[15]||(e[15]=[r(" AI回复")])),_:1}),s(m,{label:2},{default:d(()=>e[16]||(e[16]=[r(" 自定义回复")])),_:1})]),_:1},8,["modelValue"])]),_:1}),l(t).search_empty_type===2?(p(),f(i,{key:0},{default:d(()=>[a("div",$,[s(q,{modelValue:l(t).search_empty_text,"onUpdate:modelValue":e[7]||(e[7]=o=>l(t).search_empty_text=o),placeholder:"请输入回复内容，当搜索匹配不上内容时，直接回复填写的内容",type:"textarea",autosize:{minRows:6,maxRows:6},maxlength:1e3,"show-word-limit":"",clearable:""},null,8,["modelValue"])])]),_:1})):V("",!0),s(i,{label:"上下文",required:"",prop:"context_num"},{default:d(()=>[a("div",c,[s(u,{modelValue:l(t).context_num,"onUpdate:modelValue":e[8]||(e[8]=o=>l(t).context_num=o),min:0,max:5,step:1},null,8,["modelValue"]),e[17]||(e[17]=a("div",{class:"form-tips"},"生成文本的最大长度，取值范围为0~5之间的整数",-1))])]),_:1}),s(i,{label:"文件解析",prop:"support_file"},{default:d(()=>[a("div",null,[s(_,{modelValue:l(t).support_file,"onUpdate:modelValue":e[9]||(e[9]=o=>l(t).support_file=o)},{default:d(()=>[s(m,{label:1},{default:d(()=>e[18]||(e[18]=[r(" 启用 ")])),_:1}),s(m,{label:0},{default:d(()=>e[19]||(e[19]=[r(" 关闭 ")])),_:1})]),_:1},8,["modelValue"]),e[20]||(e[20]=a("div",{class:"form-tips"},"开启后对话时支持上传文件，需消耗大量token，按需启用",-1))])]),_:1}),s(i,{label:"重排开关"},{default:d(()=>[a("div",null,[s(_,{modelValue:l(t).ranking_status,"onUpdate:modelValue":e[10]||(e[10]=o=>l(t).ranking_status=o)},{default:d(()=>[s(m,{label:0},{default:d(()=>e[21]||(e[21]=[r(" 关闭")])),_:1}),s(m,{label:1},{default:d(()=>e[22]||(e[22]=[r(" 启用")])),_:1})]),_:1},8,["modelValue"]),e[23]||(e[23]=a("div",{class:"form-tips"}," 开启后，则会对从数据库检索的内容进行重新排序(去最高分数据) ",-1))])]),_:1}),l(t).ranking_status===1?(p(),f(i,{key:1,label:"重排分数"},{default:d(()=>[a("div",h,[a("div",ee,[s(u,{min:0,max:1,step:.001,modelValue:l(t).ranking_score,"onUpdate:modelValue":e[11]||(e[11]=o=>l(t).ranking_score=o)},null,8,["modelValue"])]),e[24]||(e[24]=a("div",{class:"form-tips"}," 表示如果数据重排后，分数没有达到该值则会过滤掉。 ",-1))])]),_:1})):V("",!0),l(t).ranking_status===1?(p(),f(i,{key:2,label:"重排模型",prop:"vl_models"},{default:d(()=>[a("div",le,[s(x,{class:"flex-1",type:"rankingModels",id:l(t).ranking_model,"onUpdate:id":e[12]||(e[12]=o=>l(t).ranking_model=o),"set-default":!1},null,8,["id"])])]),_:1})):V("",!0)])}}});export{Ve as _};
