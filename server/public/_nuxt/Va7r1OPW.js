import{E as x,a as g}from"./BwHhKyiz.js";import{a as k}from"./D726nzJl.js";import{b as v}from"./eyo1vffA.js";import y from"./CXCoz5aE.js";import{l as w,r as B,j as U,b as V,M as o,N as i,O as D,Z as E,a0 as s,_ as S,aq as h,u as a,a1 as n,a3 as C}from"./Dp9aCaJ6.js";import{_ as N}from"./DlAUqK2U.js";import"./Cv6HhfEG.js";import"./BxHAV5ix.js";import"./BpYIl71c.js";import"./B58jTiS9.js";import"./GbXQDMM-.js";import"./DCTLXrZ8.js";import"./DcfReECr.js";import"./DxoS9eIh.js";import"./9Bti1uB6.js";import"./DH8JVbik.js";import"./B0taQFTv.js";import"./CJW7H0Ln.js";import"./CmJNjzrM.js";import"./DcsXcc99.js";import"./DzhE9Pcm.js";import"./CjE8iZ8I.js";import"./BHChID2I.js";import"./DyqYHYmh.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./DaBGfXF7.js";import"./D7NF1x92.js";/* empty css        */import"./D8hXTSyI.js";import"./BehqZJmW.js";import"./BpD5EEWf.js";import"./DHCaImEx.js";import"./qffJON56.js";/* empty css        *//* empty css        */import"./DP2rzg_V.js";/* empty css        *//* empty css        */const q={class:"p-main flex h-full flex-col"},R=w({__name:"setUp",emits:["update"],setup(T,{emit:l}){const c=l,d=k(),r=B({current:"baseSetting",lists:[{type:"baseSetting",name:"基础信息",component:U(y)}]}),m=V({}),p=async()=>{m.value=await v({id:d.query.id})},u=()=>{p(),c("update")};return p(),(j,e)=>{const _=g,f=x;return o(),i("div",q,[e[1]||(e[1]=D("div",{class:"text-xl font-medium"},"知识库设置",-1)),E(f,{class:"flex-1 min-h-0",modelValue:a(r).current,"onUpdate:modelValue":e[0]||(e[0]=t=>a(r).current=t)},{default:s(()=>[(o(!0),i(S,null,h(a(r).lists,(t,b)=>(o(),n(_,{label:t.name,name:t.type,key:b},{default:s(()=>[(o(),n(C(t.component),{data:a(m),onUpdate:u},null,40,["data"]))]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])])}}}),Bt=N(R,[["__scopeId","data-v-4da5484e"]]);export{Bt as default};
