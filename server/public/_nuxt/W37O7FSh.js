import{E as h}from"./DnE_5Yhr.js";import{E as x}from"./B58jTiS9.js";import{b as g}from"./D726nzJl.js";/* empty css        *//* empty css        *//* empty css        */import{l as k,m as w,M as o,a1 as y,a0 as _,V as E,O as B,Z as N,u as t,N as s,a7 as a,a4 as r}from"./Dp9aCaJ6.js";const S={class:"text-center"},V={key:0,class:"font-medium text-tx-primary mt-2"},C={key:1,class:"mt-2"},D={key:2,class:"mt-2"},$=k({__name:"manual",setup(M){const u=g(),e=w(()=>u.getManualKf);return(d,b)=>{const f=h,v=x;return o(),y(v,{placement:"left",width:"auto",trigger:"hover","show-arrow":!1,transition:"custom-popover"},{reference:_(()=>[E(d.$slots,"default")]),default:_(()=>{var c,n,m,i,p,l;return[B("div",S,[N(f,{class:"w-[150px] h-[150px]",src:t(e).qr_code},null,8,["src"]),((c=t(e).title)==null?void 0:c.status)==1?(o(),s("div",V,a((n=t(e).title)==null?void 0:n.value),1)):r("",!0),((m=t(e).service_time)==null?void 0:m.status)==1?(o(),s("div",C," 服务时间："+a((i=t(e).service_time)==null?void 0:i.value),1)):r("",!0),((p=t(e).phone)==null?void 0:p.status)==1?(o(),s("div",D," 联系电话："+a((l=t(e).phone)==null?void 0:l.value),1)):r("",!0)])]}),_:3})}}});export{$ as _};
