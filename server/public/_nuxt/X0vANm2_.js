import{_ as V}from"./BY8Moot3.js";import{E as O}from"./CVlyBbl9.js";import{E as A}from"./9Ov0gw5P.js";import{j as X,E as Z,cT as G,b as H,f as z}from"./C9xud4Fy.js";/* empty css        */import"./l0sNRNKZ.js";/* empty css        */import{l as T,j as F,b as L,r as J,M as t,N as a,Z as i,a0 as x,O as s,u as o,a4 as K,_ as R,aq as q,a3 as g,a7 as p,X as Q,a5 as h,a9 as B,n as W}from"./uahP8ofS.js";import{b as Y}from"./WcH50Ugn.js";import{d as ee}from"./Bu_nKEGp.js";import{u as N}from"./BFT5V6w8.js";import{E as te,a as se}from"./BNvvS5EC.js";/* empty css        *//* empty css        *//* empty css        */import{c as oe,a as ne}from"./DjwCd26w.js";import{P as ae}from"./CWneeePX.js";import{u as ce}from"./BfGcwPP1.js";import{_ as re}from"./DlAUqK2U.js";import{E as le}from"./DVv83yww.js";const ie={class:"share-popup"},de={class:"h-[100px]"},ue={class:"dialog-footer flex justify-center pb-2"},pe=T({__name:"music-share",emits:["success","close"],setup(I,{expose:d,emit:w}){const v=X(),_=w,m=F(),b=L([]),f=J({category_id:"",records_id:""}),y=async()=>{try{const l=await ne({type:G.MUSIC,share:1});l.unshift({name:"全部",id:""}),b.value=l}catch(l){console.log("获取音乐分类失败=>",l)}},{lockFn:C,isLock:$}=ce(async()=>{var l;await oe(f),await v.getUser(),(l=m.value)==null||l.close(),_("success",f.records_id)}),D=()=>{_("close")};return d({open:l=>{var u;y(),(u=m.value)==null||u.open(),f.records_id=l}}),(l,u)=>{const S=te,E=se,c=Z;return t(),a("div",ie,[i(ae,{ref_key:"popupRef",ref:m,title:"分享至广场",async:!0,width:"400px",center:!0,cancelButtonText:"",confirmButtonText:"",appendToBody:!1,onConfirm:o(C),onClose:D},{footer:x(()=>[s("div",ue,[i(c,{type:"primary",loading:o($),class:"!rounded-md",onClick:o(C)},{default:x(()=>u[1]||(u[1]=[K(" 分享至广场 ")])),_:1},8,["loading","onClick"])])]),default:x(()=>[s("div",de,[i(E,{size:"large",class:"w-[360px]",placeholder:"全部",modelValue:o(f).category_id,"onUpdate:modelValue":u[0]||(u[0]=n=>o(f).category_id=n),style:{"--el-fill-color-blank":"#F7F7FB"}},{default:x(()=>[(t(!0),a(R,null,q(o(b),n=>(t(),g(S,{key:n.id,label:n.name,value:n.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])]),_:1},8,["onConfirm"])])}}}),_e=re(pe,[["__scopeId","data-v-0138da76"]]),me=["onClick","id"],fe={key:0,class:"flex-1 flex flex-col justify-center items-center min-h-[75px]"},xe={class:"flex-1 flex items-center"},ye={class:"w-[75px] h-[75px] flex items-center justify-center flex-none relative"},he={key:1,class:"text-tx-secondary"},ve={key:2,class:"absolute inset-0 flex items-center justify-center text-white"},ge={key:3,class:"absolute inset-0 flex items-center justify-center text-white"},be={key:0,class:"ml-[20px]"},ke={key:0,class:"mt-[4px] text-tx-secondary"},we={class:"mt-[4px] text-tx-secondary"},Ce={key:1,class:"flex-1 flex justify-center"},$e={class:"flex flex-col items-end"},Se={class:"text-tx-secondary"},Ee={class:"flex items-center mt-[25px]"},Me=["onClick"],je=["onClick"],ze=["onClick"],ct=T({__name:"list",props:{musicList:{default:()=>[]}},emits:["update"],setup(I,{emit:d}){const w=H(),v=d,_=L(!1),m=F(null),b=L([]),{playing:f,currentId:y,setCurrentId:C,togglePlay:$,currentMusic:D,getMusic:U}=N(),l=async(c,n)=>{try{const r=await $request.get({url:c,responseType:"blob",baseURL:""},{isReturnDefaultResponse:!0,apiPrefix:""});console.log(r);const M=new Blob([r._data],{type:r.headers.get("Content-Type")}),k=window.URL.createObjectURL(M);Y(k,n)}catch{z.msgError("文件下载失败")}},u=async(c,n)=>{try{if(b.value.includes(c)||n){z.msgError("该音乐已经分享过了！");return}_.value=!0,await W(),m.value.open(c)}catch(r){console.log(r)}},S=async(c,n)=>{await z.confirm("确定删除？"),await ee({id:c}),await U(),y.value=-1,v("update")},E=c=>{if(c.id===y.value){$();return}c.status===2&&C(c.id)};return(c,n)=>{const r=V,M=O,k=A;return t(),a("div",null,[(t(!0),a(R,null,q(c.musicList,(e,P)=>(t(),a("div",{key:e.id},[s("div",{class:"flex bg-page mb-[20px] p-[20px] rounded-[12px] cursor-pointer",onClick:j=>E(e),id:`music-item-${e.id}`},[e.status===1?(t(),a("div",fe,[i(r,{class:"is-loading",name:"el-icon-Loading",size:25}),n[2]||(n[2]=s("div",{class:"mt-4"},"歌曲生成中，请稍等！",-1))])):(t(),a(R,{key:1},[s("div",xe,[s("div",ye,[e.image_url?(t(),g(M,{key:0,src:e.image_url,class:"w-full h-full rounded-[12px]"},null,8,["src"])):(t(),a("div",he,[i(r,{name:"local-icon-music1",size:45})])),o(y)==e.id&&o(f)?(t(),a("div",ve,[i(r,{name:"local-icon-pause1",size:20})])):p("",!0),o(y)==e.id&&!o(f)?(t(),a("div",ge,[i(r,{name:"local-icon-play",size:20})])):p("",!0)]),e.status===2?(t(),a("div",be,[s("div",{class:Q(["text-[16px] font-bold",{"!text-primary":o(y)===e.id}])},h(e.title),3),e.style_desc?(t(),a("div",ke,h(e.style_desc),1)):p("",!0),s("div",we,h(e.duration),1)])):p("",!0),e.status===3?(t(),a("div",Ce," 歌曲生成失败，请重试！ ")):p("",!0)]),s("div",$e,[s("div",Se,h(e.create_time),1),s("div",Ee,[e.audio_url?(t(),g(k,{key:0,effect:"dark",content:"下载",placement:"bottom"},{default:x(()=>[s("div",{class:"mr-6",onClick:B(j=>l(e.audio_url,e.title),["stop"])},[i(r,{class:"cursor-pointer dark:hover:bg-[rgba(255,255,255,0.05)] hover:bg-[rgba(0,0,0,0.05)] rounded-md p-1 box-content",name:"el-icon-Download",size:"18",color:"#556477"})],8,Me)]),_:2},1024)):p("",!0),e.audio_url&&o(w).getSquareConfig.music_award.is_open?(t(),g(k,{key:1,effect:"dark",content:"分享至广场",placement:"bottom"},{default:x(()=>[s("div",{class:"cursor-pointer dark:hover:bg-[rgba(255,255,255,0.05)] hover:bg-[rgba(0,0,0,0.05)] rounded-md mr-6 pb-[7px] p-1 box-content",onClick:B(j=>u(e.id,e.is_share),["stop"])},[i(r,{name:"local-icon-share",size:"17",color:"#556477"})],8,je)]),_:2},1024)):p("",!0),i(k,{effect:"dark",content:"删除",placement:"bottom"},{default:x(()=>[s("div",{onClick:B(j=>S(e.id,P),["stop"])},[i(r,{class:"cursor-pointer dark:hover:bg-[rgba(255,255,255,0.05)] hover:bg-[rgba(0,0,0,0.05)] rounded-md p-1 box-content",name:"el-icon-Delete",size:"18",color:"#556477"})],8,ze)]),_:2},1024)])])],64))],8,me)]))),128)),o(_)?(t(),g(_e,{key:0,ref_key:"shareRef",ref:m,onClose:n[0]||(n[0]=e=>_.value=!1),onSuccess:n[1]||(n[1]=e=>o(b).push(e))},null,512)):p("",!0)])}}}),Be={class:"w-[360px] h-full ml-[16px] flex justify-center"},Le={class:"h-full bg-page w-full rounded-[12px]"},Re={class:"p-[40px] flex flex-col items-center"},Te=["src"],Ie={class:"text-2xl font-bold mt-[30px]"},De={key:0,class:"text-tx-secondary mt-[5px]"},Ue={class:"whitespace-pre-wrap text-center mt-[20px] leading-7"},Ve={key:1,class:"h-full flex flex-col items-center justify-center"},Fe={class:"text-tx-secondary"},rt=T({__name:"display",setup(I){const{currentMusic:d}=N();return(w,v)=>{const _=le,m=V;return t(),a("div",Be,[s("div",Le,[o(d).lyric?(t(),g(_,{key:0},{default:x(()=>[s("div",Re,[s("img",{src:o(d).image_url,class:"w-[200px] h-[200px] rounded-[12px]"},null,8,Te),s("div",Ie,h(o(d).title),1),o(d).style_desc?(t(),a("div",De," 风格："+h(o(d).style_desc),1)):p("",!0),s("div",Ue,h(o(d).lyric),1)])]),_:1})):(t(),a("div",Ve,[s("div",Fe,[i(m,{size:45,name:"local-icon-music1"})]),v[0]||(v[0]=s("div",{class:"my-[10px] text-tx-secondary"}," 当前还没有选中音乐哦 ",-1))]))])])}}});export{rt as _,ct as a};
