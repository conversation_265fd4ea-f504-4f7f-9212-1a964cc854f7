import{cz as kt,l as At,a as yt,i as Dt,f as pt,bD as St,cA as Mt,bN as Ft,cB as Lt}from"./C3HqF-ve.js";import{u as jt}from"./Bb2-23m7.js";import{d as It}from"./CiYvFM4x.js";import{r as _t}from"./DuiQQh4g.js";import{F as bt}from"./DQM3eVRQ.js";import{x as xt,A as mt}from"./Dp9aCaJ6.js";function wt(){const A=kt(()=>({current:"avatar",isCollapsed:!1,tabs:[{label:"形象",icon:"local-icon-avatar",id:"avatar",component:"Avatar"},{label:"配音",icon:"local-icon-dub",id:"dub",component:"Dub"},{label:"音乐",icon:"local-icon-music",id:"music",component:"Music"},{label:"背景",icon:"local-icon-bg",id:"background",component:"Background"},{label:"文字",icon:"local-icon-texts",id:"text",component:"Text"},{label:"字幕",icon:"local-icon-subtitles",id:"captions",component:"Captions"},{label:"贴图",icon:"local-icon-prospect",id:"maps",component:"Maps"},{label:"前景",icon:"local-icon-prospect",id:"prospect",component:"Prospect"}]}),"$WwxySQaITe"),f=et=>A.value.tabs.some(st=>st.id===et);return{tabsState:A,changeTabs:et=>{if(!f(et))return;const{pauseAll:st}=jt(),lt=At(),c=yt();A.value.current=et,A.value.isCollapsed=!1,lt.replace({path:"",query:{...c.query,currentTab:et}}),st()},initTabs:()=>{const st=yt().query.currentTab;A.value.current=f(st)?st:"avatar"}}}var dt={};(function(A){/*! Fabric.js Copyright 2008-2015, Printio (Juriy Zaytsev, Maxim Chernyak) */var f=f||{version:"5.3.0"};if(A.fabric=f,typeof document<"u"&&typeof window<"u")document instanceof(typeof HTMLDocument<"u"?HTMLDocument:Document)?f.document=document:f.document=document.implementation.createHTMLDocument(""),f.window=window;else{var K=_t,at=new K.JSDOM(decodeURIComponent("%3C!DOCTYPE%20html%3E%3Chtml%3E%3Chead%3E%3C%2Fhead%3E%3Cbody%3E%3C%2Fbody%3E%3C%2Fhtml%3E"),{features:{FetchExternalResources:["img"]},resources:"usable"}).window;f.document=at.document,f.jsdomImplForWrapper=_t.implForWrapper,f.nodeCanvas=_t.Canvas,f.window=at,DOMParser=f.window.DOMParser}f.isTouchSupported="ontouchstart"in f.window||"ontouchstart"in f.document||f.window&&f.window.navigator&&f.window.navigator.maxTouchPoints>0,f.isLikelyNode=typeof Buffer<"u"&&typeof window>"u",f.SHARED_ATTRIBUTES=["display","transform","fill","fill-opacity","fill-rule","opacity","stroke","stroke-dasharray","stroke-linecap","stroke-dashoffset","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","id","paint-order","vector-effect","instantiated_by_use","clip-path"],f.DPI=96,f.reNum="(?:[-+]?(?:\\d+|\\d*\\.\\d+)(?:[eE][-+]?\\d+)?)",f.commaWsp="(?:\\s+,?\\s*|,\\s*)",f.rePathCommand=/([-+]?((\d+\.\d+)|((\d+)|(\.\d+)))(?:[eE][-+]?\d+)?)/ig,f.reNonWord=/[ \n\.,;!\?\-]/,f.fontPaths={},f.iMatrix=[1,0,0,1,0,0],f.svgNS="http://www.w3.org/2000/svg",f.perfLimitSizeTotal=2097152,f.maxCacheSideLimit=4096,f.minCacheSideLimit=256,f.charWidthsCache={},f.textureSize=2048,f.disableStyleCopyPaste=!1,f.enableGLFiltering=!0,f.devicePixelRatio=f.window.devicePixelRatio||f.window.webkitDevicePixelRatio||f.window.mozDevicePixelRatio||1,f.browserShadowBlurConstant=1,f.arcToSegmentsCache={},f.boundsOfCurveCache={},f.cachesBoundsOfCurve=!0,f.forceGLPutImageData=!1,f.initFilterBackend=function(){if(f.enableGLFiltering&&f.isWebglSupported&&f.isWebglSupported(f.textureSize))return console.log("max texture size: "+f.maxTextureSize),new f.WebglFilterBackend({tileSize:f.textureSize});if(f.Canvas2dFilterBackend)return new f.Canvas2dFilterBackend},typeof document<"u"&&typeof window<"u"&&(window.fabric=f),function(){function c(t,n){if(this.__eventListeners[t]){var o=this.__eventListeners[t];n?o[o.indexOf(n)]=!1:f.util.array.fill(o,!1)}}function s(t,n){if(this.__eventListeners||(this.__eventListeners={}),arguments.length===1)for(var o in t)this.on(o,t[o]);else this.__eventListeners[t]||(this.__eventListeners[t]=[]),this.__eventListeners[t].push(n);return this}function h(t,n){var o=(function(){n.apply(this,arguments),this.off(t,o)}).bind(this);this.on(t,o)}function a(t,n){if(arguments.length===1)for(var o in t)h.call(this,o,t[o]);else h.call(this,t,n);return this}function e(t,n){if(!this.__eventListeners)return this;if(arguments.length===0)for(t in this.__eventListeners)c.call(this,t);else if(arguments.length===1&&typeof arguments[0]=="object")for(var o in t)c.call(this,o,t[o]);else c.call(this,t,n);return this}function r(t,n){if(!this.__eventListeners)return this;var o=this.__eventListeners[t];if(!o)return this;for(var i=0,l=o.length;i<l;i++)o[i]&&o[i].call(this,n||{});return this.__eventListeners[t]=o.filter(function(u){return u!==!1}),this}f.Observable={fire:r,on:s,once:a,off:e}}(),f.Collection={_objects:[],add:function(){if(this._objects.push.apply(this._objects,arguments),this._onObjectAdded)for(var c=0,s=arguments.length;c<s;c++)this._onObjectAdded(arguments[c]);return this.renderOnAddRemove&&this.requestRenderAll(),this},insertAt:function(c,s,h){var a=this._objects;return h?a[s]=c:a.splice(s,0,c),this._onObjectAdded&&this._onObjectAdded(c),this.renderOnAddRemove&&this.requestRenderAll(),this},remove:function(){for(var c=this._objects,s,h=!1,a=0,e=arguments.length;a<e;a++)s=c.indexOf(arguments[a]),s!==-1&&(h=!0,c.splice(s,1),this._onObjectRemoved&&this._onObjectRemoved(arguments[a]));return this.renderOnAddRemove&&h&&this.requestRenderAll(),this},forEachObject:function(c,s){for(var h=this.getObjects(),a=0,e=h.length;a<e;a++)c.call(s,h[a],a,h);return this},getObjects:function(c){return typeof c>"u"?this._objects.concat():this._objects.filter(function(s){return s.type===c})},item:function(c){return this._objects[c]},isEmpty:function(){return this._objects.length===0},size:function(){return this._objects.length},contains:function(c,s){return this._objects.indexOf(c)>-1?!0:s?this._objects.some(function(h){return typeof h.contains=="function"&&h.contains(c,!0)}):!1},complexity:function(){return this._objects.reduce(function(c,s){return c+=s.complexity?s.complexity():0,c},0)}},f.CommonMethods={_setOptions:function(c){for(var s in c)this.set(s,c[s])},_initGradient:function(c,s){c&&c.colorStops&&!(c instanceof f.Gradient)&&this.set(s,new f.Gradient(c))},_initPattern:function(c,s,h){c&&c.source&&!(c instanceof f.Pattern)?this.set(s,new f.Pattern(c,h)):h&&h()},_setObject:function(c){for(var s in c)this._set(s,c[s])},set:function(c,s){return typeof c=="object"?this._setObject(c):this._set(c,s),this},_set:function(c,s){this[c]=s},toggle:function(c){var s=this.get(c);return typeof s=="boolean"&&this.set(c,!s),this},get:function(c){return this[c]}},function(c){var s=Math.sqrt,h=Math.atan2,a=Math.pow,e=Math.PI/180,r=Math.PI/2;f.util={cos:function(t){if(t===0)return 1;t<0&&(t=-t);var n=t/r;switch(n){case 1:case 3:return 0;case 2:return-1}return Math.cos(t)},sin:function(t){if(t===0)return 0;var n=t/r,o=1;switch(t<0&&(o=-1),n){case 1:return o;case 2:return 0;case 3:return-o}return Math.sin(t)},removeFromArray:function(t,n){var o=t.indexOf(n);return o!==-1&&t.splice(o,1),t},getRandomInt:function(t,n){return Math.floor(Math.random()*(n-t+1))+t},degreesToRadians:function(t){return t*e},radiansToDegrees:function(t){return t/e},rotatePoint:function(t,n,o){var i=new f.Point(t.x-n.x,t.y-n.y),l=f.util.rotateVector(i,o);return new f.Point(l.x,l.y).addEquals(n)},rotateVector:function(t,n){var o=f.util.sin(n),i=f.util.cos(n),l=t.x*i-t.y*o,u=t.x*o+t.y*i;return{x:l,y:u}},createVector:function(t,n){return new f.Point(n.x-t.x,n.y-t.y)},calcAngleBetweenVectors:function(t,n){return Math.acos((t.x*n.x+t.y*n.y)/(Math.hypot(t.x,t.y)*Math.hypot(n.x,n.y)))},getHatVector:function(t){return new f.Point(t.x,t.y).multiply(1/Math.hypot(t.x,t.y))},getBisector:function(t,n,o){var i=f.util.createVector(t,n),l=f.util.createVector(t,o),u=f.util.calcAngleBetweenVectors(i,l),d=f.util.calcAngleBetweenVectors(f.util.rotateVector(i,u),l),g=u*(d===0?1:-1)/2;return{vector:f.util.getHatVector(f.util.rotateVector(i,g)),angle:u}},projectStrokeOnPoints:function(t,n,o){var i=[],l=n.strokeWidth/2,u=n.strokeUniform?new f.Point(1/n.scaleX,1/n.scaleY):new f.Point(1,1),d=function(g){var v=l/Math.hypot(g.x,g.y);return new f.Point(g.x*v*u.x,g.y*v*u.y)};return t.length<=1||t.forEach(function(g,v){var m=new f.Point(g.x,g.y),y,w;v===0?(w=t[v+1],y=o?d(f.util.createVector(w,m)).addEquals(m):t[t.length-1]):v===t.length-1?(y=t[v-1],w=o?d(f.util.createVector(y,m)).addEquals(m):t[0]):(y=t[v-1],w=t[v+1]);var F=f.util.getBisector(m,y,w),Y=F.vector,z=F.angle,N,q;if(n.strokeLineJoin==="miter"&&(N=-l/Math.sin(z/2),q=new f.Point(Y.x*N*u.x,Y.y*N*u.y),Math.hypot(q.x,q.y)/l<=n.strokeMiterLimit)){i.push(m.add(q)),i.push(m.subtract(q));return}N=-l*Math.SQRT2,q=new f.Point(Y.x*N*u.x,Y.y*N*u.y),i.push(m.add(q)),i.push(m.subtract(q))}),i},transformPoint:function(t,n,o){return o?new f.Point(n[0]*t.x+n[2]*t.y,n[1]*t.x+n[3]*t.y):new f.Point(n[0]*t.x+n[2]*t.y+n[4],n[1]*t.x+n[3]*t.y+n[5])},makeBoundingBoxFromPoints:function(t,n){if(n)for(var o=0;o<t.length;o++)t[o]=f.util.transformPoint(t[o],n);var i=[t[0].x,t[1].x,t[2].x,t[3].x],l=f.util.array.min(i),u=f.util.array.max(i),d=u-l,g=[t[0].y,t[1].y,t[2].y,t[3].y],v=f.util.array.min(g),m=f.util.array.max(g),y=m-v;return{left:l,top:v,width:d,height:y}},invertTransform:function(t){var n=1/(t[0]*t[3]-t[1]*t[2]),o=[n*t[3],-n*t[1],-n*t[2],n*t[0]],i=f.util.transformPoint({x:t[4],y:t[5]},o,!0);return o[4]=-i.x,o[5]=-i.y,o},toFixed:function(t,n){return parseFloat(Number(t).toFixed(n))},parseUnit:function(t,n){var o=/\D{0,2}$/.exec(t),i=parseFloat(t);switch(n||(n=f.Text.DEFAULT_SVG_FONT_SIZE),o[0]){case"mm":return i*f.DPI/25.4;case"cm":return i*f.DPI/2.54;case"in":return i*f.DPI;case"pt":return i*f.DPI/72;case"pc":return i*f.DPI/72*12;case"em":return i*n;default:return i}},falseFunction:function(){return!1},getKlass:function(t,n){return t=f.util.string.camelize(t.charAt(0).toUpperCase()+t.slice(1)),f.util.resolveNamespace(n)[t]},getSvgAttributes:function(t){var n=["instantiated_by_use","style","id","class"];switch(t){case"linearGradient":n=n.concat(["x1","y1","x2","y2","gradientUnits","gradientTransform"]);break;case"radialGradient":n=n.concat(["gradientUnits","gradientTransform","cx","cy","r","fx","fy","fr"]);break;case"stop":n=n.concat(["offset","stop-color","stop-opacity"]);break}return n},resolveNamespace:function(t){if(!t)return f;var n=t.split("."),o=n.length,i,l=c;for(i=0;i<o;++i)l=l[n[i]];return l},loadImage:function(t,n,o,i){if(!t){n&&n.call(o,t);return}var l=f.util.createImage(),u=function(){n&&n.call(o,l,!1),l=l.onload=l.onerror=null};l.onload=u,l.onerror=function(){f.log("Error loading "+l.src),n&&n.call(o,null,!0),l=l.onload=l.onerror=null},t.indexOf("data")!==0&&i!==void 0&&i!==null&&(l.crossOrigin=i),t.substring(0,14)==="data:image/svg"&&(l.onload=null,f.util.loadImageInDom(l,u)),l.src=t},loadImageInDom:function(t,n){var o=f.document.createElement("div");o.style.width=o.style.height="1px",o.style.left=o.style.top="-100%",o.style.position="absolute",o.appendChild(t),f.document.querySelector("body").appendChild(o),t.onload=function(){n(),o.parentNode.removeChild(o),o=null}},enlivenObjects:function(t,n,o,i){t=t||[];var l=[],u=0,d=t.length;function g(){++u===d&&n&&n(l.filter(function(v){return v}))}if(!d){n&&n(l);return}t.forEach(function(v,m){if(!v||!v.type){g();return}var y=f.util.getKlass(v.type,o);y.fromObject(v,function(w,F){F||(l[m]=w),i&&i(v,w,F),g()})})},enlivenObjectEnlivables:function(t,n,o){var i=f.Object.ENLIVEN_PROPS.filter(function(l){return!!t[l]});f.util.enlivenObjects(i.map(function(l){return t[l]}),function(l){var u={};i.forEach(function(d,g){u[d]=l[g],n&&(n[d]=l[g])}),o&&o(u)})},enlivenPatterns:function(t,n){t=t||[];function o(){++l===u&&n&&n(i)}var i=[],l=0,u=t.length;if(!u){n&&n(i);return}t.forEach(function(d,g){d&&d.source?new f.Pattern(d,function(v){i[g]=v,o()}):(i[g]=d,o())})},groupSVGElements:function(t,n,o){var i;return t&&t.length===1?(typeof o<"u"&&(t[0].sourcePath=o),t[0]):(n&&(n.width&&n.height?n.centerPoint={x:n.width/2,y:n.height/2}:(delete n.width,delete n.height)),i=new f.Group(t,n),typeof o<"u"&&(i.sourcePath=o),i)},populateWithProperties:function(t,n,o){if(o&&Array.isArray(o))for(var i=0,l=o.length;i<l;i++)o[i]in t&&(n[o[i]]=t[o[i]])},createCanvasElement:function(){return f.document.createElement("canvas")},copyCanvasElement:function(t){var n=f.util.createCanvasElement();return n.width=t.width,n.height=t.height,n.getContext("2d").drawImage(t,0,0),n},toDataURL:function(t,n,o){return t.toDataURL("image/"+n,o)},createImage:function(){return f.document.createElement("img")},multiplyTransformMatrices:function(t,n,o){return[t[0]*n[0]+t[2]*n[1],t[1]*n[0]+t[3]*n[1],t[0]*n[2]+t[2]*n[3],t[1]*n[2]+t[3]*n[3],o?0:t[0]*n[4]+t[2]*n[5]+t[4],o?0:t[1]*n[4]+t[3]*n[5]+t[5]]},qrDecompose:function(t){var n=h(t[1],t[0]),o=a(t[0],2)+a(t[1],2),i=s(o),l=(t[0]*t[3]-t[2]*t[1])/i,u=h(t[0]*t[2]+t[1]*t[3],o);return{angle:n/e,scaleX:i,scaleY:l,skewX:u/e,skewY:0,translateX:t[4],translateY:t[5]}},calcRotateMatrix:function(t){if(!t.angle)return f.iMatrix.concat();var n=f.util.degreesToRadians(t.angle),o=f.util.cos(n),i=f.util.sin(n);return[o,i,-i,o,0,0]},calcDimensionsMatrix:function(t){var n=typeof t.scaleX>"u"?1:t.scaleX,o=typeof t.scaleY>"u"?1:t.scaleY,i=[t.flipX?-n:n,0,0,t.flipY?-o:o,0,0],l=f.util.multiplyTransformMatrices,u=f.util.degreesToRadians;return t.skewX&&(i=l(i,[1,0,Math.tan(u(t.skewX)),1],!0)),t.skewY&&(i=l(i,[1,Math.tan(u(t.skewY)),0,1],!0)),i},composeMatrix:function(t){var n=[1,0,0,1,t.translateX||0,t.translateY||0],o=f.util.multiplyTransformMatrices;return t.angle&&(n=o(n,f.util.calcRotateMatrix(t))),(t.scaleX!==1||t.scaleY!==1||t.skewX||t.skewY||t.flipX||t.flipY)&&(n=o(n,f.util.calcDimensionsMatrix(t))),n},resetObjectTransform:function(t){t.scaleX=1,t.scaleY=1,t.skewX=0,t.skewY=0,t.flipX=!1,t.flipY=!1,t.rotate(0)},saveObjectTransform:function(t){return{scaleX:t.scaleX,scaleY:t.scaleY,skewX:t.skewX,skewY:t.skewY,angle:t.angle,left:t.left,flipX:t.flipX,flipY:t.flipY,top:t.top}},isTransparent:function(t,n,o,i){i>0&&(n>i?n-=i:n=0,o>i?o-=i:o=0);var l=!0,u,d,g=t.getImageData(n,o,i*2||1,i*2||1),v=g.data.length;for(u=3;u<v&&(d=g.data[u],l=d<=0,l!==!1);u+=4);return g=null,l},parsePreserveAspectRatioAttribute:function(t){var n="meet",o="Mid",i="Mid",l=t.split(" "),u;return l&&l.length&&(n=l.pop(),n!=="meet"&&n!=="slice"?(u=n,n="meet"):l.length&&(u=l.pop())),o=u!=="none"?u.slice(1,4):"none",i=u!=="none"?u.slice(5,8):"none",{meetOrSlice:n,alignX:o,alignY:i}},clearFabricFontCache:function(t){t=(t||"").toLowerCase(),t?f.charWidthsCache[t]&&delete f.charWidthsCache[t]:f.charWidthsCache={}},limitDimsByArea:function(t,n){var o=Math.sqrt(n*t),i=Math.floor(n/o);return{x:Math.floor(o),y:i}},capValue:function(t,n,o){return Math.max(t,Math.min(n,o))},findScaleToFit:function(t,n){return Math.min(n.width/t.width,n.height/t.height)},findScaleToCover:function(t,n){return Math.max(n.width/t.width,n.height/t.height)},matrixToSVG:function(t){return"matrix("+t.map(function(n){return f.util.toFixed(n,f.Object.NUM_FRACTION_DIGITS)}).join(" ")+")"},removeTransformFromObject:function(t,n){var o=f.util.invertTransform(n),i=f.util.multiplyTransformMatrices(o,t.calcOwnMatrix());f.util.applyTransformToObject(t,i)},addTransformToObject:function(t,n){f.util.applyTransformToObject(t,f.util.multiplyTransformMatrices(n,t.calcOwnMatrix()))},applyTransformToObject:function(t,n){var o=f.util.qrDecompose(n),i=new f.Point(o.translateX,o.translateY);t.flipX=!1,t.flipY=!1,t.set("scaleX",o.scaleX),t.set("scaleY",o.scaleY),t.skewX=o.skewX,t.skewY=o.skewY,t.angle=o.angle,t.setPositionByOrigin(i,"center","center")},sizeAfterTransform:function(t,n,o){var i=t/2,l=n/2,u=[{x:-i,y:-l},{x:i,y:-l},{x:-i,y:l},{x:i,y:l}],d=f.util.calcDimensionsMatrix(o),g=f.util.makeBoundingBoxFromPoints(u,d);return{x:g.width,y:g.height}},mergeClipPaths:function(t,n){var o=t,i=n;o.inverted&&!i.inverted&&(o=n,i=t),f.util.applyTransformToObject(i,f.util.multiplyTransformMatrices(f.util.invertTransform(o.calcTransformMatrix()),i.calcTransformMatrix()));var l=o.inverted&&i.inverted;return l&&(o.inverted=i.inverted=!1),new f.Group([o],{clipPath:i,inverted:l})},hasStyleChanged:function(t,n,o){return o=o||!1,t.fill!==n.fill||t.stroke!==n.stroke||t.strokeWidth!==n.strokeWidth||t.fontSize!==n.fontSize||t.fontFamily!==n.fontFamily||t.fontWeight!==n.fontWeight||t.fontStyle!==n.fontStyle||t.textBackgroundColor!==n.textBackgroundColor||t.deltaY!==n.deltaY||o&&(t.overline!==n.overline||t.underline!==n.underline||t.linethrough!==n.linethrough)},stylesToArray:function(o,n){for(var o=f.util.object.clone(o,!0),i=n.split(`
`),l=-1,u={},d=[],g=0;g<i.length;g++){if(!o[g]){l+=i[g].length;continue}for(var v=0;v<i[g].length;v++){l++;var m=o[g][v];if(m&&Object.keys(m).length>0){var y=f.util.hasStyleChanged(u,m,!0);y?d.push({start:l,end:l+1,style:m}):d[d.length-1].end++}u=m||{}}}return d},stylesFromArray:function(t,n){if(!Array.isArray(t))return t;for(var o=n.split(`
`),i=-1,l=0,u={},d=0;d<o.length;d++)for(var g=0;g<o[d].length;g++)i++,t[l]&&t[l].start<=i&&i<t[l].end&&(u[d]=u[d]||{},u[d][g]=Object.assign({},t[l].style),i===t[l].end-1&&l++);return u}}}(A),function(){var c=Array.prototype.join,s={m:2,l:2,h:1,v:1,c:6,s:4,q:4,t:2,a:7},h={m:"l",M:"L"};function a(p,b,x,S,E,C,_,T,P,L,k){var B=f.util.cos(p),W=f.util.sin(p),V=f.util.cos(b),D=f.util.sin(b),O=x*E*V-S*C*D+_,I=S*E*V+x*C*D+T,R=L+P*(-x*E*W-S*C*B),M=k+P*(-S*E*W+x*C*B),X=O+P*(x*E*D+S*C*V),G=I+P*(S*E*D-x*C*V);return["C",R,M,X,G,O,I]}function e(p,b,x,S,E,C,_){var T=Math.PI,P=_*T/180,L=f.util.sin(P),k=f.util.cos(P),B=0,W=0;x=Math.abs(x),S=Math.abs(S);var V=-k*p*.5-L*b*.5,D=-k*b*.5+L*p*.5,O=x*x,I=S*S,R=D*D,M=V*V,X=O*I-O*R-I*M,G=0;if(X<0){var it=Math.sqrt(1-X/(O*I));x*=it,S*=it}else G=(E===C?-1:1)*Math.sqrt(X/(O*R+I*M));var $=G*x*D/S,j=-G*S*V/x,tt=k*$-L*j+p*.5,rt=L*$+k*j+b*.5,nt=r(1,0,(V-$)/x,(D-j)/S),ht=r((V-$)/x,(D-j)/S,(-V-$)/x,(-D-j)/S);C===0&&ht>0?ht-=2*T:C===1&&ht<0&&(ht+=2*T);for(var ot=Math.ceil(Math.abs(ht/T*2)),ut=[],ct=ht/ot,gt=8/3*Math.sin(ct/4)*Math.sin(ct/4)/Math.sin(ct/2),vt=nt+ct,ft=0;ft<ot;ft++)ut[ft]=a(nt,vt,k,L,x,S,tt,rt,gt,B,W),B=ut[ft][5],W=ut[ft][6],nt=vt,vt+=ct;return ut}function r(p,b,x,S){var E=Math.atan2(b,p),C=Math.atan2(S,x);return C>=E?C-E:2*Math.PI-(E-C)}function t(p,b,x,S,E,C,_,T){var P;if(f.cachesBoundsOfCurve&&(P=c.call(arguments),f.boundsOfCurveCache[P]))return f.boundsOfCurveCache[P];var L=Math.sqrt,k=Math.min,B=Math.max,W=Math.abs,V=[],D=[[],[]],O,I,R,M,X,G,it,$;I=6*p-12*x+6*E,O=-3*p+9*x-9*E+3*_,R=3*x-3*p;for(var j=0;j<2;++j){if(j>0&&(I=6*b-12*S+6*C,O=-3*b+9*S-9*C+3*T,R=3*S-3*b),W(O)<1e-12){if(W(I)<1e-12)continue;M=-R/I,0<M&&M<1&&V.push(M);continue}it=I*I-4*R*O,!(it<0)&&($=L(it),X=(-I+$)/(2*O),0<X&&X<1&&V.push(X),G=(-I-$)/(2*O),0<G&&G<1&&V.push(G))}for(var tt,rt,nt=V.length,ht=nt,ot;nt--;)M=V[nt],ot=1-M,tt=ot*ot*ot*p+3*ot*ot*M*x+3*ot*M*M*E+M*M*M*_,D[0][nt]=tt,rt=ot*ot*ot*b+3*ot*ot*M*S+3*ot*M*M*C+M*M*M*T,D[1][nt]=rt;D[0][ht]=p,D[1][ht]=b,D[0][ht+1]=_,D[1][ht+1]=T;var ut=[{x:k.apply(null,D[0]),y:k.apply(null,D[1])},{x:B.apply(null,D[0]),y:B.apply(null,D[1])}];return f.cachesBoundsOfCurve&&(f.boundsOfCurveCache[P]=ut),ut}function n(p,b,x){for(var S=x[1],E=x[2],C=x[3],_=x[4],T=x[5],P=x[6],L=x[7],k=e(P-p,L-b,S,E,_,T,C),B=0,W=k.length;B<W;B++)k[B][1]+=p,k[B][2]+=b,k[B][3]+=p,k[B][4]+=b,k[B][5]+=p,k[B][6]+=b;return k}function o(p){var b=0,x=0,S=p.length,E=0,C=0,_,T,P,L=[],k,B,W;for(T=0;T<S;++T){switch(P=!1,_=p[T].slice(0),_[0]){case"l":_[0]="L",_[1]+=b,_[2]+=x;case"L":b=_[1],x=_[2];break;case"h":_[1]+=b;case"H":_[0]="L",_[2]=x,b=_[1];break;case"v":_[1]+=x;case"V":_[0]="L",x=_[1],_[1]=b,_[2]=x;break;case"m":_[0]="M",_[1]+=b,_[2]+=x;case"M":b=_[1],x=_[2],E=_[1],C=_[2];break;case"c":_[0]="C",_[1]+=b,_[2]+=x,_[3]+=b,_[4]+=x,_[5]+=b,_[6]+=x;case"C":B=_[3],W=_[4],b=_[5],x=_[6];break;case"s":_[0]="S",_[1]+=b,_[2]+=x,_[3]+=b,_[4]+=x;case"S":k==="C"?(B=2*b-B,W=2*x-W):(B=b,W=x),b=_[3],x=_[4],_[0]="C",_[5]=_[3],_[6]=_[4],_[3]=_[1],_[4]=_[2],_[1]=B,_[2]=W,B=_[3],W=_[4];break;case"q":_[0]="Q",_[1]+=b,_[2]+=x,_[3]+=b,_[4]+=x;case"Q":B=_[1],W=_[2],b=_[3],x=_[4];break;case"t":_[0]="T",_[1]+=b,_[2]+=x;case"T":k==="Q"?(B=2*b-B,W=2*x-W):(B=b,W=x),_[0]="Q",b=_[1],x=_[2],_[1]=B,_[2]=W,_[3]=b,_[4]=x;break;case"a":_[0]="A",_[6]+=b,_[7]+=x;case"A":P=!0,L=L.concat(n(b,x,_)),b=_[6],x=_[7];break;case"z":case"Z":b=E,x=C;break}P||L.push(_),k=_[0]}return L}function i(p,b,x,S){return Math.sqrt((x-p)*(x-p)+(S-b)*(S-b))}function l(p){return p*p*p}function u(p){return 3*p*p*(1-p)}function d(p){return 3*p*(1-p)*(1-p)}function g(p){return(1-p)*(1-p)*(1-p)}function v(p,b,x,S,E,C,_,T){return function(P){var L=l(P),k=u(P),B=d(P),W=g(P);return{x:_*L+E*k+x*B+p*W,y:T*L+C*k+S*B+b*W}}}function m(p,b,x,S,E,C,_,T){return function(P){var L=1-P,k=3*L*L*(x-p)+6*L*P*(E-x)+3*P*P*(_-E),B=3*L*L*(S-b)+6*L*P*(C-S)+3*P*P*(T-C);return Math.atan2(B,k)}}function y(p){return p*p}function w(p){return 2*p*(1-p)}function F(p){return(1-p)*(1-p)}function Y(p,b,x,S,E,C){return function(_){var T=y(_),P=w(_),L=F(_);return{x:E*T+x*P+p*L,y:C*T+S*P+b*L}}}function z(p,b,x,S,E,C){return function(_){var T=1-_,P=2*T*(x-p)+2*_*(E-x),L=2*T*(S-b)+2*_*(C-S);return Math.atan2(L,P)}}function N(p,b,x){var S={x:b,y:x},E,C=0,_;for(_=1;_<=100;_+=1)E=p(_/100),C+=i(S.x,S.y,E.x,E.y),S=E;return C}function q(p,b){for(var x=0,S=0,E=p.iterator,C={x:p.x,y:p.y},_,T,P=.01,L=p.angleFinder,k;S<b&&P>1e-4;)_=E(x),k=x,T=i(C.x,C.y,_.x,_.y),T+S>b?(x-=P,P/=2):(C=_,x+=P,S+=T);return _.angle=L(k),_}function H(p){for(var b=0,x=p.length,S,E=0,C=0,_=0,T=0,P=[],L,k,B,W=0;W<x;W++){switch(S=p[W],k={x:E,y:C,command:S[0]},S[0]){case"M":k.length=0,_=E=S[1],T=C=S[2];break;case"L":k.length=i(E,C,S[1],S[2]),E=S[1],C=S[2];break;case"C":L=v(E,C,S[1],S[2],S[3],S[4],S[5],S[6]),B=m(E,C,S[1],S[2],S[3],S[4],S[5],S[6]),k.iterator=L,k.angleFinder=B,k.length=N(L,E,C),E=S[5],C=S[6];break;case"Q":L=Y(E,C,S[1],S[2],S[3],S[4]),B=z(E,C,S[1],S[2],S[3],S[4]),k.iterator=L,k.angleFinder=B,k.length=N(L,E,C),E=S[3],C=S[4];break;case"Z":case"z":k.destX=_,k.destY=T,k.length=i(E,C,_,T),E=_,C=T;break}b+=k.length,P.push(k)}return P.push({length:b,x:E,y:C}),P}function U(p,b,x){x||(x=H(p));for(var S=0;b-x[S].length>0&&S<x.length-2;)b-=x[S].length,S++;var E=x[S],C=b/E.length,_=E.command,T=p[S],P;switch(_){case"M":return{x:E.x,y:E.y,angle:0};case"Z":case"z":return P=new f.Point(E.x,E.y).lerp(new f.Point(E.destX,E.destY),C),P.angle=Math.atan2(E.destY-E.y,E.destX-E.x),P;case"L":return P=new f.Point(E.x,E.y).lerp(new f.Point(T[1],T[2]),C),P.angle=Math.atan2(T[2]-E.y,T[1]-E.x),P;case"C":return q(E,b);case"Q":return q(E,b)}}function J(p){var b=[],x=[],S,E,C=f.rePathCommand,_="[-+]?(?:\\d*\\.\\d+|\\d+\\.?)(?:[eE][-+]?\\d+)?\\s*",T="("+_+")"+f.commaWsp,P="([01])"+f.commaWsp+"?",L=T+"?"+T+"?"+T+P+P+T+"?("+_+")",k=new RegExp(L,"g"),B,W,V;if(!p||!p.match)return b;V=p.match(/[mzlhvcsqta][^mzlhvcsqta]*/gi);for(var D=0,O,I=V.length;D<I;D++){S=V[D],W=S.slice(1).trim(),x.length=0;var R=S.charAt(0);if(O=[R],R.toLowerCase()==="a")for(var M;M=k.exec(W);)for(var X=1;X<M.length;X++)x.push(M[X]);else for(;B=C.exec(W);)x.push(B[0]);for(var X=0,G=x.length;X<G;X++)E=parseFloat(x[X]),isNaN(E)||O.push(E);var it=s[R.toLowerCase()],$=h[R]||R;if(O.length-1>it)for(var j=1,tt=O.length;j<tt;j+=it)b.push([R].concat(O.slice(j,j+it))),R=$;else b.push(O)}return b}function Q(p,b){var x=[],S,E=new f.Point(p[0].x,p[0].y),C=new f.Point(p[1].x,p[1].y),_=p.length,T=1,P=0,L=_>2;for(b=b||0,L&&(T=p[2].x<C.x?-1:p[2].x===C.x?0:1,P=p[2].y<C.y?-1:p[2].y===C.y?0:1),x.push(["M",E.x-T*b,E.y-P*b]),S=1;S<_;S++){if(!E.eq(C)){var k=E.midPointFrom(C);x.push(["Q",E.x,E.y,k.x,k.y])}E=p[S],S+1<p.length&&(C=p[S+1])}return L&&(T=E.x>p[S-2].x?1:E.x===p[S-2].x?0:-1,P=E.y>p[S-2].y?1:E.y===p[S-2].y?0:-1),x.push(["L",E.x+T*b,E.y+P*b]),x}function Z(p,b,x){return x&&(b=f.util.multiplyTransformMatrices(b,[1,0,0,1,-x.x,-x.y])),p.map(function(S){for(var E=S.slice(0),C={},_=1;_<S.length-1;_+=2)C.x=S[_],C.y=S[_+1],C=f.util.transformPoint(C,b),E[_]=C.x,E[_+1]=C.y;return E})}f.util.joinPath=function(p){return p.map(function(b){return b.join(" ")}).join(" ")},f.util.parsePath=J,f.util.makePathSimpler=o,f.util.getSmoothPathFromPoints=Q,f.util.getPathSegmentsInfo=H,f.util.getBoundsOfCurve=t,f.util.getPointOnPath=U,f.util.transformPath=Z}(),function(){var c=Array.prototype.slice;function s(t,n){for(var o=c.call(arguments,2),i=[],l=0,u=t.length;l<u;l++)i[l]=o.length?t[l][n].apply(t[l],o):t[l][n].call(t[l]);return i}function h(t,n){return r(t,n,function(o,i){return o>=i})}function a(t,n){return r(t,n,function(o,i){return o<i})}function e(t,n){for(var o=t.length;o--;)t[o]=n;return t}function r(t,n,o){if(!(!t||t.length===0)){var i=t.length-1,l=n?t[i][n]:t[i];if(n)for(;i--;)o(t[i][n],l)&&(l=t[i][n]);else for(;i--;)o(t[i],l)&&(l=t[i]);return l}}f.util.array={fill:e,invoke:s,min:a,max:h}}(),function(){function c(h,a,e){if(e)if(!f.isLikelyNode&&a instanceof Element)h=a;else if(a instanceof Array){h=[];for(var r=0,t=a.length;r<t;r++)h[r]=c({},a[r],e)}else if(a&&typeof a=="object")for(var n in a)n==="canvas"||n==="group"?h[n]=null:a.hasOwnProperty(n)&&(h[n]=c({},a[n],e));else h=a;else for(var n in a)h[n]=a[n];return h}function s(h,a){return c({},h,a)}f.util.object={extend:c,clone:s},f.util.object.extend(f.util,f.Observable)}(),function(){function c(r){return r.replace(/-+(.)?/g,function(t,n){return n?n.toUpperCase():""})}function s(r,t){return r.charAt(0).toUpperCase()+(t?r.slice(1):r.slice(1).toLowerCase())}function h(r){return r.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&apos;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function a(r){var t=0,n,o=[];for(t=0,n;t<r.length;t++)(n=e(r,t))!==!1&&o.push(n);return o}function e(r,t){var n=r.charCodeAt(t);if(isNaN(n))return"";if(n<55296||n>57343)return r.charAt(t);if(55296<=n&&n<=56319){if(r.length<=t+1)throw"High surrogate without following low surrogate";var o=r.charCodeAt(t+1);if(56320>o||o>57343)throw"High surrogate without following low surrogate";return r.charAt(t)+r.charAt(t+1)}if(t===0)throw"Low surrogate without preceding high surrogate";var i=r.charCodeAt(t-1);if(55296>i||i>56319)throw"Low surrogate without preceding high surrogate";return!1}f.util.string={camelize:c,capitalize:s,escapeXml:h,graphemeSplit:a}}(),function(){var c=Array.prototype.slice,s=function(){},h=function(){for(var n in{toString:1})if(n==="toString")return!1;return!0}(),a=function(n,o,i){for(var l in o)l in n.prototype&&typeof n.prototype[l]=="function"&&(o[l]+"").indexOf("callSuper")>-1?n.prototype[l]=function(u){return function(){var d=this.constructor.superclass;this.constructor.superclass=i;var g=o[u].apply(this,arguments);if(this.constructor.superclass=d,u!=="initialize")return g}}(l):n.prototype[l]=o[l],h&&(o.toString!==Object.prototype.toString&&(n.prototype.toString=o.toString),o.valueOf!==Object.prototype.valueOf&&(n.prototype.valueOf=o.valueOf))};function e(){}function r(n){for(var o=null,i=this;i.constructor.superclass;){var l=i.constructor.superclass.prototype[n];if(i[n]!==l){o=l;break}i=i.constructor.superclass.prototype}return o?arguments.length>1?o.apply(this,c.call(arguments,1)):o.call(this):console.log("tried to callSuper "+n+", method not found in prototype chain",this)}function t(){var n=null,o=c.call(arguments,0);typeof o[0]=="function"&&(n=o.shift());function i(){this.initialize.apply(this,arguments)}i.superclass=n,i.subclasses=[],n&&(e.prototype=n.prototype,i.prototype=new e,n.subclasses.push(i));for(var l=0,u=o.length;l<u;l++)a(i,o[l],n);return i.prototype.initialize||(i.prototype.initialize=s),i.prototype.constructor=i,i.prototype.callSuper=r,i}f.util.createClass=t}(),function(){var c=!!f.document.createElement("div").attachEvent,s=["touchstart","touchmove","touchend"];f.util.addListener=function(a,e,r,t){a&&a.addEventListener(e,r,c?!1:t)},f.util.removeListener=function(a,e,r,t){a&&a.removeEventListener(e,r,c?!1:t)};function h(a){var e=a.changedTouches;return e&&e[0]?e[0]:a}f.util.getPointer=function(a){var e=a.target,r=f.util.getScrollLeftTop(e),t=h(a);return{x:t.clientX+r.left,y:t.clientY+r.top}},f.util.isTouchEvent=function(a){return s.indexOf(a.type)>-1||a.pointerType==="touch"}}(),function(){function c(t,n){var o=t.style;if(!o)return t;if(typeof n=="string")return t.style.cssText+=";"+n,n.indexOf("opacity")>-1?r(t,n.match(/opacity:\s*(\d?\.?\d*)/)[1]):t;for(var i in n)if(i==="opacity")r(t,n[i]);else{var l=i==="float"||i==="cssFloat"?typeof o.styleFloat>"u"?"cssFloat":"styleFloat":i;o.setProperty(l,n[i])}return t}var s=f.document.createElement("div"),h=typeof s.style.opacity=="string",a=typeof s.style.filter=="string",e=/alpha\s*\(\s*opacity\s*=\s*([^\)]+)\)/,r=function(t){return t};h?r=function(t,n){return t.style.opacity=n,t}:a&&(r=function(t,n){var o=t.style;return t.currentStyle&&!t.currentStyle.hasLayout&&(o.zoom=1),e.test(o.filter)?(n=n>=.9999?"":"alpha(opacity="+n*100+")",o.filter=o.filter.replace(e,n)):o.filter+=" alpha(opacity="+n*100+")",t}),f.util.setStyle=c}(),function(){var c=Array.prototype.slice;function s(g){return typeof g=="string"?f.document.getElementById(g):g}var h,a=function(g){return c.call(g,0)};try{h=a(f.document.childNodes)instanceof Array}catch{}h||(a=function(g){for(var v=new Array(g.length),m=g.length;m--;)v[m]=g[m];return v});function e(g,v){var m=f.document.createElement(g);for(var y in v)y==="class"?m.className=v[y]:y==="for"?m.htmlFor=v[y]:m.setAttribute(y,v[y]);return m}function r(g,v){g&&(" "+g.className+" ").indexOf(" "+v+" ")===-1&&(g.className+=(g.className?" ":"")+v)}function t(g,v,m){return typeof v=="string"&&(v=e(v,m)),g.parentNode&&g.parentNode.replaceChild(v,g),v.appendChild(g),v}function n(g){for(var v=0,m=0,y=f.document.documentElement,w=f.document.body||{scrollLeft:0,scrollTop:0};g&&(g.parentNode||g.host)&&(g=g.parentNode||g.host,g===f.document?(v=w.scrollLeft||y.scrollLeft||0,m=w.scrollTop||y.scrollTop||0):(v+=g.scrollLeft||0,m+=g.scrollTop||0),!(g.nodeType===1&&g.style.position==="fixed")););return{left:v,top:m}}function o(g){var v,m=g&&g.ownerDocument,y={left:0,top:0},w={left:0,top:0},F,Y={borderLeftWidth:"left",borderTopWidth:"top",paddingLeft:"left",paddingTop:"top"};if(!m)return w;for(var z in Y)w[Y[z]]+=parseInt(i(g,z),10)||0;return v=m.documentElement,typeof g.getBoundingClientRect<"u"&&(y=g.getBoundingClientRect()),F=n(g),{left:y.left+F.left-(v.clientLeft||0)+w.left,top:y.top+F.top-(v.clientTop||0)+w.top}}var i;f.document.defaultView&&f.document.defaultView.getComputedStyle?i=function(g,v){var m=f.document.defaultView.getComputedStyle(g,null);return m?m[v]:void 0}:i=function(g,v){var m=g.style[v];return!m&&g.currentStyle&&(m=g.currentStyle[v]),m},function(){var g=f.document.documentElement.style,v="userSelect"in g?"userSelect":"MozUserSelect"in g?"MozUserSelect":"WebkitUserSelect"in g?"WebkitUserSelect":"KhtmlUserSelect"in g?"KhtmlUserSelect":"";function m(w){return typeof w.onselectstart<"u"&&(w.onselectstart=f.util.falseFunction),v?w.style[v]="none":typeof w.unselectable=="string"&&(w.unselectable="on"),w}function y(w){return typeof w.onselectstart<"u"&&(w.onselectstart=null),v?w.style[v]="":typeof w.unselectable=="string"&&(w.unselectable=""),w}f.util.makeElementUnselectable=m,f.util.makeElementSelectable=y}();function l(g){var v=f.jsdomImplForWrapper(g);return v._canvas||v._image}function u(g){if(f.isLikelyNode){var v=f.jsdomImplForWrapper(g);v&&(v._image=null,v._canvas=null,v._currentSrc=null,v._attributes=null,v._classList=null)}}function d(g,v){g.imageSmoothingEnabled=g.imageSmoothingEnabled||g.webkitImageSmoothingEnabled||g.mozImageSmoothingEnabled||g.msImageSmoothingEnabled||g.oImageSmoothingEnabled,g.imageSmoothingEnabled=v}f.util.setImageSmoothing=d,f.util.getById=s,f.util.toArray=a,f.util.addClass=r,f.util.makeElement=e,f.util.wrapElement=t,f.util.getScrollLeftTop=n,f.util.getElementOffset=o,f.util.getNodeCanvas=l,f.util.cleanUpJsdomNode=u}(),function(){function c(a,e){return a+(/\?/.test(a)?"&":"?")+e}function s(){}function h(a,e){e||(e={});var r=e.method?e.method.toUpperCase():"GET",t=e.onComplete||function(){},n=new f.window.XMLHttpRequest,o=e.body||e.parameters;return n.onreadystatechange=function(){n.readyState===4&&(t(n),n.onreadystatechange=s)},r==="GET"&&(o=null,typeof e.parameters=="string"&&(a=c(a,e.parameters))),n.open(r,a,!0),(r==="POST"||r==="PUT")&&n.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),n.send(o),n}f.util.request=h}(),f.log=console.log,f.warn=console.warn,function(){var c=f.util.object.extend,s=f.util.object.clone,h=[];f.util.object.extend(h,{cancelAll:function(){var l=this.splice(0);return l.forEach(function(u){u.cancel()}),l},cancelByCanvas:function(l){if(!l)return[];var u=this.filter(function(d){return typeof d.target=="object"&&d.target.canvas===l});return u.forEach(function(d){d.cancel()}),u},cancelByTarget:function(l){var u=this.findAnimationsByTarget(l);return u.forEach(function(d){d.cancel()}),u},findAnimationIndex:function(l){return this.indexOf(this.findAnimation(l))},findAnimation:function(l){return this.find(function(u){return u.cancel===l})},findAnimationsByTarget:function(l){return l?this.filter(function(u){return u.target===l}):[]}});function a(){return!1}function e(l,u,d,g){return-d*Math.cos(l/g*(Math.PI/2))+d+u}function r(l){l||(l={});var u=!1,d,g=function(){var v=f.runningAnimations.indexOf(d);return v>-1&&f.runningAnimations.splice(v,1)[0]};return d=c(s(l),{cancel:function(){return u=!0,g()},currentValue:"startValue"in l?l.startValue:0,completionRate:0,durationRate:0}),f.runningAnimations.push(d),o(function(v){var m=v||+new Date,y=l.duration||500,w=m+y,F,Y=l.onChange||a,z=l.abort||a,N=l.onComplete||a,q=l.easing||e,H="startValue"in l?l.startValue.length>0:!1,U="startValue"in l?l.startValue:0,J="endValue"in l?l.endValue:100,Q=l.byValue||(H?U.map(function(Z,p){return J[p]-U[p]}):J-U);l.onStart&&l.onStart(),function Z(p){F=p||+new Date;var b=F>w?y:F-m,x=b/y,S=H?U.map(function(C,_){return q(b,U[_],Q[_],y)}):q(b,U,Q,y),E=Math.abs(H?(S[0]-U[0])/Q[0]:(S-U)/Q);if(d.currentValue=H?S.slice():S,d.completionRate=E,d.durationRate=x,!u){if(z(S,E,x)){g();return}if(F>w){d.currentValue=H?J.slice():J,d.completionRate=1,d.durationRate=1,Y(H?J.slice():J,1,1),N(J,1,1),g();return}else Y(S,E,x),o(Z)}}(m)}),d.cancel}var t=f.window.requestAnimationFrame||f.window.webkitRequestAnimationFrame||f.window.mozRequestAnimationFrame||f.window.oRequestAnimationFrame||f.window.msRequestAnimationFrame||function(l){return f.window.setTimeout(l,1e3/60)},n=f.window.cancelAnimationFrame||f.window.clearTimeout;function o(){return t.apply(f.window,arguments)}function i(){return n.apply(f.window,arguments)}f.util.animate=r,f.util.requestAnimFrame=o,f.util.cancelAnimFrame=i,f.runningAnimations=h}(),function(){function c(h,a,e){var r="rgba("+parseInt(h[0]+e*(a[0]-h[0]),10)+","+parseInt(h[1]+e*(a[1]-h[1]),10)+","+parseInt(h[2]+e*(a[2]-h[2]),10);return r+=","+(h&&a?parseFloat(h[3]+e*(a[3]-h[3])):1),r+=")",r}function s(h,a,e,r){var t=new f.Color(h).getSource(),n=new f.Color(a).getSource(),o=r.onComplete,i=r.onChange;return r=r||{},f.util.animate(f.util.object.extend(r,{duration:e||500,startValue:t,endValue:n,byValue:n,easing:function(l,u,d,g){var v=r.colorEasing?r.colorEasing(l,g):1-Math.cos(l/g*(Math.PI/2));return c(u,d,v)},onComplete:function(l,u,d){if(o)return o(c(n,n,0),u,d)},onChange:function(l,u,d){if(i){if(Array.isArray(l))return i(c(l,l,0),u,d);i(l,u,d)}}}))}f.util.animateColor=s}(),function(){function c(p,b,x,S){return p<Math.abs(b)?(p=b,S=x/4):b===0&&p===0?S=x/(2*Math.PI)*Math.asin(1):S=x/(2*Math.PI)*Math.asin(b/p),{a:p,c:b,p:x,s:S}}function s(p,b,x){return p.a*Math.pow(2,10*(b-=1))*Math.sin((b*x-p.s)*(2*Math.PI)/p.p)}function h(p,b,x,S){return x*((p=p/S-1)*p*p+1)+b}function a(p,b,x,S){return p/=S/2,p<1?x/2*p*p*p+b:x/2*((p-=2)*p*p+2)+b}function e(p,b,x,S){return x*(p/=S)*p*p*p+b}function r(p,b,x,S){return-x*((p=p/S-1)*p*p*p-1)+b}function t(p,b,x,S){return p/=S/2,p<1?x/2*p*p*p*p+b:-x/2*((p-=2)*p*p*p-2)+b}function n(p,b,x,S){return x*(p/=S)*p*p*p*p+b}function o(p,b,x,S){return x*((p=p/S-1)*p*p*p*p+1)+b}function i(p,b,x,S){return p/=S/2,p<1?x/2*p*p*p*p*p+b:x/2*((p-=2)*p*p*p*p+2)+b}function l(p,b,x,S){return-x*Math.cos(p/S*(Math.PI/2))+x+b}function u(p,b,x,S){return x*Math.sin(p/S*(Math.PI/2))+b}function d(p,b,x,S){return-x/2*(Math.cos(Math.PI*p/S)-1)+b}function g(p,b,x,S){return p===0?b:x*Math.pow(2,10*(p/S-1))+b}function v(p,b,x,S){return p===S?b+x:x*(-Math.pow(2,-10*p/S)+1)+b}function m(p,b,x,S){return p===0?b:p===S?b+x:(p/=S/2,p<1?x/2*Math.pow(2,10*(p-1))+b:x/2*(-Math.pow(2,-10*--p)+2)+b)}function y(p,b,x,S){return-x*(Math.sqrt(1-(p/=S)*p)-1)+b}function w(p,b,x,S){return x*Math.sqrt(1-(p=p/S-1)*p)+b}function F(p,b,x,S){return p/=S/2,p<1?-x/2*(Math.sqrt(1-p*p)-1)+b:x/2*(Math.sqrt(1-(p-=2)*p)+1)+b}function Y(p,b,x,S){var E=1.70158,C=0,_=x;if(p===0)return b;if(p/=S,p===1)return b+x;C||(C=S*.3);var T=c(_,x,C,E);return-s(T,p,S)+b}function z(p,b,x,S){var E=1.70158,C=0,_=x;if(p===0)return b;if(p/=S,p===1)return b+x;C||(C=S*.3);var T=c(_,x,C,E);return T.a*Math.pow(2,-10*p)*Math.sin((p*S-T.s)*(2*Math.PI)/T.p)+T.c+b}function N(p,b,x,S){var E=1.70158,C=0,_=x;if(p===0)return b;if(p/=S/2,p===2)return b+x;C||(C=S*(.3*1.5));var T=c(_,x,C,E);return p<1?-.5*s(T,p,S)+b:T.a*Math.pow(2,-10*(p-=1))*Math.sin((p*S-T.s)*(2*Math.PI)/T.p)*.5+T.c+b}function q(p,b,x,S,E){return E===void 0&&(E=1.70158),x*(p/=S)*p*((E+1)*p-E)+b}function H(p,b,x,S,E){return E===void 0&&(E=1.70158),x*((p=p/S-1)*p*((E+1)*p+E)+1)+b}function U(p,b,x,S,E){return E===void 0&&(E=1.70158),p/=S/2,p<1?x/2*(p*p*(((E*=1.525)+1)*p-E))+b:x/2*((p-=2)*p*(((E*=1.525)+1)*p+E)+2)+b}function J(p,b,x,S){return x-Q(S-p,0,x,S)+b}function Q(p,b,x,S){return(p/=S)<1/2.75?x*(7.5625*p*p)+b:p<2/2.75?x*(7.5625*(p-=1.5/2.75)*p+.75)+b:p<2.5/2.75?x*(7.5625*(p-=2.25/2.75)*p+.9375)+b:x*(7.5625*(p-=2.625/2.75)*p+.984375)+b}function Z(p,b,x,S){return p<S/2?J(p*2,0,x,S)*.5+b:Q(p*2-S,0,x,S)*.5+x*.5+b}f.util.ease={easeInQuad:function(p,b,x,S){return x*(p/=S)*p+b},easeOutQuad:function(p,b,x,S){return-x*(p/=S)*(p-2)+b},easeInOutQuad:function(p,b,x,S){return p/=S/2,p<1?x/2*p*p+b:-x/2*(--p*(p-2)-1)+b},easeInCubic:function(p,b,x,S){return x*(p/=S)*p*p+b},easeOutCubic:h,easeInOutCubic:a,easeInQuart:e,easeOutQuart:r,easeInOutQuart:t,easeInQuint:n,easeOutQuint:o,easeInOutQuint:i,easeInSine:l,easeOutSine:u,easeInOutSine:d,easeInExpo:g,easeOutExpo:v,easeInOutExpo:m,easeInCirc:y,easeOutCirc:w,easeInOutCirc:F,easeInElastic:Y,easeOutElastic:z,easeInOutElastic:N,easeInBack:q,easeOutBack:H,easeInOutBack:U,easeInBounce:J,easeOutBounce:Q,easeInOutBounce:Z}}(),function(c){var s=c.fabric||(c.fabric={}),h=s.util.object.extend,a=s.util.object.clone,e=s.util.toFixed,r=s.util.parseUnit,t=s.util.multiplyTransformMatrices,n=["path","circle","polygon","polyline","ellipse","rect","line","image","text"],o=["symbol","image","marker","pattern","view","svg"],i=["pattern","defs","symbol","metadata","clipPath","mask","desc"],l=["symbol","g","a","svg","clipPath","defs"],u={cx:"left",x:"left",r:"radius",cy:"top",y:"top",display:"visible",visibility:"visible",transform:"transformMatrix","fill-opacity":"fillOpacity","fill-rule":"fillRule","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","letter-spacing":"charSpacing","paint-order":"paintFirst","stroke-dasharray":"strokeDashArray","stroke-dashoffset":"strokeDashOffset","stroke-linecap":"strokeLineCap","stroke-linejoin":"strokeLineJoin","stroke-miterlimit":"strokeMiterLimit","stroke-opacity":"strokeOpacity","stroke-width":"strokeWidth","text-decoration":"textDecoration","text-anchor":"textAnchor",opacity:"opacity","clip-path":"clipPath","clip-rule":"clipRule","vector-effect":"strokeUniform","image-rendering":"imageSmoothing"},d={stroke:"strokeOpacity",fill:"fillOpacity"},g="font-size",v="clip-path";s.svgValidTagNamesRegEx=w(n),s.svgViewBoxElementsRegEx=w(o),s.svgInvalidAncestorsRegEx=w(i),s.svgValidParentsRegEx=w(l),s.cssRules={},s.gradientDefs={},s.clipPaths={};function m(C){return C in u?u[C]:C}function y(C,_,T,P){var L=Array.isArray(_),k;if((C==="fill"||C==="stroke")&&_==="none")_="";else{if(C==="strokeUniform")return _==="non-scaling-stroke";if(C==="strokeDashArray")_==="none"?_=null:_=_.replace(/,/g," ").split(/\s+/).map(parseFloat);else if(C==="transformMatrix")T&&T.transformMatrix?_=t(T.transformMatrix,s.parseTransformAttribute(_)):_=s.parseTransformAttribute(_);else if(C==="visible")_=_!=="none"&&_!=="hidden",T&&T.visible===!1&&(_=!1);else if(C==="opacity")_=parseFloat(_),T&&typeof T.opacity<"u"&&(_*=T.opacity);else if(C==="textAnchor")_=_==="start"?"left":_==="end"?"right":"center";else if(C==="charSpacing")k=r(_,P)/P*1e3;else if(C==="paintFirst"){var B=_.indexOf("fill"),W=_.indexOf("stroke"),_="fill";(B>-1&&W>-1&&W<B||B===-1&&W>-1)&&(_="stroke")}else{if(C==="href"||C==="xlink:href"||C==="font")return _;if(C==="imageSmoothing")return _==="optimizeQuality";k=L?_.map(r):r(_,P)}}return!L&&isNaN(k)?_:k}function w(C){return new RegExp("^("+C.join("|")+")\\b","i")}function F(C){for(var _ in d)if(!(typeof C[d[_]]>"u"||C[_]==="")){if(typeof C[_]>"u"){if(!s.Object.prototype[_])continue;C[_]=s.Object.prototype[_]}if(C[_].indexOf("url(")!==0){var T=new s.Color(C[_]);C[_]=T.setAlpha(e(T.getAlpha()*C[d[_]],2)).toRgba()}}return C}function Y(C,_){var T,P=[],L,k,B;for(k=0,B=_.length;k<B;k++)T=_[k],L=C.getElementsByTagName(T),P=P.concat(Array.prototype.slice.call(L));return P}s.parseTransformAttribute=function(){function C(j,tt){var rt=s.util.cos(tt[0]),nt=s.util.sin(tt[0]),ht=0,ot=0;tt.length===3&&(ht=tt[1],ot=tt[2]),j[0]=rt,j[1]=nt,j[2]=-nt,j[3]=rt,j[4]=ht-(rt*ht-nt*ot),j[5]=ot-(nt*ht+rt*ot)}function _(j,tt){var rt=tt[0],nt=tt.length===2?tt[1]:tt[0];j[0]=rt,j[3]=nt}function T(j,tt,rt){j[rt]=Math.tan(s.util.degreesToRadians(tt[0]))}function P(j,tt){j[4]=tt[0],tt.length===2&&(j[5]=tt[1])}var L=s.iMatrix,k=s.reNum,B=s.commaWsp,W="(?:(skewX)\\s*\\(\\s*("+k+")\\s*\\))",V="(?:(skewY)\\s*\\(\\s*("+k+")\\s*\\))",D="(?:(rotate)\\s*\\(\\s*("+k+")(?:"+B+"("+k+")"+B+"("+k+"))?\\s*\\))",O="(?:(scale)\\s*\\(\\s*("+k+")(?:"+B+"("+k+"))?\\s*\\))",I="(?:(translate)\\s*\\(\\s*("+k+")(?:"+B+"("+k+"))?\\s*\\))",R="(?:(matrix)\\s*\\(\\s*("+k+")"+B+"("+k+")"+B+"("+k+")"+B+"("+k+")"+B+"("+k+")"+B+"("+k+")\\s*\\))",M="(?:"+R+"|"+I+"|"+O+"|"+D+"|"+W+"|"+V+")",X="(?:"+M+"(?:"+B+"*"+M+")*)",G="^\\s*(?:"+X+"?)\\s*$",it=new RegExp(G),$=new RegExp(M,"g");return function(j){var tt=L.concat(),rt=[];if(!j||j&&!it.test(j))return tt;j.replace($,function(ht){var ot=new RegExp(M).exec(ht).filter(function(gt){return!!gt}),ut=ot[1],ct=ot.slice(2).map(parseFloat);switch(ut){case"translate":P(tt,ct);break;case"rotate":ct[0]=s.util.degreesToRadians(ct[0]),C(tt,ct);break;case"scale":_(tt,ct);break;case"skewX":T(tt,ct,2);break;case"skewY":T(tt,ct,1);break;case"matrix":tt=ct;break}rt.push(tt.concat()),tt=L.concat()});for(var nt=rt[0];rt.length>1;)rt.shift(),nt=s.util.multiplyTransformMatrices(nt,rt[0]);return nt}}();function z(C,_){var T,P;C.replace(/;\s*$/,"").split(";").forEach(function(L){var k=L.split(":");T=k[0].trim().toLowerCase(),P=k[1].trim(),_[T]=P})}function N(C,_){var T,P;for(var L in C)typeof C[L]>"u"||(T=L.toLowerCase(),P=C[L],_[T]=P)}function q(C,_){var T={};for(var P in s.cssRules[_])if(H(C,P.split(" ")))for(var L in s.cssRules[_][P])T[L]=s.cssRules[_][P][L];return T}function H(C,_){var T,P=!0;return T=J(C,_.pop()),T&&_.length&&(P=U(C,_)),T&&P&&_.length===0}function U(C,_){for(var T,P=!0;C.parentNode&&C.parentNode.nodeType===1&&_.length;)P&&(T=_.pop()),C=C.parentNode,P=J(C,T);return _.length===0}function J(C,_){var T=C.nodeName,P=C.getAttribute("class"),L=C.getAttribute("id"),k,B;if(k=new RegExp("^"+T,"i"),_=_.replace(k,""),L&&_.length&&(k=new RegExp("#"+L+"(?![a-zA-Z\\-]+)","i"),_=_.replace(k,"")),P&&_.length)for(P=P.split(" "),B=P.length;B--;)k=new RegExp("\\."+P[B]+"(?![a-zA-Z\\-]+)","i"),_=_.replace(k,"");return _.length===0}function Q(C,_){var T;if(C.getElementById&&(T=C.getElementById(_)),T)return T;var P,L,k,B=C.getElementsByTagName("*");for(L=0,k=B.length;L<k;L++)if(P=B[L],_===P.getAttribute("id"))return P}function Z(C){for(var _=Y(C,["use","svg:use"]),T=0;_.length&&T<_.length;){var P=_[T],L=P.getAttribute("xlink:href")||P.getAttribute("href");if(L===null)return;var k=L.slice(1),B=P.getAttribute("x")||0,W=P.getAttribute("y")||0,V=Q(C,k).cloneNode(!0),D=(V.getAttribute("transform")||"")+" translate("+B+", "+W+")",O,I=_.length,R,M,X,G,it=s.svgNS;if(b(V),/^svg$/i.test(V.nodeName)){var $=V.ownerDocument.createElementNS(it,"g");for(M=0,X=V.attributes,G=X.length;M<G;M++)R=X.item(M),$.setAttributeNS(it,R.nodeName,R.nodeValue);for(;V.firstChild;)$.appendChild(V.firstChild);V=$}for(M=0,X=P.attributes,G=X.length;M<G;M++)R=X.item(M),!(R.nodeName==="x"||R.nodeName==="y"||R.nodeName==="xlink:href"||R.nodeName==="href")&&(R.nodeName==="transform"?D=R.nodeValue+" "+D:V.setAttribute(R.nodeName,R.nodeValue));V.setAttribute("transform",D),V.setAttribute("instantiated_by_use","1"),V.removeAttribute("id"),O=P.parentNode,O.replaceChild(V,P),_.length===I&&T++}}var p=new RegExp("^\\s*("+s.reNum+"+)\\s*,?\\s*("+s.reNum+"+)\\s*,?\\s*("+s.reNum+"+)\\s*,?\\s*("+s.reNum+"+)\\s*$");function b(C){if(!s.svgViewBoxElementsRegEx.test(C.nodeName))return{};var _=C.getAttribute("viewBox"),T=1,P=1,L=0,k=0,B,W,V,D,O=C.getAttribute("width"),I=C.getAttribute("height"),R=C.getAttribute("x")||0,M=C.getAttribute("y")||0,X=C.getAttribute("preserveAspectRatio")||"",G=!_||!(_=_.match(p)),it=!O||!I||O==="100%"||I==="100%",$=G&&it,j={},tt="",rt=0,nt=0;if(j.width=0,j.height=0,j.toBeParsed=$,G&&(R||M)&&C.parentNode&&C.parentNode.nodeName!=="#document"&&(tt=" translate("+r(R)+" "+r(M)+") ",V=(C.getAttribute("transform")||"")+tt,C.setAttribute("transform",V),C.removeAttribute("x"),C.removeAttribute("y")),$)return j;if(G)return j.width=r(O),j.height=r(I),j;if(L=-parseFloat(_[1]),k=-parseFloat(_[2]),B=parseFloat(_[3]),W=parseFloat(_[4]),j.minX=L,j.minY=k,j.viewBoxWidth=B,j.viewBoxHeight=W,it?(j.width=B,j.height=W):(j.width=r(O),j.height=r(I),T=j.width/B,P=j.height/W),X=s.util.parsePreserveAspectRatioAttribute(X),X.alignX!=="none"&&(X.meetOrSlice==="meet"&&(P=T=T>P?P:T),X.meetOrSlice==="slice"&&(P=T=T>P?T:P),rt=j.width-B*T,nt=j.height-W*T,X.alignX==="Mid"&&(rt/=2),X.alignY==="Mid"&&(nt/=2),X.alignX==="Min"&&(rt=0),X.alignY==="Min"&&(nt=0)),T===1&&P===1&&L===0&&k===0&&R===0&&M===0)return j;if((R||M)&&C.parentNode.nodeName!=="#document"&&(tt=" translate("+r(R)+" "+r(M)+") "),V=tt+" matrix("+T+" 0 0 "+P+" "+(L*T+rt)+" "+(k*P+nt)+") ",C.nodeName==="svg"){for(D=C.ownerDocument.createElementNS(s.svgNS,"g");C.firstChild;)D.appendChild(C.firstChild);C.appendChild(D)}else D=C,D.removeAttribute("x"),D.removeAttribute("y"),V=D.getAttribute("transform")+V;return D.setAttribute("transform",V),j}function x(C,_){for(;C&&(C=C.parentNode);)if(C.nodeName&&_.test(C.nodeName.replace("svg:",""))&&!C.getAttribute("instantiated_by_use"))return!0;return!1}s.parseSVGDocument=function(C,_,T,P){if(C){Z(C);var L=s.Object.__uid++,k,B,W=b(C),V=s.util.toArray(C.getElementsByTagName("*"));if(W.crossOrigin=P&&P.crossOrigin,W.svgUid=L,V.length===0&&s.isLikelyNode){V=C.selectNodes('//*[name(.)!="svg"]');var D=[];for(k=0,B=V.length;k<B;k++)D[k]=V[k];V=D}var O=V.filter(function(R){return b(R),s.svgValidTagNamesRegEx.test(R.nodeName.replace("svg:",""))&&!x(R,s.svgInvalidAncestorsRegEx)});if(!O||O&&!O.length){_&&_([],{});return}var I={};V.filter(function(R){return R.nodeName.replace("svg:","")==="clipPath"}).forEach(function(R){var M=R.getAttribute("id");I[M]=s.util.toArray(R.getElementsByTagName("*")).filter(function(X){return s.svgValidTagNamesRegEx.test(X.nodeName.replace("svg:",""))})}),s.gradientDefs[L]=s.getGradientDefs(C),s.cssRules[L]=s.getCSSRules(C),s.clipPaths[L]=I,s.parseElements(O,function(R,M){_&&(_(R,W,M,V),delete s.gradientDefs[L],delete s.cssRules[L],delete s.clipPaths[L])},a(W),T,P)}};function S(C,_){var T=["gradientTransform","x1","x2","y1","y2","gradientUnits","cx","cy","r","fx","fy"],P="xlink:href",L=_.getAttribute(P).slice(1),k=Q(C,L);if(k&&k.getAttribute(P)&&S(C,k),T.forEach(function(W){k&&!_.hasAttribute(W)&&k.hasAttribute(W)&&_.setAttribute(W,k.getAttribute(W))}),!_.children.length)for(var B=k.cloneNode(!0);B.firstChild;)_.appendChild(B.firstChild);_.removeAttribute(P)}var E=new RegExp("(normal|italic)?\\s*(normal|small-caps)?\\s*(normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900)?\\s*("+s.reNum+"(?:px|cm|mm|em|pt|pc|in)*)(?:\\/(normal|"+s.reNum+"))?\\s+(.*)");h(s,{parseFontDeclaration:function(C,_){var T=C.match(E);if(T){var P=T[1],L=T[3],k=T[4],B=T[5],W=T[6];P&&(_.fontStyle=P),L&&(_.fontWeight=isNaN(parseFloat(L))?L:parseFloat(L)),k&&(_.fontSize=r(k)),W&&(_.fontFamily=W),B&&(_.lineHeight=B==="normal"?1:B)}},getGradientDefs:function(C){var _=["linearGradient","radialGradient","svg:linearGradient","svg:radialGradient"],T=Y(C,_),P,L=0,k={};for(L=T.length;L--;)P=T[L],P.getAttribute("xlink:href")&&S(C,P),k[P.getAttribute("id")]=P;return k},parseAttributes:function(C,_,T){if(C){var P,L={},k,B;typeof T>"u"&&(T=C.getAttribute("svgUid")),C.parentNode&&s.svgValidParentsRegEx.test(C.parentNode.nodeName)&&(L=s.parseAttributes(C.parentNode,_,T));var W=_.reduce(function(X,G){return P=C.getAttribute(G),P&&(X[G]=P),X},{}),V=h(q(C,T),s.parseStyleAttribute(C));W=h(W,V),V[v]&&C.setAttribute(v,V[v]),k=B=L.fontSize||s.Text.DEFAULT_SVG_FONT_SIZE,W[g]&&(W[g]=k=r(W[g],B));var D,O,I={};for(var R in W)D=m(R),O=y(D,W[R],L,k),I[D]=O;I.font&&s.parseFontDeclaration(I.font,I);var M=h(L,I);return s.svgValidParentsRegEx.test(C.nodeName)?M:F(M)}},parseElements:function(C,_,T,P,L){new s.ElementsParser(C,_,T,P,L).parse()},parseStyleAttribute:function(C){var _={},T=C.getAttribute("style");return T&&(typeof T=="string"?z(T,_):N(T,_)),_},parsePointsAttribute:function(C){if(!C)return null;C=C.replace(/,/g," ").trim(),C=C.split(/\s+/);var _=[],T,P;for(T=0,P=C.length;T<P;T+=2)_.push({x:parseFloat(C[T]),y:parseFloat(C[T+1])});return _},getCSSRules:function(C){var _=C.getElementsByTagName("style"),T,P,L={},k;for(T=0,P=_.length;T<P;T++){var B=_[T].textContent;B=B.replace(/\/\*[\s\S]*?\*\//g,""),B.trim()!==""&&(k=B.split("}"),k=k.filter(function(W){return W.trim()}),k.forEach(function(W){var V=W.split("{"),D={},O=V[1].trim(),I=O.split(";").filter(function(G){return G.trim()});for(T=0,P=I.length;T<P;T++){var R=I[T].split(":"),M=R[0].trim(),X=R[1].trim();D[M]=X}W=V[0].trim(),W.split(",").forEach(function(G){G=G.replace(/^svg/i,"").trim(),G!==""&&(L[G]?s.util.object.extend(L[G],D):L[G]=s.util.object.clone(D))})}))}return L},loadSVGFromURL:function(C,_,T,P){C=C.replace(/^\n\s*/,"").trim(),new s.util.request(C,{method:"get",onComplete:L});function L(k){var B=k.responseXML;if(!B||!B.documentElement)return _&&_(null),!1;s.parseSVGDocument(B.documentElement,function(W,V,D,O){_&&_(W,V,D,O)},T,P)}},loadSVGFromString:function(C,_,T,P){var L=new s.window.DOMParser,k=L.parseFromString(C.trim(),"text/xml");s.parseSVGDocument(k.documentElement,function(B,W,V,D){_(B,W,V,D)},T,P)}})}(A),f.ElementsParser=function(c,s,h,a,e,r){this.elements=c,this.callback=s,this.options=h,this.reviver=a,this.svgUid=h&&h.svgUid||0,this.parsingOptions=e,this.regexUrl=/^url\(['"]?#([^'"]+)['"]?\)/g,this.doc=r},function(c){c.parse=function(){this.instances=new Array(this.elements.length),this.numElements=this.elements.length,this.createObjects()},c.createObjects=function(){var s=this;this.elements.forEach(function(h,a){h.setAttribute("svgUid",s.svgUid),s.createObject(h,a)})},c.findTag=function(s){return f[f.util.string.capitalize(s.tagName.replace("svg:",""))]},c.createObject=function(s,h){var a=this.findTag(s);if(a&&a.fromElement)try{a.fromElement(s,this.createCallback(h,s),this.options)}catch(e){f.log(e)}else this.checkIfDone()},c.createCallback=function(s,h){var a=this;return function(e){var r;a.resolveGradient(e,h,"fill"),a.resolveGradient(e,h,"stroke"),e instanceof f.Image&&e._originalElement&&(r=e.parsePreserveAspectRatioAttribute(h)),e._removeTransformMatrix(r),a.resolveClipPath(e,h),a.reviver&&a.reviver(h,e),a.instances[s]=e,a.checkIfDone()}},c.extractPropertyDefinition=function(s,h,a){var e=s[h],r=this.regexUrl;if(r.test(e)){r.lastIndex=0;var t=r.exec(e)[1];return r.lastIndex=0,f[a][this.svgUid][t]}},c.resolveGradient=function(s,h,a){var e=this.extractPropertyDefinition(s,a,"gradientDefs");if(e){var r=h.getAttribute(a+"-opacity"),t=f.Gradient.fromElement(e,s,r,this.options);s.set(a,t)}},c.createClipPathCallback=function(s,h){return function(a){a._removeTransformMatrix(),a.fillRule=a.clipRule,h.push(a)}},c.resolveClipPath=function(s,h){var a=this.extractPropertyDefinition(s,"clipPath","clipPaths"),e,r,t,n,o,i;if(a){n=[],t=f.util.invertTransform(s.calcTransformMatrix());for(var l=a[0].parentNode,u=h;u.parentNode&&u.getAttribute("clip-path")!==s.clipPath;)u=u.parentNode;u.parentNode.appendChild(l);for(var d=0;d<a.length;d++)e=a[d],r=this.findTag(e),r.fromElement(e,this.createClipPathCallback(s,n),this.options);n.length===1?a=n[0]:a=new f.Group(n),o=f.util.multiplyTransformMatrices(t,a.calcTransformMatrix()),a.clipPath&&this.resolveClipPath(a,u);var i=f.util.qrDecompose(o);a.flipX=!1,a.flipY=!1,a.set("scaleX",i.scaleX),a.set("scaleY",i.scaleY),a.angle=i.angle,a.skewX=i.skewX,a.skewY=0,a.setPositionByOrigin({x:i.translateX,y:i.translateY},"center","center"),s.clipPath=a}else delete s.clipPath},c.checkIfDone=function(){--this.numElements===0&&(this.instances=this.instances.filter(function(s){return s!=null}),this.callback(this.instances,this.elements))}}(f.ElementsParser.prototype),function(c){var s=c.fabric||(c.fabric={});if(s.Point){s.warn("fabric.Point is already defined");return}s.Point=h;function h(a,e){this.x=a,this.y=e}h.prototype={type:"point",constructor:h,add:function(a){return new h(this.x+a.x,this.y+a.y)},addEquals:function(a){return this.x+=a.x,this.y+=a.y,this},scalarAdd:function(a){return new h(this.x+a,this.y+a)},scalarAddEquals:function(a){return this.x+=a,this.y+=a,this},subtract:function(a){return new h(this.x-a.x,this.y-a.y)},subtractEquals:function(a){return this.x-=a.x,this.y-=a.y,this},scalarSubtract:function(a){return new h(this.x-a,this.y-a)},scalarSubtractEquals:function(a){return this.x-=a,this.y-=a,this},multiply:function(a){return new h(this.x*a,this.y*a)},multiplyEquals:function(a){return this.x*=a,this.y*=a,this},divide:function(a){return new h(this.x/a,this.y/a)},divideEquals:function(a){return this.x/=a,this.y/=a,this},eq:function(a){return this.x===a.x&&this.y===a.y},lt:function(a){return this.x<a.x&&this.y<a.y},lte:function(a){return this.x<=a.x&&this.y<=a.y},gt:function(a){return this.x>a.x&&this.y>a.y},gte:function(a){return this.x>=a.x&&this.y>=a.y},lerp:function(a,e){return typeof e>"u"&&(e=.5),e=Math.max(Math.min(1,e),0),new h(this.x+(a.x-this.x)*e,this.y+(a.y-this.y)*e)},distanceFrom:function(a){var e=this.x-a.x,r=this.y-a.y;return Math.sqrt(e*e+r*r)},midPointFrom:function(a){return this.lerp(a)},min:function(a){return new h(Math.min(this.x,a.x),Math.min(this.y,a.y))},max:function(a){return new h(Math.max(this.x,a.x),Math.max(this.y,a.y))},toString:function(){return this.x+","+this.y},setXY:function(a,e){return this.x=a,this.y=e,this},setX:function(a){return this.x=a,this},setY:function(a){return this.y=a,this},setFromPoint:function(a){return this.x=a.x,this.y=a.y,this},swap:function(a){var e=this.x,r=this.y;this.x=a.x,this.y=a.y,a.x=e,a.y=r},clone:function(){return new h(this.x,this.y)}}}(A),function(c){var s=c.fabric||(c.fabric={});if(s.Intersection){s.warn("fabric.Intersection is already defined");return}function h(a){this.status=a,this.points=[]}s.Intersection=h,s.Intersection.prototype={constructor:h,appendPoint:function(a){return this.points.push(a),this},appendPoints:function(a){return this.points=this.points.concat(a),this}},s.Intersection.intersectLineLine=function(a,e,r,t){var n,o=(t.x-r.x)*(a.y-r.y)-(t.y-r.y)*(a.x-r.x),i=(e.x-a.x)*(a.y-r.y)-(e.y-a.y)*(a.x-r.x),l=(t.y-r.y)*(e.x-a.x)-(t.x-r.x)*(e.y-a.y);if(l!==0){var u=o/l,d=i/l;0<=u&&u<=1&&0<=d&&d<=1?(n=new h("Intersection"),n.appendPoint(new s.Point(a.x+u*(e.x-a.x),a.y+u*(e.y-a.y)))):n=new h}else o===0||i===0?n=new h("Coincident"):n=new h("Parallel");return n},s.Intersection.intersectLinePolygon=function(a,e,r){var t=new h,n=r.length,o,i,l,u;for(u=0;u<n;u++)o=r[u],i=r[(u+1)%n],l=h.intersectLineLine(a,e,o,i),t.appendPoints(l.points);return t.points.length>0&&(t.status="Intersection"),t},s.Intersection.intersectPolygonPolygon=function(a,e){var r=new h,t=a.length,n;for(n=0;n<t;n++){var o=a[n],i=a[(n+1)%t],l=h.intersectLinePolygon(o,i,e);r.appendPoints(l.points)}return r.points.length>0&&(r.status="Intersection"),r},s.Intersection.intersectPolygonRectangle=function(a,e,r){var t=e.min(r),n=e.max(r),o=new s.Point(n.x,t.y),i=new s.Point(t.x,n.y),l=h.intersectLinePolygon(t,o,a),u=h.intersectLinePolygon(o,n,a),d=h.intersectLinePolygon(n,i,a),g=h.intersectLinePolygon(i,t,a),v=new h;return v.appendPoints(l.points),v.appendPoints(u.points),v.appendPoints(d.points),v.appendPoints(g.points),v.points.length>0&&(v.status="Intersection"),v}}(A),function(c){var s=c.fabric||(c.fabric={});if(s.Color){s.warn("fabric.Color is already defined.");return}function h(e){e?this._tryParsingColor(e):this.setSource([0,0,0,1])}s.Color=h,s.Color.prototype={_tryParsingColor:function(e){var r;e in h.colorNameMap&&(e=h.colorNameMap[e]),e==="transparent"&&(r=[255,255,255,0]),r||(r=h.sourceFromHex(e)),r||(r=h.sourceFromRgb(e)),r||(r=h.sourceFromHsl(e)),r||(r=[0,0,0,1]),r&&this.setSource(r)},_rgbToHsl:function(e,r,t){e/=255,r/=255,t/=255;var n,o,i,l=s.util.array.max([e,r,t]),u=s.util.array.min([e,r,t]);if(i=(l+u)/2,l===u)n=o=0;else{var d=l-u;switch(o=i>.5?d/(2-l-u):d/(l+u),l){case e:n=(r-t)/d+(r<t?6:0);break;case r:n=(t-e)/d+2;break;case t:n=(e-r)/d+4;break}n/=6}return[Math.round(n*360),Math.round(o*100),Math.round(i*100)]},getSource:function(){return this._source},setSource:function(e){this._source=e},toRgb:function(){var e=this.getSource();return"rgb("+e[0]+","+e[1]+","+e[2]+")"},toRgba:function(){var e=this.getSource();return"rgba("+e[0]+","+e[1]+","+e[2]+","+e[3]+")"},toHsl:function(){var e=this.getSource(),r=this._rgbToHsl(e[0],e[1],e[2]);return"hsl("+r[0]+","+r[1]+"%,"+r[2]+"%)"},toHsla:function(){var e=this.getSource(),r=this._rgbToHsl(e[0],e[1],e[2]);return"hsla("+r[0]+","+r[1]+"%,"+r[2]+"%,"+e[3]+")"},toHex:function(){var e=this.getSource(),r,t,n;return r=e[0].toString(16),r=r.length===1?"0"+r:r,t=e[1].toString(16),t=t.length===1?"0"+t:t,n=e[2].toString(16),n=n.length===1?"0"+n:n,r.toUpperCase()+t.toUpperCase()+n.toUpperCase()},toHexa:function(){var e=this.getSource(),r;return r=Math.round(e[3]*255),r=r.toString(16),r=r.length===1?"0"+r:r,this.toHex()+r.toUpperCase()},getAlpha:function(){return this.getSource()[3]},setAlpha:function(e){var r=this.getSource();return r[3]=e,this.setSource(r),this},toGrayscale:function(){var e=this.getSource(),r=parseInt((e[0]*.3+e[1]*.59+e[2]*.11).toFixed(0),10),t=e[3];return this.setSource([r,r,r,t]),this},toBlackWhite:function(e){var r=this.getSource(),t=(r[0]*.3+r[1]*.59+r[2]*.11).toFixed(0),n=r[3];return e=e||127,t=Number(t)<Number(e)?0:255,this.setSource([t,t,t,n]),this},overlayWith:function(e){e instanceof h||(e=new h(e));var r=[],t=this.getAlpha(),n=.5,o=this.getSource(),i=e.getSource(),l;for(l=0;l<3;l++)r.push(Math.round(o[l]*(1-n)+i[l]*n));return r[3]=t,this.setSource(r),this}},s.Color.reRGBa=/^rgba?\(\s*(\d{1,3}(?:\.\d+)?\%?)\s*,\s*(\d{1,3}(?:\.\d+)?\%?)\s*,\s*(\d{1,3}(?:\.\d+)?\%?)\s*(?:\s*,\s*((?:\d*\.?\d+)?)\s*)?\)$/i,s.Color.reHSLa=/^hsla?\(\s*(\d{1,3})\s*,\s*(\d{1,3}\%)\s*,\s*(\d{1,3}\%)\s*(?:\s*,\s*(\d+(?:\.\d+)?)\s*)?\)$/i,s.Color.reHex=/^#?([0-9a-f]{8}|[0-9a-f]{6}|[0-9a-f]{4}|[0-9a-f]{3})$/i,s.Color.colorNameMap={aliceblue:"#F0F8FF",antiquewhite:"#FAEBD7",aqua:"#00FFFF",aquamarine:"#7FFFD4",azure:"#F0FFFF",beige:"#F5F5DC",bisque:"#FFE4C4",black:"#000000",blanchedalmond:"#FFEBCD",blue:"#0000FF",blueviolet:"#8A2BE2",brown:"#A52A2A",burlywood:"#DEB887",cadetblue:"#5F9EA0",chartreuse:"#7FFF00",chocolate:"#D2691E",coral:"#FF7F50",cornflowerblue:"#6495ED",cornsilk:"#FFF8DC",crimson:"#DC143C",cyan:"#00FFFF",darkblue:"#00008B",darkcyan:"#008B8B",darkgoldenrod:"#B8860B",darkgray:"#A9A9A9",darkgrey:"#A9A9A9",darkgreen:"#006400",darkkhaki:"#BDB76B",darkmagenta:"#8B008B",darkolivegreen:"#556B2F",darkorange:"#FF8C00",darkorchid:"#9932CC",darkred:"#8B0000",darksalmon:"#E9967A",darkseagreen:"#8FBC8F",darkslateblue:"#483D8B",darkslategray:"#2F4F4F",darkslategrey:"#2F4F4F",darkturquoise:"#00CED1",darkviolet:"#9400D3",deeppink:"#FF1493",deepskyblue:"#00BFFF",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1E90FF",firebrick:"#B22222",floralwhite:"#FFFAF0",forestgreen:"#228B22",fuchsia:"#FF00FF",gainsboro:"#DCDCDC",ghostwhite:"#F8F8FF",gold:"#FFD700",goldenrod:"#DAA520",gray:"#808080",grey:"#808080",green:"#008000",greenyellow:"#ADFF2F",honeydew:"#F0FFF0",hotpink:"#FF69B4",indianred:"#CD5C5C",indigo:"#4B0082",ivory:"#FFFFF0",khaki:"#F0E68C",lavender:"#E6E6FA",lavenderblush:"#FFF0F5",lawngreen:"#7CFC00",lemonchiffon:"#FFFACD",lightblue:"#ADD8E6",lightcoral:"#F08080",lightcyan:"#E0FFFF",lightgoldenrodyellow:"#FAFAD2",lightgray:"#D3D3D3",lightgrey:"#D3D3D3",lightgreen:"#90EE90",lightpink:"#FFB6C1",lightsalmon:"#FFA07A",lightseagreen:"#20B2AA",lightskyblue:"#87CEFA",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#B0C4DE",lightyellow:"#FFFFE0",lime:"#00FF00",limegreen:"#32CD32",linen:"#FAF0E6",magenta:"#FF00FF",maroon:"#800000",mediumaquamarine:"#66CDAA",mediumblue:"#0000CD",mediumorchid:"#BA55D3",mediumpurple:"#9370DB",mediumseagreen:"#3CB371",mediumslateblue:"#7B68EE",mediumspringgreen:"#00FA9A",mediumturquoise:"#48D1CC",mediumvioletred:"#C71585",midnightblue:"#191970",mintcream:"#F5FFFA",mistyrose:"#FFE4E1",moccasin:"#FFE4B5",navajowhite:"#FFDEAD",navy:"#000080",oldlace:"#FDF5E6",olive:"#808000",olivedrab:"#6B8E23",orange:"#FFA500",orangered:"#FF4500",orchid:"#DA70D6",palegoldenrod:"#EEE8AA",palegreen:"#98FB98",paleturquoise:"#AFEEEE",palevioletred:"#DB7093",papayawhip:"#FFEFD5",peachpuff:"#FFDAB9",peru:"#CD853F",pink:"#FFC0CB",plum:"#DDA0DD",powderblue:"#B0E0E6",purple:"#800080",rebeccapurple:"#663399",red:"#FF0000",rosybrown:"#BC8F8F",royalblue:"#4169E1",saddlebrown:"#8B4513",salmon:"#FA8072",sandybrown:"#F4A460",seagreen:"#2E8B57",seashell:"#FFF5EE",sienna:"#A0522D",silver:"#C0C0C0",skyblue:"#87CEEB",slateblue:"#6A5ACD",slategray:"#708090",slategrey:"#708090",snow:"#FFFAFA",springgreen:"#00FF7F",steelblue:"#4682B4",tan:"#D2B48C",teal:"#008080",thistle:"#D8BFD8",tomato:"#FF6347",turquoise:"#40E0D0",violet:"#EE82EE",wheat:"#F5DEB3",white:"#FFFFFF",whitesmoke:"#F5F5F5",yellow:"#FFFF00",yellowgreen:"#9ACD32"};function a(e,r,t){return t<0&&(t+=1),t>1&&(t-=1),t<1/6?e+(r-e)*6*t:t<1/2?r:t<2/3?e+(r-e)*(2/3-t)*6:e}s.Color.fromRgb=function(e){return h.fromSource(h.sourceFromRgb(e))},s.Color.sourceFromRgb=function(e){var r=e.match(h.reRGBa);if(r){var t=parseInt(r[1],10)/(/%$/.test(r[1])?100:1)*(/%$/.test(r[1])?255:1),n=parseInt(r[2],10)/(/%$/.test(r[2])?100:1)*(/%$/.test(r[2])?255:1),o=parseInt(r[3],10)/(/%$/.test(r[3])?100:1)*(/%$/.test(r[3])?255:1);return[parseInt(t,10),parseInt(n,10),parseInt(o,10),r[4]?parseFloat(r[4]):1]}},s.Color.fromRgba=h.fromRgb,s.Color.fromHsl=function(e){return h.fromSource(h.sourceFromHsl(e))},s.Color.sourceFromHsl=function(e){var r=e.match(h.reHSLa);if(r){var t=(parseFloat(r[1])%360+360)%360/360,n=parseFloat(r[2])/(/%$/.test(r[2])?100:1),o=parseFloat(r[3])/(/%$/.test(r[3])?100:1),i,l,u;if(n===0)i=l=u=o;else{var d=o<=.5?o*(n+1):o+n-o*n,g=o*2-d;i=a(g,d,t+1/3),l=a(g,d,t),u=a(g,d,t-1/3)}return[Math.round(i*255),Math.round(l*255),Math.round(u*255),r[4]?parseFloat(r[4]):1]}},s.Color.fromHsla=h.fromHsl,s.Color.fromHex=function(e){return h.fromSource(h.sourceFromHex(e))},s.Color.sourceFromHex=function(e){if(e.match(h.reHex)){var r=e.slice(e.indexOf("#")+1),t=r.length===3||r.length===4,n=r.length===8||r.length===4,o=t?r.charAt(0)+r.charAt(0):r.substring(0,2),i=t?r.charAt(1)+r.charAt(1):r.substring(2,4),l=t?r.charAt(2)+r.charAt(2):r.substring(4,6),u=n?t?r.charAt(3)+r.charAt(3):r.substring(6,8):"FF";return[parseInt(o,16),parseInt(i,16),parseInt(l,16),parseFloat((parseInt(u,16)/255).toFixed(2))]}},s.Color.fromSource=function(e){var r=new h;return r.setSource(e),r}}(A),function(c){var s=c.fabric||(c.fabric={}),h=["e","se","s","sw","w","nw","n","ne","e"],a=["ns","nesw","ew","nwse"],e={},r="left",t="top",n="right",o="bottom",i="center",l={top:o,bottom:t,left:n,right:r,center:i},u=s.util.radiansToDegrees,d=Math.sign||function(D){return(D>0)-(D<0)||+D};function g(D,O){var I=D.angle+u(Math.atan2(O.y,O.x))+360;return Math.round(I%360/45)}function v(D,O){var I=O.transform.target,R=I.canvas,M=s.util.object.clone(O);M.target=I,R&&R.fire("object:"+D,M),I.fire(D,O)}function m(D,O){var I=O.canvas,R=I.uniScaleKey,M=D[R];return I.uniformScaling&&!M||!I.uniformScaling&&M}function y(D){return D.originX===i&&D.originY===i}function w(D,O,I){var R=D.lockScalingX,M=D.lockScalingY;return!!(R&&M||!O&&(R||M)&&I||R&&O==="x"||M&&O==="y")}function F(D,O,I){var R="not-allowed",M=m(D,I),X="";if(O.x!==0&&O.y===0?X="x":O.x===0&&O.y!==0&&(X="y"),w(I,X,M))return R;var G=g(I,O);return h[G]+"-resize"}function Y(D,O,I){var R="not-allowed";if(O.x!==0&&I.lockSkewingY||O.y!==0&&I.lockSkewingX)return R;var M=g(I,O)%4;return a[M]+"-resize"}function z(D,O,I){return D[I.canvas.altActionKey]?e.skewCursorStyleHandler(D,O,I):e.scaleCursorStyleHandler(D,O,I)}function N(D,O,I){var R=D[I.canvas.altActionKey];if(O.x===0)return R?"skewX":"scaleY";if(O.y===0)return R?"skewY":"scaleX"}function q(D,O,I){return I.lockRotation?"not-allowed":O.cursorStyle}function H(D,O,I,R){return{e:D,transform:O,pointer:{x:I,y:R}}}function U(D){return function(O,I,R,M){var X=I.target,G=X.getCenterPoint(),it=X.translateToOriginPoint(G,I.originX,I.originY),$=D(O,I,R,M);return X.setPositionByOrigin(it,I.originX,I.originY),$}}function J(D,O){return function(I,R,M,X){var G=O(I,R,M,X);return G&&v(D,H(I,R,M,X)),G}}function Q(D,O,I,R,M){var X=D.target,G=X.controls[D.corner],it=X.canvas.getZoom(),$=X.padding/it,j=X.toLocalPoint(new s.Point(R,M),O,I);return j.x>=$&&(j.x-=$),j.x<=-$&&(j.x+=$),j.y>=$&&(j.y-=$),j.y<=$&&(j.y+=$),j.x-=G.offsetX,j.y-=G.offsetY,j}function Z(D){return D.flipX!==D.flipY}function p(D,O,I,R,M){if(D[O]!==0){var X=D._getTransformedDimensions()[R],G=M/X*D[I];D.set(I,G)}}function b(D,O,I,R){var M=O.target,X=M._getTransformedDimensions(0,M.skewY),G=Q(O,O.originX,O.originY,I,R),it=Math.abs(G.x*2)-X.x,$=M.skewX,j;it<2?j=0:(j=u(Math.atan2(it/M.scaleX,X.y/M.scaleY)),O.originX===r&&O.originY===o&&(j=-j),O.originX===n&&O.originY===t&&(j=-j),Z(M)&&(j=-j));var tt=$!==j;if(tt){var rt=M._getTransformedDimensions().y;M.set("skewX",j),p(M,"skewY","scaleY","y",rt)}return tt}function x(D,O,I,R){var M=O.target,X=M._getTransformedDimensions(M.skewX,0),G=Q(O,O.originX,O.originY,I,R),it=Math.abs(G.y*2)-X.y,$=M.skewY,j;it<2?j=0:(j=u(Math.atan2(it/M.scaleY,X.x/M.scaleX)),O.originX===r&&O.originY===o&&(j=-j),O.originX===n&&O.originY===t&&(j=-j),Z(M)&&(j=-j));var tt=$!==j;if(tt){var rt=M._getTransformedDimensions().x;M.set("skewY",j),p(M,"skewX","scaleX","x",rt)}return tt}function S(D,O,I,R){var M=O.target,X=M.skewX,G,it=O.originY;if(M.lockSkewingX)return!1;if(X===0){var $=Q(O,i,i,I,R);$.x>0?G=r:G=n}else X>0&&(G=it===t?r:n),X<0&&(G=it===t?n:r),Z(M)&&(G=G===r?n:r);O.originX=G;var j=J("skewing",U(b));return j(D,O,I,R)}function E(D,O,I,R){var M=O.target,X=M.skewY,G,it=O.originX;if(M.lockSkewingY)return!1;if(X===0){var $=Q(O,i,i,I,R);$.y>0?G=t:G=o}else X>0&&(G=it===r?t:o),X<0&&(G=it===r?o:t),Z(M)&&(G=G===t?o:t);O.originY=G;var j=J("skewing",U(x));return j(D,O,I,R)}function C(D,O,I,R){var M=O,X=M.target,G=X.translateToOriginPoint(X.getCenterPoint(),M.originX,M.originY);if(X.lockRotation)return!1;var it=Math.atan2(M.ey-G.y,M.ex-G.x),$=Math.atan2(R-G.y,I-G.x),j=u($-it+M.theta),tt=!0;if(X.snapAngle>0){var rt=X.snapAngle,nt=X.snapThreshold||rt,ht=Math.ceil(j/rt)*rt,ot=Math.floor(j/rt)*rt;Math.abs(j-ot)<nt?j=ot:Math.abs(j-ht)<nt&&(j=ht)}return j<0&&(j=360+j),j%=360,tt=X.angle!==j,X.angle=j,tt}function _(D,O,I,R,M){M=M||{};var X=O.target,G=X.lockScalingX,it=X.lockScalingY,$=M.by,j,tt,rt,nt,ht=m(D,X),ot=w(X,$,ht),ut,ct,gt=O.gestureScale;if(ot)return!1;if(gt)tt=O.scaleX*gt,rt=O.scaleY*gt;else{if(j=Q(O,O.originX,O.originY,I,R),ut=$!=="y"?d(j.x):1,ct=$!=="x"?d(j.y):1,O.signX||(O.signX=ut),O.signY||(O.signY=ct),X.lockScalingFlip&&(O.signX!==ut||O.signY!==ct))return!1;if(nt=X._getTransformedDimensions(),ht&&!$){var vt=Math.abs(j.x)+Math.abs(j.y),ft=O.original,Ot=Math.abs(nt.x*ft.scaleX/X.scaleX)+Math.abs(nt.y*ft.scaleY/X.scaleY),Ct=vt/Ot;tt=ft.scaleX*Ct,rt=ft.scaleY*Ct}else tt=Math.abs(j.x*X.scaleX/nt.x),rt=Math.abs(j.y*X.scaleY/nt.y);y(O)&&(tt*=2,rt*=2),O.signX!==ut&&$!=="y"&&(O.originX=l[O.originX],tt*=-1,O.signX=ut),O.signY!==ct&&$!=="x"&&(O.originY=l[O.originY],rt*=-1,O.signY=ct)}var Pt=X.scaleX,Et=X.scaleY;return $?($==="x"&&X.set("scaleX",tt),$==="y"&&X.set("scaleY",rt)):(!G&&X.set("scaleX",tt),!it&&X.set("scaleY",rt)),Pt!==X.scaleX||Et!==X.scaleY}function T(D,O,I,R){return _(D,O,I,R)}function P(D,O,I,R){return _(D,O,I,R,{by:"x"})}function L(D,O,I,R){return _(D,O,I,R,{by:"y"})}function k(D,O,I,R){return D[O.target.canvas.altActionKey]?e.skewHandlerX(D,O,I,R):e.scalingY(D,O,I,R)}function B(D,O,I,R){return D[O.target.canvas.altActionKey]?e.skewHandlerY(D,O,I,R):e.scalingX(D,O,I,R)}function W(D,O,I,R){var M=O.target,X=Q(O,O.originX,O.originY,I,R),G=M.strokeWidth/(M.strokeUniform?M.scaleX:1),it=y(O)?2:1,$=M.width,j=Math.abs(X.x*it/M.scaleX)-G;return M.set("width",Math.max(j,0)),$!==j}function V(D,O,I,R){var M=O.target,X=I-O.offsetX,G=R-O.offsetY,it=!M.get("lockMovementX")&&M.left!==X,$=!M.get("lockMovementY")&&M.top!==G;return it&&M.set("left",X),$&&M.set("top",G),(it||$)&&v("moving",H(D,O,I,R)),it||$}e.scaleCursorStyleHandler=F,e.skewCursorStyleHandler=Y,e.scaleSkewCursorStyleHandler=z,e.rotationWithSnapping=J("rotating",U(C)),e.scalingEqually=J("scaling",U(T)),e.scalingX=J("scaling",U(P)),e.scalingY=J("scaling",U(L)),e.scalingYOrSkewingX=k,e.scalingXOrSkewingY=B,e.changeWidth=J("resizing",U(W)),e.skewHandlerX=S,e.skewHandlerY=E,e.dragHandler=V,e.scaleOrSkewActionName=N,e.rotationStyleHandler=q,e.fireEvent=v,e.wrapWithFixedAnchor=U,e.wrapWithFireEvent=J,e.getLocalPoint=Q,s.controlsUtils=e}(A),function(c){var s=c.fabric||(c.fabric={}),h=s.util.degreesToRadians,a=s.controlsUtils;function e(t,n,o,i,l){i=i||{};var u=this.sizeX||i.cornerSize||l.cornerSize,d=this.sizeY||i.cornerSize||l.cornerSize,g=typeof i.transparentCorners<"u"?i.transparentCorners:l.transparentCorners,v=g?"stroke":"fill",m=!g&&(i.cornerStrokeColor||l.cornerStrokeColor),y=n,w=o,F;t.save(),t.fillStyle=i.cornerColor||l.cornerColor,t.strokeStyle=i.cornerStrokeColor||l.cornerStrokeColor,u>d?(F=u,t.scale(1,d/u),w=o*u/d):d>u?(F=d,t.scale(u/d,1),y=n*d/u):F=u,t.lineWidth=1,t.beginPath(),t.arc(y,w,F/2,0,2*Math.PI,!1),t[v](),m&&t.stroke(),t.restore()}function r(t,n,o,i,l){i=i||{};var u=this.sizeX||i.cornerSize||l.cornerSize,d=this.sizeY||i.cornerSize||l.cornerSize,g=typeof i.transparentCorners<"u"?i.transparentCorners:l.transparentCorners,v=g?"stroke":"fill",m=!g&&(i.cornerStrokeColor||l.cornerStrokeColor),y=u/2,w=d/2;t.save(),t.fillStyle=i.cornerColor||l.cornerColor,t.strokeStyle=i.cornerStrokeColor||l.cornerStrokeColor,t.lineWidth=1,t.translate(n,o),t.rotate(h(l.angle)),t[v+"Rect"](-y,-w,u,d),m&&t.strokeRect(-y,-w,u,d),t.restore()}a.renderCircleControl=e,a.renderSquareControl=r}(A),function(c){var s=c.fabric||(c.fabric={});function h(a){for(var e in a)this[e]=a[e]}s.Control=h,s.Control.prototype={visible:!0,actionName:"scale",angle:0,x:0,y:0,offsetX:0,offsetY:0,sizeX:null,sizeY:null,touchSizeX:null,touchSizeY:null,cursorStyle:"crosshair",withConnection:!1,actionHandler:function(){},mouseDownHandler:function(){},mouseUpHandler:function(){},getActionHandler:function(){return this.actionHandler},getMouseDownHandler:function(){return this.mouseDownHandler},getMouseUpHandler:function(){return this.mouseUpHandler},cursorStyleHandler:function(a,e){return e.cursorStyle},getActionName:function(a,e){return e.actionName},getVisibility:function(a,e){var r=a._controlsVisibility;return r&&typeof r[e]<"u"?r[e]:this.visible},setVisibility:function(a){this.visible=a},positionHandler:function(a,e){var r=s.util.transformPoint({x:this.x*a.x+this.offsetX,y:this.y*a.y+this.offsetY},e);return r},calcCornerCoords:function(a,e,r,t,n){var o,i,l,u,d=n?this.touchSizeX:this.sizeX,g=n?this.touchSizeY:this.sizeY;if(d&&g&&d!==g){var v=Math.atan2(g,d),m=Math.sqrt(d*d+g*g)/2,y=v-s.util.degreesToRadians(a),w=Math.PI/2-v-s.util.degreesToRadians(a);o=m*s.util.cos(y),i=m*s.util.sin(y),l=m*s.util.cos(w),u=m*s.util.sin(w)}else{var F=d&&g?d:e;m=F*.7071067812;var y=s.util.degreesToRadians(45-a);o=l=m*s.util.cos(y),i=u=m*s.util.sin(y)}return{tl:{x:r-u,y:t-l},tr:{x:r+o,y:t-i},bl:{x:r-o,y:t+i},br:{x:r+u,y:t+l}}},render:function(a,e,r,t,n){switch(t=t||{},t.cornerStyle||n.cornerStyle){case"circle":s.controlsUtils.renderCircleControl.call(this,a,e,r,t,n);break;default:s.controlsUtils.renderSquareControl.call(this,a,e,r,t,n)}}}}(A),function(){function c(r,t){var n=r.getAttribute("style"),o=r.getAttribute("offset")||0,i,l,u,d;if(o=parseFloat(o)/(/%$/.test(o)?100:1),o=o<0?0:o>1?1:o,n){var g=n.split(/\s*;\s*/);for(g[g.length-1]===""&&g.pop(),d=g.length;d--;){var v=g[d].split(/\s*:\s*/),m=v[0].trim(),y=v[1].trim();m==="stop-color"?i=y:m==="stop-opacity"&&(u=y)}}return i||(i=r.getAttribute("stop-color")||"rgb(0,0,0)"),u||(u=r.getAttribute("stop-opacity")),i=new f.Color(i),l=i.getAlpha(),u=isNaN(parseFloat(u))?1:parseFloat(u),u*=l*t,{offset:o,color:i.toRgb(),opacity:u}}function s(r){return{x1:r.getAttribute("x1")||0,y1:r.getAttribute("y1")||0,x2:r.getAttribute("x2")||"100%",y2:r.getAttribute("y2")||0}}function h(r){return{x1:r.getAttribute("fx")||r.getAttribute("cx")||"50%",y1:r.getAttribute("fy")||r.getAttribute("cy")||"50%",r1:0,x2:r.getAttribute("cx")||"50%",y2:r.getAttribute("cy")||"50%",r2:r.getAttribute("r")||"50%"}}var a=f.util.object.clone;f.Gradient=f.util.createClass({offsetX:0,offsetY:0,gradientTransform:null,gradientUnits:"pixels",type:"linear",initialize:function(r){r||(r={}),r.coords||(r.coords={});var t,n=this;Object.keys(r).forEach(function(o){n[o]=r[o]}),this.id?this.id+="_"+f.Object.__uid++:this.id=f.Object.__uid++,t={x1:r.coords.x1||0,y1:r.coords.y1||0,x2:r.coords.x2||0,y2:r.coords.y2||0},this.type==="radial"&&(t.r1=r.coords.r1||0,t.r2=r.coords.r2||0),this.coords=t,this.colorStops=r.colorStops.slice()},addColorStop:function(r){for(var t in r){var n=new f.Color(r[t]);this.colorStops.push({offset:parseFloat(t),color:n.toRgb(),opacity:n.getAlpha()})}return this},toObject:function(r){var t={type:this.type,coords:this.coords,colorStops:this.colorStops,offsetX:this.offsetX,offsetY:this.offsetY,gradientUnits:this.gradientUnits,gradientTransform:this.gradientTransform?this.gradientTransform.concat():this.gradientTransform};return f.util.populateWithProperties(this,t,r),t},toSVG:function(r,l){var n=a(this.coords,!0),o,i,l=l||{},u,d,g=a(this.colorStops,!0),v=n.r1>n.r2,m=this.gradientTransform?this.gradientTransform.concat():f.iMatrix.concat(),y=-this.offsetX,w=-this.offsetY,F=!!l.additionalTransform,Y=this.gradientUnits==="pixels"?"userSpaceOnUse":"objectBoundingBox";if(g.sort(function(U,J){return U.offset-J.offset}),Y==="objectBoundingBox"?(y/=r.width,w/=r.height):(y+=r.width/2,w+=r.height/2),r.type==="path"&&this.gradientUnits!=="percentage"&&(y-=r.pathOffset.x,w-=r.pathOffset.y),m[4]-=y,m[5]-=w,d='id="SVGID_'+this.id+'" gradientUnits="'+Y+'"',d+=' gradientTransform="'+(F?l.additionalTransform+" ":"")+f.util.matrixToSVG(m)+'" ',this.type==="linear"?u=["<linearGradient ",d,' x1="',n.x1,'" y1="',n.y1,'" x2="',n.x2,'" y2="',n.y2,`">
`]:this.type==="radial"&&(u=["<radialGradient ",d,' cx="',v?n.x1:n.x2,'" cy="',v?n.y1:n.y2,'" r="',v?n.r1:n.r2,'" fx="',v?n.x2:n.x1,'" fy="',v?n.y2:n.y1,`">
`]),this.type==="radial"){if(v)for(g=g.concat(),g.reverse(),o=0,i=g.length;o<i;o++)g[o].offset=1-g[o].offset;var z=Math.min(n.r1,n.r2);if(z>0){var N=Math.max(n.r1,n.r2),q=z/N;for(o=0,i=g.length;o<i;o++)g[o].offset+=q*(1-g[o].offset)}}for(o=0,i=g.length;o<i;o++){var H=g[o];u.push("<stop ",'offset="',H.offset*100+"%",'" style="stop-color:',H.color,typeof H.opacity<"u"?";stop-opacity: "+H.opacity:";",`"/>
`)}return u.push(this.type==="linear"?`</linearGradient>
`:`</radialGradient>
`),u.join("")},toLive:function(r){var t,n=f.util.object.clone(this.coords),o,i;if(this.type){for(this.type==="linear"?t=r.createLinearGradient(n.x1,n.y1,n.x2,n.y2):this.type==="radial"&&(t=r.createRadialGradient(n.x1,n.y1,n.r1,n.x2,n.y2,n.r2)),o=0,i=this.colorStops.length;o<i;o++){var l=this.colorStops[o].color,u=this.colorStops[o].opacity,d=this.colorStops[o].offset;typeof u<"u"&&(l=new f.Color(l).setAlpha(u).toRgba()),t.addColorStop(d,l)}return t}}}),f.util.object.extend(f.Gradient,{fromElement:function(r,t,n,o){var i=parseFloat(n)/(/%$/.test(n)?100:1);i=i<0?0:i>1?1:i,isNaN(i)&&(i=1);var l=r.getElementsByTagName("stop"),u,d=r.getAttribute("gradientUnits")==="userSpaceOnUse"?"pixels":"percentage",g=r.getAttribute("gradientTransform")||"",v=[],m,y,w=0,F=0,Y;for(r.nodeName==="linearGradient"||r.nodeName==="LINEARGRADIENT"?(u="linear",m=s(r)):(u="radial",m=h(r)),y=l.length;y--;)v.push(c(l[y],i));Y=f.parseTransformAttribute(g),e(t,m,o,d),d==="pixels"&&(w=-t.left,F=-t.top);var z=new f.Gradient({id:r.getAttribute("id"),type:u,coords:m,colorStops:v,gradientUnits:d,gradientTransform:Y,offsetX:w,offsetY:F});return z}});function e(r,t,n,o){var i,l;Object.keys(t).forEach(function(u){i=t[u],i==="Infinity"?l=1:i==="-Infinity"?l=0:(l=parseFloat(t[u],10),typeof i=="string"&&/^(\d+\.\d+)%|(\d+)%$/.test(i)&&(l*=.01,o==="pixels"&&((u==="x1"||u==="x2"||u==="r2")&&(l*=n.viewBoxWidth||n.width),(u==="y1"||u==="y2")&&(l*=n.viewBoxHeight||n.height)))),t[u]=l})}}(),function(){var c=f.util.toFixed;f.Pattern=f.util.createClass({repeat:"repeat",offsetX:0,offsetY:0,crossOrigin:"",patternTransform:null,initialize:function(s,h){if(s||(s={}),this.id=f.Object.__uid++,this.setOptions(s),!s.source||s.source&&typeof s.source!="string"){h&&h(this);return}else{var a=this;this.source=f.util.createImage(),f.util.loadImage(s.source,function(e,r){a.source=e,h&&h(a,r)},null,this.crossOrigin)}},toObject:function(s){var h=f.Object.NUM_FRACTION_DIGITS,a,e;return typeof this.source.src=="string"?a=this.source.src:typeof this.source=="object"&&this.source.toDataURL&&(a=this.source.toDataURL()),e={type:"pattern",source:a,repeat:this.repeat,crossOrigin:this.crossOrigin,offsetX:c(this.offsetX,h),offsetY:c(this.offsetY,h),patternTransform:this.patternTransform?this.patternTransform.concat():null},f.util.populateWithProperties(this,e,s),e},toSVG:function(s){var h=typeof this.source=="function"?this.source():this.source,a=h.width/s.width,e=h.height/s.height,r=this.offsetX/s.width,t=this.offsetY/s.height,n="";return(this.repeat==="repeat-x"||this.repeat==="no-repeat")&&(e=1,t&&(e+=Math.abs(t))),(this.repeat==="repeat-y"||this.repeat==="no-repeat")&&(a=1,r&&(a+=Math.abs(r))),h.src?n=h.src:h.toDataURL&&(n=h.toDataURL()),'<pattern id="SVGID_'+this.id+'" x="'+r+'" y="'+t+'" width="'+a+'" height="'+e+`">
<image x="0" y="0" width="`+h.width+'" height="'+h.height+'" xlink:href="'+n+`"></image>
</pattern>
`},setOptions:function(s){for(var h in s)this[h]=s[h]},toLive:function(s){var h=this.source;return!h||typeof h.src<"u"&&(!h.complete||h.naturalWidth===0||h.naturalHeight===0)?"":s.createPattern(h,this.repeat)}})}(),function(c){var s=c.fabric||(c.fabric={}),h=s.util.toFixed;if(s.Shadow){s.warn("fabric.Shadow is already defined.");return}s.Shadow=s.util.createClass({color:"rgb(0,0,0)",blur:0,offsetX:0,offsetY:0,affectStroke:!1,includeDefaultValues:!0,nonScaling:!1,initialize:function(a){typeof a=="string"&&(a=this._parseShadow(a));for(var e in a)this[e]=a[e];this.id=s.Object.__uid++},_parseShadow:function(a){var e=a.trim(),r=s.Shadow.reOffsetsAndBlur.exec(e)||[],t=e.replace(s.Shadow.reOffsetsAndBlur,"")||"rgb(0,0,0)";return{color:t.trim(),offsetX:parseFloat(r[1],10)||0,offsetY:parseFloat(r[2],10)||0,blur:parseFloat(r[3],10)||0}},toString:function(){return[this.offsetX,this.offsetY,this.blur,this.color].join("px ")},toSVG:function(a){var e=40,r=40,t=s.Object.NUM_FRACTION_DIGITS,n=s.util.rotateVector({x:this.offsetX,y:this.offsetY},s.util.degreesToRadians(-a.angle)),o=20,i=new s.Color(this.color);return a.width&&a.height&&(e=h((Math.abs(n.x)+this.blur)/a.width,t)*100+o,r=h((Math.abs(n.y)+this.blur)/a.height,t)*100+o),a.flipX&&(n.x*=-1),a.flipY&&(n.y*=-1),'<filter id="SVGID_'+this.id+'" y="-'+r+'%" height="'+(100+2*r)+'%" x="-'+e+'%" width="'+(100+2*e)+`%" >
	<feGaussianBlur in="SourceAlpha" stdDeviation="`+h(this.blur?this.blur/2:0,t)+`"></feGaussianBlur>
	<feOffset dx="`+h(n.x,t)+'" dy="'+h(n.y,t)+`" result="oBlur" ></feOffset>
	<feFlood flood-color="`+i.toRgb()+'" flood-opacity="'+i.getAlpha()+`"/>
	<feComposite in2="oBlur" operator="in" />
	<feMerge>
		<feMergeNode></feMergeNode>
		<feMergeNode in="SourceGraphic"></feMergeNode>
	</feMerge>
</filter>
`},toObject:function(){if(this.includeDefaultValues)return{color:this.color,blur:this.blur,offsetX:this.offsetX,offsetY:this.offsetY,affectStroke:this.affectStroke,nonScaling:this.nonScaling};var a={},e=s.Shadow.prototype;return["color","blur","offsetX","offsetY","affectStroke","nonScaling"].forEach(function(r){this[r]!==e[r]&&(a[r]=this[r])},this),a}}),s.Shadow.reOffsetsAndBlur=/(?:\s|^)(-?\d+(?:\.\d*)?(?:px)?(?:\s?|$))?(-?\d+(?:\.\d*)?(?:px)?(?:\s?|$))?(\d+(?:\.\d*)?(?:px)?)?(?:\s?|$)(?:$|\s)/}(A),function(){if(f.StaticCanvas){f.warn("fabric.StaticCanvas is already defined.");return}var c=f.util.object.extend,s=f.util.getElementOffset,h=f.util.removeFromArray,a=f.util.toFixed,e=f.util.transformPoint,r=f.util.invertTransform,t=f.util.getNodeCanvas,n=f.util.createCanvasElement,o=new Error("Could not initialize `canvas` element");f.StaticCanvas=f.util.createClass(f.CommonMethods,{initialize:function(i,l){l||(l={}),this.renderAndResetBound=this.renderAndReset.bind(this),this.requestRenderAllBound=this.requestRenderAll.bind(this),this._initStatic(i,l)},backgroundColor:"",backgroundImage:null,overlayColor:"",overlayImage:null,includeDefaultValues:!0,stateful:!1,renderOnAddRemove:!0,controlsAboveOverlay:!1,allowTouchScrolling:!1,imageSmoothingEnabled:!0,viewportTransform:f.iMatrix.concat(),backgroundVpt:!0,overlayVpt:!0,enableRetinaScaling:!0,vptCoords:{},skipOffscreen:!0,clipPath:void 0,_initStatic:function(i,l){var u=this.requestRenderAllBound;this._objects=[],this._createLowerCanvas(i),this._initOptions(l),this.interactive||this._initRetinaScaling(),l.overlayImage&&this.setOverlayImage(l.overlayImage,u),l.backgroundImage&&this.setBackgroundImage(l.backgroundImage,u),l.backgroundColor&&this.setBackgroundColor(l.backgroundColor,u),l.overlayColor&&this.setOverlayColor(l.overlayColor,u),this.calcOffset()},_isRetinaScaling:function(){return f.devicePixelRatio>1&&this.enableRetinaScaling},getRetinaScaling:function(){return this._isRetinaScaling()?Math.max(1,f.devicePixelRatio):1},_initRetinaScaling:function(){if(this._isRetinaScaling()){var i=f.devicePixelRatio;this.__initRetinaScaling(i,this.lowerCanvasEl,this.contextContainer),this.upperCanvasEl&&this.__initRetinaScaling(i,this.upperCanvasEl,this.contextTop)}},__initRetinaScaling:function(i,l,u){l.setAttribute("width",this.width*i),l.setAttribute("height",this.height*i),u.scale(i,i)},calcOffset:function(){return this._offset=s(this.lowerCanvasEl),this},setOverlayImage:function(i,l,u){return this.__setBgOverlayImage("overlayImage",i,l,u)},setBackgroundImage:function(i,l,u){return this.__setBgOverlayImage("backgroundImage",i,l,u)},setOverlayColor:function(i,l){return this.__setBgOverlayColor("overlayColor",i,l)},setBackgroundColor:function(i,l){return this.__setBgOverlayColor("backgroundColor",i,l)},__setBgOverlayImage:function(i,l,u,d){return typeof l=="string"?f.util.loadImage(l,function(g,v){if(g){var m=new f.Image(g,d);this[i]=m,m.canvas=this}u&&u(g,v)},this,d&&d.crossOrigin):(d&&l.setOptions(d),this[i]=l,l&&(l.canvas=this),u&&u(l,!1)),this},__setBgOverlayColor:function(i,l,u){return this[i]=l,this._initGradient(l,i),this._initPattern(l,i,u),this},_createCanvasElement:function(){var i=n();if(!i||(i.style||(i.style={}),typeof i.getContext>"u"))throw o;return i},_initOptions:function(i){var l=this.lowerCanvasEl;this._setOptions(i),this.width=this.width||parseInt(l.width,10)||0,this.height=this.height||parseInt(l.height,10)||0,this.lowerCanvasEl.style&&(l.width=this.width,l.height=this.height,l.style.width=this.width+"px",l.style.height=this.height+"px",this.viewportTransform=this.viewportTransform.slice())},_createLowerCanvas:function(i){i&&i.getContext?this.lowerCanvasEl=i:this.lowerCanvasEl=f.util.getById(i)||this._createCanvasElement(),f.util.addClass(this.lowerCanvasEl,"lower-canvas"),this._originalCanvasStyle=this.lowerCanvasEl.style,this.interactive&&this._applyCanvasStyle(this.lowerCanvasEl),this.contextContainer=this.lowerCanvasEl.getContext("2d")},getWidth:function(){return this.width},getHeight:function(){return this.height},setWidth:function(i,l){return this.setDimensions({width:i},l)},setHeight:function(i,l){return this.setDimensions({height:i},l)},setDimensions:function(i,l){var u;l=l||{};for(var d in i)u=i[d],l.cssOnly||(this._setBackstoreDimension(d,i[d]),u+="px",this.hasLostContext=!0),l.backstoreOnly||this._setCssDimension(d,u);return this._isCurrentlyDrawing&&this.freeDrawingBrush&&this.freeDrawingBrush._setBrushStyles(this.contextTop),this._initRetinaScaling(),this.calcOffset(),l.cssOnly||this.requestRenderAll(),this},_setBackstoreDimension:function(i,l){return this.lowerCanvasEl[i]=l,this.upperCanvasEl&&(this.upperCanvasEl[i]=l),this.cacheCanvasEl&&(this.cacheCanvasEl[i]=l),this[i]=l,this},_setCssDimension:function(i,l){return this.lowerCanvasEl.style[i]=l,this.upperCanvasEl&&(this.upperCanvasEl.style[i]=l),this.wrapperEl&&(this.wrapperEl.style[i]=l),this},getZoom:function(){return this.viewportTransform[0]},setViewportTransform:function(i){var l=this._activeObject,u=this.backgroundImage,d=this.overlayImage,g,v,m;for(this.viewportTransform=i,v=0,m=this._objects.length;v<m;v++)g=this._objects[v],g.group||g.setCoords(!0);return l&&l.setCoords(),u&&u.setCoords(!0),d&&d.setCoords(!0),this.calcViewportBoundaries(),this.renderOnAddRemove&&this.requestRenderAll(),this},zoomToPoint:function(i,l){var u=i,d=this.viewportTransform.slice(0);i=e(i,r(this.viewportTransform)),d[0]=l,d[3]=l;var g=e(i,d);return d[4]+=u.x-g.x,d[5]+=u.y-g.y,this.setViewportTransform(d)},setZoom:function(i){return this.zoomToPoint(new f.Point(0,0),i),this},absolutePan:function(i){var l=this.viewportTransform.slice(0);return l[4]=-i.x,l[5]=-i.y,this.setViewportTransform(l)},relativePan:function(i){return this.absolutePan(new f.Point(-i.x-this.viewportTransform[4],-i.y-this.viewportTransform[5]))},getElement:function(){return this.lowerCanvasEl},_onObjectAdded:function(i){this.stateful&&i.setupState(),i._set("canvas",this),i.setCoords(),this.fire("object:added",{target:i}),i.fire("added")},_onObjectRemoved:function(i){this.fire("object:removed",{target:i}),i.fire("removed"),delete i.canvas},clearContext:function(i){return i.clearRect(0,0,this.width,this.height),this},getContext:function(){return this.contextContainer},clear:function(){return this.remove.apply(this,this.getObjects()),this.backgroundImage=null,this.overlayImage=null,this.backgroundColor="",this.overlayColor="",this._hasITextHandlers&&(this.off("mouse:up",this._mouseUpITextHandler),this._iTextInstances=null,this._hasITextHandlers=!1),this.clearContext(this.contextContainer),this.fire("canvas:cleared"),this.renderOnAddRemove&&this.requestRenderAll(),this},renderAll:function(){var i=this.contextContainer;return this.renderCanvas(i,this._objects),this},renderAndReset:function(){this.isRendering=0,this.renderAll()},requestRenderAll:function(){return this.isRendering||(this.isRendering=f.util.requestAnimFrame(this.renderAndResetBound)),this},calcViewportBoundaries:function(){var i={},l=this.width,u=this.height,d=r(this.viewportTransform);return i.tl=e({x:0,y:0},d),i.br=e({x:l,y:u},d),i.tr=new f.Point(i.br.x,i.tl.y),i.bl=new f.Point(i.tl.x,i.br.y),this.vptCoords=i,i},cancelRequestedRender:function(){this.isRendering&&(f.util.cancelAnimFrame(this.isRendering),this.isRendering=0)},renderCanvas:function(i,l){var u=this.viewportTransform,d=this.clipPath;this.cancelRequestedRender(),this.calcViewportBoundaries(),this.clearContext(i),f.util.setImageSmoothing(i,this.imageSmoothingEnabled),this.fire("before:render",{ctx:i}),this._renderBackground(i),i.save(),i.transform(u[0],u[1],u[2],u[3],u[4],u[5]),this._renderObjects(i,l),i.restore(),!this.controlsAboveOverlay&&this.interactive&&this.drawControls(i),d&&(d.canvas=this,d.shouldCache(),d._transformDone=!0,d.renderCache({forClipping:!0}),this.drawClipPathOnCanvas(i)),this._renderOverlay(i),this.controlsAboveOverlay&&this.interactive&&this.drawControls(i),this.fire("after:render",{ctx:i})},drawClipPathOnCanvas:function(i){var l=this.viewportTransform,u=this.clipPath;i.save(),i.transform(l[0],l[1],l[2],l[3],l[4],l[5]),i.globalCompositeOperation="destination-in",u.transform(i),i.scale(1/u.zoomX,1/u.zoomY),i.drawImage(u._cacheCanvas,-u.cacheTranslationX,-u.cacheTranslationY),i.restore()},_renderObjects:function(i,l){var u,d;for(u=0,d=l.length;u<d;++u)l[u]&&l[u].render(i)},_renderBackgroundOrOverlay:function(i,l){var u=this[l+"Color"],d=this[l+"Image"],g=this.viewportTransform,v=this[l+"Vpt"];if(!(!u&&!d)){if(u){i.save(),i.beginPath(),i.moveTo(0,0),i.lineTo(this.width,0),i.lineTo(this.width,this.height),i.lineTo(0,this.height),i.closePath(),i.fillStyle=u.toLive?u.toLive(i,this):u,v&&i.transform(g[0],g[1],g[2],g[3],g[4],g[5]),i.transform(1,0,0,1,u.offsetX||0,u.offsetY||0);var m=u.gradientTransform||u.patternTransform;m&&i.transform(m[0],m[1],m[2],m[3],m[4],m[5]),i.fill(),i.restore()}d&&(i.save(),v&&i.transform(g[0],g[1],g[2],g[3],g[4],g[5]),d.render(i),i.restore())}},_renderBackground:function(i){this._renderBackgroundOrOverlay(i,"background")},_renderOverlay:function(i){this._renderBackgroundOrOverlay(i,"overlay")},getCenter:function(){return{top:this.height/2,left:this.width/2}},getCenterPoint:function(){return new f.Point(this.width/2,this.height/2)},centerObjectH:function(i){return this._centerObject(i,new f.Point(this.getCenterPoint().x,i.getCenterPoint().y))},centerObjectV:function(i){return this._centerObject(i,new f.Point(i.getCenterPoint().x,this.getCenterPoint().y))},centerObject:function(i){var l=this.getCenterPoint();return this._centerObject(i,l)},viewportCenterObject:function(i){var l=this.getVpCenter();return this._centerObject(i,l)},viewportCenterObjectH:function(i){var l=this.getVpCenter();return this._centerObject(i,new f.Point(l.x,i.getCenterPoint().y)),this},viewportCenterObjectV:function(i){var l=this.getVpCenter();return this._centerObject(i,new f.Point(i.getCenterPoint().x,l.y))},getVpCenter:function(){var i=this.getCenterPoint(),l=r(this.viewportTransform);return e(i,l)},_centerObject:function(i,l){return i.setPositionByOrigin(l,"center","center"),i.setCoords(),this.renderOnAddRemove&&this.requestRenderAll(),this},toDatalessJSON:function(i){return this.toDatalessObject(i)},toObject:function(i){return this._toObjectMethod("toObject",i)},toDatalessObject:function(i){return this._toObjectMethod("toDatalessObject",i)},_toObjectMethod:function(i,l){var u=this.clipPath,d={version:f.version,objects:this._toObjects(i,l)};return u&&!u.excludeFromExport&&(d.clipPath=this._toObject(this.clipPath,i,l)),c(d,this.__serializeBgOverlay(i,l)),f.util.populateWithProperties(this,d,l),d},_toObjects:function(i,l){return this._objects.filter(function(u){return!u.excludeFromExport}).map(function(u){return this._toObject(u,i,l)},this)},_toObject:function(i,l,u){var d;this.includeDefaultValues||(d=i.includeDefaultValues,i.includeDefaultValues=!1);var g=i[l](u);return this.includeDefaultValues||(i.includeDefaultValues=d),g},__serializeBgOverlay:function(i,l){var u={},d=this.backgroundImage,g=this.overlayImage,v=this.backgroundColor,m=this.overlayColor;return v&&v.toObject?v.excludeFromExport||(u.background=v.toObject(l)):v&&(u.background=v),m&&m.toObject?m.excludeFromExport||(u.overlay=m.toObject(l)):m&&(u.overlay=m),d&&!d.excludeFromExport&&(u.backgroundImage=this._toObject(d,i,l)),g&&!g.excludeFromExport&&(u.overlayImage=this._toObject(g,i,l)),u},svgViewportTransformation:!0,toSVG:function(i,l){i||(i={}),i.reviver=l;var u=[];return this._setSVGPreamble(u,i),this._setSVGHeader(u,i),this.clipPath&&u.push('<g clip-path="url(#'+this.clipPath.clipPathId+`)" >
`),this._setSVGBgOverlayColor(u,"background"),this._setSVGBgOverlayImage(u,"backgroundImage",l),this._setSVGObjects(u,l),this.clipPath&&u.push(`</g>
`),this._setSVGBgOverlayColor(u,"overlay"),this._setSVGBgOverlayImage(u,"overlayImage",l),u.push("</svg>"),u.join("")},_setSVGPreamble:function(i,l){l.suppressPreamble||i.push('<?xml version="1.0" encoding="',l.encoding||"UTF-8",`" standalone="no" ?>
`,'<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" ',`"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
`)},_setSVGHeader:function(i,l){var u=l.width||this.width,d=l.height||this.height,g,v='viewBox="0 0 '+this.width+" "+this.height+'" ',m=f.Object.NUM_FRACTION_DIGITS;l.viewBox?v='viewBox="'+l.viewBox.x+" "+l.viewBox.y+" "+l.viewBox.width+" "+l.viewBox.height+'" ':this.svgViewportTransformation&&(g=this.viewportTransform,v='viewBox="'+a(-g[4]/g[0],m)+" "+a(-g[5]/g[3],m)+" "+a(this.width/g[0],m)+" "+a(this.height/g[3],m)+'" '),i.push("<svg ",'xmlns="http://www.w3.org/2000/svg" ','xmlns:xlink="http://www.w3.org/1999/xlink" ','version="1.1" ','width="',u,'" ','height="',d,'" ',v,`xml:space="preserve">
`,"<desc>Created with Fabric.js ",f.version,`</desc>
`,`<defs>
`,this.createSVGFontFacesMarkup(),this.createSVGRefElementsMarkup(),this.createSVGClipPathMarkup(l),`</defs>
`)},createSVGClipPathMarkup:function(i){var l=this.clipPath;return l?(l.clipPathId="CLIPPATH_"+f.Object.__uid++,'<clipPath id="'+l.clipPathId+`" >
`+this.clipPath.toClipPathSVG(i.reviver)+`</clipPath>
`):""},createSVGRefElementsMarkup:function(){var i=this,l=["background","overlay"].map(function(u){var d=i[u+"Color"];if(d&&d.toLive){var g=i[u+"Vpt"],v=i.viewportTransform,m={width:i.width/(g?v[0]:1),height:i.height/(g?v[3]:1)};return d.toSVG(m,{additionalTransform:g?f.util.matrixToSVG(v):""})}});return l.join("")},createSVGFontFacesMarkup:function(){var i="",l={},u,d,g,v,m,y,w,F,Y,z=f.fontPaths,N=[];for(this._objects.forEach(function H(U){N.push(U),U._objects&&U._objects.forEach(H)}),F=0,Y=N.length;F<Y;F++)if(u=N[F],d=u.fontFamily,!(u.type.indexOf("text")===-1||l[d]||!z[d])&&(l[d]=!0,!!u.styles)){g=u.styles;for(m in g){v=g[m];for(w in v)y=v[w],d=y.fontFamily,!l[d]&&z[d]&&(l[d]=!0)}}for(var q in l)i+=[`		@font-face {
`,"			font-family: '",q,`';
`,"			src: url('",z[q],`');
`,`		}
`].join("");return i&&(i=['	<style type="text/css">',`<![CDATA[
`,i,"]]>",`</style>
`].join("")),i},_setSVGObjects:function(i,l){var u,d,g,v=this._objects;for(d=0,g=v.length;d<g;d++)u=v[d],!u.excludeFromExport&&this._setSVGObject(i,u,l)},_setSVGObject:function(i,l,u){i.push(l.toSVG(u))},_setSVGBgOverlayImage:function(i,l,u){this[l]&&!this[l].excludeFromExport&&this[l].toSVG&&i.push(this[l].toSVG(u))},_setSVGBgOverlayColor:function(i,l){var u=this[l+"Color"],d=this.viewportTransform,g=this.width,v=this.height;if(u)if(u.toLive){var m=u.repeat,y=f.util.invertTransform(d),w=this[l+"Vpt"],F=w?f.util.matrixToSVG(y):"";i.push('<rect transform="'+F+" translate(",g/2,",",v/2,')"',' x="',u.offsetX-g/2,'" y="',u.offsetY-v/2,'" ','width="',m==="repeat-y"||m==="no-repeat"?u.source.width:g,'" height="',m==="repeat-x"||m==="no-repeat"?u.source.height:v,'" fill="url(#SVGID_'+u.id+')"',`></rect>
`)}else i.push('<rect x="0" y="0" width="100%" height="100%" ','fill="',u,'"',`></rect>
`)},sendToBack:function(i){if(!i)return this;var l=this._activeObject,u,d,g;if(i===l&&i.type==="activeSelection")for(g=l._objects,u=g.length;u--;)d=g[u],h(this._objects,d),this._objects.unshift(d);else h(this._objects,i),this._objects.unshift(i);return this.renderOnAddRemove&&this.requestRenderAll(),this},bringToFront:function(i){if(!i)return this;var l=this._activeObject,u,d,g;if(i===l&&i.type==="activeSelection")for(g=l._objects,u=0;u<g.length;u++)d=g[u],h(this._objects,d),this._objects.push(d);else h(this._objects,i),this._objects.push(i);return this.renderOnAddRemove&&this.requestRenderAll(),this},sendBackwards:function(i,l){if(!i)return this;var u=this._activeObject,d,g,v,m,y,w=0;if(i===u&&i.type==="activeSelection")for(y=u._objects,d=0;d<y.length;d++)g=y[d],v=this._objects.indexOf(g),v>0+w&&(m=v-1,h(this._objects,g),this._objects.splice(m,0,g)),w++;else v=this._objects.indexOf(i),v!==0&&(m=this._findNewLowerIndex(i,v,l),h(this._objects,i),this._objects.splice(m,0,i));return this.renderOnAddRemove&&this.requestRenderAll(),this},_findNewLowerIndex:function(i,l,u){var d,g;if(u)for(d=l,g=l-1;g>=0;--g){var v=i.intersectsWithObject(this._objects[g])||i.isContainedWithinObject(this._objects[g])||this._objects[g].isContainedWithinObject(i);if(v){d=g;break}}else d=l-1;return d},bringForward:function(i,l){if(!i)return this;var u=this._activeObject,d,g,v,m,y,w=0;if(i===u&&i.type==="activeSelection")for(y=u._objects,d=y.length;d--;)g=y[d],v=this._objects.indexOf(g),v<this._objects.length-1-w&&(m=v+1,h(this._objects,g),this._objects.splice(m,0,g)),w++;else v=this._objects.indexOf(i),v!==this._objects.length-1&&(m=this._findNewUpperIndex(i,v,l),h(this._objects,i),this._objects.splice(m,0,i));return this.renderOnAddRemove&&this.requestRenderAll(),this},_findNewUpperIndex:function(i,l,u){var d,g,v;if(u)for(d=l,g=l+1,v=this._objects.length;g<v;++g){var m=i.intersectsWithObject(this._objects[g])||i.isContainedWithinObject(this._objects[g])||this._objects[g].isContainedWithinObject(i);if(m){d=g;break}}else d=l+1;return d},moveTo:function(i,l){return h(this._objects,i),this._objects.splice(l,0,i),this.renderOnAddRemove&&this.requestRenderAll()},dispose:function(){return this.isRendering&&(f.util.cancelAnimFrame(this.isRendering),this.isRendering=0),this.forEachObject(function(i){i.dispose&&i.dispose()}),this._objects=[],this.backgroundImage&&this.backgroundImage.dispose&&this.backgroundImage.dispose(),this.backgroundImage=null,this.overlayImage&&this.overlayImage.dispose&&this.overlayImage.dispose(),this.overlayImage=null,this._iTextInstances=null,this.contextContainer=null,this.lowerCanvasEl.classList.remove("lower-canvas"),f.util.setStyle(this.lowerCanvasEl,this._originalCanvasStyle),delete this._originalCanvasStyle,this.lowerCanvasEl.setAttribute("width",this.width),this.lowerCanvasEl.setAttribute("height",this.height),f.util.cleanUpJsdomNode(this.lowerCanvasEl),this.lowerCanvasEl=void 0,this},toString:function(){return"#<fabric.Canvas ("+this.complexity()+"): { objects: "+this._objects.length+" }>"}}),c(f.StaticCanvas.prototype,f.Observable),c(f.StaticCanvas.prototype,f.Collection),c(f.StaticCanvas.prototype,f.DataURLExporter),c(f.StaticCanvas,{EMPTY_JSON:'{"objects": [], "background": "white"}',supports:function(i){var l=n();if(!l||!l.getContext)return null;var u=l.getContext("2d");if(!u)return null;switch(i){case"setLineDash":return typeof u.setLineDash<"u";default:return null}}}),f.StaticCanvas.prototype.toJSON=f.StaticCanvas.prototype.toObject,f.isLikelyNode&&(f.StaticCanvas.prototype.createPNGStream=function(){var i=t(this.lowerCanvasEl);return i&&i.createPNGStream()},f.StaticCanvas.prototype.createJPEGStream=function(i){var l=t(this.lowerCanvasEl);return l&&l.createJPEGStream(i)})}(),f.BaseBrush=f.util.createClass({color:"rgb(0, 0, 0)",width:1,shadow:null,strokeLineCap:"round",strokeLineJoin:"round",strokeMiterLimit:10,strokeDashArray:null,limitedToCanvasSize:!1,_setBrushStyles:function(c){c.strokeStyle=this.color,c.lineWidth=this.width,c.lineCap=this.strokeLineCap,c.miterLimit=this.strokeMiterLimit,c.lineJoin=this.strokeLineJoin,c.setLineDash(this.strokeDashArray||[])},_saveAndTransform:function(c){var s=this.canvas.viewportTransform;c.save(),c.transform(s[0],s[1],s[2],s[3],s[4],s[5])},_setShadow:function(){if(this.shadow){var c=this.canvas,s=this.shadow,h=c.contextTop,a=c.getZoom();c&&c._isRetinaScaling()&&(a*=f.devicePixelRatio),h.shadowColor=s.color,h.shadowBlur=s.blur*a,h.shadowOffsetX=s.offsetX*a,h.shadowOffsetY=s.offsetY*a}},needsFullRender:function(){var c=new f.Color(this.color);return c.getAlpha()<1||!!this.shadow},_resetShadow:function(){var c=this.canvas.contextTop;c.shadowColor="",c.shadowBlur=c.shadowOffsetX=c.shadowOffsetY=0},_isOutSideCanvas:function(c){return c.x<0||c.x>this.canvas.getWidth()||c.y<0||c.y>this.canvas.getHeight()}}),function(){f.PencilBrush=f.util.createClass(f.BaseBrush,{decimate:.4,drawStraightLine:!1,straightLineKey:"shiftKey",initialize:function(c){this.canvas=c,this._points=[]},needsFullRender:function(){return this.callSuper("needsFullRender")||this._hasStraightLine},_drawSegment:function(c,s,h){var a=s.midPointFrom(h);return c.quadraticCurveTo(s.x,s.y,a.x,a.y),a},onMouseDown:function(c,s){this.canvas._isMainEvent(s.e)&&(this.drawStraightLine=s.e[this.straightLineKey],this._prepareForDrawing(c),this._captureDrawingPath(c),this._render())},onMouseMove:function(c,s){if(this.canvas._isMainEvent(s.e)&&(this.drawStraightLine=s.e[this.straightLineKey],!(this.limitedToCanvasSize===!0&&this._isOutSideCanvas(c))&&this._captureDrawingPath(c)&&this._points.length>1))if(this.needsFullRender())this.canvas.clearContext(this.canvas.contextTop),this._render();else{var h=this._points,a=h.length,e=this.canvas.contextTop;this._saveAndTransform(e),this.oldEnd&&(e.beginPath(),e.moveTo(this.oldEnd.x,this.oldEnd.y)),this.oldEnd=this._drawSegment(e,h[a-2],h[a-1],!0),e.stroke(),e.restore()}},onMouseUp:function(c){return this.canvas._isMainEvent(c.e)?(this.drawStraightLine=!1,this.oldEnd=void 0,this._finalizeAndAddPath(),!1):!0},_prepareForDrawing:function(c){var s=new f.Point(c.x,c.y);this._reset(),this._addPoint(s),this.canvas.contextTop.moveTo(s.x,s.y)},_addPoint:function(c){return this._points.length>1&&c.eq(this._points[this._points.length-1])?!1:(this.drawStraightLine&&this._points.length>1&&(this._hasStraightLine=!0,this._points.pop()),this._points.push(c),!0)},_reset:function(){this._points=[],this._setBrushStyles(this.canvas.contextTop),this._setShadow(),this._hasStraightLine=!1},_captureDrawingPath:function(c){var s=new f.Point(c.x,c.y);return this._addPoint(s)},_render:function(c){var s,h,a=this._points[0],e=this._points[1];if(c=c||this.canvas.contextTop,this._saveAndTransform(c),c.beginPath(),this._points.length===2&&a.x===e.x&&a.y===e.y){var r=this.width/1e3;a=new f.Point(a.x,a.y),e=new f.Point(e.x,e.y),a.x-=r,e.x+=r}for(c.moveTo(a.x,a.y),s=1,h=this._points.length;s<h;s++)this._drawSegment(c,a,e),a=this._points[s],e=this._points[s+1];c.lineTo(a.x,a.y),c.stroke(),c.restore()},convertPointsToSVGPath:function(c){var s=this.width/1e3;return f.util.getSmoothPathFromPoints(c,s)},_isEmptySVGPath:function(c){var s=f.util.joinPath(c);return s==="M 0 0 Q 0 0 0 0 L 0 0"},createPath:function(c){var s=new f.Path(c,{fill:null,stroke:this.color,strokeWidth:this.width,strokeLineCap:this.strokeLineCap,strokeMiterLimit:this.strokeMiterLimit,strokeLineJoin:this.strokeLineJoin,strokeDashArray:this.strokeDashArray});return this.shadow&&(this.shadow.affectStroke=!0,s.shadow=new f.Shadow(this.shadow)),s},decimatePoints:function(c,s){if(c.length<=2)return c;var h=this.canvas.getZoom(),a=Math.pow(s/h,2),e,r=c.length-1,t=c[0],n=[t],o;for(e=1;e<r-1;e++)o=Math.pow(t.x-c[e].x,2)+Math.pow(t.y-c[e].y,2),o>=a&&(t=c[e],n.push(t));return n.push(c[r]),n},_finalizeAndAddPath:function(){var c=this.canvas.contextTop;c.closePath(),this.decimate&&(this._points=this.decimatePoints(this._points,this.decimate));var s=this.convertPointsToSVGPath(this._points);if(this._isEmptySVGPath(s)){this.canvas.requestRenderAll();return}var h=this.createPath(s);this.canvas.clearContext(this.canvas.contextTop),this.canvas.fire("before:path:created",{path:h}),this.canvas.add(h),this.canvas.requestRenderAll(),h.setCoords(),this._resetShadow(),this.canvas.fire("path:created",{path:h})}})}(),f.CircleBrush=f.util.createClass(f.BaseBrush,{width:10,initialize:function(c){this.canvas=c,this.points=[]},drawDot:function(c){var s=this.addPoint(c),h=this.canvas.contextTop;this._saveAndTransform(h),this.dot(h,s),h.restore()},dot:function(c,s){c.fillStyle=s.fill,c.beginPath(),c.arc(s.x,s.y,s.radius,0,Math.PI*2,!1),c.closePath(),c.fill()},onMouseDown:function(c){this.points.length=0,this.canvas.clearContext(this.canvas.contextTop),this._setShadow(),this.drawDot(c)},_render:function(){var c=this.canvas.contextTop,s,h,a=this.points;for(this._saveAndTransform(c),s=0,h=a.length;s<h;s++)this.dot(c,a[s]);c.restore()},onMouseMove:function(c){this.limitedToCanvasSize===!0&&this._isOutSideCanvas(c)||(this.needsFullRender()?(this.canvas.clearContext(this.canvas.contextTop),this.addPoint(c),this._render()):this.drawDot(c))},onMouseUp:function(){var c=this.canvas.renderOnAddRemove,s,h;this.canvas.renderOnAddRemove=!1;var a=[];for(s=0,h=this.points.length;s<h;s++){var e=this.points[s],r=new f.Circle({radius:e.radius,left:e.x,top:e.y,originX:"center",originY:"center",fill:e.fill});this.shadow&&(r.shadow=new f.Shadow(this.shadow)),a.push(r)}var t=new f.Group(a);t.canvas=this.canvas,this.canvas.fire("before:path:created",{path:t}),this.canvas.add(t),this.canvas.fire("path:created",{path:t}),this.canvas.clearContext(this.canvas.contextTop),this._resetShadow(),this.canvas.renderOnAddRemove=c,this.canvas.requestRenderAll()},addPoint:function(c){var s=new f.Point(c.x,c.y),h=f.util.getRandomInt(Math.max(0,this.width-20),this.width+20)/2,a=new f.Color(this.color).setAlpha(f.util.getRandomInt(0,100)/100).toRgba();return s.radius=h,s.fill=a,this.points.push(s),s}}),f.SprayBrush=f.util.createClass(f.BaseBrush,{width:10,density:20,dotWidth:1,dotWidthVariance:1,randomOpacity:!1,optimizeOverlapping:!0,initialize:function(c){this.canvas=c,this.sprayChunks=[]},onMouseDown:function(c){this.sprayChunks.length=0,this.canvas.clearContext(this.canvas.contextTop),this._setShadow(),this.addSprayChunk(c),this.render(this.sprayChunkPoints)},onMouseMove:function(c){this.limitedToCanvasSize===!0&&this._isOutSideCanvas(c)||(this.addSprayChunk(c),this.render(this.sprayChunkPoints))},onMouseUp:function(){var c=this.canvas.renderOnAddRemove;this.canvas.renderOnAddRemove=!1;for(var s=[],h=0,a=this.sprayChunks.length;h<a;h++)for(var e=this.sprayChunks[h],r=0,t=e.length;r<t;r++){var n=new f.Rect({width:e[r].width,height:e[r].width,left:e[r].x+1,top:e[r].y+1,originX:"center",originY:"center",fill:this.color});s.push(n)}this.optimizeOverlapping&&(s=this._getOptimizedRects(s));var o=new f.Group(s);this.shadow&&o.set("shadow",new f.Shadow(this.shadow)),this.canvas.fire("before:path:created",{path:o}),this.canvas.add(o),this.canvas.fire("path:created",{path:o}),this.canvas.clearContext(this.canvas.contextTop),this._resetShadow(),this.canvas.renderOnAddRemove=c,this.canvas.requestRenderAll()},_getOptimizedRects:function(c){var s={},h,a,e;for(a=0,e=c.length;a<e;a++)h=c[a].left+""+c[a].top,s[h]||(s[h]=c[a]);var r=[];for(h in s)r.push(s[h]);return r},render:function(c){var s=this.canvas.contextTop,h,a;for(s.fillStyle=this.color,this._saveAndTransform(s),h=0,a=c.length;h<a;h++){var e=c[h];typeof e.opacity<"u"&&(s.globalAlpha=e.opacity),s.fillRect(e.x,e.y,e.width,e.width)}s.restore()},_render:function(){var c=this.canvas.contextTop,s,h;for(c.fillStyle=this.color,this._saveAndTransform(c),s=0,h=this.sprayChunks.length;s<h;s++)this.render(this.sprayChunks[s]);c.restore()},addSprayChunk:function(c){this.sprayChunkPoints=[];var s,h,a,e=this.width/2,r;for(r=0;r<this.density;r++){s=f.util.getRandomInt(c.x-e,c.x+e),h=f.util.getRandomInt(c.y-e,c.y+e),this.dotWidthVariance?a=f.util.getRandomInt(Math.max(1,this.dotWidth-this.dotWidthVariance),this.dotWidth+this.dotWidthVariance):a=this.dotWidth;var t=new f.Point(s,h);t.width=a,this.randomOpacity&&(t.opacity=f.util.getRandomInt(0,100)/100),this.sprayChunkPoints.push(t)}this.sprayChunks.push(this.sprayChunkPoints)}}),f.PatternBrush=f.util.createClass(f.PencilBrush,{getPatternSrc:function(){var c=20,s=5,h=f.util.createCanvasElement(),a=h.getContext("2d");return h.width=h.height=c+s,a.fillStyle=this.color,a.beginPath(),a.arc(c/2,c/2,c/2,0,Math.PI*2,!1),a.closePath(),a.fill(),h},getPatternSrcFunction:function(){return String(this.getPatternSrc).replace("this.color",'"'+this.color+'"')},getPattern:function(c){return c.createPattern(this.source||this.getPatternSrc(),"repeat")},_setBrushStyles:function(c){this.callSuper("_setBrushStyles",c),c.strokeStyle=this.getPattern(c)},createPath:function(c){var s=this.callSuper("createPath",c),h=s._getLeftTopCoords().scalarAdd(s.strokeWidth/2);return s.stroke=new f.Pattern({source:this.source||this.getPatternSrcFunction(),offsetX:-h.x,offsetY:-h.y}),s}}),function(){var c=f.util.getPointer,s=f.util.degreesToRadians,h=f.util.isTouchEvent;f.Canvas=f.util.createClass(f.StaticCanvas,{initialize:function(e,r){r||(r={}),this.renderAndResetBound=this.renderAndReset.bind(this),this.requestRenderAllBound=this.requestRenderAll.bind(this),this._initStatic(e,r),this._initInteractive(),this._createCacheCanvas()},uniformScaling:!0,uniScaleKey:"shiftKey",centeredScaling:!1,centeredRotation:!1,centeredKey:"altKey",altActionKey:"shiftKey",interactive:!0,selection:!0,selectionKey:"shiftKey",altSelectionKey:null,selectionColor:"rgba(100, 100, 255, 0.3)",selectionDashArray:[],selectionBorderColor:"rgba(255, 255, 255, 0.3)",selectionLineWidth:1,selectionFullyContained:!1,hoverCursor:"move",moveCursor:"move",defaultCursor:"default",freeDrawingCursor:"crosshair",notAllowedCursor:"not-allowed",containerClass:"canvas-container",perPixelTargetFind:!1,targetFindTolerance:0,skipTargetFind:!1,isDrawingMode:!1,preserveObjectStacking:!1,snapAngle:0,snapThreshold:null,stopContextMenu:!1,fireRightClick:!1,fireMiddleClick:!1,targets:[],enablePointerEvents:!1,_hoveredTarget:null,_hoveredTargets:[],_initInteractive:function(){this._currentTransform=null,this._groupSelector=null,this._initWrapperElement(),this._createUpperCanvas(),this._initEventListeners(),this._initRetinaScaling(),this.freeDrawingBrush=f.PencilBrush&&new f.PencilBrush(this),this.calcOffset()},_chooseObjectsToRender:function(){var e=this.getActiveObjects(),r,t,n;if(e.length>0&&!this.preserveObjectStacking){t=[],n=[];for(var o=0,i=this._objects.length;o<i;o++)r=this._objects[o],e.indexOf(r)===-1?t.push(r):n.push(r);e.length>1&&(this._activeObject._objects=n),t.push.apply(t,n)}else t=this._objects;return t},renderAll:function(){this.contextTopDirty&&!this._groupSelector&&!this.isDrawingMode&&(this.clearContext(this.contextTop),this.contextTopDirty=!1),this.hasLostContext&&(this.renderTopLayer(this.contextTop),this.hasLostContext=!1);var e=this.contextContainer;return this.renderCanvas(e,this._chooseObjectsToRender()),this},renderTopLayer:function(e){e.save(),this.isDrawingMode&&this._isCurrentlyDrawing&&(this.freeDrawingBrush&&this.freeDrawingBrush._render(),this.contextTopDirty=!0),this.selection&&this._groupSelector&&(this._drawSelection(e),this.contextTopDirty=!0),e.restore()},renderTop:function(){var e=this.contextTop;return this.clearContext(e),this.renderTopLayer(e),this.fire("after:render"),this},_normalizePointer:function(e,r){var t=e.calcTransformMatrix(),n=f.util.invertTransform(t),o=this.restorePointerVpt(r);return f.util.transformPoint(o,n)},isTargetTransparent:function(e,r,t){if(e.shouldCache()&&e._cacheCanvas&&e!==this._activeObject){var n=this._normalizePointer(e,{x:r,y:t}),o=Math.max(e.cacheTranslationX+n.x*e.zoomX,0),i=Math.max(e.cacheTranslationY+n.y*e.zoomY,0),g=f.util.isTransparent(e._cacheContext,Math.round(o),Math.round(i),this.targetFindTolerance);return g}var l=this.contextCache,u=e.selectionBackgroundColor,d=this.viewportTransform;e.selectionBackgroundColor="",this.clearContext(l),l.save(),l.transform(d[0],d[1],d[2],d[3],d[4],d[5]),e.render(l),l.restore(),e.selectionBackgroundColor=u;var g=f.util.isTransparent(l,r,t,this.targetFindTolerance);return g},_isSelectionKeyPressed:function(e){var r=!1;return Array.isArray(this.selectionKey)?r=!!this.selectionKey.find(function(t){return e[t]===!0}):r=e[this.selectionKey],r},_shouldClearSelection:function(e,r){var t=this.getActiveObjects(),n=this._activeObject;return!r||r&&n&&t.length>1&&t.indexOf(r)===-1&&n!==r&&!this._isSelectionKeyPressed(e)||r&&!r.evented||r&&!r.selectable&&n&&n!==r},_shouldCenterTransform:function(e,r,t){if(e){var n;return r==="scale"||r==="scaleX"||r==="scaleY"||r==="resizing"?n=this.centeredScaling||e.centeredScaling:r==="rotate"&&(n=this.centeredRotation||e.centeredRotation),n?!t:t}},_getOriginFromCorner:function(e,r){var t={x:e.originX,y:e.originY};return r==="ml"||r==="tl"||r==="bl"?t.x="right":(r==="mr"||r==="tr"||r==="br")&&(t.x="left"),r==="tl"||r==="mt"||r==="tr"?t.y="bottom":(r==="bl"||r==="mb"||r==="br")&&(t.y="top"),t},_getActionFromCorner:function(e,r,t,n){if(!r||!e)return"drag";var o=n.controls[r];return o.getActionName(t,o,n)},_setupCurrentTransform:function(e,r,t){if(r){var n=this.getPointer(e),o=r.__corner,i=r.controls[o],l=t&&o?i.getActionHandler(e,r,i):f.controlsUtils.dragHandler,u=this._getActionFromCorner(t,o,e,r),d=this._getOriginFromCorner(r,o),g=e[this.centeredKey],v={target:r,action:u,actionHandler:l,corner:o,scaleX:r.scaleX,scaleY:r.scaleY,skewX:r.skewX,skewY:r.skewY,offsetX:n.x-r.left,offsetY:n.y-r.top,originX:d.x,originY:d.y,ex:n.x,ey:n.y,lastX:n.x,lastY:n.y,theta:s(r.angle),width:r.width*r.scaleX,shiftKey:e.shiftKey,altKey:g,original:f.util.saveObjectTransform(r)};this._shouldCenterTransform(r,u,g)&&(v.originX="center",v.originY="center"),v.original.originX=d.x,v.original.originY=d.y,this._currentTransform=v,this._beforeTransform(e)}},setCursor:function(e){this.upperCanvasEl.style.cursor=e},_drawSelection:function(e){var r=this._groupSelector,t=new f.Point(r.ex,r.ey),n=f.util.transformPoint(t,this.viewportTransform),o=new f.Point(r.ex+r.left,r.ey+r.top),i=f.util.transformPoint(o,this.viewportTransform),l=Math.min(n.x,i.x),u=Math.min(n.y,i.y),d=Math.max(n.x,i.x),g=Math.max(n.y,i.y),v=this.selectionLineWidth/2;this.selectionColor&&(e.fillStyle=this.selectionColor,e.fillRect(l,u,d-l,g-u)),!(!this.selectionLineWidth||!this.selectionBorderColor)&&(e.lineWidth=this.selectionLineWidth,e.strokeStyle=this.selectionBorderColor,l+=v,u+=v,d-=v,g-=v,f.Object.prototype._setLineDash.call(this,e,this.selectionDashArray),e.strokeRect(l,u,d-l,g-u))},findTarget:function(e,r){if(!this.skipTargetFind){var t=!0,n=this.getPointer(e,t),o=this._activeObject,i=this.getActiveObjects(),l,u,d=h(e),g=i.length>1&&!r||i.length===1;if(this.targets=[],g&&o._findTargetCorner(n,d)||i.length>1&&!r&&o===this._searchPossibleTargets([o],n))return o;if(i.length===1&&o===this._searchPossibleTargets([o],n))if(this.preserveObjectStacking)l=o,u=this.targets,this.targets=[];else return o;var v=this._searchPossibleTargets(this._objects,n);return e[this.altSelectionKey]&&v&&l&&v!==l&&(v=l,this.targets=u),v}},_checkTarget:function(e,r,t){if(r&&r.visible&&r.evented&&r.containsPoint(e))if((this.perPixelTargetFind||r.perPixelTargetFind)&&!r.isEditing){var n=this.isTargetTransparent(r,t.x,t.y);if(!n)return!0}else return!0},_searchPossibleTargets:function(e,r){for(var t,n=e.length,o;n--;){var i=e[n],l=i.group?this._normalizePointer(i.group,r):r;if(this._checkTarget(l,i,r)){t=e[n],t.subTargetCheck&&t instanceof f.Group&&(o=this._searchPossibleTargets(t._objects,r),o&&this.targets.push(o));break}}return t},restorePointerVpt:function(e){return f.util.transformPoint(e,f.util.invertTransform(this.viewportTransform))},getPointer:function(e,r){if(this._absolutePointer&&!r)return this._absolutePointer;if(this._pointer&&r)return this._pointer;var t=c(e),n=this.upperCanvasEl,o=n.getBoundingClientRect(),i=o.width||0,l=o.height||0,u;(!i||!l)&&("top"in o&&"bottom"in o&&(l=Math.abs(o.top-o.bottom)),"right"in o&&"left"in o&&(i=Math.abs(o.right-o.left))),this.calcOffset(),t.x=t.x-this._offset.left,t.y=t.y-this._offset.top,r||(t=this.restorePointerVpt(t));var d=this.getRetinaScaling();return d!==1&&(t.x/=d,t.y/=d),i===0||l===0?u={width:1,height:1}:u={width:n.width/i,height:n.height/l},{x:t.x*u.width,y:t.y*u.height}},_createUpperCanvas:function(){var e=this.lowerCanvasEl.className.replace(/\s*lower-canvas\s*/,""),r=this.lowerCanvasEl,t=this.upperCanvasEl;t?t.className="":(t=this._createCanvasElement(),this.upperCanvasEl=t),f.util.addClass(t,"upper-canvas "+e),this.wrapperEl.appendChild(t),this._copyCanvasStyle(r,t),this._applyCanvasStyle(t),this.contextTop=t.getContext("2d")},getTopContext:function(){return this.contextTop},_createCacheCanvas:function(){this.cacheCanvasEl=this._createCanvasElement(),this.cacheCanvasEl.setAttribute("width",this.width),this.cacheCanvasEl.setAttribute("height",this.height),this.contextCache=this.cacheCanvasEl.getContext("2d")},_initWrapperElement:function(){this.wrapperEl=f.util.wrapElement(this.lowerCanvasEl,"div",{class:this.containerClass}),f.util.setStyle(this.wrapperEl,{width:this.width+"px",height:this.height+"px",position:"relative"}),f.util.makeElementUnselectable(this.wrapperEl)},_applyCanvasStyle:function(e){var r=this.width||e.width,t=this.height||e.height;f.util.setStyle(e,{position:"absolute",width:r+"px",height:t+"px",left:0,top:0,"touch-action":this.allowTouchScrolling?"manipulation":"none","-ms-touch-action":this.allowTouchScrolling?"manipulation":"none"}),e.width=r,e.height=t,f.util.makeElementUnselectable(e)},_copyCanvasStyle:function(e,r){r.style.cssText=e.style.cssText},getSelectionContext:function(){return this.contextTop},getSelectionElement:function(){return this.upperCanvasEl},getActiveObject:function(){return this._activeObject},getActiveObjects:function(){var e=this._activeObject;return e?e.type==="activeSelection"&&e._objects?e._objects.slice(0):[e]:[]},_onObjectRemoved:function(e){e===this._activeObject&&(this.fire("before:selection:cleared",{target:e}),this._discardActiveObject(),this.fire("selection:cleared",{target:e}),e.fire("deselected")),e===this._hoveredTarget&&(this._hoveredTarget=null,this._hoveredTargets=[]),this.callSuper("_onObjectRemoved",e)},_fireSelectionEvents:function(e,r){var t=!1,n=this.getActiveObjects(),o=[],i=[];e.forEach(function(l){n.indexOf(l)===-1&&(t=!0,l.fire("deselected",{e:r,target:l}),i.push(l))}),n.forEach(function(l){e.indexOf(l)===-1&&(t=!0,l.fire("selected",{e:r,target:l}),o.push(l))}),e.length>0&&n.length>0?t&&this.fire("selection:updated",{e:r,selected:o,deselected:i}):n.length>0?this.fire("selection:created",{e:r,selected:o}):e.length>0&&this.fire("selection:cleared",{e:r,deselected:i})},setActiveObject:function(e,r){var t=this.getActiveObjects();return this._setActiveObject(e,r),this._fireSelectionEvents(t,r),this},_setActiveObject:function(e,r){return this._activeObject===e||!this._discardActiveObject(r,e)||e.onSelect({e:r})?!1:(this._activeObject=e,!0)},_discardActiveObject:function(e,r){var t=this._activeObject;if(t){if(t.onDeselect({e,object:r}))return!1;this._activeObject=null}return!0},discardActiveObject:function(e){var r=this.getActiveObjects(),t=this.getActiveObject();return r.length&&this.fire("before:selection:cleared",{target:t,e}),this._discardActiveObject(e),this._fireSelectionEvents(r,e),this},dispose:function(){var e=this.wrapperEl;return this.removeListeners(),e.removeChild(this.upperCanvasEl),e.removeChild(this.lowerCanvasEl),this.contextCache=null,this.contextTop=null,["upperCanvasEl","cacheCanvasEl"].forEach((function(r){f.util.cleanUpJsdomNode(this[r]),this[r]=void 0}).bind(this)),e.parentNode&&e.parentNode.replaceChild(this.lowerCanvasEl,this.wrapperEl),delete this.wrapperEl,f.StaticCanvas.prototype.dispose.call(this),this},clear:function(){return this.discardActiveObject(),this.clearContext(this.contextTop),this.callSuper("clear")},drawControls:function(e){var r=this._activeObject;r&&r._renderControls(e)},_toObject:function(e,r,t){var n=this._realizeGroupTransformOnObject(e),o=this.callSuper("_toObject",e,r,t);return this._unwindGroupTransformOnObject(e,n),o},_realizeGroupTransformOnObject:function(e){if(e.group&&e.group.type==="activeSelection"&&this._activeObject===e.group){var r=["angle","flipX","flipY","left","scaleX","scaleY","skewX","skewY","top"],t={};return r.forEach(function(n){t[n]=e[n]}),f.util.addTransformToObject(e,this._activeObject.calcOwnMatrix()),t}else return null},_unwindGroupTransformOnObject:function(e,r){r&&e.set(r)},_setSVGObject:function(e,r,t){var n=this._realizeGroupTransformOnObject(r);this.callSuper("_setSVGObject",e,r,t),this._unwindGroupTransformOnObject(r,n)},setViewportTransform:function(e){this.renderOnAddRemove&&this._activeObject&&this._activeObject.isEditing&&this._activeObject.clearContextTop(),f.StaticCanvas.prototype.setViewportTransform.call(this,e)}});for(var a in f.StaticCanvas)a!=="prototype"&&(f.Canvas[a]=f.StaticCanvas[a])}(),function(){var c=f.util.addListener,s=f.util.removeListener,h=3,a=2,e=1,r={passive:!1};function t(n,o){return n.button&&n.button===o-1}f.util.object.extend(f.Canvas.prototype,{mainTouchId:null,_initEventListeners:function(){this.removeListeners(),this._bindEvents(),this.addOrRemove(c,"add")},_getEventPrefix:function(){return this.enablePointerEvents?"pointer":"mouse"},addOrRemove:function(n,o){var i=this.upperCanvasEl,l=this._getEventPrefix();n(f.window,"resize",this._onResize),n(i,l+"down",this._onMouseDown),n(i,l+"move",this._onMouseMove,r),n(i,l+"out",this._onMouseOut),n(i,l+"enter",this._onMouseEnter),n(i,"wheel",this._onMouseWheel),n(i,"contextmenu",this._onContextMenu),n(i,"dblclick",this._onDoubleClick),n(i,"dragover",this._onDragOver),n(i,"dragenter",this._onDragEnter),n(i,"dragleave",this._onDragLeave),n(i,"drop",this._onDrop),this.enablePointerEvents||n(i,"touchstart",this._onTouchStart,r),typeof eventjs<"u"&&o in eventjs&&(eventjs[o](i,"gesture",this._onGesture),eventjs[o](i,"drag",this._onDrag),eventjs[o](i,"orientation",this._onOrientationChange),eventjs[o](i,"shake",this._onShake),eventjs[o](i,"longpress",this._onLongPress))},removeListeners:function(){this.addOrRemove(s,"remove");var n=this._getEventPrefix();s(f.document,n+"up",this._onMouseUp),s(f.document,"touchend",this._onTouchEnd,r),s(f.document,n+"move",this._onMouseMove,r),s(f.document,"touchmove",this._onMouseMove,r)},_bindEvents:function(){this.eventsBound||(this._onMouseDown=this._onMouseDown.bind(this),this._onTouchStart=this._onTouchStart.bind(this),this._onMouseMove=this._onMouseMove.bind(this),this._onMouseUp=this._onMouseUp.bind(this),this._onTouchEnd=this._onTouchEnd.bind(this),this._onResize=this._onResize.bind(this),this._onGesture=this._onGesture.bind(this),this._onDrag=this._onDrag.bind(this),this._onShake=this._onShake.bind(this),this._onLongPress=this._onLongPress.bind(this),this._onOrientationChange=this._onOrientationChange.bind(this),this._onMouseWheel=this._onMouseWheel.bind(this),this._onMouseOut=this._onMouseOut.bind(this),this._onMouseEnter=this._onMouseEnter.bind(this),this._onContextMenu=this._onContextMenu.bind(this),this._onDoubleClick=this._onDoubleClick.bind(this),this._onDragOver=this._onDragOver.bind(this),this._onDragEnter=this._simpleEventHandler.bind(this,"dragenter"),this._onDragLeave=this._simpleEventHandler.bind(this,"dragleave"),this._onDrop=this._onDrop.bind(this),this.eventsBound=!0)},_onGesture:function(n,o){this.__onTransformGesture&&this.__onTransformGesture(n,o)},_onDrag:function(n,o){this.__onDrag&&this.__onDrag(n,o)},_onMouseWheel:function(n){this.__onMouseWheel(n)},_onMouseOut:function(n){var o=this._hoveredTarget;this.fire("mouse:out",{target:o,e:n}),this._hoveredTarget=null,o&&o.fire("mouseout",{e:n});var i=this;this._hoveredTargets.forEach(function(l){i.fire("mouse:out",{target:o,e:n}),l&&o.fire("mouseout",{e:n})}),this._hoveredTargets=[]},_onMouseEnter:function(n){!this._currentTransform&&!this.findTarget(n)&&(this.fire("mouse:over",{target:null,e:n}),this._hoveredTarget=null,this._hoveredTargets=[])},_onOrientationChange:function(n,o){this.__onOrientationChange&&this.__onOrientationChange(n,o)},_onShake:function(n,o){this.__onShake&&this.__onShake(n,o)},_onLongPress:function(n,o){this.__onLongPress&&this.__onLongPress(n,o)},_onDragOver:function(n){n.preventDefault();var o=this._simpleEventHandler("dragover",n);this._fireEnterLeaveEvents(o,n)},_onDrop:function(n){return this._simpleEventHandler("drop:before",n),this._simpleEventHandler("drop",n)},_onContextMenu:function(n){return this.stopContextMenu&&(n.stopPropagation(),n.preventDefault()),!1},_onDoubleClick:function(n){this._cacheTransformEventData(n),this._handleEvent(n,"dblclick"),this._resetTransformEventData(n)},getPointerId:function(n){var o=n.changedTouches;return o?o[0]&&o[0].identifier:this.enablePointerEvents?n.pointerId:-1},_isMainEvent:function(n){return n.isPrimary===!0?!0:n.isPrimary===!1?!1:n.type==="touchend"&&n.touches.length===0?!0:n.changedTouches?n.changedTouches[0].identifier===this.mainTouchId:!0},_onTouchStart:function(n){n.preventDefault(),this.mainTouchId===null&&(this.mainTouchId=this.getPointerId(n)),this.__onMouseDown(n),this._resetTransformEventData();var o=this.upperCanvasEl,i=this._getEventPrefix();c(f.document,"touchend",this._onTouchEnd,r),c(f.document,"touchmove",this._onMouseMove,r),s(o,i+"down",this._onMouseDown)},_onMouseDown:function(n){this.__onMouseDown(n),this._resetTransformEventData();var o=this.upperCanvasEl,i=this._getEventPrefix();s(o,i+"move",this._onMouseMove,r),c(f.document,i+"up",this._onMouseUp),c(f.document,i+"move",this._onMouseMove,r)},_onTouchEnd:function(n){if(!(n.touches.length>0)){this.__onMouseUp(n),this._resetTransformEventData(),this.mainTouchId=null;var o=this._getEventPrefix();s(f.document,"touchend",this._onTouchEnd,r),s(f.document,"touchmove",this._onMouseMove,r);var i=this;this._willAddMouseDown&&clearTimeout(this._willAddMouseDown),this._willAddMouseDown=setTimeout(function(){c(i.upperCanvasEl,o+"down",i._onMouseDown),i._willAddMouseDown=0},400)}},_onMouseUp:function(n){this.__onMouseUp(n),this._resetTransformEventData();var o=this.upperCanvasEl,i=this._getEventPrefix();this._isMainEvent(n)&&(s(f.document,i+"up",this._onMouseUp),s(f.document,i+"move",this._onMouseMove,r),c(o,i+"move",this._onMouseMove,r))},_onMouseMove:function(n){!this.allowTouchScrolling&&n.preventDefault&&n.preventDefault(),this.__onMouseMove(n)},_onResize:function(){this.calcOffset()},_shouldRender:function(n){var o=this._activeObject;return!!o!=!!n||o&&n&&o!==n?!0:(o&&o.isEditing,!1)},__onMouseUp:function(n){var o,i=this._currentTransform,l=this._groupSelector,u=!1,d=!l||l.left===0&&l.top===0;if(this._cacheTransformEventData(n),o=this._target,this._handleEvent(n,"up:before"),t(n,h)){this.fireRightClick&&this._handleEvent(n,"up",h,d);return}if(t(n,a)){this.fireMiddleClick&&this._handleEvent(n,"up",a,d),this._resetTransformEventData();return}if(this.isDrawingMode&&this._isCurrentlyDrawing){this._onMouseUpInDrawingMode(n);return}if(this._isMainEvent(n)){if(i&&(this._finalizeCurrentTransform(n),u=i.actionPerformed),!d){var g=o===this._activeObject;this._maybeGroupObjects(n),u||(u=this._shouldRender(o)||!g&&o===this._activeObject)}var v,m;if(o){if(v=o._findTargetCorner(this.getPointer(n,!0),f.util.isTouchEvent(n)),o.selectable&&o!==this._activeObject&&o.activeOn==="up")this.setActiveObject(o,n),u=!0;else{var y=o.controls[v],w=y&&y.getMouseUpHandler(n,o,y);w&&(m=this.getPointer(n),w(n,i,m.x,m.y))}o.isMoving=!1}if(i&&(i.target!==o||i.corner!==v)){var F=i.target&&i.target.controls[i.corner],Y=F&&F.getMouseUpHandler(n,o,y);m=m||this.getPointer(n),Y&&Y(n,i,m.x,m.y)}this._setCursorFromEvent(n,o),this._handleEvent(n,"up",e,d),this._groupSelector=null,this._currentTransform=null,o&&(o.__corner=0),u?this.requestRenderAll():d||this.renderTop()}},_simpleEventHandler:function(n,o){var i=this.findTarget(o),l=this.targets,u={e:o,target:i,subTargets:l};if(this.fire(n,u),i&&i.fire(n,u),!l)return i;for(var d=0;d<l.length;d++)l[d].fire(n,u);return i},_handleEvent:function(n,o,i,l){var u=this._target,d=this.targets||[],g={e:n,target:u,subTargets:d,button:i||e,isClick:l||!1,pointer:this._pointer,absolutePointer:this._absolutePointer,transform:this._currentTransform};o==="up"&&(g.currentTarget=this.findTarget(n),g.currentSubTargets=this.targets),this.fire("mouse:"+o,g),u&&u.fire("mouse"+o,g);for(var v=0;v<d.length;v++)d[v].fire("mouse"+o,g)},_finalizeCurrentTransform:function(n){var o=this._currentTransform,i=o.target,l={e:n,target:i,transform:o,action:o.action};i._scaling&&(i._scaling=!1),i.setCoords(),(o.actionPerformed||this.stateful&&i.hasStateChanged())&&this._fire("modified",l)},_onMouseDownInDrawingMode:function(n){this._isCurrentlyDrawing=!0,this.getActiveObject()&&this.discardActiveObject(n).requestRenderAll();var o=this.getPointer(n);this.freeDrawingBrush.onMouseDown(o,{e:n,pointer:o}),this._handleEvent(n,"down")},_onMouseMoveInDrawingMode:function(n){if(this._isCurrentlyDrawing){var o=this.getPointer(n);this.freeDrawingBrush.onMouseMove(o,{e:n,pointer:o})}this.setCursor(this.freeDrawingCursor),this._handleEvent(n,"move")},_onMouseUpInDrawingMode:function(n){var o=this.getPointer(n);this._isCurrentlyDrawing=this.freeDrawingBrush.onMouseUp({e:n,pointer:o}),this._handleEvent(n,"up")},__onMouseDown:function(n){this._cacheTransformEventData(n),this._handleEvent(n,"down:before");var o=this._target;if(t(n,h)){this.fireRightClick&&this._handleEvent(n,"down",h);return}if(t(n,a)){this.fireMiddleClick&&this._handleEvent(n,"down",a);return}if(this.isDrawingMode){this._onMouseDownInDrawingMode(n);return}if(this._isMainEvent(n)&&!this._currentTransform){var i=this._pointer;this._previousPointer=i;var l=this._shouldRender(o),u=this._shouldGroup(n,o);if(this._shouldClearSelection(n,o)?this.discardActiveObject(n):u&&(this._handleGrouping(n,o),o=this._activeObject),this.selection&&(!o||!o.selectable&&!o.isEditing&&o!==this._activeObject)&&(this._groupSelector={ex:this._absolutePointer.x,ey:this._absolutePointer.y,top:0,left:0}),o){var d=o===this._activeObject;o.selectable&&o.activeOn==="down"&&this.setActiveObject(o,n);var g=o._findTargetCorner(this.getPointer(n,!0),f.util.isTouchEvent(n));if(o.__corner=g,o===this._activeObject&&(g||!u)){this._setupCurrentTransform(n,o,d);var v=o.controls[g],i=this.getPointer(n),m=v&&v.getMouseDownHandler(n,o,v);m&&m(n,this._currentTransform,i.x,i.y)}}this._handleEvent(n,"down"),(l||u)&&this.requestRenderAll()}},_resetTransformEventData:function(){this._target=null,this._pointer=null,this._absolutePointer=null},_cacheTransformEventData:function(n){this._resetTransformEventData(),this._pointer=this.getPointer(n,!0),this._absolutePointer=this.restorePointerVpt(this._pointer),this._target=this._currentTransform?this._currentTransform.target:this.findTarget(n)||null},_beforeTransform:function(n){var o=this._currentTransform;this.stateful&&o.target.saveState(),this.fire("before:transform",{e:n,transform:o})},__onMouseMove:function(n){this._handleEvent(n,"move:before"),this._cacheTransformEventData(n);var o,i;if(this.isDrawingMode){this._onMouseMoveInDrawingMode(n);return}if(this._isMainEvent(n)){var l=this._groupSelector;l?(i=this._absolutePointer,l.left=i.x-l.ex,l.top=i.y-l.ey,this.renderTop()):this._currentTransform?this._transformObject(n):(o=this.findTarget(n)||null,this._setCursorFromEvent(n,o),this._fireOverOutEvents(o,n)),this._handleEvent(n,"move"),this._resetTransformEventData()}},_fireOverOutEvents:function(n,o){var i=this._hoveredTarget,l=this._hoveredTargets,u=this.targets,d=Math.max(l.length,u.length);this.fireSyntheticInOutEvents(n,o,{oldTarget:i,evtOut:"mouseout",canvasEvtOut:"mouse:out",evtIn:"mouseover",canvasEvtIn:"mouse:over"});for(var g=0;g<d;g++)this.fireSyntheticInOutEvents(u[g],o,{oldTarget:l[g],evtOut:"mouseout",evtIn:"mouseover"});this._hoveredTarget=n,this._hoveredTargets=this.targets.concat()},_fireEnterLeaveEvents:function(n,o){var i=this._draggedoverTarget,l=this._hoveredTargets,u=this.targets,d=Math.max(l.length,u.length);this.fireSyntheticInOutEvents(n,o,{oldTarget:i,evtOut:"dragleave",evtIn:"dragenter"});for(var g=0;g<d;g++)this.fireSyntheticInOutEvents(u[g],o,{oldTarget:l[g],evtOut:"dragleave",evtIn:"dragenter"});this._draggedoverTarget=n},fireSyntheticInOutEvents:function(n,o,i){var l,u,d=i.oldTarget,g,v,m=d!==n,y=i.canvasEvtIn,w=i.canvasEvtOut;m&&(l={e:o,target:n,previousTarget:d},u={e:o,target:d,nextTarget:n}),v=n&&m,g=d&&m,g&&(w&&this.fire(w,u),d.fire(i.evtOut,u)),v&&(y&&this.fire(y,l),n.fire(i.evtIn,l))},__onMouseWheel:function(n){this._cacheTransformEventData(n),this._handleEvent(n,"wheel"),this._resetTransformEventData()},_transformObject:function(n){var o=this.getPointer(n),i=this._currentTransform;i.reset=!1,i.shiftKey=n.shiftKey,i.altKey=n[this.centeredKey],this._performTransformAction(n,i,o),i.actionPerformed&&this.requestRenderAll()},_performTransformAction:function(n,o,i){var l=i.x,u=i.y,d=o.action,g=!1,v=o.actionHandler;v&&(g=v(n,o,l,u)),d==="drag"&&g&&(o.target.isMoving=!0,this.setCursor(o.target.moveCursor||this.moveCursor)),o.actionPerformed=o.actionPerformed||g},_fire:f.controlsUtils.fireEvent,_setCursorFromEvent:function(n,o){if(!o)return this.setCursor(this.defaultCursor),!1;var i=o.hoverCursor||this.hoverCursor,l=this._activeObject&&this._activeObject.type==="activeSelection"?this._activeObject:null,u=(!l||!l.contains(o))&&o._findTargetCorner(this.getPointer(n,!0));u?this.setCursor(this.getCornerCursor(u,o,n)):(o.subTargetCheck&&this.targets.concat().reverse().map(function(d){i=d.hoverCursor||i}),this.setCursor(i))},getCornerCursor:function(n,o,i){var l=o.controls[n];return l.cursorStyleHandler(i,l,o)}})}(),function(){var c=Math.min,s=Math.max;f.util.object.extend(f.Canvas.prototype,{_shouldGroup:function(h,a){var e=this._activeObject;return e&&this._isSelectionKeyPressed(h)&&a&&a.selectable&&this.selection&&(e!==a||e.type==="activeSelection")&&!a.onSelect({e:h})},_handleGrouping:function(h,a){var e=this._activeObject;e.__corner||a===e&&(a=this.findTarget(h,!0),!a||!a.selectable)||(e&&e.type==="activeSelection"?this._updateActiveSelection(a,h):this._createActiveSelection(a,h))},_updateActiveSelection:function(h,a){var e=this._activeObject,r=e._objects.slice(0);e.contains(h)?(e.removeWithUpdate(h),this._hoveredTarget=h,this._hoveredTargets=this.targets.concat(),e.size()===1&&this._setActiveObject(e.item(0),a)):(e.addWithUpdate(h),this._hoveredTarget=e,this._hoveredTargets=this.targets.concat()),this._fireSelectionEvents(r,a)},_createActiveSelection:function(h,a){var e=this.getActiveObjects(),r=this._createGroup(h);this._hoveredTarget=r,this._setActiveObject(r,a),this._fireSelectionEvents(e,a)},_createGroup:function(h){var a=this._objects,e=a.indexOf(this._activeObject)<a.indexOf(h),r=e?[this._activeObject,h]:[h,this._activeObject];return this._activeObject.isEditing&&this._activeObject.exitEditing(),new f.ActiveSelection(r,{canvas:this})},_groupSelectedObjects:function(h){var a=this._collectObjects(h),e;a.length===1?this.setActiveObject(a[0],h):a.length>1&&(e=new f.ActiveSelection(a.reverse(),{canvas:this}),this.setActiveObject(e,h))},_collectObjects:function(h){for(var a=[],e,r=this._groupSelector.ex,t=this._groupSelector.ey,n=r+this._groupSelector.left,o=t+this._groupSelector.top,i=new f.Point(c(r,n),c(t,o)),l=new f.Point(s(r,n),s(t,o)),u=!this.selectionFullyContained,d=r===n&&t===o,g=this._objects.length;g--&&(e=this._objects[g],!(!(!e||!e.selectable||!e.visible)&&(u&&e.intersectsWithRect(i,l,!0)||e.isContainedWithinRect(i,l,!0)||u&&e.containsPoint(i,null,!0)||u&&e.containsPoint(l,null,!0))&&(a.push(e),d))););return a.length>1&&(a=a.filter(function(v){return!v.onSelect({e:h})})),a},_maybeGroupObjects:function(h){this.selection&&this._groupSelector&&this._groupSelectedObjects(h),this.setCursor(this.defaultCursor),this._groupSelector=null}})}(),function(){f.util.object.extend(f.StaticCanvas.prototype,{toDataURL:function(c){c||(c={});var s=c.format||"png",h=c.quality||1,a=(c.multiplier||1)*(c.enableRetinaScaling?this.getRetinaScaling():1),e=this.toCanvasElement(a,c);return f.util.toDataURL(e,s,h)},toCanvasElement:function(c,s){c=c||1,s=s||{};var h=(s.width||this.width)*c,a=(s.height||this.height)*c,e=this.getZoom(),r=this.width,t=this.height,n=e*c,o=this.viewportTransform,i=(o[4]-(s.left||0))*c,l=(o[5]-(s.top||0))*c,u=this.interactive,d=[n,0,0,n,i,l],g=this.enableRetinaScaling,v=f.util.createCanvasElement(),m=this.contextTop;return v.width=h,v.height=a,this.contextTop=null,this.enableRetinaScaling=!1,this.interactive=!1,this.viewportTransform=d,this.width=h,this.height=a,this.calcViewportBoundaries(),this.renderCanvas(v.getContext("2d"),this._objects),this.viewportTransform=o,this.width=r,this.height=t,this.calcViewportBoundaries(),this.interactive=u,this.enableRetinaScaling=g,this.contextTop=m,v}})}(),f.util.object.extend(f.StaticCanvas.prototype,{loadFromJSON:function(c,s,h){if(c){var a=typeof c=="string"?JSON.parse(c):f.util.object.clone(c),e=this,r=a.clipPath,t=this.renderOnAddRemove;return this.renderOnAddRemove=!1,delete a.clipPath,this._enlivenObjects(a.objects,function(n){e.clear(),e._setBgOverlay(a,function(){r?e._enlivenObjects([r],function(o){e.clipPath=o[0],e.__setupCanvas.call(e,a,n,t,s)}):e.__setupCanvas.call(e,a,n,t,s)})},h),this}},__setupCanvas:function(c,s,h,a){var e=this;s.forEach(function(r,t){e.insertAt(r,t)}),this.renderOnAddRemove=h,delete c.objects,delete c.backgroundImage,delete c.overlayImage,delete c.background,delete c.overlay,this._setOptions(c),this.renderAll(),a&&a()},_setBgOverlay:function(c,s){var h={backgroundColor:!1,overlayColor:!1,backgroundImage:!1,overlayImage:!1};if(!c.backgroundImage&&!c.overlayImage&&!c.background&&!c.overlay){s&&s();return}var a=function(){h.backgroundImage&&h.overlayImage&&h.backgroundColor&&h.overlayColor&&s&&s()};this.__setBgOverlay("backgroundImage",c.backgroundImage,h,a),this.__setBgOverlay("overlayImage",c.overlayImage,h,a),this.__setBgOverlay("backgroundColor",c.background,h,a),this.__setBgOverlay("overlayColor",c.overlay,h,a)},__setBgOverlay:function(c,s,h,a){var e=this;if(!s){h[c]=!0,a&&a();return}c==="backgroundImage"||c==="overlayImage"?f.util.enlivenObjects([s],function(r){e[c]=r[0],h[c]=!0,a&&a()}):this["set"+f.util.string.capitalize(c,!0)](s,function(){h[c]=!0,a&&a()})},_enlivenObjects:function(c,s,h){if(!c||c.length===0){s&&s([]);return}f.util.enlivenObjects(c,function(a){s&&s(a)},null,h)},_toDataURL:function(c,s){this.clone(function(h){s(h.toDataURL(c))})},_toDataURLWithMultiplier:function(c,s,h){this.clone(function(a){h(a.toDataURLWithMultiplier(c,s))})},clone:function(c,s){var h=JSON.stringify(this.toJSON(s));this.cloneWithoutData(function(a){a.loadFromJSON(h,function(){c&&c(a)})})},cloneWithoutData:function(c){var s=f.util.createCanvasElement();s.width=this.width,s.height=this.height;var h=new f.Canvas(s);this.backgroundImage?(h.setBackgroundImage(this.backgroundImage.src,function(){h.renderAll(),c&&c(h)}),h.backgroundImageOpacity=this.backgroundImageOpacity,h.backgroundImageStretch=this.backgroundImageStretch):c&&c(h)}}),function(c){var s=c.fabric||(c.fabric={}),h=s.util.object.extend,a=s.util.object.clone,e=s.util.toFixed,r=s.util.string.capitalize,t=s.util.degreesToRadians,n=!s.isLikelyNode,o=2;s.Object||(s.Object=s.util.createClass(s.CommonMethods,{type:"object",originX:"left",originY:"top",top:0,left:0,width:0,height:0,scaleX:1,scaleY:1,flipX:!1,flipY:!1,opacity:1,angle:0,skewX:0,skewY:0,cornerSize:13,touchCornerSize:24,transparentCorners:!0,hoverCursor:null,moveCursor:null,padding:0,borderColor:"rgb(178,204,255)",borderDashArray:null,cornerColor:"rgb(178,204,255)",cornerStrokeColor:null,cornerStyle:"rect",cornerDashArray:null,centeredScaling:!1,centeredRotation:!0,fill:"rgb(0,0,0)",fillRule:"nonzero",globalCompositeOperation:"source-over",backgroundColor:"",selectionBackgroundColor:"",stroke:null,strokeWidth:1,strokeDashArray:null,strokeDashOffset:0,strokeLineCap:"butt",strokeLineJoin:"miter",strokeMiterLimit:4,shadow:null,borderOpacityWhenMoving:.4,borderScaleFactor:1,minScaleLimit:0,selectable:!0,evented:!0,visible:!0,hasControls:!0,hasBorders:!0,perPixelTargetFind:!1,includeDefaultValues:!0,lockMovementX:!1,lockMovementY:!1,lockRotation:!1,lockScalingX:!1,lockScalingY:!1,lockSkewingX:!1,lockSkewingY:!1,lockScalingFlip:!1,excludeFromExport:!1,objectCaching:n,statefullCache:!1,noScaleCache:!0,strokeUniform:!1,dirty:!0,__corner:0,paintFirst:"fill",activeOn:"down",stateProperties:"top left width height scaleX scaleY flipX flipY originX originY transformMatrix stroke strokeWidth strokeDashArray strokeLineCap strokeDashOffset strokeLineJoin strokeMiterLimit angle opacity fill globalCompositeOperation shadow visible backgroundColor skewX skewY fillRule paintFirst clipPath strokeUniform".split(" "),cacheProperties:"fill stroke strokeWidth strokeDashArray width height paintFirst strokeUniform strokeLineCap strokeDashOffset strokeLineJoin strokeMiterLimit backgroundColor clipPath".split(" "),colorProperties:"fill stroke backgroundColor".split(" "),clipPath:void 0,inverted:!1,absolutePositioned:!1,initialize:function(i){i&&this.setOptions(i)},_createCacheCanvas:function(){this._cacheProperties={},this._cacheCanvas=s.util.createCanvasElement(),this._cacheContext=this._cacheCanvas.getContext("2d"),this._updateCacheCanvas(),this.dirty=!0},_limitCacheSize:function(i){var l=s.perfLimitSizeTotal,u=i.width,d=i.height,g=s.maxCacheSideLimit,v=s.minCacheSideLimit;if(u<=g&&d<=g&&u*d<=l)return u<v&&(i.width=v),d<v&&(i.height=v),i;var m=u/d,y=s.util.limitDimsByArea(m,l),w=s.util.capValue,F=w(v,y.x,g),Y=w(v,y.y,g);return u>F&&(i.zoomX/=u/F,i.width=F,i.capped=!0),d>Y&&(i.zoomY/=d/Y,i.height=Y,i.capped=!0),i},_getCacheCanvasDimensions:function(){var i=this.getTotalObjectScaling(),l=this._getTransformedDimensions(0,0),u=l.x*i.scaleX/this.scaleX,d=l.y*i.scaleY/this.scaleY;return{width:u+o,height:d+o,zoomX:i.scaleX,zoomY:i.scaleY,x:u,y:d}},_updateCacheCanvas:function(){var i=this.canvas;if(this.noScaleCache&&i&&i._currentTransform){var l=i._currentTransform.target,u=i._currentTransform.action;if(this===l&&u.slice&&u.slice(0,5)==="scale")return!1}var d=this._cacheCanvas,g=this._limitCacheSize(this._getCacheCanvasDimensions()),v=s.minCacheSideLimit,m=g.width,y=g.height,w,F,Y=g.zoomX,z=g.zoomY,N=m!==this.cacheWidth||y!==this.cacheHeight,q=this.zoomX!==Y||this.zoomY!==z,H=N||q,U=0,J=0,Q=!1;if(N){var Z=this._cacheCanvas.width,p=this._cacheCanvas.height,b=m>Z||y>p,x=(m<Z*.9||y<p*.9)&&Z>v&&p>v;Q=b||x,b&&!g.capped&&(m>v||y>v)&&(U=m*.1,J=y*.1)}return this instanceof s.Text&&this.path&&(H=!0,Q=!0,U+=this.getHeightOfLine(0)*this.zoomX,J+=this.getHeightOfLine(0)*this.zoomY),H?(Q?(d.width=Math.ceil(m+U),d.height=Math.ceil(y+J)):(this._cacheContext.setTransform(1,0,0,1,0,0),this._cacheContext.clearRect(0,0,d.width,d.height)),w=g.x/2,F=g.y/2,this.cacheTranslationX=Math.round(d.width/2-w)+w,this.cacheTranslationY=Math.round(d.height/2-F)+F,this.cacheWidth=m,this.cacheHeight=y,this._cacheContext.translate(this.cacheTranslationX,this.cacheTranslationY),this._cacheContext.scale(Y,z),this.zoomX=Y,this.zoomY=z,!0):!1},setOptions:function(i){this._setOptions(i),this._initGradient(i.fill,"fill"),this._initGradient(i.stroke,"stroke"),this._initPattern(i.fill,"fill"),this._initPattern(i.stroke,"stroke")},transform:function(i){var l=this.group&&!this.group._transformDone||this.group&&this.canvas&&i===this.canvas.contextTop,u=this.calcTransformMatrix(!l);i.transform(u[0],u[1],u[2],u[3],u[4],u[5])},toObject:function(i){var l=s.Object.NUM_FRACTION_DIGITS,u={type:this.type,version:s.version,originX:this.originX,originY:this.originY,left:e(this.left,l),top:e(this.top,l),width:e(this.width,l),height:e(this.height,l),fill:this.fill&&this.fill.toObject?this.fill.toObject():this.fill,stroke:this.stroke&&this.stroke.toObject?this.stroke.toObject():this.stroke,strokeWidth:e(this.strokeWidth,l),strokeDashArray:this.strokeDashArray?this.strokeDashArray.concat():this.strokeDashArray,strokeLineCap:this.strokeLineCap,strokeDashOffset:this.strokeDashOffset,strokeLineJoin:this.strokeLineJoin,strokeUniform:this.strokeUniform,strokeMiterLimit:e(this.strokeMiterLimit,l),scaleX:e(this.scaleX,l),scaleY:e(this.scaleY,l),angle:e(this.angle,l),flipX:this.flipX,flipY:this.flipY,opacity:e(this.opacity,l),shadow:this.shadow&&this.shadow.toObject?this.shadow.toObject():this.shadow,visible:this.visible,backgroundColor:this.backgroundColor,fillRule:this.fillRule,paintFirst:this.paintFirst,globalCompositeOperation:this.globalCompositeOperation,skewX:e(this.skewX,l),skewY:e(this.skewY,l)};return this.clipPath&&!this.clipPath.excludeFromExport&&(u.clipPath=this.clipPath.toObject(i),u.clipPath.inverted=this.clipPath.inverted,u.clipPath.absolutePositioned=this.clipPath.absolutePositioned),s.util.populateWithProperties(this,u,i),this.includeDefaultValues||(u=this._removeDefaultValues(u)),u},toDatalessObject:function(i){return this.toObject(i)},_removeDefaultValues:function(i){var l=s.util.getKlass(i.type).prototype,u=l.stateProperties;return u.forEach(function(d){d==="left"||d==="top"||(i[d]===l[d]&&delete i[d],Array.isArray(i[d])&&Array.isArray(l[d])&&i[d].length===0&&l[d].length===0&&delete i[d])}),i},toString:function(){return"#<fabric."+r(this.type)+">"},getObjectScaling:function(){if(!this.group)return{scaleX:this.scaleX,scaleY:this.scaleY};var i=s.util.qrDecompose(this.calcTransformMatrix());return{scaleX:Math.abs(i.scaleX),scaleY:Math.abs(i.scaleY)}},getTotalObjectScaling:function(){var i=this.getObjectScaling(),l=i.scaleX,u=i.scaleY;if(this.canvas){var d=this.canvas.getZoom(),g=this.canvas.getRetinaScaling();l*=d*g,u*=d*g}return{scaleX:l,scaleY:u}},getObjectOpacity:function(){var i=this.opacity;return this.group&&(i*=this.group.getObjectOpacity()),i},_set:function(i,l){var u=i==="scaleX"||i==="scaleY",d=this[i]!==l,g=!1;return u&&(l=this._constrainScale(l)),i==="scaleX"&&l<0?(this.flipX=!this.flipX,l*=-1):i==="scaleY"&&l<0?(this.flipY=!this.flipY,l*=-1):i==="shadow"&&l&&!(l instanceof s.Shadow)?l=new s.Shadow(l):i==="dirty"&&this.group&&this.group.set("dirty",l),this[i]=l,d&&(g=this.group&&this.group.isOnACache(),this.cacheProperties.indexOf(i)>-1?(this.dirty=!0,g&&this.group.set("dirty",!0)):g&&this.stateProperties.indexOf(i)>-1&&this.group.set("dirty",!0)),this},setOnGroup:function(){},getViewportTransform:function(){return this.canvas&&this.canvas.viewportTransform?this.canvas.viewportTransform:s.iMatrix.concat()},isNotVisible:function(){return this.opacity===0||!this.width&&!this.height&&this.strokeWidth===0||!this.visible},render:function(i){this.isNotVisible()||this.canvas&&this.canvas.skipOffscreen&&!this.group&&!this.isOnScreen()||(i.save(),this._setupCompositeOperation(i),this.drawSelectionBackground(i),this.transform(i),this._setOpacity(i),this._setShadow(i,this),this.shouldCache()?(this.renderCache(),this.drawCacheOnCanvas(i)):(this._removeCacheCanvas(),this.dirty=!1,this.drawObject(i),this.objectCaching&&this.statefullCache&&this.saveState({propertySet:"cacheProperties"})),i.restore())},renderCache:function(i){i=i||{},(!this._cacheCanvas||!this._cacheContext)&&this._createCacheCanvas(),this.isCacheDirty()&&(this.statefullCache&&this.saveState({propertySet:"cacheProperties"}),this.drawObject(this._cacheContext,i.forClipping),this.dirty=!1)},_removeCacheCanvas:function(){this._cacheCanvas=null,this._cacheContext=null,this.cacheWidth=0,this.cacheHeight=0},hasStroke:function(){return this.stroke&&this.stroke!=="transparent"&&this.strokeWidth!==0},hasFill:function(){return this.fill&&this.fill!=="transparent"},needsItsOwnCache:function(){return!!(this.paintFirst==="stroke"&&this.hasFill()&&this.hasStroke()&&typeof this.shadow=="object"||this.clipPath)},shouldCache:function(){return this.ownCaching=this.needsItsOwnCache()||this.objectCaching&&(!this.group||!this.group.isOnACache()),this.ownCaching},willDrawShadow:function(){return!!this.shadow&&(this.shadow.offsetX!==0||this.shadow.offsetY!==0)},drawClipPathOnCache:function(i,l){if(i.save(),l.inverted?i.globalCompositeOperation="destination-out":i.globalCompositeOperation="destination-in",l.absolutePositioned){var u=s.util.invertTransform(this.calcTransformMatrix());i.transform(u[0],u[1],u[2],u[3],u[4],u[5])}l.transform(i),i.scale(1/l.zoomX,1/l.zoomY),i.drawImage(l._cacheCanvas,-l.cacheTranslationX,-l.cacheTranslationY),i.restore()},drawObject:function(i,l){var u=this.fill,d=this.stroke;l?(this.fill="black",this.stroke="",this._setClippingProperties(i)):this._renderBackground(i),this._render(i),this._drawClipPath(i,this.clipPath),this.fill=u,this.stroke=d},_drawClipPath:function(i,l){l&&(l.canvas=this.canvas,l.shouldCache(),l._transformDone=!0,l.renderCache({forClipping:!0}),this.drawClipPathOnCache(i,l))},drawCacheOnCanvas:function(i){i.scale(1/this.zoomX,1/this.zoomY),i.drawImage(this._cacheCanvas,-this.cacheTranslationX,-this.cacheTranslationY)},isCacheDirty:function(i){if(this.isNotVisible())return!1;if(this._cacheCanvas&&this._cacheContext&&!i&&this._updateCacheCanvas())return!0;if(this.dirty||this.clipPath&&this.clipPath.absolutePositioned||this.statefullCache&&this.hasStateChanged("cacheProperties")){if(this._cacheCanvas&&this._cacheContext&&!i){var l=this.cacheWidth/this.zoomX,u=this.cacheHeight/this.zoomY;this._cacheContext.clearRect(-l/2,-u/2,l,u)}return!0}return!1},_renderBackground:function(i){if(this.backgroundColor){var l=this._getNonTransformedDimensions();i.fillStyle=this.backgroundColor,i.fillRect(-l.x/2,-l.y/2,l.x,l.y),this._removeShadow(i)}},_setOpacity:function(i){this.group&&!this.group._transformDone?i.globalAlpha=this.getObjectOpacity():i.globalAlpha*=this.opacity},_setStrokeStyles:function(i,l){var u=l.stroke;u&&(i.lineWidth=l.strokeWidth,i.lineCap=l.strokeLineCap,i.lineDashOffset=l.strokeDashOffset,i.lineJoin=l.strokeLineJoin,i.miterLimit=l.strokeMiterLimit,u.toLive?u.gradientUnits==="percentage"||u.gradientTransform||u.patternTransform?this._applyPatternForTransformedGradient(i,u):(i.strokeStyle=u.toLive(i,this),this._applyPatternGradientTransform(i,u)):i.strokeStyle=l.stroke)},_setFillStyles:function(i,l){var u=l.fill;u&&(u.toLive?(i.fillStyle=u.toLive(i,this),this._applyPatternGradientTransform(i,l.fill)):i.fillStyle=u)},_setClippingProperties:function(i){i.globalAlpha=1,i.strokeStyle="transparent",i.fillStyle="#000000"},_setLineDash:function(i,l){!l||l.length===0||(1&l.length&&l.push.apply(l,l),i.setLineDash(l))},_renderControls:function(i,l){var u=this.getViewportTransform(),d=this.calcTransformMatrix(),g,v,m;l=l||{},v=typeof l.hasBorders<"u"?l.hasBorders:this.hasBorders,m=typeof l.hasControls<"u"?l.hasControls:this.hasControls,d=s.util.multiplyTransformMatrices(u,d),g=s.util.qrDecompose(d),i.save(),i.translate(g.translateX,g.translateY),i.lineWidth=1*this.borderScaleFactor,this.group||(i.globalAlpha=this.isMoving?this.borderOpacityWhenMoving:1),this.flipX&&(g.angle-=180),i.rotate(t(this.group?g.angle:this.angle)),l.forActiveSelection||this.group?v&&this.drawBordersInGroup(i,g,l):v&&this.drawBorders(i,l),m&&this.drawControls(i,l),i.restore()},_setShadow:function(i){if(this.shadow){var l=this.shadow,u=this.canvas,d,g=u&&u.viewportTransform[0]||1,v=u&&u.viewportTransform[3]||1;l.nonScaling?d={scaleX:1,scaleY:1}:d=this.getObjectScaling(),u&&u._isRetinaScaling()&&(g*=s.devicePixelRatio,v*=s.devicePixelRatio),i.shadowColor=l.color,i.shadowBlur=l.blur*s.browserShadowBlurConstant*(g+v)*(d.scaleX+d.scaleY)/4,i.shadowOffsetX=l.offsetX*g*d.scaleX,i.shadowOffsetY=l.offsetY*v*d.scaleY}},_removeShadow:function(i){this.shadow&&(i.shadowColor="",i.shadowBlur=i.shadowOffsetX=i.shadowOffsetY=0)},_applyPatternGradientTransform:function(i,l){if(!l||!l.toLive)return{offsetX:0,offsetY:0};var u=l.gradientTransform||l.patternTransform,d=-this.width/2+l.offsetX||0,g=-this.height/2+l.offsetY||0;return l.gradientUnits==="percentage"?i.transform(this.width,0,0,this.height,d,g):i.transform(1,0,0,1,d,g),u&&i.transform(u[0],u[1],u[2],u[3],u[4],u[5]),{offsetX:d,offsetY:g}},_renderPaintInOrder:function(i){this.paintFirst==="stroke"?(this._renderStroke(i),this._renderFill(i)):(this._renderFill(i),this._renderStroke(i))},_render:function(){},_renderFill:function(i){this.fill&&(i.save(),this._setFillStyles(i,this),this.fillRule==="evenodd"?i.fill("evenodd"):i.fill(),i.restore())},_renderStroke:function(i){if(!(!this.stroke||this.strokeWidth===0)){if(this.shadow&&!this.shadow.affectStroke&&this._removeShadow(i),i.save(),this.strokeUniform&&this.group){var l=this.getObjectScaling();i.scale(1/l.scaleX,1/l.scaleY)}else this.strokeUniform&&i.scale(1/this.scaleX,1/this.scaleY);this._setLineDash(i,this.strokeDashArray),this._setStrokeStyles(i,this),i.stroke(),i.restore()}},_applyPatternForTransformedGradient:function(i,l){var u=this._limitCacheSize(this._getCacheCanvasDimensions()),d=s.util.createCanvasElement(),g,v=this.canvas.getRetinaScaling(),m=u.x/this.scaleX/v,y=u.y/this.scaleY/v;d.width=m,d.height=y,g=d.getContext("2d"),g.beginPath(),g.moveTo(0,0),g.lineTo(m,0),g.lineTo(m,y),g.lineTo(0,y),g.closePath(),g.translate(m/2,y/2),g.scale(u.zoomX/this.scaleX/v,u.zoomY/this.scaleY/v),this._applyPatternGradientTransform(g,l),g.fillStyle=l.toLive(i),g.fill(),i.translate(-this.width/2-this.strokeWidth/2,-this.height/2-this.strokeWidth/2),i.scale(v*this.scaleX/u.zoomX,v*this.scaleY/u.zoomY),i.strokeStyle=g.createPattern(d,"no-repeat")},_findCenterFromElement:function(){return{x:this.left+this.width/2,y:this.top+this.height/2}},_assignTransformMatrixProps:function(){if(this.transformMatrix){var i=s.util.qrDecompose(this.transformMatrix);this.flipX=!1,this.flipY=!1,this.set("scaleX",i.scaleX),this.set("scaleY",i.scaleY),this.angle=i.angle,this.skewX=i.skewX,this.skewY=0}},_removeTransformMatrix:function(i){var l=this._findCenterFromElement();this.transformMatrix&&(this._assignTransformMatrixProps(),l=s.util.transformPoint(l,this.transformMatrix)),this.transformMatrix=null,i&&(this.scaleX*=i.scaleX,this.scaleY*=i.scaleY,this.cropX=i.cropX,this.cropY=i.cropY,l.x+=i.offsetLeft,l.y+=i.offsetTop,this.width=i.width,this.height=i.height),this.setPositionByOrigin(l,"center","center")},clone:function(i,l){var u=this.toObject(l);this.constructor.fromObject?this.constructor.fromObject(u,i):s.Object._fromObject("Object",u,i)},cloneAsImage:function(i,l){var u=this.toCanvasElement(l);return i&&i(new s.Image(u)),this},toCanvasElement:function(i){i||(i={});var l=s.util,u=l.saveObjectTransform(this),d=this.group,g=this.shadow,v=Math.abs,m=(i.multiplier||1)*(i.enableRetinaScaling?s.devicePixelRatio:1);delete this.group,i.withoutTransform&&l.resetObjectTransform(this),i.withoutShadow&&(this.shadow=null);var y=s.util.createCanvasElement(),w=this.getBoundingRect(!0,!0),F=this.shadow,Y,z={x:0,y:0},N,q,H;F&&(N=F.blur,F.nonScaling?Y={scaleX:1,scaleY:1}:Y=this.getObjectScaling(),z.x=2*Math.round(v(F.offsetX)+N)*v(Y.scaleX),z.y=2*Math.round(v(F.offsetY)+N)*v(Y.scaleY)),q=w.width+z.x,H=w.height+z.y,y.width=Math.ceil(q),y.height=Math.ceil(H);var U=new s.StaticCanvas(y,{enableRetinaScaling:!1,renderOnAddRemove:!1,skipOffscreen:!1});i.format==="jpeg"&&(U.backgroundColor="#fff"),this.setPositionByOrigin(new s.Point(U.width/2,U.height/2),"center","center");var J=this.canvas;U.add(this);var Q=U.toCanvasElement(m||1,i);return this.shadow=g,this.set("canvas",J),d&&(this.group=d),this.set(u).setCoords(),U._objects=[],U.dispose(),U=null,Q},toDataURL:function(i){return i||(i={}),s.util.toDataURL(this.toCanvasElement(i),i.format||"png",i.quality||1)},isType:function(i){return arguments.length>1?Array.from(arguments).includes(this.type):this.type===i},complexity:function(){return 1},toJSON:function(i){return this.toObject(i)},rotate:function(i){var l=(this.originX!=="center"||this.originY!=="center")&&this.centeredRotation;return l&&this._setOriginToCenter(),this.set("angle",i),l&&this._resetOrigin(),this},centerH:function(){return this.canvas&&this.canvas.centerObjectH(this),this},viewportCenterH:function(){return this.canvas&&this.canvas.viewportCenterObjectH(this),this},centerV:function(){return this.canvas&&this.canvas.centerObjectV(this),this},viewportCenterV:function(){return this.canvas&&this.canvas.viewportCenterObjectV(this),this},center:function(){return this.canvas&&this.canvas.centerObject(this),this},viewportCenter:function(){return this.canvas&&this.canvas.viewportCenterObject(this),this},getLocalPointer:function(i,l){l=l||this.canvas.getPointer(i);var u=new s.Point(l.x,l.y),d=this._getLeftTopCoords();return this.angle&&(u=s.util.rotatePoint(u,d,t(-this.angle))),{x:u.x-d.x,y:u.y-d.y}},_setupCompositeOperation:function(i){this.globalCompositeOperation&&(i.globalCompositeOperation=this.globalCompositeOperation)},dispose:function(){s.runningAnimations&&s.runningAnimations.cancelByTarget(this)}}),s.util.createAccessors&&s.util.createAccessors(s.Object),h(s.Object.prototype,s.Observable),s.Object.NUM_FRACTION_DIGITS=2,s.Object.ENLIVEN_PROPS=["clipPath"],s.Object._fromObject=function(i,l,u,d){var g=s[i];l=a(l,!0),s.util.enlivenPatterns([l.fill,l.stroke],function(v){typeof v[0]<"u"&&(l.fill=v[0]),typeof v[1]<"u"&&(l.stroke=v[1]),s.util.enlivenObjectEnlivables(l,l,function(){var m=d?new g(l[d],l):new g(l);u&&u(m)})})},s.Object.__uid=0)}(A),function(){var c=f.util.degreesToRadians,s={left:-.5,center:0,right:.5},h={top:-.5,center:0,bottom:.5};f.util.object.extend(f.Object.prototype,{translateToGivenOrigin:function(a,e,r,t,n){var o=a.x,i=a.y,l,u,d;return typeof e=="string"?e=s[e]:e-=.5,typeof t=="string"?t=s[t]:t-=.5,l=t-e,typeof r=="string"?r=h[r]:r-=.5,typeof n=="string"?n=h[n]:n-=.5,u=n-r,(l||u)&&(d=this._getTransformedDimensions(),o=a.x+l*d.x,i=a.y+u*d.y),new f.Point(o,i)},translateToCenterPoint:function(a,e,r){var t=this.translateToGivenOrigin(a,e,r,"center","center");return this.angle?f.util.rotatePoint(t,a,c(this.angle)):t},translateToOriginPoint:function(a,e,r){var t=this.translateToGivenOrigin(a,"center","center",e,r);return this.angle?f.util.rotatePoint(t,a,c(this.angle)):t},getCenterPoint:function(){var a=new f.Point(this.left,this.top);return this.translateToCenterPoint(a,this.originX,this.originY)},getPointByOrigin:function(a,e){var r=this.getCenterPoint();return this.translateToOriginPoint(r,a,e)},toLocalPoint:function(a,e,r){var t=this.getCenterPoint(),n,o;return typeof e<"u"&&typeof r<"u"?n=this.translateToGivenOrigin(t,"center","center",e,r):n=new f.Point(this.left,this.top),o=new f.Point(a.x,a.y),this.angle&&(o=f.util.rotatePoint(o,t,-c(this.angle))),o.subtractEquals(n)},setPositionByOrigin:function(a,e,r){var t=this.translateToCenterPoint(a,e,r),n=this.translateToOriginPoint(t,this.originX,this.originY);this.set("left",n.x),this.set("top",n.y)},adjustPosition:function(a){var e=c(this.angle),r=this.getScaledWidth(),t=f.util.cos(e)*r,n=f.util.sin(e)*r,o,i;typeof this.originX=="string"?o=s[this.originX]:o=this.originX-.5,typeof a=="string"?i=s[a]:i=a-.5,this.left+=t*(i-o),this.top+=n*(i-o),this.setCoords(),this.originX=a},_setOriginToCenter:function(){this._originalOriginX=this.originX,this._originalOriginY=this.originY;var a=this.getCenterPoint();this.originX="center",this.originY="center",this.left=a.x,this.top=a.y},_resetOrigin:function(){var a=this.translateToOriginPoint(this.getCenterPoint(),this._originalOriginX,this._originalOriginY);this.originX=this._originalOriginX,this.originY=this._originalOriginY,this.left=a.x,this.top=a.y,this._originalOriginX=null,this._originalOriginY=null},_getLeftTopCoords:function(){return this.translateToOriginPoint(this.getCenterPoint(),"left","top")}})}(),function(){function c(r){return[new f.Point(r.tl.x,r.tl.y),new f.Point(r.tr.x,r.tr.y),new f.Point(r.br.x,r.br.y),new f.Point(r.bl.x,r.bl.y)]}var s=f.util,h=s.degreesToRadians,a=s.multiplyTransformMatrices,e=s.transformPoint;s.object.extend(f.Object.prototype,{oCoords:null,aCoords:null,lineCoords:null,ownMatrixCache:null,matrixCache:null,controls:{},_getCoords:function(r,t){return t?r?this.calcACoords():this.calcLineCoords():((!this.aCoords||!this.lineCoords)&&this.setCoords(!0),r?this.aCoords:this.lineCoords)},getCoords:function(r,t){return c(this._getCoords(r,t))},intersectsWithRect:function(r,t,n,o){var i=this.getCoords(n,o),l=f.Intersection.intersectPolygonRectangle(i,r,t);return l.status==="Intersection"},intersectsWithObject:function(r,t,n){var o=f.Intersection.intersectPolygonPolygon(this.getCoords(t,n),r.getCoords(t,n));return o.status==="Intersection"||r.isContainedWithinObject(this,t,n)||this.isContainedWithinObject(r,t,n)},isContainedWithinObject:function(r,t,n){for(var o=this.getCoords(t,n),i=t?r.aCoords:r.lineCoords,l=0,u=r._getImageLines(i);l<4;l++)if(!r.containsPoint(o[l],u))return!1;return!0},isContainedWithinRect:function(r,t,n,o){var i=this.getBoundingRect(n,o);return i.left>=r.x&&i.left+i.width<=t.x&&i.top>=r.y&&i.top+i.height<=t.y},containsPoint:function(r,l,n,o){var i=this._getCoords(n,o),l=l||this._getImageLines(i),u=this._findCrossPoints(r,l);return u!==0&&u%2===1},isOnScreen:function(r){if(!this.canvas)return!1;var t=this.canvas.vptCoords.tl,n=this.canvas.vptCoords.br,o=this.getCoords(!0,r);return o.some(function(i){return i.x<=n.x&&i.x>=t.x&&i.y<=n.y&&i.y>=t.y})||this.intersectsWithRect(t,n,!0,r)?!0:this._containsCenterOfCanvas(t,n,r)},_containsCenterOfCanvas:function(r,t,n){var o={x:(r.x+t.x)/2,y:(r.y+t.y)/2};return!!this.containsPoint(o,null,!0,n)},isPartiallyOnScreen:function(r){if(!this.canvas)return!1;var t=this.canvas.vptCoords.tl,n=this.canvas.vptCoords.br;if(this.intersectsWithRect(t,n,!0,r))return!0;var o=this.getCoords(!0,r).every(function(i){return(i.x>=n.x||i.x<=t.x)&&(i.y>=n.y||i.y<=t.y)});return o&&this._containsCenterOfCanvas(t,n,r)},_getImageLines:function(r){var t={topline:{o:r.tl,d:r.tr},rightline:{o:r.tr,d:r.br},bottomline:{o:r.br,d:r.bl},leftline:{o:r.bl,d:r.tl}};return t},_findCrossPoints:function(r,t){var n,o,i,l,u,d=0,g;for(var v in t)if(g=t[v],!(g.o.y<r.y&&g.d.y<r.y)&&!(g.o.y>=r.y&&g.d.y>=r.y)&&(g.o.x===g.d.x&&g.o.x>=r.x?u=g.o.x:(n=0,o=(g.d.y-g.o.y)/(g.d.x-g.o.x),i=r.y-n*r.x,l=g.o.y-o*g.o.x,u=-(i-l)/(n-o)),u>=r.x&&(d+=1),d===2))break;return d},getBoundingRect:function(r,t){var n=this.getCoords(r,t);return s.makeBoundingBoxFromPoints(n)},getScaledWidth:function(){return this._getTransformedDimensions().x},getScaledHeight:function(){return this._getTransformedDimensions().y},_constrainScale:function(r){return Math.abs(r)<this.minScaleLimit?r<0?-this.minScaleLimit:this.minScaleLimit:r===0?1e-4:r},scale:function(r){return this._set("scaleX",r),this._set("scaleY",r),this.setCoords()},scaleToWidth:function(r,t){var n=this.getBoundingRect(t).width/this.getScaledWidth();return this.scale(r/this.width/n)},scaleToHeight:function(r,t){var n=this.getBoundingRect(t).height/this.getScaledHeight();return this.scale(r/this.height/n)},calcLineCoords:function(){var r=this.getViewportTransform(),t=this.padding,n=h(this.angle),o=s.cos(n),i=s.sin(n),l=o*t,u=i*t,d=l+u,g=l-u,v=this.calcACoords(),m={tl:e(v.tl,r),tr:e(v.tr,r),bl:e(v.bl,r),br:e(v.br,r)};return t&&(m.tl.x-=g,m.tl.y-=d,m.tr.x+=d,m.tr.y-=g,m.bl.x-=d,m.bl.y+=g,m.br.x+=g,m.br.y+=d),m},calcOCoords:function(){var r=this._calcRotateMatrix(),t=this._calcTranslateMatrix(),n=this.getViewportTransform(),o=a(n,t),i=a(o,r),i=a(i,[1/n[0],0,0,1/n[3],0,0]),l=this._calculateCurrentDimensions(),u={};return this.forEachControl(function(d,g,v){u[g]=d.positionHandler(l,i,v)}),u},calcACoords:function(){var r=this._calcRotateMatrix(),t=this._calcTranslateMatrix(),n=a(t,r),o=this._getTransformedDimensions(),i=o.x/2,l=o.y/2;return{tl:e({x:-i,y:-l},n),tr:e({x:i,y:-l},n),bl:e({x:-i,y:l},n),br:e({x:i,y:l},n)}},setCoords:function(r){return this.aCoords=this.calcACoords(),this.lineCoords=this.group?this.aCoords:this.calcLineCoords(),r?this:(this.oCoords=this.calcOCoords(),this._setCornerCoords&&this._setCornerCoords(),this)},_calcRotateMatrix:function(){return s.calcRotateMatrix(this)},_calcTranslateMatrix:function(){var r=this.getCenterPoint();return[1,0,0,1,r.x,r.y]},transformMatrixKey:function(r){var t="_",n="";return!r&&this.group&&(n=this.group.transformMatrixKey(r)+t),n+this.top+t+this.left+t+this.scaleX+t+this.scaleY+t+this.skewX+t+this.skewY+t+this.angle+t+this.originX+t+this.originY+t+this.width+t+this.height+t+this.strokeWidth+this.flipX+this.flipY},calcTransformMatrix:function(r){var t=this.calcOwnMatrix();if(r||!this.group)return t;var n=this.transformMatrixKey(r),o=this.matrixCache||(this.matrixCache={});return o.key===n?o.value:(this.group&&(t=a(this.group.calcTransformMatrix(!1),t)),o.key=n,o.value=t,t)},calcOwnMatrix:function(){var r=this.transformMatrixKey(!0),t=this.ownMatrixCache||(this.ownMatrixCache={});if(t.key===r)return t.value;var n=this._calcTranslateMatrix(),o={angle:this.angle,translateX:n[4],translateY:n[5],scaleX:this.scaleX,scaleY:this.scaleY,skewX:this.skewX,skewY:this.skewY,flipX:this.flipX,flipY:this.flipY};return t.key=r,t.value=s.composeMatrix(o),t.value},_getNonTransformedDimensions:function(){var r=this.strokeWidth,t=this.width+r,n=this.height+r;return{x:t,y:n}},_getTransformedDimensions:function(r,t){typeof r>"u"&&(r=this.skewX),typeof t>"u"&&(t=this.skewY);var n,o,i,l=r===0&&t===0;if(this.strokeUniform?(o=this.width,i=this.height):(n=this._getNonTransformedDimensions(),o=n.x,i=n.y),l)return this._finalizeDimensions(o*this.scaleX,i*this.scaleY);var u=s.sizeAfterTransform(o,i,{scaleX:this.scaleX,scaleY:this.scaleY,skewX:r,skewY:t});return this._finalizeDimensions(u.x,u.y)},_finalizeDimensions:function(r,t){return this.strokeUniform?{x:r+this.strokeWidth,y:t+this.strokeWidth}:{x:r,y:t}},_calculateCurrentDimensions:function(){var r=this.getViewportTransform(),t=this._getTransformedDimensions(),n=e(t,r,!0);return n.scalarAdd(2*this.padding)}})}(),f.util.object.extend(f.Object.prototype,{sendToBack:function(){return this.group?f.StaticCanvas.prototype.sendToBack.call(this.group,this):this.canvas&&this.canvas.sendToBack(this),this},bringToFront:function(){return this.group?f.StaticCanvas.prototype.bringToFront.call(this.group,this):this.canvas&&this.canvas.bringToFront(this),this},sendBackwards:function(c){return this.group?f.StaticCanvas.prototype.sendBackwards.call(this.group,this,c):this.canvas&&this.canvas.sendBackwards(this,c),this},bringForward:function(c){return this.group?f.StaticCanvas.prototype.bringForward.call(this.group,this,c):this.canvas&&this.canvas.bringForward(this,c),this},moveTo:function(c){return this.group&&this.group.type!=="activeSelection"?f.StaticCanvas.prototype.moveTo.call(this.group,this,c):this.canvas&&this.canvas.moveTo(this,c),this}}),function(){function c(h,a){if(a){if(a.toLive)return h+": url(#SVGID_"+a.id+"); ";var e=new f.Color(a),r=h+": "+e.toRgb()+"; ",t=e.getAlpha();return t!==1&&(r+=h+"-opacity: "+t.toString()+"; "),r}else return h+": none; "}var s=f.util.toFixed;f.util.object.extend(f.Object.prototype,{getSvgStyles:function(h){var a=this.fillRule?this.fillRule:"nonzero",e=this.strokeWidth?this.strokeWidth:"0",r=this.strokeDashArray?this.strokeDashArray.join(" "):"none",t=this.strokeDashOffset?this.strokeDashOffset:"0",n=this.strokeLineCap?this.strokeLineCap:"butt",o=this.strokeLineJoin?this.strokeLineJoin:"miter",i=this.strokeMiterLimit?this.strokeMiterLimit:"4",l=typeof this.opacity<"u"?this.opacity:"1",u=this.visible?"":" visibility: hidden;",d=h?"":this.getSvgFilter(),g=c("fill",this.fill),v=c("stroke",this.stroke);return[v,"stroke-width: ",e,"; ","stroke-dasharray: ",r,"; ","stroke-linecap: ",n,"; ","stroke-dashoffset: ",t,"; ","stroke-linejoin: ",o,"; ","stroke-miterlimit: ",i,"; ",g,"fill-rule: ",a,"; ","opacity: ",l,";",d,u].join("")},getSvgSpanStyles:function(h,a){var e="; ",t=h.fontFamily?"font-family: "+(h.fontFamily.indexOf("'")===-1&&h.fontFamily.indexOf('"')===-1?"'"+h.fontFamily+"'":h.fontFamily)+e:"",r=h.strokeWidth?"stroke-width: "+h.strokeWidth+e:"",t=t,n=h.fontSize?"font-size: "+h.fontSize+"px"+e:"",o=h.fontStyle?"font-style: "+h.fontStyle+e:"",i=h.fontWeight?"font-weight: "+h.fontWeight+e:"",l=h.fill?c("fill",h.fill):"",u=h.stroke?c("stroke",h.stroke):"",d=this.getSvgTextDecoration(h),g=h.deltaY?"baseline-shift: "+-h.deltaY+"; ":"";return d&&(d="text-decoration: "+d+e),[u,r,t,n,o,i,d,l,g,a?"white-space: pre; ":""].join("")},getSvgTextDecoration:function(h){return["overline","underline","line-through"].filter(function(a){return h[a.replace("-","")]}).join(" ")},getSvgFilter:function(){return this.shadow?"filter: url(#SVGID_"+this.shadow.id+");":""},getSvgCommons:function(){return[this.id?'id="'+this.id+'" ':"",this.clipPath?'clip-path="url(#'+this.clipPath.clipPathId+')" ':""].join("")},getSvgTransform:function(h,a){var e=h?this.calcTransformMatrix():this.calcOwnMatrix(),r='transform="'+f.util.matrixToSVG(e);return r+(a||"")+'" '},_setSVGBg:function(h){if(this.backgroundColor){var a=f.Object.NUM_FRACTION_DIGITS;h.push("		<rect ",this._getFillAttributes(this.backgroundColor),' x="',s(-this.width/2,a),'" y="',s(-this.height/2,a),'" width="',s(this.width,a),'" height="',s(this.height,a),`"></rect>
`)}},toSVG:function(h){return this._createBaseSVGMarkup(this._toSVG(h),{reviver:h})},toClipPathSVG:function(h){return"	"+this._createBaseClipPathSVGMarkup(this._toSVG(h),{reviver:h})},_createBaseClipPathSVGMarkup:function(h,a){a=a||{};var e=a.reviver,r=a.additionalTransform||"",t=[this.getSvgTransform(!0,r),this.getSvgCommons()].join(""),n=h.indexOf("COMMON_PARTS");return h[n]=t,e?e(h.join("")):h.join("")},_createBaseSVGMarkup:function(h,a){a=a||{};var e=a.noStyle,r=a.reviver,t=e?"":'style="'+this.getSvgStyles()+'" ',n=a.withShadow?'style="'+this.getSvgFilter()+'" ':"",o=this.clipPath,i=this.strokeUniform?'vector-effect="non-scaling-stroke" ':"",l=o&&o.absolutePositioned,u=this.stroke,d=this.fill,g=this.shadow,v,m=[],y,w=h.indexOf("COMMON_PARTS"),F=a.additionalTransform;return o&&(o.clipPathId="CLIPPATH_"+f.Object.__uid++,y='<clipPath id="'+o.clipPathId+`" >
`+o.toClipPathSVG(r)+`</clipPath>
`),l&&m.push("<g ",n,this.getSvgCommons(),` >
`),m.push("<g ",this.getSvgTransform(!1),l?"":n+this.getSvgCommons(),` >
`),v=[t,i,e?"":this.addPaintOrder()," ",F?'transform="'+F+'" ':""].join(""),h[w]=v,d&&d.toLive&&m.push(d.toSVG(this)),u&&u.toLive&&m.push(u.toSVG(this)),g&&m.push(g.toSVG(this)),o&&m.push(y),m.push(h.join("")),m.push(`</g>
`),l&&m.push(`</g>
`),r?r(m.join("")):m.join("")},addPaintOrder:function(){return this.paintFirst!=="fill"?' paint-order="'+this.paintFirst+'" ':""}})}(),function(){var c=f.util.object.extend,s="stateProperties";function h(e,r,t){var n={},o=!0;t.forEach(function(i){n[i]=e[i]}),c(e[r],n,o)}function a(e,r,t){if(e===r)return!0;if(Array.isArray(e)){if(!Array.isArray(r)||e.length!==r.length)return!1;for(var n=0,o=e.length;n<o;n++)if(!a(e[n],r[n]))return!1;return!0}else if(e&&typeof e=="object"){var i=Object.keys(e),l;if(!r||typeof r!="object"||!t&&i.length!==Object.keys(r).length)return!1;for(var n=0,o=i.length;n<o;n++)if(l=i[n],!(l==="canvas"||l==="group")&&!a(e[l],r[l]))return!1;return!0}}f.util.object.extend(f.Object.prototype,{hasStateChanged:function(e){e=e||s;var r="_"+e;return Object.keys(this[r]).length<this[e].length?!0:!a(this[r],this,!0)},saveState:function(e){var r=e&&e.propertySet||s,t="_"+r;return this[t]?(h(this,t,this[r]),e&&e.stateProperties&&h(this,t,e.stateProperties),this):this.setupState(e)},setupState:function(e){e=e||{};var r=e.propertySet||s;return e.propertySet=r,this["_"+r]={},this.saveState(e),this}})}(),function(){var c=f.util.degreesToRadians;f.util.object.extend(f.Object.prototype,{_findTargetCorner:function(s,h){if(!this.hasControls||this.group||!this.canvas||this.canvas._activeObject!==this)return!1;var a=s.x,e=s.y,r,t,n=Object.keys(this.oCoords),o=n.length-1,i;for(this.__corner=0;o>=0;o--)if(i=n[o],!!this.isControlVisible(i)&&(t=this._getImageLines(h?this.oCoords[i].touchCorner:this.oCoords[i].corner),r=this._findCrossPoints({x:a,y:e},t),r!==0&&r%2===1))return this.__corner=i,i;return!1},forEachControl:function(s){for(var h in this.controls)s(this.controls[h],h,this)},_setCornerCoords:function(){var s=this.oCoords;for(var h in s){var a=this.controls[h];s[h].corner=a.calcCornerCoords(this.angle,this.cornerSize,s[h].x,s[h].y,!1),s[h].touchCorner=a.calcCornerCoords(this.angle,this.touchCornerSize,s[h].x,s[h].y,!0)}},drawSelectionBackground:function(s){if(!this.selectionBackgroundColor||this.canvas&&!this.canvas.interactive||this.canvas&&this.canvas._activeObject!==this)return this;s.save();var h=this.getCenterPoint(),a=this._calculateCurrentDimensions(),e=this.canvas.viewportTransform;return s.translate(h.x,h.y),s.scale(1/e[0],1/e[3]),s.rotate(c(this.angle)),s.fillStyle=this.selectionBackgroundColor,s.fillRect(-a.x/2,-a.y/2,a.x,a.y),s.restore(),this},drawBorders:function(s,h){h=h||{};var a=this._calculateCurrentDimensions(),e=this.borderScaleFactor,r=a.x+e,t=a.y+e,n=typeof h.hasControls<"u"?h.hasControls:this.hasControls,o=!1;return s.save(),s.strokeStyle=h.borderColor||this.borderColor,this._setLineDash(s,h.borderDashArray||this.borderDashArray),s.strokeRect(-r/2,-t/2,r,t),n&&(s.beginPath(),this.forEachControl(function(i,l,u){i.withConnection&&i.getVisibility(u,l)&&(o=!0,s.moveTo(i.x*r,i.y*t),s.lineTo(i.x*r+i.offsetX,i.y*t+i.offsetY))}),o&&s.stroke()),s.restore(),this},drawBordersInGroup:function(s,h,a){a=a||{};var e=f.util.sizeAfterTransform(this.width,this.height,h),r=this.strokeWidth,t=this.strokeUniform,n=this.borderScaleFactor,o=e.x+r*(t?this.canvas.getZoom():h.scaleX)+n,i=e.y+r*(t?this.canvas.getZoom():h.scaleY)+n;return s.save(),this._setLineDash(s,a.borderDashArray||this.borderDashArray),s.strokeStyle=a.borderColor||this.borderColor,s.strokeRect(-o/2,-i/2,o,i),s.restore(),this},drawControls:function(s,h){h=h||{},s.save();var a=this.canvas.getRetinaScaling(),e,r;return s.setTransform(a,0,0,a,0,0),s.strokeStyle=s.fillStyle=h.cornerColor||this.cornerColor,this.transparentCorners||(s.strokeStyle=h.cornerStrokeColor||this.cornerStrokeColor),this._setLineDash(s,h.cornerDashArray||this.cornerDashArray),this.setCoords(),this.group&&(e=this.group.calcTransformMatrix()),this.forEachControl(function(t,n,o){r=o.oCoords[n],t.getVisibility(o,n)&&(e&&(r=f.util.transformPoint(r,e)),t.render(s,r.x,r.y,h,o))}),s.restore(),this},isControlVisible:function(s){return this.controls[s]&&this.controls[s].getVisibility(this,s)},setControlVisible:function(s,h){return this._controlsVisibility||(this._controlsVisibility={}),this._controlsVisibility[s]=h,this},setControlsVisibility:function(s){s||(s={});for(var h in s)this.setControlVisible(h,s[h]);return this},onDeselect:function(){},onSelect:function(){}})}(),f.util.object.extend(f.StaticCanvas.prototype,{FX_DURATION:500,fxCenterObjectH:function(c,s){s=s||{};var h=function(){},a=s.onComplete||h,e=s.onChange||h,r=this;return f.util.animate({target:this,startValue:c.left,endValue:this.getCenterPoint().x,duration:this.FX_DURATION,onChange:function(t){c.set("left",t),r.requestRenderAll(),e()},onComplete:function(){c.setCoords(),a()}})},fxCenterObjectV:function(c,s){s=s||{};var h=function(){},a=s.onComplete||h,e=s.onChange||h,r=this;return f.util.animate({target:this,startValue:c.top,endValue:this.getCenterPoint().y,duration:this.FX_DURATION,onChange:function(t){c.set("top",t),r.requestRenderAll(),e()},onComplete:function(){c.setCoords(),a()}})},fxRemove:function(c,s){s=s||{};var h=function(){},a=s.onComplete||h,e=s.onChange||h,r=this;return f.util.animate({target:this,startValue:c.opacity,endValue:0,duration:this.FX_DURATION,onChange:function(t){c.set("opacity",t),r.requestRenderAll(),e()},onComplete:function(){r.remove(c),a()}})}}),f.util.object.extend(f.Object.prototype,{animate:function(){if(arguments[0]&&typeof arguments[0]=="object"){var c=[],s,h,a=[];for(s in arguments[0])c.push(s);for(var e=0,r=c.length;e<r;e++)s=c[e],h=e!==r-1,a.push(this._animate(s,arguments[0][s],arguments[1],h));return a}else return this._animate.apply(this,arguments)},_animate:function(c,s,h,a){var e=this,r;s=s.toString(),h?h=f.util.object.clone(h):h={},~c.indexOf(".")&&(r=c.split("."));var t=e.colorProperties.indexOf(c)>-1||r&&e.colorProperties.indexOf(r[1])>-1,n=r?this.get(r[0])[r[1]]:this.get(c);"from"in h||(h.from=n),t||(~s.indexOf("=")?s=n+parseFloat(s.replace("=","")):s=parseFloat(s));var o={target:this,startValue:h.from,endValue:s,byValue:h.by,easing:h.easing,duration:h.duration,abort:h.abort&&function(i,l,u){return h.abort.call(e,i,l,u)},onChange:function(i,l,u){r?e[r[0]][r[1]]=i:e.set(c,i),!a&&h.onChange&&h.onChange(i,l,u)},onComplete:function(i,l,u){a||(e.setCoords(),h.onComplete&&h.onComplete(i,l,u))}};return t?f.util.animateColor(o.startValue,o.endValue,o.duration,o):f.util.animate(o)}}),function(c){var s=c.fabric||(c.fabric={}),h=s.util.object.extend,a=s.util.object.clone,e={x1:1,x2:1,y1:1,y2:1};if(s.Line){s.warn("fabric.Line is already defined");return}s.Line=s.util.createClass(s.Object,{type:"line",x1:0,y1:0,x2:0,y2:0,cacheProperties:s.Object.prototype.cacheProperties.concat("x1","x2","y1","y2"),initialize:function(t,n){t||(t=[0,0,0,0]),this.callSuper("initialize",n),this.set("x1",t[0]),this.set("y1",t[1]),this.set("x2",t[2]),this.set("y2",t[3]),this._setWidthHeight(n)},_setWidthHeight:function(t){t||(t={}),this.width=Math.abs(this.x2-this.x1),this.height=Math.abs(this.y2-this.y1),this.left="left"in t?t.left:this._getLeftToOriginX(),this.top="top"in t?t.top:this._getTopToOriginY()},_set:function(t,n){return this.callSuper("_set",t,n),typeof e[t]<"u"&&this._setWidthHeight(),this},_getLeftToOriginX:r({origin:"originX",axis1:"x1",axis2:"x2",dimension:"width"},{nearest:"left",center:"center",farthest:"right"}),_getTopToOriginY:r({origin:"originY",axis1:"y1",axis2:"y2",dimension:"height"},{nearest:"top",center:"center",farthest:"bottom"}),_render:function(t){t.beginPath();var n=this.calcLinePoints();t.moveTo(n.x1,n.y1),t.lineTo(n.x2,n.y2),t.lineWidth=this.strokeWidth;var o=t.strokeStyle;t.strokeStyle=this.stroke||t.fillStyle,this.stroke&&this._renderStroke(t),t.strokeStyle=o},_findCenterFromElement:function(){return{x:(this.x1+this.x2)/2,y:(this.y1+this.y2)/2}},toObject:function(t){return h(this.callSuper("toObject",t),this.calcLinePoints())},_getNonTransformedDimensions:function(){var t=this.callSuper("_getNonTransformedDimensions");return this.strokeLineCap==="butt"&&(this.width===0&&(t.y-=this.strokeWidth),this.height===0&&(t.x-=this.strokeWidth)),t},calcLinePoints:function(){var t=this.x1<=this.x2?-1:1,n=this.y1<=this.y2?-1:1,o=t*this.width*.5,i=n*this.height*.5,l=t*this.width*-.5,u=n*this.height*-.5;return{x1:o,x2:l,y1:i,y2:u}},_toSVG:function(){var t=this.calcLinePoints();return["<line ","COMMON_PARTS",'x1="',t.x1,'" y1="',t.y1,'" x2="',t.x2,'" y2="',t.y2,`" />
`]}}),s.Line.ATTRIBUTE_NAMES=s.SHARED_ATTRIBUTES.concat("x1 y1 x2 y2".split(" ")),s.Line.fromElement=function(t,n,o){o=o||{};var i=s.parseAttributes(t,s.Line.ATTRIBUTE_NAMES),l=[i.x1||0,i.y1||0,i.x2||0,i.y2||0];n(new s.Line(l,h(i,o)))},s.Line.fromObject=function(t,n){function o(l){delete l.points,n&&n(l)}var i=a(t,!0);i.points=[t.x1,t.y1,t.x2,t.y2],s.Object._fromObject("Line",i,o,"points")};function r(t,n){var o=t.origin,i=t.axis1,l=t.axis2,u=t.dimension,d=n.nearest,g=n.center,v=n.farthest;return function(){switch(this.get(o)){case d:return Math.min(this.get(i),this.get(l));case g:return Math.min(this.get(i),this.get(l))+.5*this.get(u);case v:return Math.max(this.get(i),this.get(l))}}}}(A),function(c){var s=c.fabric||(c.fabric={}),h=s.util.degreesToRadians;if(s.Circle){s.warn("fabric.Circle is already defined.");return}s.Circle=s.util.createClass(s.Object,{type:"circle",radius:0,startAngle:0,endAngle:360,cacheProperties:s.Object.prototype.cacheProperties.concat("radius","startAngle","endAngle"),_set:function(e,r){return this.callSuper("_set",e,r),e==="radius"&&this.setRadius(r),this},toObject:function(e){return this.callSuper("toObject",["radius","startAngle","endAngle"].concat(e))},_toSVG:function(){var e,r=0,t=0,n=(this.endAngle-this.startAngle)%360;if(n===0)e=["<circle ","COMMON_PARTS",'cx="'+r+'" cy="'+t+'" ','r="',this.radius,`" />
`];else{var o=h(this.startAngle),i=h(this.endAngle),l=this.radius,u=s.util.cos(o)*l,d=s.util.sin(o)*l,g=s.util.cos(i)*l,v=s.util.sin(i)*l,m=n>180?"1":"0";e=['<path d="M '+u+" "+d," A "+l+" "+l," 0 ",+m+" 1"," "+g+" "+v,'" ',"COMMON_PARTS",` />
`]}return e},_render:function(e){e.beginPath(),e.arc(0,0,this.radius,h(this.startAngle),h(this.endAngle),!1),this._renderPaintInOrder(e)},getRadiusX:function(){return this.get("radius")*this.get("scaleX")},getRadiusY:function(){return this.get("radius")*this.get("scaleY")},setRadius:function(e){return this.radius=e,this.set("width",e*2).set("height",e*2)}}),s.Circle.ATTRIBUTE_NAMES=s.SHARED_ATTRIBUTES.concat("cx cy r".split(" ")),s.Circle.fromElement=function(e,r){var t=s.parseAttributes(e,s.Circle.ATTRIBUTE_NAMES);if(!a(t))throw new Error("value of `r` attribute is required and can not be negative");t.left=(t.left||0)-t.radius,t.top=(t.top||0)-t.radius,r(new s.Circle(t))};function a(e){return"radius"in e&&e.radius>=0}s.Circle.fromObject=function(e,r){s.Object._fromObject("Circle",e,r)}}(A),function(c){var s=c.fabric||(c.fabric={});if(s.Triangle){s.warn("fabric.Triangle is already defined");return}s.Triangle=s.util.createClass(s.Object,{type:"triangle",width:100,height:100,_render:function(h){var a=this.width/2,e=this.height/2;h.beginPath(),h.moveTo(-a,e),h.lineTo(0,-e),h.lineTo(a,e),h.closePath(),this._renderPaintInOrder(h)},_toSVG:function(){var h=this.width/2,a=this.height/2,e=[-h+" "+a,"0 "+-a,h+" "+a].join(",");return["<polygon ","COMMON_PARTS",'points="',e,'" />']}}),s.Triangle.fromObject=function(h,a){return s.Object._fromObject("Triangle",h,a)}}(A),function(c){var s=c.fabric||(c.fabric={}),h=Math.PI*2;if(s.Ellipse){s.warn("fabric.Ellipse is already defined.");return}s.Ellipse=s.util.createClass(s.Object,{type:"ellipse",rx:0,ry:0,cacheProperties:s.Object.prototype.cacheProperties.concat("rx","ry"),initialize:function(a){this.callSuper("initialize",a),this.set("rx",a&&a.rx||0),this.set("ry",a&&a.ry||0)},_set:function(a,e){switch(this.callSuper("_set",a,e),a){case"rx":this.rx=e,this.set("width",e*2);break;case"ry":this.ry=e,this.set("height",e*2);break}return this},getRx:function(){return this.get("rx")*this.get("scaleX")},getRy:function(){return this.get("ry")*this.get("scaleY")},toObject:function(a){return this.callSuper("toObject",["rx","ry"].concat(a))},_toSVG:function(){return["<ellipse ","COMMON_PARTS",'cx="0" cy="0" ','rx="',this.rx,'" ry="',this.ry,`" />
`]},_render:function(a){a.beginPath(),a.save(),a.transform(1,0,0,this.ry/this.rx,0,0),a.arc(0,0,this.rx,0,h,!1),a.restore(),this._renderPaintInOrder(a)}}),s.Ellipse.ATTRIBUTE_NAMES=s.SHARED_ATTRIBUTES.concat("cx cy rx ry".split(" ")),s.Ellipse.fromElement=function(a,e){var r=s.parseAttributes(a,s.Ellipse.ATTRIBUTE_NAMES);r.left=(r.left||0)-r.rx,r.top=(r.top||0)-r.ry,e(new s.Ellipse(r))},s.Ellipse.fromObject=function(a,e){s.Object._fromObject("Ellipse",a,e)}}(A),function(c){var s=c.fabric||(c.fabric={}),h=s.util.object.extend;if(s.Rect){s.warn("fabric.Rect is already defined");return}s.Rect=s.util.createClass(s.Object,{stateProperties:s.Object.prototype.stateProperties.concat("rx","ry"),type:"rect",rx:0,ry:0,cacheProperties:s.Object.prototype.cacheProperties.concat("rx","ry"),initialize:function(a){this.callSuper("initialize",a),this._initRxRy()},_initRxRy:function(){this.rx&&!this.ry?this.ry=this.rx:this.ry&&!this.rx&&(this.rx=this.ry)},_render:function(a){var e=this.rx?Math.min(this.rx,this.width/2):0,r=this.ry?Math.min(this.ry,this.height/2):0,t=this.width,n=this.height,o=-this.width/2,i=-this.height/2,l=e!==0||r!==0,u=1-.5522847498;a.beginPath(),a.moveTo(o+e,i),a.lineTo(o+t-e,i),l&&a.bezierCurveTo(o+t-u*e,i,o+t,i+u*r,o+t,i+r),a.lineTo(o+t,i+n-r),l&&a.bezierCurveTo(o+t,i+n-u*r,o+t-u*e,i+n,o+t-e,i+n),a.lineTo(o+e,i+n),l&&a.bezierCurveTo(o+u*e,i+n,o,i+n-u*r,o,i+n-r),a.lineTo(o,i+r),l&&a.bezierCurveTo(o,i+u*r,o+u*e,i,o+e,i),a.closePath(),this._renderPaintInOrder(a)},toObject:function(a){return this.callSuper("toObject",["rx","ry"].concat(a))},_toSVG:function(){var a=-this.width/2,e=-this.height/2;return["<rect ","COMMON_PARTS",'x="',a,'" y="',e,'" rx="',this.rx,'" ry="',this.ry,'" width="',this.width,'" height="',this.height,`" />
`]}}),s.Rect.ATTRIBUTE_NAMES=s.SHARED_ATTRIBUTES.concat("x y rx ry width height".split(" ")),s.Rect.fromElement=function(a,e,r){if(!a)return e(null);r=r||{};var t=s.parseAttributes(a,s.Rect.ATTRIBUTE_NAMES);t.left=t.left||0,t.top=t.top||0,t.height=t.height||0,t.width=t.width||0;var n=new s.Rect(h(r?s.util.object.clone(r):{},t));n.visible=n.visible&&n.width>0&&n.height>0,e(n)},s.Rect.fromObject=function(a,e){return s.Object._fromObject("Rect",a,e)}}(A),function(c){var s=c.fabric||(c.fabric={}),h=s.util.object.extend,a=s.util.array.min,e=s.util.array.max,r=s.util.toFixed,t=s.util.projectStrokeOnPoints;if(s.Polyline){s.warn("fabric.Polyline is already defined");return}s.Polyline=s.util.createClass(s.Object,{type:"polyline",points:null,exactBoundingBox:!1,cacheProperties:s.Object.prototype.cacheProperties.concat("points"),initialize:function(n,o){o=o||{},this.points=n||[],this.callSuper("initialize",o),this._setPositionDimensions(o)},_projectStrokeOnPoints:function(){return t(this.points,this,!0)},_setPositionDimensions:function(n){var o=this._calcDimensions(n),i,l=this.exactBoundingBox?this.strokeWidth:0;this.width=o.width-l,this.height=o.height-l,n.fromSVG||(i=this.translateToGivenOrigin({x:o.left-this.strokeWidth/2+l/2,y:o.top-this.strokeWidth/2+l/2},"left","top",this.originX,this.originY)),typeof n.left>"u"&&(this.left=n.fromSVG?o.left:i.x),typeof n.top>"u"&&(this.top=n.fromSVG?o.top:i.y),this.pathOffset={x:o.left+this.width/2+l/2,y:o.top+this.height/2+l/2}},_calcDimensions:function(){var n=this.exactBoundingBox?this._projectStrokeOnPoints():this.points,o=a(n,"x")||0,i=a(n,"y")||0,l=e(n,"x")||0,u=e(n,"y")||0,d=l-o,g=u-i;return{left:o,top:i,width:d,height:g}},toObject:function(n){return h(this.callSuper("toObject",n),{points:this.points.concat()})},_toSVG:function(){for(var n=[],o=this.pathOffset.x,i=this.pathOffset.y,l=s.Object.NUM_FRACTION_DIGITS,u=0,d=this.points.length;u<d;u++)n.push(r(this.points[u].x-o,l),",",r(this.points[u].y-i,l)," ");return["<"+this.type+" ","COMMON_PARTS",'points="',n.join(""),`" />
`]},commonRender:function(n){var o,i=this.points.length,l=this.pathOffset.x,u=this.pathOffset.y;if(!i||isNaN(this.points[i-1].y))return!1;n.beginPath(),n.moveTo(this.points[0].x-l,this.points[0].y-u);for(var d=0;d<i;d++)o=this.points[d],n.lineTo(o.x-l,o.y-u);return!0},_render:function(n){this.commonRender(n)&&this._renderPaintInOrder(n)},complexity:function(){return this.get("points").length}}),s.Polyline.ATTRIBUTE_NAMES=s.SHARED_ATTRIBUTES.concat(),s.Polyline.fromElementGenerator=function(n){return function(o,i,l){if(!o)return i(null);l||(l={});var u=s.parsePointsAttribute(o.getAttribute("points")),d=s.parseAttributes(o,s[n].ATTRIBUTE_NAMES);d.fromSVG=!0,i(new s[n](u,h(d,l)))}},s.Polyline.fromElement=s.Polyline.fromElementGenerator("Polyline"),s.Polyline.fromObject=function(n,o){return s.Object._fromObject("Polyline",n,o,"points")}}(A),function(c){var s=c.fabric||(c.fabric={}),h=s.util.projectStrokeOnPoints;if(s.Polygon){s.warn("fabric.Polygon is already defined");return}s.Polygon=s.util.createClass(s.Polyline,{type:"polygon",_projectStrokeOnPoints:function(){return h(this.points,this)},_render:function(a){this.commonRender(a)&&(a.closePath(),this._renderPaintInOrder(a))}}),s.Polygon.ATTRIBUTE_NAMES=s.SHARED_ATTRIBUTES.concat(),s.Polygon.fromElement=s.Polyline.fromElementGenerator("Polygon"),s.Polygon.fromObject=function(a,e){s.Object._fromObject("Polygon",a,e,"points")}}(A),function(c){var s=c.fabric||(c.fabric={}),h=s.util.array.min,a=s.util.array.max,e=s.util.object.extend,r=s.util.object.clone,t=s.util.toFixed;if(s.Path){s.warn("fabric.Path is already defined");return}s.Path=s.util.createClass(s.Object,{type:"path",path:null,cacheProperties:s.Object.prototype.cacheProperties.concat("path","fillRule"),stateProperties:s.Object.prototype.stateProperties.concat("path"),initialize:function(n,o){o=r(o||{}),delete o.path,this.callSuper("initialize",o),this._setPath(n||[],o)},_setPath:function(n,o){this.path=s.util.makePathSimpler(Array.isArray(n)?n:s.util.parsePath(n)),s.Polyline.prototype._setPositionDimensions.call(this,o||{})},_renderPathCommands:function(n){var o,i=0,l=0,u=0,d=0,g=0,v=0,m=-this.pathOffset.x,y=-this.pathOffset.y;n.beginPath();for(var w=0,F=this.path.length;w<F;++w)switch(o=this.path[w],o[0]){case"L":u=o[1],d=o[2],n.lineTo(u+m,d+y);break;case"M":u=o[1],d=o[2],i=u,l=d,n.moveTo(u+m,d+y);break;case"C":u=o[5],d=o[6],g=o[3],v=o[4],n.bezierCurveTo(o[1]+m,o[2]+y,g+m,v+y,u+m,d+y);break;case"Q":n.quadraticCurveTo(o[1]+m,o[2]+y,o[3]+m,o[4]+y),u=o[3],d=o[4],g=o[1],v=o[2];break;case"z":case"Z":u=i,d=l,n.closePath();break}},_render:function(n){this._renderPathCommands(n),this._renderPaintInOrder(n)},toString:function(){return"#<fabric.Path ("+this.complexity()+'): { "top": '+this.top+', "left": '+this.left+" }>"},toObject:function(n){return e(this.callSuper("toObject",n),{path:this.path.map(function(o){return o.slice()})})},toDatalessObject:function(n){var o=this.toObject(["sourcePath"].concat(n));return o.sourcePath&&delete o.path,o},_toSVG:function(){var n=s.util.joinPath(this.path);return["<path ","COMMON_PARTS",'d="',n,'" stroke-linecap="round" ',`/>
`]},_getOffsetTransform:function(){var n=s.Object.NUM_FRACTION_DIGITS;return" translate("+t(-this.pathOffset.x,n)+", "+t(-this.pathOffset.y,n)+")"},toClipPathSVG:function(n){var o=this._getOffsetTransform();return"	"+this._createBaseClipPathSVGMarkup(this._toSVG(),{reviver:n,additionalTransform:o})},toSVG:function(n){var o=this._getOffsetTransform();return this._createBaseSVGMarkup(this._toSVG(),{reviver:n,additionalTransform:o})},complexity:function(){return this.path.length},_calcDimensions:function(){for(var n=[],o=[],i,l=0,u=0,d=0,g=0,v,m=0,y=this.path.length;m<y;++m){switch(i=this.path[m],i[0]){case"L":d=i[1],g=i[2],v=[];break;case"M":d=i[1],g=i[2],l=d,u=g,v=[];break;case"C":v=s.util.getBoundsOfCurve(d,g,i[1],i[2],i[3],i[4],i[5],i[6]),d=i[5],g=i[6];break;case"Q":v=s.util.getBoundsOfCurve(d,g,i[1],i[2],i[1],i[2],i[3],i[4]),d=i[3],g=i[4];break;case"z":case"Z":d=l,g=u;break}v.forEach(function(H){n.push(H.x),o.push(H.y)}),n.push(d),o.push(g)}var w=h(n)||0,F=h(o)||0,Y=a(n)||0,z=a(o)||0,N=Y-w,q=z-F;return{left:w,top:F,width:N,height:q}}}),s.Path.fromObject=function(n,o){if(typeof n.sourcePath=="string"){var i=n.sourcePath;s.loadSVGFromURL(i,function(l){var u=l[0];u.setOptions(n),n.clipPath?s.util.enlivenObjects([n.clipPath],function(d){u.clipPath=d[0],o&&o(u)}):o&&o(u)})}else s.Object._fromObject("Path",n,o,"path")},s.Path.ATTRIBUTE_NAMES=s.SHARED_ATTRIBUTES.concat(["d"]),s.Path.fromElement=function(n,o,i){var l=s.parseAttributes(n,s.Path.ATTRIBUTE_NAMES);l.fromSVG=!0,o(new s.Path(l.d,e(l,i)))}}(A),function(c){var s=c.fabric||(c.fabric={}),h=s.util.array.min,a=s.util.array.max;s.Group||(s.Group=s.util.createClass(s.Object,s.Collection,{type:"group",strokeWidth:0,subTargetCheck:!1,cacheProperties:[],useSetOnGroup:!1,initialize:function(e,r,t){r=r||{},this._objects=[],t&&this.callSuper("initialize",r),this._objects=e||[];for(var n=this._objects.length;n--;)this._objects[n].group=this;if(t)this._updateObjectsACoords();else{var o=r&&r.centerPoint;r.originX!==void 0&&(this.originX=r.originX),r.originY!==void 0&&(this.originY=r.originY),o||this._calcBounds(),this._updateObjectsCoords(o),delete r.centerPoint,this.callSuper("initialize",r)}this.setCoords()},_updateObjectsACoords:function(){for(var e=!0,r=this._objects.length;r--;)this._objects[r].setCoords(e)},_updateObjectsCoords:function(r){for(var r=r||this.getCenterPoint(),t=this._objects.length;t--;)this._updateObjectCoords(this._objects[t],r)},_updateObjectCoords:function(e,r){var t=e.left,n=e.top,o=!0;e.set({left:t-r.x,top:n-r.y}),e.group=this,e.setCoords(o)},toString:function(){return"#<fabric.Group: ("+this.complexity()+")>"},addWithUpdate:function(e){var r=!!this.group;return this._restoreObjectsState(),s.util.resetObjectTransform(this),e&&(r&&s.util.removeTransformFromObject(e,this.group.calcTransformMatrix()),this._objects.push(e),e.group=this,e._set("canvas",this.canvas)),this._calcBounds(),this._updateObjectsCoords(),this.dirty=!0,r?this.group.addWithUpdate():this.setCoords(),this},removeWithUpdate:function(e){return this._restoreObjectsState(),s.util.resetObjectTransform(this),this.remove(e),this._calcBounds(),this._updateObjectsCoords(),this.setCoords(),this.dirty=!0,this},_onObjectAdded:function(e){this.dirty=!0,e.group=this,e._set("canvas",this.canvas)},_onObjectRemoved:function(e){this.dirty=!0,delete e.group},_set:function(e,r){var t=this._objects.length;if(this.useSetOnGroup)for(;t--;)this._objects[t].setOnGroup(e,r);if(e==="canvas")for(;t--;)this._objects[t]._set(e,r);s.Object.prototype._set.call(this,e,r)},toObject:function(e){var r=this.includeDefaultValues,t=this._objects.filter(function(o){return!o.excludeFromExport}).map(function(o){var i=o.includeDefaultValues;o.includeDefaultValues=r;var l=o.toObject(e);return o.includeDefaultValues=i,l}),n=s.Object.prototype.toObject.call(this,e);return n.objects=t,n},toDatalessObject:function(e){var r,t=this.sourcePath;if(t)r=t;else{var n=this.includeDefaultValues;r=this._objects.map(function(i){var l=i.includeDefaultValues;i.includeDefaultValues=n;var u=i.toDatalessObject(e);return i.includeDefaultValues=l,u})}var o=s.Object.prototype.toDatalessObject.call(this,e);return o.objects=r,o},render:function(e){this._transformDone=!0,this.callSuper("render",e),this._transformDone=!1},shouldCache:function(){var e=s.Object.prototype.shouldCache.call(this);if(e){for(var r=0,t=this._objects.length;r<t;r++)if(this._objects[r].willDrawShadow())return this.ownCaching=!1,!1}return e},willDrawShadow:function(){if(s.Object.prototype.willDrawShadow.call(this))return!0;for(var e=0,r=this._objects.length;e<r;e++)if(this._objects[e].willDrawShadow())return!0;return!1},isOnACache:function(){return this.ownCaching||this.group&&this.group.isOnACache()},drawObject:function(e){for(var r=0,t=this._objects.length;r<t;r++)this._objects[r].render(e);this._drawClipPath(e,this.clipPath)},isCacheDirty:function(e){if(this.callSuper("isCacheDirty",e))return!0;if(!this.statefullCache)return!1;for(var r=0,t=this._objects.length;r<t;r++)if(this._objects[r].isCacheDirty(!0)){if(this._cacheCanvas){var n=this.cacheWidth/this.zoomX,o=this.cacheHeight/this.zoomY;this._cacheContext.clearRect(-n/2,-o/2,n,o)}return!0}return!1},_restoreObjectsState:function(){var e=this.calcOwnMatrix();return this._objects.forEach(function(r){s.util.addTransformToObject(r,e),delete r.group,r.setCoords()}),this},destroy:function(){return this._objects.forEach(function(e){e.set("dirty",!0)}),this._restoreObjectsState()},dispose:function(){this.callSuper("dispose"),this.forEachObject(function(e){e.dispose&&e.dispose()}),this._objects=[]},toActiveSelection:function(){if(this.canvas){var e=this._objects,r=this.canvas;this._objects=[];var t=this.toObject();delete t.objects;var n=new s.ActiveSelection([]);return n.set(t),n.type="activeSelection",r.remove(this),e.forEach(function(o){o.group=n,o.dirty=!0,r.add(o)}),n.canvas=r,n._objects=e,r._activeObject=n,n.setCoords(),n}},ungroupOnCanvas:function(){return this._restoreObjectsState()},setObjectsCoords:function(){var e=!0;return this.forEachObject(function(r){r.setCoords(e)}),this},_calcBounds:function(e){for(var r=[],t=[],n,o,i,l=["tr","br","bl","tl"],u=0,d=this._objects.length,g,v=l.length;u<d;++u){for(n=this._objects[u],i=n.calcACoords(),g=0;g<v;g++)o=l[g],r.push(i[o].x),t.push(i[o].y);n.aCoords=i}this._getBounds(r,t,e)},_getBounds:function(e,r,t){var n=new s.Point(h(e),h(r)),o=new s.Point(a(e),a(r)),i=n.y||0,l=n.x||0,u=o.x-n.x||0,d=o.y-n.y||0;this.width=u,this.height=d,t||this.setPositionByOrigin({x:l,y:i},"left","top")},_toSVG:function(e){for(var r=["<g ","COMMON_PARTS",` >
`],t=0,n=this._objects.length;t<n;t++)r.push("		",this._objects[t].toSVG(e));return r.push(`</g>
`),r},getSvgStyles:function(){var e=typeof this.opacity<"u"&&this.opacity!==1?"opacity: "+this.opacity+";":"",r=this.visible?"":" visibility: hidden;";return[e,this.getSvgFilter(),r].join("")},toClipPathSVG:function(e){for(var r=[],t=0,n=this._objects.length;t<n;t++)r.push("	",this._objects[t].toClipPathSVG(e));return this._createBaseClipPathSVGMarkup(r,{reviver:e})}}),s.Group.fromObject=function(e,r){var t=e.objects,n=s.util.object.clone(e,!0);if(delete n.objects,typeof t=="string"){s.loadSVGFromURL(t,function(o){var i=s.util.groupSVGElements(o,e,t),l=n.clipPath;delete n.clipPath,i.set(n),l?s.util.enlivenObjects([l],function(u){i.clipPath=u[0],r&&r(i)}):r&&r(i)});return}s.util.enlivenObjects(t,function(o){s.util.enlivenObjectEnlivables(e,n,function(){r&&r(new s.Group(o,n,!0))})})})}(A),function(c){var s=c.fabric||(c.fabric={});s.ActiveSelection||(s.ActiveSelection=s.util.createClass(s.Group,{type:"activeSelection",initialize:function(h,a){a=a||{},this._objects=h||[];for(var e=this._objects.length;e--;)this._objects[e].group=this;a.originX&&(this.originX=a.originX),a.originY&&(this.originY=a.originY),this._calcBounds(),this._updateObjectsCoords(),s.Object.prototype.initialize.call(this,a),this.setCoords()},toGroup:function(){var h=this._objects.concat();this._objects=[];var a=s.Object.prototype.toObject.call(this),e=new s.Group([]);if(delete a.type,e.set(a),h.forEach(function(t){t.canvas.remove(t),t.group=e}),e._objects=h,!this.canvas)return e;var r=this.canvas;return r.add(e),r._activeObject=e,e.setCoords(),e},onDeselect:function(){return this.destroy(),!1},toString:function(){return"#<fabric.ActiveSelection: ("+this.complexity()+")>"},shouldCache:function(){return!1},isOnACache:function(){return!1},_renderControls:function(h,a,e){h.save(),h.globalAlpha=this.isMoving?this.borderOpacityWhenMoving:1,this.callSuper("_renderControls",h,a),e=e||{},typeof e.hasControls>"u"&&(e.hasControls=!1),e.forActiveSelection=!0;for(var r=0,t=this._objects.length;r<t;r++)this._objects[r]._renderControls(h,e);h.restore()}}),s.ActiveSelection.fromObject=function(h,a){s.util.enlivenObjects(h.objects,function(e){delete h.objects,a&&a(new s.ActiveSelection(e,h,!0))})})}(A),function(c){var s=f.util.object.extend;if(c.fabric||(c.fabric={}),c.fabric.Image){f.warn("fabric.Image is already defined.");return}f.Image=f.util.createClass(f.Object,{type:"image",strokeWidth:0,srcFromAttribute:!1,_lastScaleX:1,_lastScaleY:1,_filterScalingX:1,_filterScalingY:1,minimumScaleTrigger:.5,stateProperties:f.Object.prototype.stateProperties.concat("cropX","cropY"),cacheProperties:f.Object.prototype.cacheProperties.concat("cropX","cropY"),cacheKey:"",cropX:0,cropY:0,imageSmoothing:!0,initialize:function(h,a){a||(a={}),this.filters=[],this.cacheKey="texture"+f.Object.__uid++,this.callSuper("initialize",a),this._initElement(h,a)},getElement:function(){return this._element||{}},setElement:function(h,a){return this.removeTexture(this.cacheKey),this.removeTexture(this.cacheKey+"_filtered"),this._element=h,this._originalElement=h,this._initConfig(a),this.filters.length!==0&&this.applyFilters(),this.resizeFilter&&this.applyResizeFilters(),this},removeTexture:function(h){var a=f.filterBackend;a&&a.evictCachesForKey&&a.evictCachesForKey(h)},dispose:function(){this.callSuper("dispose"),this.removeTexture(this.cacheKey),this.removeTexture(this.cacheKey+"_filtered"),this._cacheContext=void 0,["_originalElement","_element","_filteredEl","_cacheCanvas"].forEach((function(h){f.util.cleanUpJsdomNode(this[h]),this[h]=void 0}).bind(this))},getCrossOrigin:function(){return this._originalElement&&(this._originalElement.crossOrigin||null)},getOriginalSize:function(){var h=this.getElement();return{width:h.naturalWidth||h.width,height:h.naturalHeight||h.height}},_stroke:function(h){if(!(!this.stroke||this.strokeWidth===0)){var a=this.width/2,e=this.height/2;h.beginPath(),h.moveTo(-a,-e),h.lineTo(a,-e),h.lineTo(a,e),h.lineTo(-a,e),h.lineTo(-a,-e),h.closePath()}},toObject:function(h){var a=[];this.filters.forEach(function(r){r&&a.push(r.toObject())});var e=s(this.callSuper("toObject",["cropX","cropY"].concat(h)),{src:this.getSrc(),crossOrigin:this.getCrossOrigin(),filters:a});return this.resizeFilter&&(e.resizeFilter=this.resizeFilter.toObject()),e},hasCrop:function(){return this.cropX||this.cropY||this.width<this._element.width||this.height<this._element.height},_toSVG:function(){var h=[],a=[],e,r=this._element,t=-this.width/2,n=-this.height/2,o="",i="";if(!r)return[];if(this.hasCrop()){var l=f.Object.__uid++;h.push('<clipPath id="imageCrop_'+l+`">
`,'	<rect x="'+t+'" y="'+n+'" width="'+this.width+'" height="'+this.height+`" />
`,`</clipPath>
`),o=' clip-path="url(#imageCrop_'+l+')" '}if(this.imageSmoothing||(i='" image-rendering="optimizeSpeed'),a.push("	<image ","COMMON_PARTS",'xlink:href="',this.getSvgSrc(!0),'" x="',t-this.cropX,'" y="',n-this.cropY,'" width="',r.width||r.naturalWidth,'" height="',r.height||r.height,i,'"',o,`></image>
`),this.stroke||this.strokeDashArray){var u=this.fill;this.fill=null,e=["	<rect ",'x="',t,'" y="',n,'" width="',this.width,'" height="',this.height,'" style="',this.getSvgStyles(),`"/>
`],this.fill=u}return this.paintFirst!=="fill"?h=h.concat(e,a):h=h.concat(a,e),h},getSrc:function(h){var a=h?this._element:this._originalElement;return a?a.toDataURL?a.toDataURL():this.srcFromAttribute?a.getAttribute("src"):a.src:this.src||""},setSrc:function(h,a,e){return f.util.loadImage(h,function(r,t){this.setElement(r,e),this._setWidthHeight(),a&&a(this,t)},this,e&&e.crossOrigin),this},toString:function(){return'#<fabric.Image: { src: "'+this.getSrc()+'" }>'},applyResizeFilters:function(){var h=this.resizeFilter,a=this.minimumScaleTrigger,e=this.getTotalObjectScaling(),r=e.scaleX,t=e.scaleY,n=this._filteredEl||this._originalElement;if(this.group&&this.set("dirty",!0),!h||r>a&&t>a){this._element=n,this._filterScalingX=1,this._filterScalingY=1,this._lastScaleX=r,this._lastScaleY=t;return}f.filterBackend||(f.filterBackend=f.initFilterBackend());var o=f.util.createCanvasElement(),i=this._filteredEl?this.cacheKey+"_filtered":this.cacheKey,l=n.width,u=n.height;o.width=l,o.height=u,this._element=o,this._lastScaleX=h.scaleX=r,this._lastScaleY=h.scaleY=t,f.filterBackend.applyFilters([h],n,l,u,this._element,i),this._filterScalingX=o.width/this._originalElement.width,this._filterScalingY=o.height/this._originalElement.height},applyFilters:function(h){if(h=h||this.filters||[],h=h.filter(function(n){return n&&!n.isNeutralState()}),this.set("dirty",!0),this.removeTexture(this.cacheKey+"_filtered"),h.length===0)return this._element=this._originalElement,this._filteredEl=null,this._filterScalingX=1,this._filterScalingY=1,this;var a=this._originalElement,e=a.naturalWidth||a.width,r=a.naturalHeight||a.height;if(this._element===this._originalElement){var t=f.util.createCanvasElement();t.width=e,t.height=r,this._element=t,this._filteredEl=t}else this._element=this._filteredEl,this._filteredEl.getContext("2d").clearRect(0,0,e,r),this._lastScaleX=1,this._lastScaleY=1;return f.filterBackend||(f.filterBackend=f.initFilterBackend()),f.filterBackend.applyFilters(h,this._originalElement,e,r,this._element,this.cacheKey),(this._originalElement.width!==this._element.width||this._originalElement.height!==this._element.height)&&(this._filterScalingX=this._element.width/this._originalElement.width,this._filterScalingY=this._element.height/this._originalElement.height),this},_render:function(h){f.util.setImageSmoothing(h,this.imageSmoothing),this.isMoving!==!0&&this.resizeFilter&&this._needsResize()&&this.applyResizeFilters(),this._stroke(h),this._renderPaintInOrder(h)},drawCacheOnCanvas:function(h){f.util.setImageSmoothing(h,this.imageSmoothing),f.Object.prototype.drawCacheOnCanvas.call(this,h)},shouldCache:function(){return this.needsItsOwnCache()},_renderFill:function(h){var a=this._element;if(a){var e=this._filterScalingX,r=this._filterScalingY,t=this.width,n=this.height,o=Math.min,i=Math.max,l=i(this.cropX,0),u=i(this.cropY,0),d=a.naturalWidth||a.width,g=a.naturalHeight||a.height,v=l*e,m=u*r,y=o(t*e,d-v),w=o(n*r,g-m),F=-t/2,Y=-n/2,z=o(t,d/e-l),N=o(n,g/r-u);a&&h.drawImage(a,v,m,y,w,F,Y,z,N)}},_needsResize:function(){var h=this.getTotalObjectScaling();return h.scaleX!==this._lastScaleX||h.scaleY!==this._lastScaleY},_resetWidthHeight:function(){this.set(this.getOriginalSize())},_initElement:function(h,a){this.setElement(f.util.getById(h),a),f.util.addClass(this.getElement(),f.Image.CSS_CANVAS)},_initConfig:function(h){h||(h={}),this.setOptions(h),this._setWidthHeight(h)},_initFilters:function(h,a){h&&h.length?f.util.enlivenObjects(h,function(e){a&&a(e)},"fabric.Image.filters"):a&&a()},_setWidthHeight:function(h){h||(h={});var a=this.getElement();this.width=h.width||a.naturalWidth||a.width||0,this.height=h.height||a.naturalHeight||a.height||0},parsePreserveAspectRatioAttribute:function(){var h=f.util.parsePreserveAspectRatioAttribute(this.preserveAspectRatio||""),a=this._element.width,e=this._element.height,r=1,t=1,n=0,o=0,i=0,l=0,u,d=this.width,g=this.height,v={width:d,height:g};return h&&(h.alignX!=="none"||h.alignY!=="none")?(h.meetOrSlice==="meet"&&(r=t=f.util.findScaleToFit(this._element,v),u=(d-a*r)/2,h.alignX==="Min"&&(n=-u),h.alignX==="Max"&&(n=u),u=(g-e*t)/2,h.alignY==="Min"&&(o=-u),h.alignY==="Max"&&(o=u)),h.meetOrSlice==="slice"&&(r=t=f.util.findScaleToCover(this._element,v),u=a-d/r,h.alignX==="Mid"&&(i=u/2),h.alignX==="Max"&&(i=u),u=e-g/t,h.alignY==="Mid"&&(l=u/2),h.alignY==="Max"&&(l=u),a=d/r,e=g/t)):(r=d/a,t=g/e),{width:a,height:e,scaleX:r,scaleY:t,offsetLeft:n,offsetTop:o,cropX:i,cropY:l}}}),f.Image.CSS_CANVAS="canvas-img",f.Image.prototype.getSvgSrc=f.Image.prototype.getSrc,f.Image.fromObject=function(h,a){var e=f.util.object.clone(h);f.util.loadImage(e.src,function(r,t){if(t){a&&a(null,!0);return}f.Image.prototype._initFilters.call(e,e.filters,function(n){e.filters=n||[],f.Image.prototype._initFilters.call(e,[e.resizeFilter],function(o){e.resizeFilter=o[0],f.util.enlivenObjectEnlivables(e,e,function(){var i=new f.Image(r,e);a(i,!1)})})})},null,e.crossOrigin)},f.Image.fromURL=function(h,a,e){f.util.loadImage(h,function(r,t){a&&a(new f.Image(r,e),t)},null,e&&e.crossOrigin)},f.Image.ATTRIBUTE_NAMES=f.SHARED_ATTRIBUTES.concat("x y width height preserveAspectRatio xlink:href crossOrigin image-rendering".split(" ")),f.Image.fromElement=function(h,a,e){var r=f.parseAttributes(h,f.Image.ATTRIBUTE_NAMES);f.Image.fromURL(r["xlink:href"],a,s(e?f.util.object.clone(e):{},r))}}(A),f.util.object.extend(f.Object.prototype,{_getAngleValueForStraighten:function(){var c=this.angle%360;return c>0?Math.round((c-1)/90)*90:Math.round(c/90)*90},straighten:function(){return this.rotate(this._getAngleValueForStraighten())},fxStraighten:function(c){c=c||{};var s=function(){},h=c.onComplete||s,a=c.onChange||s,e=this;return f.util.animate({target:this,startValue:this.get("angle"),endValue:this._getAngleValueForStraighten(),duration:this.FX_DURATION,onChange:function(r){e.rotate(r),a()},onComplete:function(){e.setCoords(),h()}})}}),f.util.object.extend(f.StaticCanvas.prototype,{straightenObject:function(c){return c.straighten(),this.requestRenderAll(),this},fxStraightenObject:function(c){return c.fxStraighten({onChange:this.requestRenderAllBound})}}),function(){function c(h,a){var e="precision "+a+` float;
void main(){}`,r=h.createShader(h.FRAGMENT_SHADER);return h.shaderSource(r,e),h.compileShader(r),!!h.getShaderParameter(r,h.COMPILE_STATUS)}f.isWebglSupported=function(h){if(f.isLikelyNode)return!1;h=h||f.WebglFilterBackend.prototype.tileSize;var a=document.createElement("canvas"),e=a.getContext("webgl")||a.getContext("experimental-webgl"),r=!1;if(e){f.maxTextureSize=e.getParameter(e.MAX_TEXTURE_SIZE),r=f.maxTextureSize>=h;for(var t=["highp","mediump","lowp"],n=0;n<3;n++)if(c(e,t[n])){f.webGlPrecision=t[n];break}}return this.isSupported=r,r},f.WebglFilterBackend=s;function s(h){h&&h.tileSize&&(this.tileSize=h.tileSize),this.setupGLContext(this.tileSize,this.tileSize),this.captureGPUInfo()}s.prototype={tileSize:2048,resources:{},setupGLContext:function(h,a){this.dispose(),this.createWebGLCanvas(h,a),this.aPosition=new Float32Array([0,0,0,1,1,0,1,1]),this.chooseFastestCopyGLTo2DMethod(h,a)},chooseFastestCopyGLTo2DMethod:function(h,a){var e=typeof window.performance<"u",r;try{new ImageData(1,1),r=!0}catch{r=!1}var t=typeof ArrayBuffer<"u",n=typeof Uint8ClampedArray<"u";if(e&&r&&t&&n){var o=f.util.createCanvasElement(),i=new ArrayBuffer(h*a*4);if(f.forceGLPutImageData){this.imageBuffer=i,this.copyGLTo2D=lt;return}var l={imageBuffer:i,destinationWidth:h,destinationHeight:a,targetCanvas:o},u,d,g;o.width=h,o.height=a,u=window.performance.now(),st.call(l,this.gl,l),d=window.performance.now()-u,u=window.performance.now(),lt.call(l,this.gl,l),g=window.performance.now()-u,d>g?(this.imageBuffer=i,this.copyGLTo2D=lt):this.copyGLTo2D=st}},createWebGLCanvas:function(h,a){var e=f.util.createCanvasElement();e.width=h,e.height=a;var r={alpha:!0,premultipliedAlpha:!1,depth:!1,stencil:!1,antialias:!1},t=e.getContext("webgl",r);t||(t=e.getContext("experimental-webgl",r)),t&&(t.clearColor(0,0,0,0),this.canvas=e,this.gl=t)},applyFilters:function(h,a,e,r,t,n){var o=this.gl,i;n&&(i=this.getCachedTexture(n,a));var l={originalWidth:a.width||a.originalWidth,originalHeight:a.height||a.originalHeight,sourceWidth:e,sourceHeight:r,destinationWidth:e,destinationHeight:r,context:o,sourceTexture:this.createTexture(o,e,r,!i&&a),targetTexture:this.createTexture(o,e,r),originalTexture:i||this.createTexture(o,e,r,!i&&a),passes:h.length,webgl:!0,aPosition:this.aPosition,programCache:this.programCache,pass:0,filterBackend:this,targetCanvas:t},u=o.createFramebuffer();return o.bindFramebuffer(o.FRAMEBUFFER,u),h.forEach(function(d){d&&d.applyTo(l)}),et(l),this.copyGLTo2D(o,l),o.bindTexture(o.TEXTURE_2D,null),o.deleteTexture(l.sourceTexture),o.deleteTexture(l.targetTexture),o.deleteFramebuffer(u),t.getContext("2d").setTransform(1,0,0,1,0,0),l},dispose:function(){this.canvas&&(this.canvas=null,this.gl=null),this.clearWebGLCaches()},clearWebGLCaches:function(){this.programCache={},this.textureCache={}},createTexture:function(h,a,e,r,t){var n=h.createTexture();return h.bindTexture(h.TEXTURE_2D,n),h.texParameteri(h.TEXTURE_2D,h.TEXTURE_MAG_FILTER,t||h.NEAREST),h.texParameteri(h.TEXTURE_2D,h.TEXTURE_MIN_FILTER,t||h.NEAREST),h.texParameteri(h.TEXTURE_2D,h.TEXTURE_WRAP_S,h.CLAMP_TO_EDGE),h.texParameteri(h.TEXTURE_2D,h.TEXTURE_WRAP_T,h.CLAMP_TO_EDGE),r?h.texImage2D(h.TEXTURE_2D,0,h.RGBA,h.RGBA,h.UNSIGNED_BYTE,r):h.texImage2D(h.TEXTURE_2D,0,h.RGBA,a,e,0,h.RGBA,h.UNSIGNED_BYTE,null),n},getCachedTexture:function(h,a){if(this.textureCache[h])return this.textureCache[h];var e=this.createTexture(this.gl,a.width,a.height,a);return this.textureCache[h]=e,e},evictCachesForKey:function(h){this.textureCache[h]&&(this.gl.deleteTexture(this.textureCache[h]),delete this.textureCache[h])},copyGLTo2D:st,captureGPUInfo:function(){if(this.gpuInfo)return this.gpuInfo;var h=this.gl,a={renderer:"",vendor:""};if(!h)return a;var e=h.getExtension("WEBGL_debug_renderer_info");if(e){var r=h.getParameter(e.UNMASKED_RENDERER_WEBGL),t=h.getParameter(e.UNMASKED_VENDOR_WEBGL);r&&(a.renderer=r.toLowerCase()),t&&(a.vendor=t.toLowerCase())}return this.gpuInfo=a,a}}}();function et(c){var s=c.targetCanvas,h=s.width,a=s.height,e=c.destinationWidth,r=c.destinationHeight;(h!==e||a!==r)&&(s.width=e,s.height=r)}function st(c,s){var h=c.canvas,a=s.targetCanvas,e=a.getContext("2d");e.translate(0,a.height),e.scale(1,-1);var r=h.height-a.height;e.drawImage(h,0,r,a.width,a.height,0,0,a.width,a.height)}function lt(c,s){var h=s.targetCanvas,a=h.getContext("2d"),e=s.destinationWidth,r=s.destinationHeight,t=e*r*4,n=new Uint8Array(this.imageBuffer,0,t),o=new Uint8ClampedArray(this.imageBuffer,0,t);c.readPixels(0,0,e,r,c.RGBA,c.UNSIGNED_BYTE,n);var i=new ImageData(o,e,r);a.putImageData(i,0,0)}(function(){var c=function(){};f.Canvas2dFilterBackend=s;function s(){}s.prototype={evictCachesForKey:c,dispose:c,clearWebGLCaches:c,resources:{},applyFilters:function(h,a,e,r,t){var n=t.getContext("2d");n.drawImage(a,0,0,e,r);var o=n.getImageData(0,0,e,r),i=n.getImageData(0,0,e,r),l={sourceWidth:e,sourceHeight:r,imageData:o,originalEl:a,originalImageData:i,canvasEl:t,ctx:n,filterBackend:this};return h.forEach(function(u){u.applyTo(l)}),(l.imageData.width!==e||l.imageData.height!==r)&&(t.width=l.imageData.width,t.height=l.imageData.height),n.putImageData(l.imageData,0,0),l}}})(),f.Image=f.Image||{},f.Image.filters=f.Image.filters||{},f.Image.filters.BaseFilter=f.util.createClass({type:"BaseFilter",vertexSource:`attribute vec2 aPosition;
varying vec2 vTexCoord;
void main() {
vTexCoord = aPosition;
gl_Position = vec4(aPosition * 2.0 - 1.0, 0.0, 1.0);
}`,fragmentSource:`precision highp float;
varying vec2 vTexCoord;
uniform sampler2D uTexture;
void main() {
gl_FragColor = texture2D(uTexture, vTexCoord);
}`,initialize:function(c){c&&this.setOptions(c)},setOptions:function(c){for(var s in c)this[s]=c[s]},createProgram:function(c,s,h){s=s||this.fragmentSource,h=h||this.vertexSource,f.webGlPrecision!=="highp"&&(s=s.replace(/precision highp float/g,"precision "+f.webGlPrecision+" float"));var a=c.createShader(c.VERTEX_SHADER);if(c.shaderSource(a,h),c.compileShader(a),!c.getShaderParameter(a,c.COMPILE_STATUS))throw new Error("Vertex shader compile error for "+this.type+": "+c.getShaderInfoLog(a));var e=c.createShader(c.FRAGMENT_SHADER);if(c.shaderSource(e,s),c.compileShader(e),!c.getShaderParameter(e,c.COMPILE_STATUS))throw new Error("Fragment shader compile error for "+this.type+": "+c.getShaderInfoLog(e));var r=c.createProgram();if(c.attachShader(r,a),c.attachShader(r,e),c.linkProgram(r),!c.getProgramParameter(r,c.LINK_STATUS))throw new Error('Shader link error for "${this.type}" '+c.getProgramInfoLog(r));var t=this.getAttributeLocations(c,r),n=this.getUniformLocations(c,r)||{};return n.uStepW=c.getUniformLocation(r,"uStepW"),n.uStepH=c.getUniformLocation(r,"uStepH"),{program:r,attributeLocations:t,uniformLocations:n}},getAttributeLocations:function(c,s){return{aPosition:c.getAttribLocation(s,"aPosition")}},getUniformLocations:function(){return{}},sendAttributeData:function(c,s,h){var a=s.aPosition,e=c.createBuffer();c.bindBuffer(c.ARRAY_BUFFER,e),c.enableVertexAttribArray(a),c.vertexAttribPointer(a,2,c.FLOAT,!1,0,0),c.bufferData(c.ARRAY_BUFFER,h,c.STATIC_DRAW)},_setupFrameBuffer:function(c){var s=c.context,h,a;c.passes>1?(h=c.destinationWidth,a=c.destinationHeight,(c.sourceWidth!==h||c.sourceHeight!==a)&&(s.deleteTexture(c.targetTexture),c.targetTexture=c.filterBackend.createTexture(s,h,a)),s.framebufferTexture2D(s.FRAMEBUFFER,s.COLOR_ATTACHMENT0,s.TEXTURE_2D,c.targetTexture,0)):(s.bindFramebuffer(s.FRAMEBUFFER,null),s.finish())},_swapTextures:function(c){c.passes--,c.pass++;var s=c.targetTexture;c.targetTexture=c.sourceTexture,c.sourceTexture=s},isNeutralState:function(){var c=this.mainParameter,s=f.Image.filters[this.type].prototype;if(c)if(Array.isArray(s[c])){for(var h=s[c].length;h--;)if(this[c][h]!==s[c][h])return!1;return!0}else return s[c]===this[c];else return!1},applyTo:function(c){c.webgl?(this._setupFrameBuffer(c),this.applyToWebGL(c),this._swapTextures(c)):this.applyTo2d(c)},retrieveShader:function(c){return c.programCache.hasOwnProperty(this.type)||(c.programCache[this.type]=this.createProgram(c.context)),c.programCache[this.type]},applyToWebGL:function(c){var s=c.context,h=this.retrieveShader(c);c.pass===0&&c.originalTexture?s.bindTexture(s.TEXTURE_2D,c.originalTexture):s.bindTexture(s.TEXTURE_2D,c.sourceTexture),s.useProgram(h.program),this.sendAttributeData(s,h.attributeLocations,c.aPosition),s.uniform1f(h.uniformLocations.uStepW,1/c.sourceWidth),s.uniform1f(h.uniformLocations.uStepH,1/c.sourceHeight),this.sendUniformData(s,h.uniformLocations),s.viewport(0,0,c.destinationWidth,c.destinationHeight),s.drawArrays(s.TRIANGLE_STRIP,0,4)},bindAdditionalTexture:function(c,s,h){c.activeTexture(h),c.bindTexture(c.TEXTURE_2D,s),c.activeTexture(c.TEXTURE0)},unbindAdditionalTexture:function(c,s){c.activeTexture(s),c.bindTexture(c.TEXTURE_2D,null),c.activeTexture(c.TEXTURE0)},getMainParameter:function(){return this[this.mainParameter]},setMainParameter:function(c){this[this.mainParameter]=c},sendUniformData:function(){},createHelpLayer:function(c){if(!c.helpLayer){var s=document.createElement("canvas");s.width=c.sourceWidth,s.height=c.sourceHeight,c.helpLayer=s}},toObject:function(){var c={type:this.type},s=this.mainParameter;return s&&(c[s]=this[s]),c},toJSON:function(){return this.toObject()}}),f.Image.filters.BaseFilter.fromObject=function(c,s){var h=new f.Image.filters[c.type](c);return s&&s(h),h},function(c){var s=c.fabric||(c.fabric={}),h=s.Image.filters,a=s.util.createClass;h.ColorMatrix=a(h.BaseFilter,{type:"ColorMatrix",fragmentSource:`precision highp float;
uniform sampler2D uTexture;
varying vec2 vTexCoord;
uniform mat4 uColorMatrix;
uniform vec4 uConstants;
void main() {
vec4 color = texture2D(uTexture, vTexCoord);
color *= uColorMatrix;
color += uConstants;
gl_FragColor = color;
}`,matrix:[1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0],mainParameter:"matrix",colorsOnly:!0,initialize:function(e){this.callSuper("initialize",e),this.matrix=this.matrix.slice(0)},applyTo2d:function(e){var r=e.imageData,t=r.data,n=t.length,o=this.matrix,i,l,u,d,g,v=this.colorsOnly;for(g=0;g<n;g+=4)i=t[g],l=t[g+1],u=t[g+2],v?(t[g]=i*o[0]+l*o[1]+u*o[2]+o[4]*255,t[g+1]=i*o[5]+l*o[6]+u*o[7]+o[9]*255,t[g+2]=i*o[10]+l*o[11]+u*o[12]+o[14]*255):(d=t[g+3],t[g]=i*o[0]+l*o[1]+u*o[2]+d*o[3]+o[4]*255,t[g+1]=i*o[5]+l*o[6]+u*o[7]+d*o[8]+o[9]*255,t[g+2]=i*o[10]+l*o[11]+u*o[12]+d*o[13]+o[14]*255,t[g+3]=i*o[15]+l*o[16]+u*o[17]+d*o[18]+o[19]*255)},getUniformLocations:function(e,r){return{uColorMatrix:e.getUniformLocation(r,"uColorMatrix"),uConstants:e.getUniformLocation(r,"uConstants")}},sendUniformData:function(e,r){var t=this.matrix,n=[t[0],t[1],t[2],t[3],t[5],t[6],t[7],t[8],t[10],t[11],t[12],t[13],t[15],t[16],t[17],t[18]],o=[t[4],t[9],t[14],t[19]];e.uniformMatrix4fv(r.uColorMatrix,!1,n),e.uniform4fv(r.uConstants,o)}}),s.Image.filters.ColorMatrix.fromObject=s.Image.filters.BaseFilter.fromObject}(A),function(c){var s=c.fabric||(c.fabric={}),h=s.Image.filters,a=s.util.createClass;h.Brightness=a(h.BaseFilter,{type:"Brightness",fragmentSource:`precision highp float;
uniform sampler2D uTexture;
uniform float uBrightness;
varying vec2 vTexCoord;
void main() {
vec4 color = texture2D(uTexture, vTexCoord);
color.rgb += uBrightness;
gl_FragColor = color;
}`,brightness:0,mainParameter:"brightness",applyTo2d:function(e){if(this.brightness!==0){var r=e.imageData,t=r.data,n,o=t.length,i=Math.round(this.brightness*255);for(n=0;n<o;n+=4)t[n]=t[n]+i,t[n+1]=t[n+1]+i,t[n+2]=t[n+2]+i}},getUniformLocations:function(e,r){return{uBrightness:e.getUniformLocation(r,"uBrightness")}},sendUniformData:function(e,r){e.uniform1f(r.uBrightness,this.brightness)}}),s.Image.filters.Brightness.fromObject=s.Image.filters.BaseFilter.fromObject}(A),function(c){var s=c.fabric||(c.fabric={}),h=s.util.object.extend,a=s.Image.filters,e=s.util.createClass;a.Convolute=e(a.BaseFilter,{type:"Convolute",opaque:!1,matrix:[0,0,0,0,1,0,0,0,0],fragmentSource:{Convolute_3_1:`precision highp float;
uniform sampler2D uTexture;
uniform float uMatrix[9];
uniform float uStepW;
uniform float uStepH;
varying vec2 vTexCoord;
void main() {
vec4 color = vec4(0, 0, 0, 0);
for (float h = 0.0; h < 3.0; h+=1.0) {
for (float w = 0.0; w < 3.0; w+=1.0) {
vec2 matrixPos = vec2(uStepW * (w - 1), uStepH * (h - 1));
color += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 3.0 + w)];
}
}
gl_FragColor = color;
}`,Convolute_3_0:`precision highp float;
uniform sampler2D uTexture;
uniform float uMatrix[9];
uniform float uStepW;
uniform float uStepH;
varying vec2 vTexCoord;
void main() {
vec4 color = vec4(0, 0, 0, 1);
for (float h = 0.0; h < 3.0; h+=1.0) {
for (float w = 0.0; w < 3.0; w+=1.0) {
vec2 matrixPos = vec2(uStepW * (w - 1.0), uStepH * (h - 1.0));
color.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 3.0 + w)];
}
}
float alpha = texture2D(uTexture, vTexCoord).a;
gl_FragColor = color;
gl_FragColor.a = alpha;
}`,Convolute_5_1:`precision highp float;
uniform sampler2D uTexture;
uniform float uMatrix[25];
uniform float uStepW;
uniform float uStepH;
varying vec2 vTexCoord;
void main() {
vec4 color = vec4(0, 0, 0, 0);
for (float h = 0.0; h < 5.0; h+=1.0) {
for (float w = 0.0; w < 5.0; w+=1.0) {
vec2 matrixPos = vec2(uStepW * (w - 2.0), uStepH * (h - 2.0));
color += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 5.0 + w)];
}
}
gl_FragColor = color;
}`,Convolute_5_0:`precision highp float;
uniform sampler2D uTexture;
uniform float uMatrix[25];
uniform float uStepW;
uniform float uStepH;
varying vec2 vTexCoord;
void main() {
vec4 color = vec4(0, 0, 0, 1);
for (float h = 0.0; h < 5.0; h+=1.0) {
for (float w = 0.0; w < 5.0; w+=1.0) {
vec2 matrixPos = vec2(uStepW * (w - 2.0), uStepH * (h - 2.0));
color.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 5.0 + w)];
}
}
float alpha = texture2D(uTexture, vTexCoord).a;
gl_FragColor = color;
gl_FragColor.a = alpha;
}`,Convolute_7_1:`precision highp float;
uniform sampler2D uTexture;
uniform float uMatrix[49];
uniform float uStepW;
uniform float uStepH;
varying vec2 vTexCoord;
void main() {
vec4 color = vec4(0, 0, 0, 0);
for (float h = 0.0; h < 7.0; h+=1.0) {
for (float w = 0.0; w < 7.0; w+=1.0) {
vec2 matrixPos = vec2(uStepW * (w - 3.0), uStepH * (h - 3.0));
color += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 7.0 + w)];
}
}
gl_FragColor = color;
}`,Convolute_7_0:`precision highp float;
uniform sampler2D uTexture;
uniform float uMatrix[49];
uniform float uStepW;
uniform float uStepH;
varying vec2 vTexCoord;
void main() {
vec4 color = vec4(0, 0, 0, 1);
for (float h = 0.0; h < 7.0; h+=1.0) {
for (float w = 0.0; w < 7.0; w+=1.0) {
vec2 matrixPos = vec2(uStepW * (w - 3.0), uStepH * (h - 3.0));
color.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 7.0 + w)];
}
}
float alpha = texture2D(uTexture, vTexCoord).a;
gl_FragColor = color;
gl_FragColor.a = alpha;
}`,Convolute_9_1:`precision highp float;
uniform sampler2D uTexture;
uniform float uMatrix[81];
uniform float uStepW;
uniform float uStepH;
varying vec2 vTexCoord;
void main() {
vec4 color = vec4(0, 0, 0, 0);
for (float h = 0.0; h < 9.0; h+=1.0) {
for (float w = 0.0; w < 9.0; w+=1.0) {
vec2 matrixPos = vec2(uStepW * (w - 4.0), uStepH * (h - 4.0));
color += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 9.0 + w)];
}
}
gl_FragColor = color;
}`,Convolute_9_0:`precision highp float;
uniform sampler2D uTexture;
uniform float uMatrix[81];
uniform float uStepW;
uniform float uStepH;
varying vec2 vTexCoord;
void main() {
vec4 color = vec4(0, 0, 0, 1);
for (float h = 0.0; h < 9.0; h+=1.0) {
for (float w = 0.0; w < 9.0; w+=1.0) {
vec2 matrixPos = vec2(uStepW * (w - 4.0), uStepH * (h - 4.0));
color.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 9.0 + w)];
}
}
float alpha = texture2D(uTexture, vTexCoord).a;
gl_FragColor = color;
gl_FragColor.a = alpha;
}`},retrieveShader:function(r){var t=Math.sqrt(this.matrix.length),n=this.type+"_"+t+"_"+(this.opaque?1:0),o=this.fragmentSource[n];return r.programCache.hasOwnProperty(n)||(r.programCache[n]=this.createProgram(r.context,o)),r.programCache[n]},applyTo2d:function(r){var t=r.imageData,n=t.data,o=this.matrix,i=Math.round(Math.sqrt(o.length)),l=Math.floor(i/2),u=t.width,d=t.height,g=r.ctx.createImageData(u,d),v=g.data,m=this.opaque?1:0,y,w,F,Y,z,N,q,H,U,J,Q,Z,p;for(Q=0;Q<d;Q++)for(J=0;J<u;J++){for(z=(Q*u+J)*4,y=0,w=0,F=0,Y=0,p=0;p<i;p++)for(Z=0;Z<i;Z++)q=Q+p-l,N=J+Z-l,!(q<0||q>=d||N<0||N>=u)&&(H=(q*u+N)*4,U=o[p*i+Z],y+=n[H]*U,w+=n[H+1]*U,F+=n[H+2]*U,m||(Y+=n[H+3]*U));v[z]=y,v[z+1]=w,v[z+2]=F,m?v[z+3]=n[z+3]:v[z+3]=Y}r.imageData=g},getUniformLocations:function(r,t){return{uMatrix:r.getUniformLocation(t,"uMatrix"),uOpaque:r.getUniformLocation(t,"uOpaque"),uHalfSize:r.getUniformLocation(t,"uHalfSize"),uSize:r.getUniformLocation(t,"uSize")}},sendUniformData:function(r,t){r.uniform1fv(t.uMatrix,this.matrix)},toObject:function(){return h(this.callSuper("toObject"),{opaque:this.opaque,matrix:this.matrix})}}),s.Image.filters.Convolute.fromObject=s.Image.filters.BaseFilter.fromObject}(A),function(c){var s=c.fabric||(c.fabric={}),h=s.Image.filters,a=s.util.createClass;h.Grayscale=a(h.BaseFilter,{type:"Grayscale",fragmentSource:{average:`precision highp float;
uniform sampler2D uTexture;
varying vec2 vTexCoord;
void main() {
vec4 color = texture2D(uTexture, vTexCoord);
float average = (color.r + color.b + color.g) / 3.0;
gl_FragColor = vec4(average, average, average, color.a);
}`,lightness:`precision highp float;
uniform sampler2D uTexture;
uniform int uMode;
varying vec2 vTexCoord;
void main() {
vec4 col = texture2D(uTexture, vTexCoord);
float average = (max(max(col.r, col.g),col.b) + min(min(col.r, col.g),col.b)) / 2.0;
gl_FragColor = vec4(average, average, average, col.a);
}`,luminosity:`precision highp float;
uniform sampler2D uTexture;
uniform int uMode;
varying vec2 vTexCoord;
void main() {
vec4 col = texture2D(uTexture, vTexCoord);
float average = 0.21 * col.r + 0.72 * col.g + 0.07 * col.b;
gl_FragColor = vec4(average, average, average, col.a);
}`},mode:"average",mainParameter:"mode",applyTo2d:function(e){var r=e.imageData,t=r.data,n,o=t.length,i,l=this.mode;for(n=0;n<o;n+=4)l==="average"?i=(t[n]+t[n+1]+t[n+2])/3:l==="lightness"?i=(Math.min(t[n],t[n+1],t[n+2])+Math.max(t[n],t[n+1],t[n+2]))/2:l==="luminosity"&&(i=.21*t[n]+.72*t[n+1]+.07*t[n+2]),t[n]=i,t[n+1]=i,t[n+2]=i},retrieveShader:function(e){var r=this.type+"_"+this.mode;if(!e.programCache.hasOwnProperty(r)){var t=this.fragmentSource[this.mode];e.programCache[r]=this.createProgram(e.context,t)}return e.programCache[r]},getUniformLocations:function(e,r){return{uMode:e.getUniformLocation(r,"uMode")}},sendUniformData:function(e,r){var t=1;e.uniform1i(r.uMode,t)},isNeutralState:function(){return!1}}),s.Image.filters.Grayscale.fromObject=s.Image.filters.BaseFilter.fromObject}(A),function(c){var s=c.fabric||(c.fabric={}),h=s.Image.filters,a=s.util.createClass;h.Invert=a(h.BaseFilter,{type:"Invert",fragmentSource:`precision highp float;
uniform sampler2D uTexture;
uniform int uInvert;
varying vec2 vTexCoord;
void main() {
vec4 color = texture2D(uTexture, vTexCoord);
if (uInvert == 1) {
gl_FragColor = vec4(1.0 - color.r,1.0 -color.g,1.0 -color.b,color.a);
} else {
gl_FragColor = color;
}
}`,invert:!0,mainParameter:"invert",applyTo2d:function(e){var r=e.imageData,t=r.data,n,o=t.length;for(n=0;n<o;n+=4)t[n]=255-t[n],t[n+1]=255-t[n+1],t[n+2]=255-t[n+2]},isNeutralState:function(){return!this.invert},getUniformLocations:function(e,r){return{uInvert:e.getUniformLocation(r,"uInvert")}},sendUniformData:function(e,r){e.uniform1i(r.uInvert,this.invert)}}),s.Image.filters.Invert.fromObject=s.Image.filters.BaseFilter.fromObject}(A),function(c){var s=c.fabric||(c.fabric={}),h=s.util.object.extend,a=s.Image.filters,e=s.util.createClass;a.Noise=e(a.BaseFilter,{type:"Noise",fragmentSource:`precision highp float;
uniform sampler2D uTexture;
uniform float uStepH;
uniform float uNoise;
uniform float uSeed;
varying vec2 vTexCoord;
float rand(vec2 co, float seed, float vScale) {
return fract(sin(dot(co.xy * vScale ,vec2(12.9898 , 78.233))) * 43758.5453 * (seed + 0.01) / 2.0);
}
void main() {
vec4 color = texture2D(uTexture, vTexCoord);
color.rgb += (0.5 - rand(vTexCoord, uSeed, 0.1 / uStepH)) * uNoise;
gl_FragColor = color;
}`,mainParameter:"noise",noise:0,applyTo2d:function(r){if(this.noise!==0){var t=r.imageData,n=t.data,o,i=n.length,l=this.noise,u;for(o=0,i=n.length;o<i;o+=4)u=(.5-Math.random())*l,n[o]+=u,n[o+1]+=u,n[o+2]+=u}},getUniformLocations:function(r,t){return{uNoise:r.getUniformLocation(t,"uNoise"),uSeed:r.getUniformLocation(t,"uSeed")}},sendUniformData:function(r,t){r.uniform1f(t.uNoise,this.noise/255),r.uniform1f(t.uSeed,Math.random())},toObject:function(){return h(this.callSuper("toObject"),{noise:this.noise})}}),s.Image.filters.Noise.fromObject=s.Image.filters.BaseFilter.fromObject}(A),function(c){var s=c.fabric||(c.fabric={}),h=s.Image.filters,a=s.util.createClass;h.Pixelate=a(h.BaseFilter,{type:"Pixelate",blocksize:4,mainParameter:"blocksize",fragmentSource:`precision highp float;
uniform sampler2D uTexture;
uniform float uBlocksize;
uniform float uStepW;
uniform float uStepH;
varying vec2 vTexCoord;
void main() {
float blockW = uBlocksize * uStepW;
float blockH = uBlocksize * uStepW;
int posX = int(vTexCoord.x / blockW);
int posY = int(vTexCoord.y / blockH);
float fposX = float(posX);
float fposY = float(posY);
vec2 squareCoords = vec2(fposX * blockW, fposY * blockH);
vec4 color = texture2D(uTexture, squareCoords);
gl_FragColor = color;
}`,applyTo2d:function(e){var r=e.imageData,t=r.data,n=r.height,o=r.width,i,l,u,d,g,v,m,y,w,F,Y;for(l=0;l<n;l+=this.blocksize)for(u=0;u<o;u+=this.blocksize)for(i=l*4*o+u*4,d=t[i],g=t[i+1],v=t[i+2],m=t[i+3],F=Math.min(l+this.blocksize,n),Y=Math.min(u+this.blocksize,o),y=l;y<F;y++)for(w=u;w<Y;w++)i=y*4*o+w*4,t[i]=d,t[i+1]=g,t[i+2]=v,t[i+3]=m},isNeutralState:function(){return this.blocksize===1},getUniformLocations:function(e,r){return{uBlocksize:e.getUniformLocation(r,"uBlocksize"),uStepW:e.getUniformLocation(r,"uStepW"),uStepH:e.getUniformLocation(r,"uStepH")}},sendUniformData:function(e,r){e.uniform1f(r.uBlocksize,this.blocksize)}}),s.Image.filters.Pixelate.fromObject=s.Image.filters.BaseFilter.fromObject}(A),function(c){var s=c.fabric||(c.fabric={}),h=s.util.object.extend,a=s.Image.filters,e=s.util.createClass;a.RemoveColor=e(a.BaseFilter,{type:"RemoveColor",color:"#FFFFFF",fragmentSource:`precision highp float;
uniform sampler2D uTexture;
uniform vec4 uLow;
uniform vec4 uHigh;
varying vec2 vTexCoord;
void main() {
gl_FragColor = texture2D(uTexture, vTexCoord);
if(all(greaterThan(gl_FragColor.rgb,uLow.rgb)) && all(greaterThan(uHigh.rgb,gl_FragColor.rgb))) {
gl_FragColor.a = 0.0;
}
}`,distance:.02,useAlpha:!1,applyTo2d:function(r){var t=r.imageData,n=t.data,o,i=this.distance*255,l,u,d,g=new s.Color(this.color).getSource(),v=[g[0]-i,g[1]-i,g[2]-i],m=[g[0]+i,g[1]+i,g[2]+i];for(o=0;o<n.length;o+=4)l=n[o],u=n[o+1],d=n[o+2],l>v[0]&&u>v[1]&&d>v[2]&&l<m[0]&&u<m[1]&&d<m[2]&&(n[o+3]=0)},getUniformLocations:function(r,t){return{uLow:r.getUniformLocation(t,"uLow"),uHigh:r.getUniformLocation(t,"uHigh")}},sendUniformData:function(r,t){var n=new s.Color(this.color).getSource(),o=parseFloat(this.distance),i=[0+n[0]/255-o,0+n[1]/255-o,0+n[2]/255-o,1],l=[n[0]/255+o,n[1]/255+o,n[2]/255+o,1];r.uniform4fv(t.uLow,i),r.uniform4fv(t.uHigh,l)},toObject:function(){return h(this.callSuper("toObject"),{color:this.color,distance:this.distance})}}),s.Image.filters.RemoveColor.fromObject=s.Image.filters.BaseFilter.fromObject}(A),function(c){var s=c.fabric||(c.fabric={}),h=s.Image.filters,a=s.util.createClass,e={Brownie:[.5997,.34553,-.27082,0,.186,-.0377,.86095,.15059,0,-.1449,.24113,-.07441,.44972,0,-.02965,0,0,0,1,0],Vintage:[.62793,.32021,-.03965,0,.03784,.02578,.64411,.03259,0,.02926,.0466,-.08512,.52416,0,.02023,0,0,0,1,0],Kodachrome:[1.12855,-.39673,-.03992,0,.24991,-.16404,1.08352,-.05498,0,.09698,-.16786,-.56034,1.60148,0,.13972,0,0,0,1,0],Technicolor:[1.91252,-.85453,-.09155,0,.04624,-.30878,1.76589,-.10601,0,-.27589,-.2311,-.75018,1.84759,0,.12137,0,0,0,1,0],Polaroid:[1.438,-.062,-.062,0,0,-.122,1.378,-.122,0,0,-.016,-.016,1.483,0,0,0,0,0,1,0],Sepia:[.393,.769,.189,0,0,.349,.686,.168,0,0,.272,.534,.131,0,0,0,0,0,1,0],BlackWhite:[1.5,1.5,1.5,0,-1,1.5,1.5,1.5,0,-1,1.5,1.5,1.5,0,-1,0,0,0,1,0]};for(var r in e)h[r]=a(h.ColorMatrix,{type:r,matrix:e[r],mainParameter:!1,colorsOnly:!0}),s.Image.filters[r].fromObject=s.Image.filters.BaseFilter.fromObject}(A),function(c){var s=c.fabric,h=s.Image.filters,a=s.util.createClass;h.BlendColor=a(h.BaseFilter,{type:"BlendColor",color:"#F95C63",mode:"multiply",alpha:1,fragmentSource:{multiply:`gl_FragColor.rgb *= uColor.rgb;
`,screen:`gl_FragColor.rgb = 1.0 - (1.0 - gl_FragColor.rgb) * (1.0 - uColor.rgb);
`,add:`gl_FragColor.rgb += uColor.rgb;
`,diff:`gl_FragColor.rgb = abs(gl_FragColor.rgb - uColor.rgb);
`,subtract:`gl_FragColor.rgb -= uColor.rgb;
`,lighten:`gl_FragColor.rgb = max(gl_FragColor.rgb, uColor.rgb);
`,darken:`gl_FragColor.rgb = min(gl_FragColor.rgb, uColor.rgb);
`,exclusion:`gl_FragColor.rgb += uColor.rgb - 2.0 * (uColor.rgb * gl_FragColor.rgb);
`,overlay:`if (uColor.r < 0.5) {
gl_FragColor.r *= 2.0 * uColor.r;
} else {
gl_FragColor.r = 1.0 - 2.0 * (1.0 - gl_FragColor.r) * (1.0 - uColor.r);
}
if (uColor.g < 0.5) {
gl_FragColor.g *= 2.0 * uColor.g;
} else {
gl_FragColor.g = 1.0 - 2.0 * (1.0 - gl_FragColor.g) * (1.0 - uColor.g);
}
if (uColor.b < 0.5) {
gl_FragColor.b *= 2.0 * uColor.b;
} else {
gl_FragColor.b = 1.0 - 2.0 * (1.0 - gl_FragColor.b) * (1.0 - uColor.b);
}
`,tint:`gl_FragColor.rgb *= (1.0 - uColor.a);
gl_FragColor.rgb += uColor.rgb;
`},buildSource:function(e){return`precision highp float;
uniform sampler2D uTexture;
uniform vec4 uColor;
varying vec2 vTexCoord;
void main() {
vec4 color = texture2D(uTexture, vTexCoord);
gl_FragColor = color;
if (color.a > 0.0) {
`+this.fragmentSource[e]+`}
}`},retrieveShader:function(e){var r=this.type+"_"+this.mode,t;return e.programCache.hasOwnProperty(r)||(t=this.buildSource(this.mode),e.programCache[r]=this.createProgram(e.context,t)),e.programCache[r]},applyTo2d:function(e){var r=e.imageData,t=r.data,n=t.length,o,i,l,u,d,g,v,m=1-this.alpha;v=new s.Color(this.color).getSource(),o=v[0]*this.alpha,i=v[1]*this.alpha,l=v[2]*this.alpha;for(var y=0;y<n;y+=4)switch(u=t[y],d=t[y+1],g=t[y+2],this.mode){case"multiply":t[y]=u*o/255,t[y+1]=d*i/255,t[y+2]=g*l/255;break;case"screen":t[y]=255-(255-u)*(255-o)/255,t[y+1]=255-(255-d)*(255-i)/255,t[y+2]=255-(255-g)*(255-l)/255;break;case"add":t[y]=u+o,t[y+1]=d+i,t[y+2]=g+l;break;case"diff":case"difference":t[y]=Math.abs(u-o),t[y+1]=Math.abs(d-i),t[y+2]=Math.abs(g-l);break;case"subtract":t[y]=u-o,t[y+1]=d-i,t[y+2]=g-l;break;case"darken":t[y]=Math.min(u,o),t[y+1]=Math.min(d,i),t[y+2]=Math.min(g,l);break;case"lighten":t[y]=Math.max(u,o),t[y+1]=Math.max(d,i),t[y+2]=Math.max(g,l);break;case"overlay":t[y]=o<128?2*u*o/255:255-2*(255-u)*(255-o)/255,t[y+1]=i<128?2*d*i/255:255-2*(255-d)*(255-i)/255,t[y+2]=l<128?2*g*l/255:255-2*(255-g)*(255-l)/255;break;case"exclusion":t[y]=o+u-2*o*u/255,t[y+1]=i+d-2*i*d/255,t[y+2]=l+g-2*l*g/255;break;case"tint":t[y]=o+u*m,t[y+1]=i+d*m,t[y+2]=l+g*m}},getUniformLocations:function(e,r){return{uColor:e.getUniformLocation(r,"uColor")}},sendUniformData:function(e,r){var t=new s.Color(this.color).getSource();t[0]=this.alpha*t[0]/255,t[1]=this.alpha*t[1]/255,t[2]=this.alpha*t[2]/255,t[3]=this.alpha,e.uniform4fv(r.uColor,t)},toObject:function(){return{type:this.type,color:this.color,mode:this.mode,alpha:this.alpha}}}),s.Image.filters.BlendColor.fromObject=s.Image.filters.BaseFilter.fromObject}(A),function(c){var s=c.fabric,h=s.Image.filters,a=s.util.createClass;h.BlendImage=a(h.BaseFilter,{type:"BlendImage",image:null,mode:"multiply",alpha:1,vertexSource:`attribute vec2 aPosition;
varying vec2 vTexCoord;
varying vec2 vTexCoord2;
uniform mat3 uTransformMatrix;
void main() {
vTexCoord = aPosition;
vTexCoord2 = (uTransformMatrix * vec3(aPosition, 1.0)).xy;
gl_Position = vec4(aPosition * 2.0 - 1.0, 0.0, 1.0);
}`,fragmentSource:{multiply:`precision highp float;
uniform sampler2D uTexture;
uniform sampler2D uImage;
uniform vec4 uColor;
varying vec2 vTexCoord;
varying vec2 vTexCoord2;
void main() {
vec4 color = texture2D(uTexture, vTexCoord);
vec4 color2 = texture2D(uImage, vTexCoord2);
color.rgba *= color2.rgba;
gl_FragColor = color;
}`,mask:`precision highp float;
uniform sampler2D uTexture;
uniform sampler2D uImage;
uniform vec4 uColor;
varying vec2 vTexCoord;
varying vec2 vTexCoord2;
void main() {
vec4 color = texture2D(uTexture, vTexCoord);
vec4 color2 = texture2D(uImage, vTexCoord2);
color.a = color2.a;
gl_FragColor = color;
}`},retrieveShader:function(e){var r=this.type+"_"+this.mode,t=this.fragmentSource[this.mode];return e.programCache.hasOwnProperty(r)||(e.programCache[r]=this.createProgram(e.context,t)),e.programCache[r]},applyToWebGL:function(e){var r=e.context,t=this.createTexture(e.filterBackend,this.image);this.bindAdditionalTexture(r,t,r.TEXTURE1),this.callSuper("applyToWebGL",e),this.unbindAdditionalTexture(r,r.TEXTURE1)},createTexture:function(e,r){return e.getCachedTexture(r.cacheKey,r._element)},calculateMatrix:function(){var e=this.image,r=e._element.width,t=e._element.height;return[1/e.scaleX,0,0,0,1/e.scaleY,0,-e.left/r,-e.top/t,1]},applyTo2d:function(e){var r=e.imageData,t=e.filterBackend.resources,n=r.data,o=n.length,i=r.width,l=r.height,u,d,g,v,m,y,w,F,Y,z,N=this.image,q;t.blendImage||(t.blendImage=s.util.createCanvasElement()),Y=t.blendImage,z=Y.getContext("2d"),Y.width!==i||Y.height!==l?(Y.width=i,Y.height=l):z.clearRect(0,0,i,l),z.setTransform(N.scaleX,0,0,N.scaleY,N.left,N.top),z.drawImage(N._element,0,0,i,l),q=z.getImageData(0,0,i,l).data;for(var H=0;H<o;H+=4)switch(m=n[H],y=n[H+1],w=n[H+2],F=n[H+3],u=q[H],d=q[H+1],g=q[H+2],v=q[H+3],this.mode){case"multiply":n[H]=m*u/255,n[H+1]=y*d/255,n[H+2]=w*g/255,n[H+3]=F*v/255;break;case"mask":n[H+3]=v;break}},getUniformLocations:function(e,r){return{uTransformMatrix:e.getUniformLocation(r,"uTransformMatrix"),uImage:e.getUniformLocation(r,"uImage")}},sendUniformData:function(e,r){var t=this.calculateMatrix();e.uniform1i(r.uImage,1),e.uniformMatrix3fv(r.uTransformMatrix,!1,t)},toObject:function(){return{type:this.type,image:this.image&&this.image.toObject(),mode:this.mode,alpha:this.alpha}}}),s.Image.filters.BlendImage.fromObject=function(e,r){s.Image.fromObject(e.image,function(t){var n=s.util.object.clone(e);n.image=t,r(new s.Image.filters.BlendImage(n))})}}(A),function(c){var s=c.fabric||(c.fabric={}),h=Math.pow,a=Math.floor,e=Math.sqrt,r=Math.abs,t=Math.round,n=Math.sin,o=Math.ceil,i=s.Image.filters,l=s.util.createClass;i.Resize=l(i.BaseFilter,{type:"Resize",resizeType:"hermite",scaleX:1,scaleY:1,lanczosLobes:3,getUniformLocations:function(u,d){return{uDelta:u.getUniformLocation(d,"uDelta"),uTaps:u.getUniformLocation(d,"uTaps")}},sendUniformData:function(u,d){u.uniform2fv(d.uDelta,this.horizontal?[1/this.width,0]:[0,1/this.height]),u.uniform1fv(d.uTaps,this.taps)},retrieveShader:function(u){var d=this.getFilterWindow(),g=this.type+"_"+d;if(!u.programCache.hasOwnProperty(g)){var v=this.generateShader(d);u.programCache[g]=this.createProgram(u.context,v)}return u.programCache[g]},getFilterWindow:function(){var u=this.tempScale;return Math.ceil(this.lanczosLobes/u)},getTaps:function(){for(var u=this.lanczosCreate(this.lanczosLobes),d=this.tempScale,g=this.getFilterWindow(),v=new Array(g),m=1;m<=g;m++)v[m-1]=u(m*d);return v},generateShader:function(v){for(var d=new Array(v),g=this.fragmentSourceTOP,v,m=1;m<=v;m++)d[m-1]=m+".0 * uDelta";return g+="uniform float uTaps["+v+`];
`,g+=`void main() {
`,g+=`  vec4 color = texture2D(uTexture, vTexCoord);
`,g+=`  float sum = 1.0;
`,d.forEach(function(y,w){g+="  color += texture2D(uTexture, vTexCoord + "+y+") * uTaps["+w+`];
`,g+="  color += texture2D(uTexture, vTexCoord - "+y+") * uTaps["+w+`];
`,g+="  sum += 2.0 * uTaps["+w+`];
`}),g+=`  gl_FragColor = color / sum;
`,g+="}",g},fragmentSourceTOP:`precision highp float;
uniform sampler2D uTexture;
uniform vec2 uDelta;
varying vec2 vTexCoord;
`,applyTo:function(u){u.webgl?(u.passes++,this.width=u.sourceWidth,this.horizontal=!0,this.dW=Math.round(this.width*this.scaleX),this.dH=u.sourceHeight,this.tempScale=this.dW/this.width,this.taps=this.getTaps(),u.destinationWidth=this.dW,this._setupFrameBuffer(u),this.applyToWebGL(u),this._swapTextures(u),u.sourceWidth=u.destinationWidth,this.height=u.sourceHeight,this.horizontal=!1,this.dH=Math.round(this.height*this.scaleY),this.tempScale=this.dH/this.height,this.taps=this.getTaps(),u.destinationHeight=this.dH,this._setupFrameBuffer(u),this.applyToWebGL(u),this._swapTextures(u),u.sourceHeight=u.destinationHeight):this.applyTo2d(u)},isNeutralState:function(){return this.scaleX===1&&this.scaleY===1},lanczosCreate:function(u){return function(d){if(d>=u||d<=-u)return 0;if(d<11920929e-14&&d>-11920929e-14)return 1;d*=Math.PI;var g=d/u;return n(d)/d*n(g)/g}},applyTo2d:function(u){var d=u.imageData,g=this.scaleX,v=this.scaleY;this.rcpScaleX=1/g,this.rcpScaleY=1/v;var m=d.width,y=d.height,w=t(m*g),F=t(y*v),Y;this.resizeType==="sliceHack"?Y=this.sliceByTwo(u,m,y,w,F):this.resizeType==="hermite"?Y=this.hermiteFastResize(u,m,y,w,F):this.resizeType==="bilinear"?Y=this.bilinearFiltering(u,m,y,w,F):this.resizeType==="lanczos"&&(Y=this.lanczosResize(u,m,y,w,F)),u.imageData=Y},sliceByTwo:function(u,d,g,v,m){var y=u.imageData,w=.5,F=!1,Y=!1,z=d*w,N=g*w,q=s.filterBackend.resources,H,U,J=0,Q=0,Z=d,p=0;for(q.sliceByTwo||(q.sliceByTwo=document.createElement("canvas")),H=q.sliceByTwo,(H.width<d*1.5||H.height<g)&&(H.width=d*1.5,H.height=g),U=H.getContext("2d"),U.clearRect(0,0,d*1.5,g),U.putImageData(y,0,0),v=a(v),m=a(m);!F||!Y;)d=z,g=N,v<a(z*w)?z=a(z*w):(z=v,F=!0),m<a(N*w)?N=a(N*w):(N=m,Y=!0),U.drawImage(H,J,Q,d,g,Z,p,z,N),J=Z,Q=p,p+=N;return U.getImageData(J,Q,v,m)},lanczosResize:function(u,d,g,v,m){function y(x){var S,E,C,_,T,P,L,k,B,W,V;for(p.x=(x+.5)*N,b.x=a(p.x),S=0;S<m;S++){for(p.y=(S+.5)*q,b.y=a(p.y),T=0,P=0,L=0,k=0,B=0,E=b.x-J;E<=b.x+J;E++)if(!(E<0||E>=d)){W=a(1e3*r(E-p.x)),Z[W]||(Z[W]={});for(var D=b.y-Q;D<=b.y+Q;D++)D<0||D>=g||(V=a(1e3*r(D-p.y)),Z[W][V]||(Z[W][V]=z(e(h(W*H,2)+h(V*U,2))/1e3)),C=Z[W][V],C>0&&(_=(D*d+E)*4,T+=C,P+=C*w[_],L+=C*w[_+1],k+=C*w[_+2],B+=C*w[_+3]))}_=(S*v+x)*4,Y[_]=P/T,Y[_+1]=L/T,Y[_+2]=k/T,Y[_+3]=B/T}return++x<v?y(x):F}var w=u.imageData.data,F=u.ctx.createImageData(v,m),Y=F.data,z=this.lanczosCreate(this.lanczosLobes),N=this.rcpScaleX,q=this.rcpScaleY,H=2/this.rcpScaleX,U=2/this.rcpScaleY,J=o(N*this.lanczosLobes/2),Q=o(q*this.lanczosLobes/2),Z={},p={},b={};return y(0)},bilinearFiltering:function(u,d,g,v,m){var y,w,F,Y,z,N,q,H,U,J,Q,Z,p=0,b,x=this.rcpScaleX,S=this.rcpScaleY,E=4*(d-1),C=u.imageData,_=C.data,T=u.ctx.createImageData(v,m),P=T.data;for(q=0;q<m;q++)for(H=0;H<v;H++)for(z=a(x*H),N=a(S*q),U=x*H-z,J=S*q-N,b=4*(N*d+z),Q=0;Q<4;Q++)y=_[b+Q],w=_[b+4+Q],F=_[b+E+Q],Y=_[b+E+4+Q],Z=y*(1-U)*(1-J)+w*U*(1-J)+F*J*(1-U)+Y*U*J,P[p++]=Z;return T},hermiteFastResize:function(u,d,g,v,m){for(var y=this.rcpScaleX,w=this.rcpScaleY,F=o(y/2),Y=o(w/2),z=u.imageData,N=z.data,q=u.ctx.createImageData(v,m),H=q.data,U=0;U<m;U++)for(var J=0;J<v;J++){for(var Q=(J+U*v)*4,Z=0,p=0,b=0,x=0,S=0,E=0,C=0,_=(U+.5)*w,T=a(U*w);T<(U+1)*w;T++)for(var P=r(_-(T+.5))/Y,L=(J+.5)*y,k=P*P,B=a(J*y);B<(J+1)*y;B++){var W=r(L-(B+.5))/F,V=e(k+W*W);V>1&&V<-1||(Z=2*V*V*V-3*V*V+1,Z>0&&(W=4*(B+T*d),C+=Z*N[W+3],b+=Z,N[W+3]<255&&(Z=Z*N[W+3]/250),x+=Z*N[W],S+=Z*N[W+1],E+=Z*N[W+2],p+=Z))}H[Q]=x/p,H[Q+1]=S/p,H[Q+2]=E/p,H[Q+3]=C/b}return q},toObject:function(){return{type:this.type,scaleX:this.scaleX,scaleY:this.scaleY,resizeType:this.resizeType,lanczosLobes:this.lanczosLobes}}}),s.Image.filters.Resize.fromObject=s.Image.filters.BaseFilter.fromObject}(A),function(c){var s=c.fabric||(c.fabric={}),h=s.Image.filters,a=s.util.createClass;h.Contrast=a(h.BaseFilter,{type:"Contrast",fragmentSource:`precision highp float;
uniform sampler2D uTexture;
uniform float uContrast;
varying vec2 vTexCoord;
void main() {
vec4 color = texture2D(uTexture, vTexCoord);
float contrastF = 1.015 * (uContrast + 1.0) / (1.0 * (1.015 - uContrast));
color.rgb = contrastF * (color.rgb - 0.5) + 0.5;
gl_FragColor = color;
}`,contrast:0,mainParameter:"contrast",applyTo2d:function(e){if(this.contrast!==0){var r=e.imageData,t,o,n=r.data,o=n.length,i=Math.floor(this.contrast*255),l=259*(i+255)/(255*(259-i));for(t=0;t<o;t+=4)n[t]=l*(n[t]-128)+128,n[t+1]=l*(n[t+1]-128)+128,n[t+2]=l*(n[t+2]-128)+128}},getUniformLocations:function(e,r){return{uContrast:e.getUniformLocation(r,"uContrast")}},sendUniformData:function(e,r){e.uniform1f(r.uContrast,this.contrast)}}),s.Image.filters.Contrast.fromObject=s.Image.filters.BaseFilter.fromObject}(A),function(c){var s=c.fabric||(c.fabric={}),h=s.Image.filters,a=s.util.createClass;h.Saturation=a(h.BaseFilter,{type:"Saturation",fragmentSource:`precision highp float;
uniform sampler2D uTexture;
uniform float uSaturation;
varying vec2 vTexCoord;
void main() {
vec4 color = texture2D(uTexture, vTexCoord);
float rgMax = max(color.r, color.g);
float rgbMax = max(rgMax, color.b);
color.r += rgbMax != color.r ? (rgbMax - color.r) * uSaturation : 0.00;
color.g += rgbMax != color.g ? (rgbMax - color.g) * uSaturation : 0.00;
color.b += rgbMax != color.b ? (rgbMax - color.b) * uSaturation : 0.00;
gl_FragColor = color;
}`,saturation:0,mainParameter:"saturation",applyTo2d:function(e){if(this.saturation!==0){var r=e.imageData,t=r.data,n=t.length,o=-this.saturation,i,l;for(i=0;i<n;i+=4)l=Math.max(t[i],t[i+1],t[i+2]),t[i]+=l!==t[i]?(l-t[i])*o:0,t[i+1]+=l!==t[i+1]?(l-t[i+1])*o:0,t[i+2]+=l!==t[i+2]?(l-t[i+2])*o:0}},getUniformLocations:function(e,r){return{uSaturation:e.getUniformLocation(r,"uSaturation")}},sendUniformData:function(e,r){e.uniform1f(r.uSaturation,-this.saturation)}}),s.Image.filters.Saturation.fromObject=s.Image.filters.BaseFilter.fromObject}(A),function(c){var s=c.fabric||(c.fabric={}),h=s.Image.filters,a=s.util.createClass;h.Vibrance=a(h.BaseFilter,{type:"Vibrance",fragmentSource:`precision highp float;
uniform sampler2D uTexture;
uniform float uVibrance;
varying vec2 vTexCoord;
void main() {
vec4 color = texture2D(uTexture, vTexCoord);
float max = max(color.r, max(color.g, color.b));
float avg = (color.r + color.g + color.b) / 3.0;
float amt = (abs(max - avg) * 2.0) * uVibrance;
color.r += max != color.r ? (max - color.r) * amt : 0.00;
color.g += max != color.g ? (max - color.g) * amt : 0.00;
color.b += max != color.b ? (max - color.b) * amt : 0.00;
gl_FragColor = color;
}`,vibrance:0,mainParameter:"vibrance",applyTo2d:function(e){if(this.vibrance!==0){var r=e.imageData,t=r.data,n=t.length,o=-this.vibrance,i,l,u,d;for(i=0;i<n;i+=4)l=Math.max(t[i],t[i+1],t[i+2]),u=(t[i]+t[i+1]+t[i+2])/3,d=Math.abs(l-u)*2/255*o,t[i]+=l!==t[i]?(l-t[i])*d:0,t[i+1]+=l!==t[i+1]?(l-t[i+1])*d:0,t[i+2]+=l!==t[i+2]?(l-t[i+2])*d:0}},getUniformLocations:function(e,r){return{uVibrance:e.getUniformLocation(r,"uVibrance")}},sendUniformData:function(e,r){e.uniform1f(r.uVibrance,-this.vibrance)}}),s.Image.filters.Vibrance.fromObject=s.Image.filters.BaseFilter.fromObject}(A),function(c){var s=c.fabric||(c.fabric={}),h=s.Image.filters,a=s.util.createClass;h.Blur=a(h.BaseFilter,{type:"Blur",fragmentSource:`precision highp float;
uniform sampler2D uTexture;
uniform vec2 uDelta;
varying vec2 vTexCoord;
const float nSamples = 15.0;
vec3 v3offset = vec3(12.9898, 78.233, 151.7182);
float random(vec3 scale) {
return fract(sin(dot(gl_FragCoord.xyz, scale)) * 43758.5453);
}
void main() {
vec4 color = vec4(0.0);
float total = 0.0;
float offset = random(v3offset);
for (float t = -nSamples; t <= nSamples; t++) {
float percent = (t + offset - 0.5) / nSamples;
float weight = 1.0 - abs(percent);
color += texture2D(uTexture, vTexCoord + uDelta * percent) * weight;
total += weight;
}
gl_FragColor = color / total;
}`,blur:0,mainParameter:"blur",applyTo:function(e){e.webgl?(this.aspectRatio=e.sourceWidth/e.sourceHeight,e.passes++,this._setupFrameBuffer(e),this.horizontal=!0,this.applyToWebGL(e),this._swapTextures(e),this._setupFrameBuffer(e),this.horizontal=!1,this.applyToWebGL(e),this._swapTextures(e)):this.applyTo2d(e)},applyTo2d:function(e){e.imageData=this.simpleBlur(e)},simpleBlur:function(e){var r=e.filterBackend.resources,t,n,o=e.imageData.width,i=e.imageData.height;r.blurLayer1||(r.blurLayer1=s.util.createCanvasElement(),r.blurLayer2=s.util.createCanvasElement()),t=r.blurLayer1,n=r.blurLayer2,(t.width!==o||t.height!==i)&&(n.width=t.width=o,n.height=t.height=i);var l=t.getContext("2d"),u=n.getContext("2d"),d=15,g,v,m,y,w=this.blur*.06*.5;for(l.putImageData(e.imageData,0,0),u.clearRect(0,0,o,i),y=-d;y<=d;y++)g=(Math.random()-.5)/4,v=y/d,m=w*v*o+g,u.globalAlpha=1-Math.abs(v),u.drawImage(t,m,g),l.drawImage(n,0,0),u.globalAlpha=1,u.clearRect(0,0,n.width,n.height);for(y=-d;y<=d;y++)g=(Math.random()-.5)/4,v=y/d,m=w*v*i+g,u.globalAlpha=1-Math.abs(v),u.drawImage(t,g,m),l.drawImage(n,0,0),u.globalAlpha=1,u.clearRect(0,0,n.width,n.height);e.ctx.drawImage(t,0,0);var F=e.ctx.getImageData(0,0,t.width,t.height);return l.globalAlpha=1,l.clearRect(0,0,t.width,t.height),F},getUniformLocations:function(e,r){return{delta:e.getUniformLocation(r,"uDelta")}},sendUniformData:function(e,r){var t=this.chooseRightDelta();e.uniform2fv(r.delta,t)},chooseRightDelta:function(){var e=1,r=[0,0],t;return this.horizontal?this.aspectRatio>1&&(e=1/this.aspectRatio):this.aspectRatio<1&&(e=this.aspectRatio),t=e*this.blur*.12,this.horizontal?r[0]=t:r[1]=t,r}}),h.Blur.fromObject=s.Image.filters.BaseFilter.fromObject}(A),function(c){var s=c.fabric||(c.fabric={}),h=s.Image.filters,a=s.util.createClass;h.Gamma=a(h.BaseFilter,{type:"Gamma",fragmentSource:`precision highp float;
uniform sampler2D uTexture;
uniform vec3 uGamma;
varying vec2 vTexCoord;
void main() {
vec4 color = texture2D(uTexture, vTexCoord);
vec3 correction = (1.0 / uGamma);
color.r = pow(color.r, correction.r);
color.g = pow(color.g, correction.g);
color.b = pow(color.b, correction.b);
gl_FragColor = color;
gl_FragColor.rgb *= color.a;
}`,gamma:[1,1,1],mainParameter:"gamma",initialize:function(e){this.gamma=[1,1,1],h.BaseFilter.prototype.initialize.call(this,e)},applyTo2d:function(e){var r=e.imageData,t=r.data,n=this.gamma,o=t.length,i=1/n[0],l=1/n[1],u=1/n[2],d;for(this.rVals||(this.rVals=new Uint8Array(256),this.gVals=new Uint8Array(256),this.bVals=new Uint8Array(256)),d=0,o=256;d<o;d++)this.rVals[d]=Math.pow(d/255,i)*255,this.gVals[d]=Math.pow(d/255,l)*255,this.bVals[d]=Math.pow(d/255,u)*255;for(d=0,o=t.length;d<o;d+=4)t[d]=this.rVals[t[d]],t[d+1]=this.gVals[t[d+1]],t[d+2]=this.bVals[t[d+2]]},getUniformLocations:function(e,r){return{uGamma:e.getUniformLocation(r,"uGamma")}},sendUniformData:function(e,r){e.uniform3fv(r.uGamma,this.gamma)}}),s.Image.filters.Gamma.fromObject=s.Image.filters.BaseFilter.fromObject}(A),function(c){var s=c.fabric||(c.fabric={}),h=s.Image.filters,a=s.util.createClass;h.Composed=a(h.BaseFilter,{type:"Composed",subFilters:[],initialize:function(e){this.callSuper("initialize",e),this.subFilters=this.subFilters.slice(0)},applyTo:function(e){e.passes+=this.subFilters.length-1,this.subFilters.forEach(function(r){r.applyTo(e)})},toObject:function(){return s.util.object.extend(this.callSuper("toObject"),{subFilters:this.subFilters.map(function(e){return e.toObject()})})},isNeutralState:function(){return!this.subFilters.some(function(e){return!e.isNeutralState()})}}),s.Image.filters.Composed.fromObject=function(e,r){var t=e.subFilters||[],n=t.map(function(i){return new s.Image.filters[i.type](i)}),o=new s.Image.filters.Composed({subFilters:n});return r&&r(o),o}}(A),function(c){var s=c.fabric||(c.fabric={}),h=s.Image.filters,a=s.util.createClass;h.HueRotation=a(h.ColorMatrix,{type:"HueRotation",rotation:0,mainParameter:"rotation",calculateMatrix:function(){var e=this.rotation*Math.PI,r=s.util.cos(e),t=s.util.sin(e),n=1/3,o=Math.sqrt(n)*t,i=1-r;this.matrix=[1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0],this.matrix[0]=r+i/3,this.matrix[1]=n*i-o,this.matrix[2]=n*i+o,this.matrix[5]=n*i+o,this.matrix[6]=r+n*i,this.matrix[7]=n*i-o,this.matrix[10]=n*i-o,this.matrix[11]=n*i+o,this.matrix[12]=r+n*i},isNeutralState:function(e){return this.calculateMatrix(),h.BaseFilter.prototype.isNeutralState.call(this,e)},applyTo:function(e){this.calculateMatrix(),h.BaseFilter.prototype.applyTo.call(this,e)}}),s.Image.filters.HueRotation.fromObject=s.Image.filters.BaseFilter.fromObject}(A),function(c){var s=c.fabric||(c.fabric={}),h=s.util.object.clone;if(s.Text){s.warn("fabric.Text is already defined");return}var a="fontFamily fontWeight fontSize text underline overline linethrough textAlign fontStyle lineHeight textBackgroundColor charSpacing styles direction path pathStartOffset pathSide pathAlign".split(" ");s.Text=s.util.createClass(s.Object,{_dimensionAffectingProps:["fontSize","fontWeight","fontFamily","fontStyle","lineHeight","text","charSpacing","textAlign","styles","path","pathStartOffset","pathSide","pathAlign"],_reNewline:/\r?\n/,_reSpacesAndTabs:/[ \t\r]/g,_reSpaceAndTab:/[ \t\r]/,_reWords:/\S+/g,type:"text",fontSize:40,fontWeight:"normal",fontFamily:"Times New Roman",underline:!1,overline:!1,linethrough:!1,textAlign:"left",fontStyle:"normal",lineHeight:1.16,superscript:{size:.6,baseline:-.35},subscript:{size:.6,baseline:.11},textBackgroundColor:"",stateProperties:s.Object.prototype.stateProperties.concat(a),cacheProperties:s.Object.prototype.cacheProperties.concat(a),stroke:null,shadow:null,path:null,pathStartOffset:0,pathSide:"left",pathAlign:"baseline",_fontSizeFraction:.222,offsets:{underline:.1,linethrough:-.315,overline:-.88},_fontSizeMult:1.13,charSpacing:0,styles:null,_measuringContext:null,deltaY:0,direction:"ltr",_styleProperties:["stroke","strokeWidth","fill","fontFamily","fontSize","fontWeight","fontStyle","underline","overline","linethrough","deltaY","textBackgroundColor"],__charBounds:[],CACHE_FONT_SIZE:400,MIN_TEXT_WIDTH:2,initialize:function(e,r){this.styles=r?r.styles||{}:{},this.text=e,this.__skipDimension=!0,this.callSuper("initialize",r),this.path&&this.setPathInfo(),this.__skipDimension=!1,this.initDimensions(),this.setCoords(),this.setupState({propertySet:"_dimensionAffectingProps"})},setPathInfo:function(){var e=this.path;e&&(e.segmentsInfo=s.util.getPathSegmentsInfo(e.path))},getMeasuringContext:function(){return s._measuringContext||(s._measuringContext=this.canvas&&this.canvas.contextCache||s.util.createCanvasElement().getContext("2d")),s._measuringContext},_splitText:function(){var e=this._splitTextIntoLines(this.text);return this.textLines=e.lines,this._textLines=e.graphemeLines,this._unwrappedTextLines=e._unwrappedLines,this._text=e.graphemeText,e},initDimensions:function(){this.__skipDimension||(this._splitText(),this._clearCache(),this.path?(this.width=this.path.width,this.height=this.path.height):(this.width=this.calcTextWidth()||this.cursorWidth||this.MIN_TEXT_WIDTH,this.height=this.calcTextHeight()),this.textAlign.indexOf("justify")!==-1&&this.enlargeSpaces(),this.saveState({propertySet:"_dimensionAffectingProps"}))},enlargeSpaces:function(){for(var e,r,t,n,o,i,l,u=0,d=this._textLines.length;u<d;u++)if(!(this.textAlign!=="justify"&&(u===d-1||this.isEndOfWrapping(u)))&&(n=0,o=this._textLines[u],r=this.getLineWidth(u),r<this.width&&(l=this.textLines[u].match(this._reSpacesAndTabs)))){t=l.length,e=(this.width-r)/t;for(var g=0,v=o.length;g<=v;g++)i=this.__charBounds[u][g],this._reSpaceAndTab.test(o[g])?(i.width+=e,i.kernedWidth+=e,i.left+=n,n+=e):i.left+=n}},isEndOfWrapping:function(e){return e===this._textLines.length-1},missingNewlineOffset:function(){return 1},toString:function(){return"#<fabric.Text ("+this.complexity()+'): { "text": "'+this.text+'", "fontFamily": "'+this.fontFamily+'" }>'},_getCacheCanvasDimensions:function(){var e=this.callSuper("_getCacheCanvasDimensions"),r=this.fontSize;return e.width+=r*e.zoomX,e.height+=r*e.zoomY,e},_render:function(e){var r=this.path;r&&!r.isNotVisible()&&r._render(e),this._setTextStyles(e),this._renderTextLinesBackground(e),this._renderTextDecoration(e,"underline"),this._renderText(e),this._renderTextDecoration(e,"overline"),this._renderTextDecoration(e,"linethrough")},_renderText:function(e){this.paintFirst==="stroke"?(this._renderTextStroke(e),this._renderTextFill(e)):(this._renderTextFill(e),this._renderTextStroke(e))},_setTextStyles:function(e,r,t){if(e.textBaseline="alphabetical",this.path)switch(this.pathAlign){case"center":e.textBaseline="middle";break;case"ascender":e.textBaseline="top";break;case"descender":e.textBaseline="bottom";break}e.font=this._getFontDeclaration(r,t)},calcTextWidth:function(){for(var e=this.getLineWidth(0),r=1,t=this._textLines.length;r<t;r++){var n=this.getLineWidth(r);n>e&&(e=n)}return e},_renderTextLine:function(e,r,t,n,o,i){this._renderChars(e,r,t,n,o,i)},_renderTextLinesBackground:function(e){if(!(!this.textBackgroundColor&&!this.styleHas("textBackgroundColor"))){for(var r,t,n=e.fillStyle,o,i,l=this._getLeftOffset(),u=this._getTopOffset(),d=0,g=0,v,m,y=this.path,w,F=0,Y=this._textLines.length;F<Y;F++){if(r=this.getHeightOfLine(F),!this.textBackgroundColor&&!this.styleHas("textBackgroundColor",F)){u+=r;continue}o=this._textLines[F],t=this._getLineLeftOffset(F),g=0,d=0,i=this.getValueOfPropertyAt(F,0,"textBackgroundColor");for(var z=0,N=o.length;z<N;z++)v=this.__charBounds[F][z],m=this.getValueOfPropertyAt(F,z,"textBackgroundColor"),y?(e.save(),e.translate(v.renderLeft,v.renderTop),e.rotate(v.angle),e.fillStyle=m,m&&e.fillRect(-v.width/2,-r/this.lineHeight*(1-this._fontSizeFraction),v.width,r/this.lineHeight),e.restore()):m!==i?(w=l+t+d,this.direction==="rtl"&&(w=this.width-w-g),e.fillStyle=i,i&&e.fillRect(w,u,g,r/this.lineHeight),d=v.left,g=v.width,i=m):g+=v.kernedWidth;m&&!y&&(w=l+t+d,this.direction==="rtl"&&(w=this.width-w-g),e.fillStyle=m,e.fillRect(w,u,g,r/this.lineHeight)),u+=r}e.fillStyle=n,this._removeShadow(e)}},getFontCache:function(e){var r=e.fontFamily.toLowerCase();s.charWidthsCache[r]||(s.charWidthsCache[r]={});var t=s.charWidthsCache[r],n=e.fontStyle.toLowerCase()+"_"+(e.fontWeight+"").toLowerCase();return t[n]||(t[n]={}),t[n]},_measureChar:function(e,r,t,n){var o=this.getFontCache(r),i=this._getFontDeclaration(r),l=this._getFontDeclaration(n),u=t+e,d=i===l,g,v,m,y=r.fontSize/this.CACHE_FONT_SIZE,w;if(t&&o[t]!==void 0&&(m=o[t]),o[e]!==void 0&&(w=g=o[e]),d&&o[u]!==void 0&&(v=o[u],w=v-m),g===void 0||m===void 0||v===void 0){var F=this.getMeasuringContext();this._setTextStyles(F,r,!0)}return g===void 0&&(w=g=F.measureText(e).width,o[e]=g),m===void 0&&d&&t&&(m=F.measureText(t).width,o[t]=m),d&&v===void 0&&(v=F.measureText(u).width,o[u]=v,w=v-m),{width:g*y,kernedWidth:w*y}},getHeightOfChar:function(e,r){return this.getValueOfPropertyAt(e,r,"fontSize")},measureLine:function(e){var r=this._measureLine(e);return this.charSpacing!==0&&(r.width-=this._getWidthOfCharSpacing()),r.width<0&&(r.width=0),r},_measureLine:function(e){var r=0,t,n,o=this._textLines[e],i,l,u=0,d=new Array(o.length),g=0,v,m,y=this.path,w=this.pathSide==="right";for(this.__charBounds[e]=d,t=0;t<o.length;t++)n=o[t],l=this._getGraphemeBox(n,e,t,i),d[t]=l,r+=l.kernedWidth,i=n;if(d[t]={left:l?l.left+l.width:0,width:0,kernedWidth:0,height:this.fontSize},y){switch(m=y.segmentsInfo[y.segmentsInfo.length-1].length,v=s.util.getPointOnPath(y.path,0,y.segmentsInfo),v.x+=y.pathOffset.x,v.y+=y.pathOffset.y,this.textAlign){case"left":g=w?m-r:0;break;case"center":g=(m-r)/2;break;case"right":g=w?0:m-r;break}for(g+=this.pathStartOffset*(w?-1:1),t=w?o.length-1:0;w?t>=0:t<o.length;w?t--:t++)l=d[t],g>m?g%=m:g<0&&(g+=m),this._setGraphemeOnPath(g,l,v),g+=l.kernedWidth}return{width:r,numOfSpaces:u}},_setGraphemeOnPath:function(e,r,t){var n=e+r.kernedWidth/2,o=this.path,i=s.util.getPointOnPath(o.path,n,o.segmentsInfo);r.renderLeft=i.x-t.x,r.renderTop=i.y-t.y,r.angle=i.angle+(this.pathSide==="right"?Math.PI:0)},_getGraphemeBox:function(e,r,t,n,o){var i=this.getCompleteStyleDeclaration(r,t),l=n?this.getCompleteStyleDeclaration(r,t-1):{},u=this._measureChar(e,i,n,l),d=u.kernedWidth,g=u.width,v;this.charSpacing!==0&&(v=this._getWidthOfCharSpacing(),g+=v,d+=v);var m={width:g,left:0,height:i.fontSize,kernedWidth:d,deltaY:i.deltaY};if(t>0&&!o){var y=this.__charBounds[r][t-1];m.left=y.left+y.width+u.kernedWidth-u.width}return m},getHeightOfLine:function(e){if(this.__lineHeights[e])return this.__lineHeights[e];for(var r=this._textLines[e],t=this.getHeightOfChar(e,0),n=1,o=r.length;n<o;n++)t=Math.max(this.getHeightOfChar(e,n),t);return this.__lineHeights[e]=t*this.lineHeight*this._fontSizeMult},calcTextHeight:function(){for(var e,r=0,t=0,n=this._textLines.length;t<n;t++)e=this.getHeightOfLine(t),r+=t===n-1?e/this.lineHeight:e;return r},_getLeftOffset:function(){return this.direction==="ltr"?-this.width/2:this.width/2},_getTopOffset:function(){return-this.height/2},_renderTextCommon:function(e,r){e.save();for(var t=0,n=this._getLeftOffset(),o=this._getTopOffset(),i=0,l=this._textLines.length;i<l;i++){var u=this.getHeightOfLine(i),d=u/this.lineHeight,g=this._getLineLeftOffset(i);this._renderTextLine(r,e,this._textLines[i],n+g,o+t+d,i),t+=u}e.restore()},_renderTextFill:function(e){!this.fill&&!this.styleHas("fill")||this._renderTextCommon(e,"fillText")},_renderTextStroke:function(e){(!this.stroke||this.strokeWidth===0)&&this.isEmptyStyles()||(this.shadow&&!this.shadow.affectStroke&&this._removeShadow(e),e.save(),this._setLineDash(e,this.strokeDashArray),e.beginPath(),this._renderTextCommon(e,"strokeText"),e.closePath(),e.restore())},_renderChars:function(e,r,t,n,o,i){var l=this.getHeightOfLine(i),u=this.textAlign.indexOf("justify")!==-1,d,g,v="",m,y=0,w,F=this.path,Y=!u&&this.charSpacing===0&&this.isEmptyStyles(i)&&!F,z=this.direction==="ltr",N=this.direction==="ltr"?1:-1,q,H=r.canvas.getAttribute("dir");if(r.save(),H!==this.direction&&(r.canvas.setAttribute("dir",z?"ltr":"rtl"),r.direction=z?"ltr":"rtl",r.textAlign=z?"left":"right"),o-=l*this._fontSizeFraction/this.lineHeight,Y){this._renderChar(e,r,i,0,t.join(""),n,o,l),r.restore();return}for(var U=0,J=t.length-1;U<=J;U++)w=U===J||this.charSpacing||F,v+=t[U],m=this.__charBounds[i][U],y===0?(n+=N*(m.kernedWidth-m.width),y+=m.width):y+=m.kernedWidth,u&&!w&&this._reSpaceAndTab.test(t[U])&&(w=!0),w||(d=d||this.getCompleteStyleDeclaration(i,U),g=this.getCompleteStyleDeclaration(i,U+1),w=s.util.hasStyleChanged(d,g,!1)),w&&(F?(r.save(),r.translate(m.renderLeft,m.renderTop),r.rotate(m.angle),this._renderChar(e,r,i,U,v,-y/2,0,l),r.restore()):(q=n,this._renderChar(e,r,i,U,v,q,o,l)),v="",d=g,n+=N*y,y=0);r.restore()},_applyPatternGradientTransformText:function(e){var r=s.util.createCanvasElement(),t,n=this.width+this.strokeWidth,o=this.height+this.strokeWidth;return r.width=n,r.height=o,t=r.getContext("2d"),t.beginPath(),t.moveTo(0,0),t.lineTo(n,0),t.lineTo(n,o),t.lineTo(0,o),t.closePath(),t.translate(n/2,o/2),t.fillStyle=e.toLive(t),this._applyPatternGradientTransform(t,e),t.fill(),t.createPattern(r,"no-repeat")},handleFiller:function(e,r,t){var n,o;return t.toLive?t.gradientUnits==="percentage"||t.gradientTransform||t.patternTransform?(n=-this.width/2,o=-this.height/2,e.translate(n,o),e[r]=this._applyPatternGradientTransformText(t),{offsetX:n,offsetY:o}):(e[r]=t.toLive(e,this),this._applyPatternGradientTransform(e,t)):(e[r]=t,{offsetX:0,offsetY:0})},_setStrokeStyles:function(e,r){return e.lineWidth=r.strokeWidth,e.lineCap=this.strokeLineCap,e.lineDashOffset=this.strokeDashOffset,e.lineJoin=this.strokeLineJoin,e.miterLimit=this.strokeMiterLimit,this.handleFiller(e,"strokeStyle",r.stroke)},_setFillStyles:function(e,r){return this.handleFiller(e,"fillStyle",r.fill)},_renderChar:function(e,r,t,n,o,i,l){var u=this._getStyleDeclaration(t,n),d=this.getCompleteStyleDeclaration(t,n),g=e==="fillText"&&d.fill,v=e==="strokeText"&&d.stroke&&d.strokeWidth,m,y;!v&&!g||(r.save(),g&&(m=this._setFillStyles(r,d)),v&&(y=this._setStrokeStyles(r,d)),r.font=this._getFontDeclaration(d),u&&u.textBackgroundColor&&this._removeShadow(r),u&&u.deltaY&&(l+=u.deltaY),g&&r.fillText(o,i-m.offsetX,l-m.offsetY),v&&r.strokeText(o,i-y.offsetX,l-y.offsetY),r.restore())},setSuperscript:function(e,r){return this._setScript(e,r,this.superscript)},setSubscript:function(e,r){return this._setScript(e,r,this.subscript)},_setScript:function(e,r,t){var n=this.get2DCursorLocation(e,!0),o=this.getValueOfPropertyAt(n.lineIndex,n.charIndex,"fontSize"),i=this.getValueOfPropertyAt(n.lineIndex,n.charIndex,"deltaY"),l={fontSize:o*t.size,deltaY:i+o*t.baseline};return this.setSelectionStyles(l,e,r),this},_getLineLeftOffset:function(e){var r=this.getLineWidth(e),t=this.width-r,n=this.textAlign,o=this.direction,l,i=0,l=this.isEndOfWrapping(e);return n==="justify"||n==="justify-center"&&!l||n==="justify-right"&&!l||n==="justify-left"&&!l?0:(n==="center"&&(i=t/2),n==="right"&&(i=t),n==="justify-center"&&(i=t/2),n==="justify-right"&&(i=t),o==="rtl"&&(i-=t),i)},_clearCache:function(){this.__lineWidths=[],this.__lineHeights=[],this.__charBounds=[]},_shouldClearDimensionCache:function(){var e=this._forceClearCache;return e||(e=this.hasStateChanged("_dimensionAffectingProps")),e&&(this.dirty=!0,this._forceClearCache=!1),e},getLineWidth:function(e){if(this.__lineWidths[e]!==void 0)return this.__lineWidths[e];var r=this.measureLine(e),t=r.width;return this.__lineWidths[e]=t,t},_getWidthOfCharSpacing:function(){return this.charSpacing!==0?this.fontSize*this.charSpacing/1e3:0},getValueOfPropertyAt:function(e,r,t){var n=this._getStyleDeclaration(e,r);return n&&typeof n[t]<"u"?n[t]:this[t]},_renderTextDecoration:function(e,r){if(!(!this[r]&&!this.styleHas(r))){for(var t,n,o,i,l,u,d,g,v=this._getLeftOffset(),m=this._getTopOffset(),y,w,F,Y,z,N,q,H,U=this.path,J=this._getWidthOfCharSpacing(),Q=this.offsets[r],Z=0,p=this._textLines.length;Z<p;Z++){if(t=this.getHeightOfLine(Z),!this[r]&&!this.styleHas(r,Z)){m+=t;continue}d=this._textLines[Z],N=t/this.lineHeight,i=this._getLineLeftOffset(Z),w=0,F=0,g=this.getValueOfPropertyAt(Z,0,r),H=this.getValueOfPropertyAt(Z,0,"fill"),y=m+N*(1-this._fontSizeFraction),n=this.getHeightOfChar(Z,0),l=this.getValueOfPropertyAt(Z,0,"deltaY");for(var b=0,x=d.length;b<x;b++)if(Y=this.__charBounds[Z][b],z=this.getValueOfPropertyAt(Z,b,r),q=this.getValueOfPropertyAt(Z,b,"fill"),o=this.getHeightOfChar(Z,b),u=this.getValueOfPropertyAt(Z,b,"deltaY"),U&&z&&q)e.save(),e.fillStyle=H,e.translate(Y.renderLeft,Y.renderTop),e.rotate(Y.angle),e.fillRect(-Y.kernedWidth/2,Q*o+u,Y.kernedWidth,this.fontSize/15),e.restore();else if((z!==g||q!==H||o!==n||u!==l)&&F>0){var S=v+i+w;this.direction==="rtl"&&(S=this.width-S-F),g&&H&&(e.fillStyle=H,e.fillRect(S,y+Q*n+l,F,this.fontSize/15)),w=Y.left,F=Y.width,g=z,H=q,n=o,l=u}else F+=Y.kernedWidth;var S=v+i+w;this.direction==="rtl"&&(S=this.width-S-F),e.fillStyle=q,z&&q&&e.fillRect(S,y+Q*n+l,F-J,this.fontSize/15),m+=t}this._removeShadow(e)}},_getFontDeclaration:function(e,r){var t=e||this,n=this.fontFamily,o=s.Text.genericFonts.indexOf(n.toLowerCase())>-1,i=n===void 0||n.indexOf("'")>-1||n.indexOf(",")>-1||n.indexOf('"')>-1||o?t.fontFamily:'"'+t.fontFamily+'"';return[s.isLikelyNode?t.fontWeight:t.fontStyle,s.isLikelyNode?t.fontStyle:t.fontWeight,r?this.CACHE_FONT_SIZE+"px":t.fontSize+"px",i].join(" ")},render:function(e){this.visible&&(this.canvas&&this.canvas.skipOffscreen&&!this.group&&!this.isOnScreen()||(this._shouldClearDimensionCache()&&this.initDimensions(),this.callSuper("render",e)))},_splitTextIntoLines:function(e){for(var r=e.split(this._reNewline),t=new Array(r.length),n=[`
`],o=[],i=0;i<r.length;i++)t[i]=s.util.string.graphemeSplit(r[i]),o=o.concat(t[i],n);return o.pop(),{_unwrappedLines:t,lines:r,graphemeText:o,graphemeLines:t}},toObject:function(e){var r=a.concat(e),t=this.callSuper("toObject",r);return t.styles=s.util.stylesToArray(this.styles,this.text),t.path&&(t.path=this.path.toObject()),t},set:function(e,r){this.callSuper("set",e,r);var t=!1,n=!1;if(typeof e=="object")for(var o in e)o==="path"&&this.setPathInfo(),t=t||this._dimensionAffectingProps.indexOf(o)!==-1,n=n||o==="path";else t=this._dimensionAffectingProps.indexOf(e)!==-1,n=e==="path";return n&&this.setPathInfo(),t&&(this.initDimensions(),this.setCoords()),this},complexity:function(){return 1}}),s.Text.ATTRIBUTE_NAMES=s.SHARED_ATTRIBUTES.concat("x y dx dy font-family font-style font-weight font-size letter-spacing text-decoration text-anchor".split(" ")),s.Text.DEFAULT_SVG_FONT_SIZE=16,s.Text.fromElement=function(e,r,t){if(!e)return r(null);var n=s.parseAttributes(e,s.Text.ATTRIBUTE_NAMES),o=n.textAnchor||"left";if(t=s.util.object.extend(t?h(t):{},n),t.top=t.top||0,t.left=t.left||0,n.textDecoration){var i=n.textDecoration;i.indexOf("underline")!==-1&&(t.underline=!0),i.indexOf("overline")!==-1&&(t.overline=!0),i.indexOf("line-through")!==-1&&(t.linethrough=!0),delete t.textDecoration}"dx"in n&&(t.left+=n.dx),"dy"in n&&(t.top+=n.dy),"fontSize"in t||(t.fontSize=s.Text.DEFAULT_SVG_FONT_SIZE);var l="";"textContent"in e?l=e.textContent:"firstChild"in e&&e.firstChild!==null&&"data"in e.firstChild&&e.firstChild.data!==null&&(l=e.firstChild.data),l=l.replace(/^\s+|\s+$|\n+/g,"").replace(/\s+/g," ");var u=t.strokeWidth;t.strokeWidth=0;var d=new s.Text(l,t),g=d.getScaledHeight()/d.height,v=(d.height+d.strokeWidth)*d.lineHeight-d.height,m=v*g,y=d.getScaledHeight()+m,w=0;o==="center"&&(w=d.getScaledWidth()/2),o==="right"&&(w=d.getScaledWidth()),d.set({left:d.left-w,top:d.top-(y-d.fontSize*(.07+d._fontSizeFraction))/d.lineHeight,strokeWidth:typeof u<"u"?u:1}),r(d)},s.Text.fromObject=function(e,r){var t=h(e),n=e.path;return delete t.path,s.Object._fromObject("Text",t,function(o){o.styles=s.util.stylesFromArray(e.styles,e.text),n?s.Object._fromObject("Path",n,function(i){o.set("path",i),r(o)},"path"):r(o)},"text")},s.Text.genericFonts=["sans-serif","serif","cursive","fantasy","monospace"],s.util.createAccessors&&s.util.createAccessors(s.Text)}(A),function(){f.util.object.extend(f.Text.prototype,{isEmptyStyles:function(c){if(!this.styles||typeof c<"u"&&!this.styles[c])return!0;var s=typeof c>"u"?this.styles:{line:this.styles[c]};for(var h in s)for(var a in s[h])for(var e in s[h][a])return!1;return!0},styleHas:function(c,s){if(!this.styles||!c||c===""||typeof s<"u"&&!this.styles[s])return!1;var h=typeof s>"u"?this.styles:{0:this.styles[s]};for(var a in h)for(var e in h[a])if(typeof h[a][e][c]<"u")return!0;return!1},cleanStyle:function(c){if(!this.styles||!c||c==="")return!1;var s=this.styles,h=0,a,e,r=!0,t=0,n;for(var o in s){a=0;for(var i in s[o]){var n=s[o][i],l=n.hasOwnProperty(c);h++,l?(e?n[c]!==e&&(r=!1):e=n[c],n[c]===this[c]&&delete n[c]):r=!1,Object.keys(n).length!==0?a++:delete s[o][i]}a===0&&delete s[o]}for(var u=0;u<this._textLines.length;u++)t+=this._textLines[u].length;r&&h===t&&(this[c]=e,this.removeStyle(c))},removeStyle:function(c){if(!(!this.styles||!c||c==="")){var s=this.styles,h,a,e;for(a in s){h=s[a];for(e in h)delete h[e][c],Object.keys(h[e]).length===0&&delete h[e];Object.keys(h).length===0&&delete s[a]}}},_extendStyles:function(c,s){var h=this.get2DCursorLocation(c);this._getLineStyle(h.lineIndex)||this._setLineStyle(h.lineIndex),this._getStyleDeclaration(h.lineIndex,h.charIndex)||this._setStyleDeclaration(h.lineIndex,h.charIndex,{}),f.util.object.extend(this._getStyleDeclaration(h.lineIndex,h.charIndex),s)},get2DCursorLocation:function(c,s){typeof c>"u"&&(c=this.selectionStart);for(var h=s?this._unwrappedTextLines:this._textLines,a=h.length,e=0;e<a;e++){if(c<=h[e].length)return{lineIndex:e,charIndex:c};c-=h[e].length+this.missingNewlineOffset(e)}return{lineIndex:e-1,charIndex:h[e-1].length<c?h[e-1].length:c}},getSelectionStyles:function(c,s,h){typeof c>"u"&&(c=this.selectionStart||0),typeof s>"u"&&(s=this.selectionEnd||c);for(var a=[],e=c;e<s;e++)a.push(this.getStyleAtPosition(e,h));return a},getStyleAtPosition:function(c,s){var h=this.get2DCursorLocation(c),a=s?this.getCompleteStyleDeclaration(h.lineIndex,h.charIndex):this._getStyleDeclaration(h.lineIndex,h.charIndex);return a||{}},setSelectionStyles:function(c,s,h){typeof s>"u"&&(s=this.selectionStart||0),typeof h>"u"&&(h=this.selectionEnd||s);for(var a=s;a<h;a++)this._extendStyles(a,c);return this._forceClearCache=!0,this},_getStyleDeclaration:function(c,s){var h=this.styles&&this.styles[c];return h?h[s]:null},getCompleteStyleDeclaration:function(c,s){for(var h=this._getStyleDeclaration(c,s)||{},a={},e,r=0;r<this._styleProperties.length;r++)e=this._styleProperties[r],a[e]=typeof h[e]>"u"?this[e]:h[e];return a},_setStyleDeclaration:function(c,s,h){this.styles[c][s]=h},_deleteStyleDeclaration:function(c,s){delete this.styles[c][s]},_getLineStyle:function(c){return!!this.styles[c]},_setLineStyle:function(c){this.styles[c]={}},_deleteLineStyle:function(c){delete this.styles[c]}})}(),function(){function c(s){s.textDecoration&&(s.textDecoration.indexOf("underline")>-1&&(s.underline=!0),s.textDecoration.indexOf("line-through")>-1&&(s.linethrough=!0),s.textDecoration.indexOf("overline")>-1&&(s.overline=!0),delete s.textDecoration)}f.IText=f.util.createClass(f.Text,f.Observable,{type:"i-text",selectionStart:0,selectionEnd:0,selectionColor:"rgba(17,119,255,0.3)",isEditing:!1,editable:!0,editingBorderColor:"rgba(102,153,255,0.25)",cursorWidth:2,cursorColor:"",cursorDelay:1e3,cursorDuration:600,caching:!0,hiddenTextareaContainer:null,_reSpace:/\s|\n/,_currentCursorOpacity:0,_selectionDirection:null,_abortCursorAnimation:!1,__widthOfSpace:[],inCompositionMode:!1,initialize:function(s,h){this.callSuper("initialize",s,h),this.initBehavior()},setSelectionStart:function(s){s=Math.max(s,0),this._updateAndFire("selectionStart",s)},setSelectionEnd:function(s){s=Math.min(s,this.text.length),this._updateAndFire("selectionEnd",s)},_updateAndFire:function(s,h){this[s]!==h&&(this._fireSelectionChanged(),this[s]=h),this._updateTextarea()},_fireSelectionChanged:function(){this.fire("selection:changed"),this.canvas&&this.canvas.fire("text:selection:changed",{target:this})},initDimensions:function(){this.isEditing&&this.initDelayedCursor(),this.clearContextTop(),this.callSuper("initDimensions")},render:function(s){this.clearContextTop(),this.callSuper("render",s),this.cursorOffsetCache={},this.renderCursorOrSelection()},_render:function(s){this.callSuper("_render",s)},clearContextTop:function(s){if(!(!this.isEditing||!this.canvas||!this.canvas.contextTop)){var h=this.canvas.contextTop,a=this.canvas.viewportTransform;h.save(),h.transform(a[0],a[1],a[2],a[3],a[4],a[5]),this.transform(h),this._clearTextArea(h),s||h.restore()}},renderCursorOrSelection:function(){if(!(!this.isEditing||!this.canvas||!this.canvas.contextTop)){var s=this._getCursorBoundaries(),h=this.canvas.contextTop;this.clearContextTop(!0),this.selectionStart===this.selectionEnd?this.renderCursor(s,h):this.renderSelection(s,h),h.restore()}},_clearTextArea:function(s){var h=this.width+4,a=this.height+4;s.clearRect(-h/2,-a/2,h,a)},_getCursorBoundaries:function(s){typeof s>"u"&&(s=this.selectionStart);var h=this._getLeftOffset(),a=this._getTopOffset(),e=this._getCursorBoundariesOffsets(s);return{left:h,top:a,leftOffset:e.left,topOffset:e.top}},_getCursorBoundariesOffsets:function(s){if(this.cursorOffsetCache&&"top"in this.cursorOffsetCache)return this.cursorOffsetCache;var h,a,e,r=0,t=0,n,o=this.get2DCursorLocation(s);e=o.charIndex,a=o.lineIndex;for(var i=0;i<a;i++)r+=this.getHeightOfLine(i);h=this._getLineLeftOffset(a);var l=this.__charBounds[a][e];return l&&(t=l.left),this.charSpacing!==0&&e===this._textLines[a].length&&(t-=this._getWidthOfCharSpacing()),n={top:r,left:h+(t>0?t:0)},this.direction==="rtl"&&(n.left*=-1),this.cursorOffsetCache=n,this.cursorOffsetCache},renderCursor:function(s,h){var a=this.get2DCursorLocation(),e=a.lineIndex,r=a.charIndex>0?a.charIndex-1:0,t=this.getValueOfPropertyAt(e,r,"fontSize"),n=this.scaleX*this.canvas.getZoom(),o=this.cursorWidth/n,i=s.topOffset,l=this.getValueOfPropertyAt(e,r,"deltaY");i+=(1-this._fontSizeFraction)*this.getHeightOfLine(e)/this.lineHeight-t*(1-this._fontSizeFraction),this.inCompositionMode&&this.renderSelection(s,h),h.fillStyle=this.cursorColor||this.getValueOfPropertyAt(e,r,"fill"),h.globalAlpha=this.__isMousedown?1:this._currentCursorOpacity,h.fillRect(s.left+s.leftOffset-o/2,i+s.top+l,o,t)},renderSelection:function(s,h){for(var a=this.inCompositionMode?this.hiddenTextarea.selectionStart:this.selectionStart,e=this.inCompositionMode?this.hiddenTextarea.selectionEnd:this.selectionEnd,r=this.textAlign.indexOf("justify")!==-1,t=this.get2DCursorLocation(a),n=this.get2DCursorLocation(e),o=t.lineIndex,i=n.lineIndex,l=t.charIndex<0?0:t.charIndex,u=n.charIndex<0?0:n.charIndex,d=o;d<=i;d++){var g=this._getLineLeftOffset(d)||0,v=this.getHeightOfLine(d),m=0,y=0,w=0;if(d===o&&(y=this.__charBounds[o][l].left),d>=o&&d<i)w=r&&!this.isEndOfWrapping(d)?this.width:this.getLineWidth(d)||5;else if(d===i)if(u===0)w=this.__charBounds[i][u].left;else{var F=this._getWidthOfCharSpacing();w=this.__charBounds[i][u-1].left+this.__charBounds[i][u-1].width-F}m=v,(this.lineHeight<1||d===i&&this.lineHeight>1)&&(v/=this.lineHeight);var Y=s.left+g+y,z=w-y,N=v,q=0;this.inCompositionMode?(h.fillStyle=this.compositionColor||"black",N=1,q=v):h.fillStyle=this.selectionColor,this.direction==="rtl"&&(Y=this.width-Y-z),h.fillRect(Y,s.top+s.topOffset+q,z,N),s.topOffset+=m}},getCurrentCharFontSize:function(){var s=this._getCurrentCharIndex();return this.getValueOfPropertyAt(s.l,s.c,"fontSize")},getCurrentCharColor:function(){var s=this._getCurrentCharIndex();return this.getValueOfPropertyAt(s.l,s.c,"fill")},_getCurrentCharIndex:function(){var s=this.get2DCursorLocation(this.selectionStart,!0),h=s.charIndex>0?s.charIndex-1:0;return{l:s.lineIndex,c:h}}}),f.IText.fromObject=function(s,h){var a=f.util.stylesFromArray(s.styles,s.text),e=Object.assign({},s,{styles:a});if(c(e),e.styles)for(var r in e.styles)for(var t in e.styles[r])c(e.styles[r][t]);f.Object._fromObject("IText",e,h,"text")}}(),function(){var c=f.util.object.clone;f.util.object.extend(f.IText.prototype,{initBehavior:function(){this.initAddedHandler(),this.initRemovedHandler(),this.initCursorSelectionHandlers(),this.initDoubleClickSimulation(),this.mouseMoveHandler=this.mouseMoveHandler.bind(this)},onDeselect:function(){this.isEditing&&this.exitEditing(),this.selected=!1},initAddedHandler:function(){var s=this;this.on("added",function(){var h=s.canvas;h&&(h._hasITextHandlers||(h._hasITextHandlers=!0,s._initCanvasHandlers(h)),h._iTextInstances=h._iTextInstances||[],h._iTextInstances.push(s))})},initRemovedHandler:function(){var s=this;this.on("removed",function(){var h=s.canvas;h&&(h._iTextInstances=h._iTextInstances||[],f.util.removeFromArray(h._iTextInstances,s),h._iTextInstances.length===0&&(h._hasITextHandlers=!1,s._removeCanvasHandlers(h)))})},_initCanvasHandlers:function(s){s._mouseUpITextHandler=function(){s._iTextInstances&&s._iTextInstances.forEach(function(h){h.__isMousedown=!1})},s.on("mouse:up",s._mouseUpITextHandler)},_removeCanvasHandlers:function(s){s.off("mouse:up",s._mouseUpITextHandler)},_tick:function(){this._currentTickState=this._animateCursor(this,1,this.cursorDuration,"_onTickComplete")},_animateCursor:function(s,h,a,e){var r;return r={isAborted:!1,abort:function(){this.isAborted=!0}},s.animate("_currentCursorOpacity",h,{duration:a,onComplete:function(){r.isAborted||s[e]()},onChange:function(){s.canvas&&s.selectionStart===s.selectionEnd&&s.renderCursorOrSelection()},abort:function(){return r.isAborted}}),r},_onTickComplete:function(){var s=this;this._cursorTimeout1&&clearTimeout(this._cursorTimeout1),this._cursorTimeout1=setTimeout(function(){s._currentTickCompleteState=s._animateCursor(s,0,this.cursorDuration/2,"_tick")},100)},initDelayedCursor:function(s){var h=this,a=s?0:this.cursorDelay;this.abortCursorAnimation(),this._currentCursorOpacity=1,this._cursorTimeout2=setTimeout(function(){h._tick()},a)},abortCursorAnimation:function(){var s=this._currentTickState||this._currentTickCompleteState,h=this.canvas;this._currentTickState&&this._currentTickState.abort(),this._currentTickCompleteState&&this._currentTickCompleteState.abort(),clearTimeout(this._cursorTimeout1),clearTimeout(this._cursorTimeout2),this._currentCursorOpacity=0,s&&h&&h.clearContext(h.contextTop||h.contextContainer)},selectAll:function(){return this.selectionStart=0,this.selectionEnd=this._text.length,this._fireSelectionChanged(),this._updateTextarea(),this},getSelectedText:function(){return this._text.slice(this.selectionStart,this.selectionEnd).join("")},findWordBoundaryLeft:function(s){var h=0,a=s-1;if(this._reSpace.test(this._text[a]))for(;this._reSpace.test(this._text[a]);)h++,a--;for(;/\S/.test(this._text[a])&&a>-1;)h++,a--;return s-h},findWordBoundaryRight:function(s){var h=0,a=s;if(this._reSpace.test(this._text[a]))for(;this._reSpace.test(this._text[a]);)h++,a++;for(;/\S/.test(this._text[a])&&a<this._text.length;)h++,a++;return s+h},findLineBoundaryLeft:function(s){for(var h=0,a=s-1;!/\n/.test(this._text[a])&&a>-1;)h++,a--;return s-h},findLineBoundaryRight:function(s){for(var h=0,a=s;!/\n/.test(this._text[a])&&a<this._text.length;)h++,a++;return s+h},searchWordBoundary:function(s,h){for(var a=this._text,e=this._reSpace.test(a[s])?s-1:s,r=a[e],t=f.reNonWord;!t.test(r)&&e>0&&e<a.length;)e+=h,r=a[e];return t.test(r)&&(e+=h===1?0:1),e},selectWord:function(s){s=s||this.selectionStart;var h=this.searchWordBoundary(s,-1),a=this.searchWordBoundary(s,1);this.selectionStart=h,this.selectionEnd=a,this._fireSelectionChanged(),this._updateTextarea(),this.renderCursorOrSelection()},selectLine:function(s){s=s||this.selectionStart;var h=this.findLineBoundaryLeft(s),a=this.findLineBoundaryRight(s);return this.selectionStart=h,this.selectionEnd=a,this._fireSelectionChanged(),this._updateTextarea(),this},enterEditing:function(s){if(!(this.isEditing||!this.editable))return this.canvas&&(this.canvas.calcOffset(),this.exitEditingOnOthers(this.canvas)),this.isEditing=!0,this.initHiddenTextarea(s),this.hiddenTextarea.focus(),this.hiddenTextarea.value=this.text,this._updateTextarea(),this._saveEditingProps(),this._setEditingProps(),this._textBeforeEdit=this.text,this._tick(),this.fire("editing:entered"),this._fireSelectionChanged(),this.canvas?(this.canvas.fire("text:editing:entered",{target:this}),this.initMouseMoveHandler(),this.canvas.requestRenderAll(),this):this},exitEditingOnOthers:function(s){s._iTextInstances&&s._iTextInstances.forEach(function(h){h.selected=!1,h.isEditing&&h.exitEditing()})},initMouseMoveHandler:function(){this.canvas.on("mouse:move",this.mouseMoveHandler)},mouseMoveHandler:function(s){if(!(!this.__isMousedown||!this.isEditing)){document.activeElement!==this.hiddenTextarea&&this.hiddenTextarea.focus();var h=this.getSelectionStartFromPointer(s.e),a=this.selectionStart,e=this.selectionEnd;(h!==this.__selectionStartOnMouseDown||a===e)&&(a===h||e===h)||(h>this.__selectionStartOnMouseDown?(this.selectionStart=this.__selectionStartOnMouseDown,this.selectionEnd=h):(this.selectionStart=h,this.selectionEnd=this.__selectionStartOnMouseDown),(this.selectionStart!==a||this.selectionEnd!==e)&&(this.restartCursorIfNeeded(),this._fireSelectionChanged(),this._updateTextarea(),this.renderCursorOrSelection()))}},_setEditingProps:function(){this.hoverCursor="text",this.canvas&&(this.canvas.defaultCursor=this.canvas.moveCursor="text"),this.borderColor=this.editingBorderColor,this.hasControls=this.selectable=!1,this.lockMovementX=this.lockMovementY=!0},fromStringToGraphemeSelection:function(s,h,a){var e=a.slice(0,s),r=f.util.string.graphemeSplit(e).length;if(s===h)return{selectionStart:r,selectionEnd:r};var t=a.slice(s,h),n=f.util.string.graphemeSplit(t).length;return{selectionStart:r,selectionEnd:r+n}},fromGraphemeToStringSelection:function(s,h,a){var e=a.slice(0,s),r=e.join("").length;if(s===h)return{selectionStart:r,selectionEnd:r};var t=a.slice(s,h),n=t.join("").length;return{selectionStart:r,selectionEnd:r+n}},_updateTextarea:function(){if(this.cursorOffsetCache={},!!this.hiddenTextarea){if(!this.inCompositionMode){var s=this.fromGraphemeToStringSelection(this.selectionStart,this.selectionEnd,this._text);this.hiddenTextarea.selectionStart=s.selectionStart,this.hiddenTextarea.selectionEnd=s.selectionEnd}this.updateTextareaPosition()}},updateFromTextArea:function(){if(this.hiddenTextarea){this.cursorOffsetCache={},this.text=this.hiddenTextarea.value,this._shouldClearDimensionCache()&&(this.initDimensions(),this.setCoords());var s=this.fromStringToGraphemeSelection(this.hiddenTextarea.selectionStart,this.hiddenTextarea.selectionEnd,this.hiddenTextarea.value);this.selectionEnd=this.selectionStart=s.selectionEnd,this.inCompositionMode||(this.selectionStart=s.selectionStart),this.updateTextareaPosition()}},updateTextareaPosition:function(){if(this.selectionStart===this.selectionEnd){var s=this._calcTextareaPosition();this.hiddenTextarea.style.left=s.left,this.hiddenTextarea.style.top=s.top}},_calcTextareaPosition:function(){if(!this.canvas)return{x:1,y:1};var s=this.inCompositionMode?this.compositionStart:this.selectionStart,h=this._getCursorBoundaries(s),a=this.get2DCursorLocation(s),e=a.lineIndex,r=a.charIndex,t=this.getValueOfPropertyAt(e,r,"fontSize")*this.lineHeight,n=h.leftOffset,o=this.calcTransformMatrix(),i={x:h.left+n,y:h.top+h.topOffset+t},l=this.canvas.getRetinaScaling(),u=this.canvas.upperCanvasEl,d=u.width/l,g=u.height/l,v=d-t,m=g-t,y=u.clientWidth/d,w=u.clientHeight/g;return i=f.util.transformPoint(i,o),i=f.util.transformPoint(i,this.canvas.viewportTransform),i.x*=y,i.y*=w,i.x<0&&(i.x=0),i.x>v&&(i.x=v),i.y<0&&(i.y=0),i.y>m&&(i.y=m),i.x+=this.canvas._offset.left,i.y+=this.canvas._offset.top,{left:i.x+"px",top:i.y+"px",fontSize:t+"px",charHeight:t}},_saveEditingProps:function(){this._savedProps={hasControls:this.hasControls,borderColor:this.borderColor,lockMovementX:this.lockMovementX,lockMovementY:this.lockMovementY,hoverCursor:this.hoverCursor,selectable:this.selectable,defaultCursor:this.canvas&&this.canvas.defaultCursor,moveCursor:this.canvas&&this.canvas.moveCursor}},_restoreEditingProps:function(){this._savedProps&&(this.hoverCursor=this._savedProps.hoverCursor,this.hasControls=this._savedProps.hasControls,this.borderColor=this._savedProps.borderColor,this.selectable=this._savedProps.selectable,this.lockMovementX=this._savedProps.lockMovementX,this.lockMovementY=this._savedProps.lockMovementY,this.canvas&&(this.canvas.defaultCursor=this._savedProps.defaultCursor,this.canvas.moveCursor=this._savedProps.moveCursor))},exitEditing:function(){var s=this._textBeforeEdit!==this.text,h=this.hiddenTextarea;return this.selected=!1,this.isEditing=!1,this.selectionEnd=this.selectionStart,h&&(h.blur&&h.blur(),h.parentNode&&h.parentNode.removeChild(h)),this.hiddenTextarea=null,this.abortCursorAnimation(),this._restoreEditingProps(),this._currentCursorOpacity=0,this._shouldClearDimensionCache()&&(this.initDimensions(),this.setCoords()),this.fire("editing:exited"),s&&this.fire("modified"),this.canvas&&(this.canvas.off("mouse:move",this.mouseMoveHandler),this.canvas.fire("text:editing:exited",{target:this}),s&&this.canvas.fire("object:modified",{target:this})),this},_removeExtraneousStyles:function(){for(var s in this.styles)this._textLines[s]||delete this.styles[s]},removeStyleFromTo:function(s,h){var a=this.get2DCursorLocation(s,!0),e=this.get2DCursorLocation(h,!0),r=a.lineIndex,t=a.charIndex,n=e.lineIndex,o=e.charIndex,i,l;if(r!==n){if(this.styles[r])for(i=t;i<this._unwrappedTextLines[r].length;i++)delete this.styles[r][i];if(this.styles[n])for(i=o;i<this._unwrappedTextLines[n].length;i++)l=this.styles[n][i],l&&(this.styles[r]||(this.styles[r]={}),this.styles[r][t+i-o]=l);for(i=r+1;i<=n;i++)delete this.styles[i];this.shiftLineStyles(n,r-n)}else if(this.styles[r]){l=this.styles[r];var u=o-t,d,g;for(i=t;i<o;i++)delete l[i];for(g in this.styles[r])d=parseInt(g,10),d>=o&&(l[d-u]=l[g],delete l[g])}},shiftLineStyles:function(s,h){var a=c(this.styles);for(var e in this.styles){var r=parseInt(e,10);r>s&&(this.styles[r+h]=a[r],a[r-h]||delete this.styles[r])}},restartCursorIfNeeded:function(){(!this._currentTickState||this._currentTickState.isAborted||!this._currentTickCompleteState||this._currentTickCompleteState.isAborted)&&this.initDelayedCursor()},insertNewlineStyleObject:function(s,h,a,e){var r,t={},n=!1,o=this._unwrappedTextLines[s].length===h;a||(a=1),this.shiftLineStyles(s,a),this.styles[s]&&(r=this.styles[s][h===0?h:h-1]);for(var i in this.styles[s]){var l=parseInt(i,10);l>=h&&(n=!0,t[l-h]=this.styles[s][i],o&&h===0||delete this.styles[s][i])}var u=!1;for(n&&!o&&(this.styles[s+a]=t,u=!0),u&&a--;a>0;)e&&e[a-1]?this.styles[s+a]={0:c(e[a-1])}:r?this.styles[s+a]={0:c(r)}:delete this.styles[s+a],a--;this._forceClearCache=!0},insertCharStyleObject:function(s,h,a,e){this.styles||(this.styles={});var r=this.styles[s],t=r?c(r):{};a||(a=1);for(var n in t){var o=parseInt(n,10);o>=h&&(r[o+a]=t[o],t[o-a]||delete r[o])}if(this._forceClearCache=!0,e){for(;a--;)Object.keys(e[a]).length&&(this.styles[s]||(this.styles[s]={}),this.styles[s][h+a]=c(e[a]));return}if(r)for(var i=r[h?h-1:1];i&&a--;)this.styles[s][h+a]=c(i)},insertNewStyleBlock:function(s,h,a){for(var e=this.get2DCursorLocation(h,!0),r=[0],t=0,n=0;n<s.length;n++)s[n]===`
`?(t++,r[t]=0):r[t]++;r[0]>0&&(this.insertCharStyleObject(e.lineIndex,e.charIndex,r[0],a),a=a&&a.slice(r[0]+1)),t&&this.insertNewlineStyleObject(e.lineIndex,e.charIndex+r[0],t);for(var n=1;n<t;n++)r[n]>0?this.insertCharStyleObject(e.lineIndex+n,0,r[n],a):a&&this.styles[e.lineIndex+n]&&a[0]&&(this.styles[e.lineIndex+n][0]=a[0]),a=a&&a.slice(r[n]+1);r[n]>0&&this.insertCharStyleObject(e.lineIndex+n,0,r[n],a)},setSelectionStartEndWithShift:function(s,h,a){a<=s?(h===s?this._selectionDirection="left":this._selectionDirection==="right"&&(this._selectionDirection="left",this.selectionEnd=s),this.selectionStart=a):a>s&&a<h?this._selectionDirection==="right"?this.selectionEnd=a:this.selectionStart=a:(h===s?this._selectionDirection="right":this._selectionDirection==="left"&&(this._selectionDirection="right",this.selectionStart=h),this.selectionEnd=a)},setSelectionInBoundaries:function(){var s=this.text.length;this.selectionStart>s?this.selectionStart=s:this.selectionStart<0&&(this.selectionStart=0),this.selectionEnd>s?this.selectionEnd=s:this.selectionEnd<0&&(this.selectionEnd=0)}})}(),f.util.object.extend(f.IText.prototype,{initDoubleClickSimulation:function(){this.__lastClickTime=+new Date,this.__lastLastClickTime=+new Date,this.__lastPointer={},this.on("mousedown",this.onMouseDown)},onMouseDown:function(c){if(this.canvas){this.__newClickTime=+new Date;var s=c.pointer;this.isTripleClick(s)&&(this.fire("tripleclick",c),this._stopEvent(c.e)),this.__lastLastClickTime=this.__lastClickTime,this.__lastClickTime=this.__newClickTime,this.__lastPointer=s,this.__lastIsEditing=this.isEditing,this.__lastSelected=this.selected}},isTripleClick:function(c){return this.__newClickTime-this.__lastClickTime<500&&this.__lastClickTime-this.__lastLastClickTime<500&&this.__lastPointer.x===c.x&&this.__lastPointer.y===c.y},_stopEvent:function(c){c.preventDefault&&c.preventDefault(),c.stopPropagation&&c.stopPropagation()},initCursorSelectionHandlers:function(){this.initMousedownHandler(),this.initMouseupHandler(),this.initClicks()},doubleClickHandler:function(c){this.isEditing&&this.selectWord(this.getSelectionStartFromPointer(c.e))},tripleClickHandler:function(c){this.isEditing&&this.selectLine(this.getSelectionStartFromPointer(c.e))},initClicks:function(){this.on("mousedblclick",this.doubleClickHandler),this.on("tripleclick",this.tripleClickHandler)},_mouseDownHandler:function(c){!this.canvas||!this.editable||c.e.button&&c.e.button!==1||(this.__isMousedown=!0,this.selected&&(this.inCompositionMode=!1,this.setCursorByClick(c.e)),this.isEditing&&(this.__selectionStartOnMouseDown=this.selectionStart,this.selectionStart===this.selectionEnd&&this.abortCursorAnimation(),this.renderCursorOrSelection()))},_mouseDownHandlerBefore:function(c){!this.canvas||!this.editable||c.e.button&&c.e.button!==1||(this.selected=this===this.canvas._activeObject)},initMousedownHandler:function(){this.on("mousedown",this._mouseDownHandler),this.on("mousedown:before",this._mouseDownHandlerBefore)},initMouseupHandler:function(){this.on("mouseup",this.mouseUpHandler)},mouseUpHandler:function(c){if(this.__isMousedown=!1,!(!this.editable||this.group||c.transform&&c.transform.actionPerformed||c.e.button&&c.e.button!==1)){if(this.canvas){var s=this.canvas._activeObject;if(s&&s!==this)return}this.__lastSelected&&!this.__corner?(this.selected=!1,this.__lastSelected=!1,this.enterEditing(c.e),this.selectionStart===this.selectionEnd?this.initDelayedCursor(!0):this.renderCursorOrSelection()):this.selected=!0}},setCursorByClick:function(c){var s=this.getSelectionStartFromPointer(c),h=this.selectionStart,a=this.selectionEnd;c.shiftKey?this.setSelectionStartEndWithShift(h,a,s):(this.selectionStart=s,this.selectionEnd=s),this.isEditing&&(this._fireSelectionChanged(),this._updateTextarea())},getSelectionStartFromPointer:function(c){for(var s=this.getLocalPointer(c),h=0,a=0,e=0,r=0,t=0,n,o,i=0,l=this._textLines.length;i<l&&e<=s.y;i++)e+=this.getHeightOfLine(i)*this.scaleY,t=i,i>0&&(r+=this._textLines[i-1].length+this.missingNewlineOffset(i-1));n=this._getLineLeftOffset(t),a=n*this.scaleX,o=this._textLines[t],this.direction==="rtl"&&(s.x=this.width*this.scaleX-s.x+a);for(var u=0,d=o.length;u<d&&(h=a,a+=this.__charBounds[t][u].kernedWidth*this.scaleX,a<=s.x);u++)r++;return this._getNewSelectionStartFromOffset(s,h,a,r,d)},_getNewSelectionStartFromOffset:function(c,s,h,a,e){var r=c.x-s,t=h-c.x,n=t>r||t<0?0:1,o=a+n;return this.flipX&&(o=e-o),o>this._text.length&&(o=this._text.length),o}}),f.util.object.extend(f.IText.prototype,{initHiddenTextarea:function(){this.hiddenTextarea=f.document.createElement("textarea"),this.hiddenTextarea.setAttribute("autocapitalize","off"),this.hiddenTextarea.setAttribute("autocorrect","off"),this.hiddenTextarea.setAttribute("autocomplete","off"),this.hiddenTextarea.setAttribute("spellcheck","false"),this.hiddenTextarea.setAttribute("data-fabric-hiddentextarea",""),this.hiddenTextarea.setAttribute("wrap","off");var c=this._calcTextareaPosition();this.hiddenTextarea.style.cssText="position: absolute; top: "+c.top+"; left: "+c.left+"; z-index: -999; opacity: 0; width: 1px; height: 1px; font-size: 1px; padding-top: "+c.fontSize+";",this.hiddenTextareaContainer?this.hiddenTextareaContainer.appendChild(this.hiddenTextarea):f.document.body.appendChild(this.hiddenTextarea),f.util.addListener(this.hiddenTextarea,"keydown",this.onKeyDown.bind(this)),f.util.addListener(this.hiddenTextarea,"keyup",this.onKeyUp.bind(this)),f.util.addListener(this.hiddenTextarea,"input",this.onInput.bind(this)),f.util.addListener(this.hiddenTextarea,"copy",this.copy.bind(this)),f.util.addListener(this.hiddenTextarea,"cut",this.copy.bind(this)),f.util.addListener(this.hiddenTextarea,"paste",this.paste.bind(this)),f.util.addListener(this.hiddenTextarea,"compositionstart",this.onCompositionStart.bind(this)),f.util.addListener(this.hiddenTextarea,"compositionupdate",this.onCompositionUpdate.bind(this)),f.util.addListener(this.hiddenTextarea,"compositionend",this.onCompositionEnd.bind(this)),!this._clickHandlerInitialized&&this.canvas&&(f.util.addListener(this.canvas.upperCanvasEl,"click",this.onClick.bind(this)),this._clickHandlerInitialized=!0)},keysMap:{9:"exitEditing",27:"exitEditing",33:"moveCursorUp",34:"moveCursorDown",35:"moveCursorRight",36:"moveCursorLeft",37:"moveCursorLeft",38:"moveCursorUp",39:"moveCursorRight",40:"moveCursorDown"},keysMapRtl:{9:"exitEditing",27:"exitEditing",33:"moveCursorUp",34:"moveCursorDown",35:"moveCursorLeft",36:"moveCursorRight",37:"moveCursorRight",38:"moveCursorUp",39:"moveCursorLeft",40:"moveCursorDown"},ctrlKeysMapUp:{67:"copy",88:"cut"},ctrlKeysMapDown:{65:"selectAll"},onClick:function(){this.hiddenTextarea&&this.hiddenTextarea.focus()},onKeyDown:function(c){if(this.isEditing){var s=this.direction==="rtl"?this.keysMapRtl:this.keysMap;if(c.keyCode in s)this[s[c.keyCode]](c);else if(c.keyCode in this.ctrlKeysMapDown&&(c.ctrlKey||c.metaKey))this[this.ctrlKeysMapDown[c.keyCode]](c);else return;c.stopImmediatePropagation(),c.preventDefault(),c.keyCode>=33&&c.keyCode<=40?(this.inCompositionMode=!1,this.clearContextTop(),this.renderCursorOrSelection()):this.canvas&&this.canvas.requestRenderAll()}},onKeyUp:function(c){if(!this.isEditing||this._copyDone||this.inCompositionMode){this._copyDone=!1;return}if(c.keyCode in this.ctrlKeysMapUp&&(c.ctrlKey||c.metaKey))this[this.ctrlKeysMapUp[c.keyCode]](c);else return;c.stopImmediatePropagation(),c.preventDefault(),this.canvas&&this.canvas.requestRenderAll()},onInput:function(c){var s=this.fromPaste;if(this.fromPaste=!1,c&&c.stopPropagation(),!!this.isEditing){var h=this._splitTextIntoLines(this.hiddenTextarea.value).graphemeText,a=this._text.length,e=h.length,r,t,n=e-a,o=this.selectionStart,i=this.selectionEnd,l=o!==i,u,d,g;if(this.hiddenTextarea.value===""){this.styles={},this.updateFromTextArea(),this.fire("changed"),this.canvas&&(this.canvas.fire("text:changed",{target:this}),this.canvas.requestRenderAll());return}var v=this.fromStringToGraphemeSelection(this.hiddenTextarea.selectionStart,this.hiddenTextarea.selectionEnd,this.hiddenTextarea.value),m=o>v.selectionStart;l?(r=this._text.slice(o,i),n+=i-o):e<a&&(m?r=this._text.slice(i+n,i):r=this._text.slice(o,o-n)),t=h.slice(v.selectionEnd-n,v.selectionEnd),r&&r.length&&(t.length&&(u=this.getSelectionStyles(o,o+1,!1),u=t.map(function(){return u[0]})),l?(d=o,g=i):m?(d=i-r.length,g=i):(d=i,g=i+r.length),this.removeStyleFromTo(d,g)),t.length&&(s&&t.join("")===f.copiedText&&!f.disableStyleCopyPaste&&(u=f.copiedTextStyle),this.insertNewStyleBlock(t,o,u)),this.updateFromTextArea(),this.fire("changed"),this.canvas&&(this.canvas.fire("text:changed",{target:this}),this.canvas.requestRenderAll())}},onCompositionStart:function(){this.inCompositionMode=!0},onCompositionEnd:function(){this.inCompositionMode=!1},onCompositionUpdate:function(c){this.compositionStart=c.target.selectionStart,this.compositionEnd=c.target.selectionEnd,this.updateTextareaPosition()},copy:function(){this.selectionStart!==this.selectionEnd&&(f.copiedText=this.getSelectedText(),f.disableStyleCopyPaste?f.copiedTextStyle=null:f.copiedTextStyle=this.getSelectionStyles(this.selectionStart,this.selectionEnd,!0),this._copyDone=!0)},paste:function(){this.fromPaste=!0},_getClipboardData:function(c){return c&&c.clipboardData||f.window.clipboardData},_getWidthBeforeCursor:function(c,s){var h=this._getLineLeftOffset(c),a;return s>0&&(a=this.__charBounds[c][s-1],h+=a.left+a.width),h},getDownCursorOffset:function(c,s){var h=this._getSelectionForOffset(c,s),a=this.get2DCursorLocation(h),e=a.lineIndex;if(e===this._textLines.length-1||c.metaKey||c.keyCode===34)return this._text.length-h;var r=a.charIndex,t=this._getWidthBeforeCursor(e,r),n=this._getIndexOnLine(e+1,t),o=this._textLines[e].slice(r);return o.length+n+1+this.missingNewlineOffset(e)},_getSelectionForOffset:function(c,s){return c.shiftKey&&this.selectionStart!==this.selectionEnd&&s?this.selectionEnd:this.selectionStart},getUpCursorOffset:function(c,s){var h=this._getSelectionForOffset(c,s),a=this.get2DCursorLocation(h),e=a.lineIndex;if(e===0||c.metaKey||c.keyCode===33)return-h;var r=a.charIndex,t=this._getWidthBeforeCursor(e,r),n=this._getIndexOnLine(e-1,t),o=this._textLines[e].slice(0,r),i=this.missingNewlineOffset(e-1);return-this._textLines[e-1].length+n-o.length+(1-i)},_getIndexOnLine:function(c,s){for(var h=this._textLines[c],a=this._getLineLeftOffset(c),e=a,r=0,t,n,o=0,i=h.length;o<i;o++)if(t=this.__charBounds[c][o].width,e+=t,e>s){n=!0;var l=e-t,u=e,d=Math.abs(l-s),g=Math.abs(u-s);r=g<d?o:o-1;break}return n||(r=h.length-1),r},moveCursorDown:function(c){this.selectionStart>=this._text.length&&this.selectionEnd>=this._text.length||this._moveCursorUpOrDown("Down",c)},moveCursorUp:function(c){this.selectionStart===0&&this.selectionEnd===0||this._moveCursorUpOrDown("Up",c)},_moveCursorUpOrDown:function(c,s){var h="get"+c+"CursorOffset",a=this[h](s,this._selectionDirection==="right");s.shiftKey?this.moveCursorWithShift(a):this.moveCursorWithoutShift(a),a!==0&&(this.setSelectionInBoundaries(),this.abortCursorAnimation(),this._currentCursorOpacity=1,this.initDelayedCursor(),this._fireSelectionChanged(),this._updateTextarea())},moveCursorWithShift:function(c){var s=this._selectionDirection==="left"?this.selectionStart+c:this.selectionEnd+c;return this.setSelectionStartEndWithShift(this.selectionStart,this.selectionEnd,s),c!==0},moveCursorWithoutShift:function(c){return c<0?(this.selectionStart+=c,this.selectionEnd=this.selectionStart):(this.selectionEnd+=c,this.selectionStart=this.selectionEnd),c!==0},moveCursorLeft:function(c){this.selectionStart===0&&this.selectionEnd===0||this._moveCursorLeftOrRight("Left",c)},_move:function(c,s,h){var a;if(c.altKey)a=this["findWordBoundary"+h](this[s]);else if(c.metaKey||c.keyCode===35||c.keyCode===36)a=this["findLineBoundary"+h](this[s]);else return this[s]+=h==="Left"?-1:1,!0;if(typeof a<"u"&&this[s]!==a)return this[s]=a,!0},_moveLeft:function(c,s){return this._move(c,s,"Left")},_moveRight:function(c,s){return this._move(c,s,"Right")},moveCursorLeftWithoutShift:function(c){var s=!0;return this._selectionDirection="left",this.selectionEnd===this.selectionStart&&this.selectionStart!==0&&(s=this._moveLeft(c,"selectionStart")),this.selectionEnd=this.selectionStart,s},moveCursorLeftWithShift:function(c){if(this._selectionDirection==="right"&&this.selectionStart!==this.selectionEnd)return this._moveLeft(c,"selectionEnd");if(this.selectionStart!==0)return this._selectionDirection="left",this._moveLeft(c,"selectionStart")},moveCursorRight:function(c){this.selectionStart>=this._text.length&&this.selectionEnd>=this._text.length||this._moveCursorLeftOrRight("Right",c)},_moveCursorLeftOrRight:function(c,s){var h="moveCursor"+c+"With";this._currentCursorOpacity=1,s.shiftKey?h+="Shift":h+="outShift",this[h](s)&&(this.abortCursorAnimation(),this.initDelayedCursor(),this._fireSelectionChanged(),this._updateTextarea())},moveCursorRightWithShift:function(c){if(this._selectionDirection==="left"&&this.selectionStart!==this.selectionEnd)return this._moveRight(c,"selectionStart");if(this.selectionEnd!==this._text.length)return this._selectionDirection="right",this._moveRight(c,"selectionEnd")},moveCursorRightWithoutShift:function(c){var s=!0;return this._selectionDirection="right",this.selectionStart===this.selectionEnd?(s=this._moveRight(c,"selectionStart"),this.selectionEnd=this.selectionStart):this.selectionStart=this.selectionEnd,s},removeChars:function(c,s){typeof s>"u"&&(s=c+1),this.removeStyleFromTo(c,s),this._text.splice(c,s-c),this.text=this._text.join(""),this.set("dirty",!0),this._shouldClearDimensionCache()&&(this.initDimensions(),this.setCoords()),this._removeExtraneousStyles()},insertChars:function(c,s,h,a){typeof a>"u"&&(a=h),a>h&&this.removeStyleFromTo(h,a);var e=f.util.string.graphemeSplit(c);this.insertNewStyleBlock(e,h,s),this._text=[].concat(this._text.slice(0,h),e,this._text.slice(a)),this.text=this._text.join(""),this.set("dirty",!0),this._shouldClearDimensionCache()&&(this.initDimensions(),this.setCoords()),this._removeExtraneousStyles()}}),function(){var c=f.util.toFixed,s=/  +/g;f.util.object.extend(f.Text.prototype,{_toSVG:function(){var h=this._getSVGLeftTopOffsets(),a=this._getSVGTextAndBg(h.textTop,h.textLeft);return this._wrapSVGTextAndBg(a)},toSVG:function(h){return this._createBaseSVGMarkup(this._toSVG(),{reviver:h,noStyle:!0,withShadow:!0})},_getSVGLeftTopOffsets:function(){return{textLeft:-this.width/2,textTop:-this.height/2,lineTop:this.getHeightOfLine(0)}},_wrapSVGTextAndBg:function(h){var a=!0,e=this.getSvgTextDecoration(this);return[h.textBgRects.join(""),'		<text xml:space="preserve" ',this.fontFamily?'font-family="'+this.fontFamily.replace(/"/g,"'")+'" ':"",this.fontSize?'font-size="'+this.fontSize+'" ':"",this.fontStyle?'font-style="'+this.fontStyle+'" ':"",this.fontWeight?'font-weight="'+this.fontWeight+'" ':"",e?'text-decoration="'+e+'" ':"",'style="',this.getSvgStyles(a),'"',this.addPaintOrder()," >",h.textSpans.join(""),`</text>
`]},_getSVGTextAndBg:function(h,a){var e=[],r=[],t=h,n;this._setSVGBg(r);for(var o=0,i=this._textLines.length;o<i;o++)n=this._getLineLeftOffset(o),(this.textBackgroundColor||this.styleHas("textBackgroundColor",o))&&this._setSVGTextLineBg(r,o,a+n,t),this._setSVGTextLineText(e,o,a+n,t),t+=this.getHeightOfLine(o);return{textSpans:e,textBgRects:r}},_createTextCharSpan:function(h,a,e,r){var t=h!==h.trim()||h.match(s),n=this.getSvgSpanStyles(a,t),o=n?'style="'+n+'"':"",i=a.deltaY,l="",u=f.Object.NUM_FRACTION_DIGITS;return i&&(l=' dy="'+c(i,u)+'" '),['<tspan x="',c(e,u),'" y="',c(r,u),'" ',l,o,">",f.util.string.escapeXml(h),"</tspan>"].join("")},_setSVGTextLineText:function(h,a,e,r){var t=this.getHeightOfLine(a),n=this.textAlign.indexOf("justify")!==-1,o,i,l="",u,d,g=0,v=this._textLines[a],m;r+=t*(1-this._fontSizeFraction)/this.lineHeight;for(var y=0,w=v.length-1;y<=w;y++)m=y===w||this.charSpacing,l+=v[y],u=this.__charBounds[a][y],g===0?(e+=u.kernedWidth-u.width,g+=u.width):g+=u.kernedWidth,n&&!m&&this._reSpaceAndTab.test(v[y])&&(m=!0),m||(o=o||this.getCompleteStyleDeclaration(a,y),i=this.getCompleteStyleDeclaration(a,y+1),m=f.util.hasStyleChanged(o,i,!0)),m&&(d=this._getStyleDeclaration(a,y)||{},h.push(this._createTextCharSpan(l,d,e,r)),l="",o=i,e+=g,g=0)},_pushTextBgRect:function(h,a,e,r,t,n){var o=f.Object.NUM_FRACTION_DIGITS;h.push("		<rect ",this._getFillAttributes(a),' x="',c(e,o),'" y="',c(r,o),'" width="',c(t,o),'" height="',c(n,o),`"></rect>
`)},_setSVGTextLineBg:function(h,a,e,r){for(var t=this._textLines[a],n=this.getHeightOfLine(a)/this.lineHeight,o=0,i=0,l,u,d=this.getValueOfPropertyAt(a,0,"textBackgroundColor"),g=0,v=t.length;g<v;g++)l=this.__charBounds[a][g],u=this.getValueOfPropertyAt(a,g,"textBackgroundColor"),u!==d?(d&&this._pushTextBgRect(h,d,e+i,r,o,n),i=l.left,o=l.width,d=u):o+=l.kernedWidth;u&&this._pushTextBgRect(h,u,e+i,r,o,n)},_getFillAttributes:function(h){var a=h&&typeof h=="string"?new f.Color(h):"";return!a||!a.getSource()||a.getAlpha()===1?'fill="'+h+'"':'opacity="'+a.getAlpha()+'" fill="'+a.setAlpha(1).toRgb()+'"'},_getSVGLineTopOffset:function(h){for(var a=0,e=0,r=0;r<h;r++)a+=this.getHeightOfLine(r);return e=this.getHeightOfLine(r),{lineTop:a,offset:(this._fontSizeMult-this._fontSizeFraction)*e/(this.lineHeight*this._fontSizeMult)}},getSvgStyles:function(h){var a=f.Object.prototype.getSvgStyles.call(this,h);return a+" white-space: pre;"}})}(),function(c){var s=c.fabric||(c.fabric={});s.Textbox=s.util.createClass(s.IText,s.Observable,{type:"textbox",minWidth:20,dynamicMinWidth:2,__cachedLines:null,lockScalingFlip:!0,noScaleCache:!1,_dimensionAffectingProps:s.Text.prototype._dimensionAffectingProps.concat("width"),_wordJoiners:/[ \t\r]/,splitByGrapheme:!1,initDimensions:function(){this.__skipDimension||(this.isEditing&&this.initDelayedCursor(),this.clearContextTop(),this._clearCache(),this.dynamicMinWidth=0,this._styleMap=this._generateStyleMap(this._splitText()),this.dynamicMinWidth>this.width&&this._set("width",this.dynamicMinWidth),this.textAlign.indexOf("justify")!==-1&&this.enlargeSpaces(),this.height=this.calcTextHeight(),this.saveState({propertySet:"_dimensionAffectingProps"}))},_generateStyleMap:function(h){for(var a=0,e=0,r=0,t={},n=0;n<h.graphemeLines.length;n++)h.graphemeText[r]===`
`&&n>0?(e=0,r++,a++):!this.splitByGrapheme&&this._reSpaceAndTab.test(h.graphemeText[r])&&n>0&&(e++,r++),t[n]={line:a,offset:e},r+=h.graphemeLines[n].length,e+=h.graphemeLines[n].length;return t},styleHas:function(h,a){if(this._styleMap&&!this.isWrapping){var e=this._styleMap[a];e&&(a=e.line)}return s.Text.prototype.styleHas.call(this,h,a)},isEmptyStyles:function(h){if(!this.styles)return!0;var a=0,e=h+1,r,t,n=!1,o=this._styleMap[h],i=this._styleMap[h+1];o&&(h=o.line,a=o.offset),i&&(e=i.line,n=e===h,r=i.offset),t=typeof h>"u"?this.styles:{line:this.styles[h]};for(var l in t)for(var u in t[l])if(u>=a&&(!n||u<r))for(var d in t[l][u])return!1;return!0},_getStyleDeclaration:function(h,a){if(this._styleMap&&!this.isWrapping){var e=this._styleMap[h];if(!e)return null;h=e.line,a=e.offset+a}return this.callSuper("_getStyleDeclaration",h,a)},_setStyleDeclaration:function(h,a,e){var r=this._styleMap[h];h=r.line,a=r.offset+a,this.styles[h][a]=e},_deleteStyleDeclaration:function(h,a){var e=this._styleMap[h];h=e.line,a=e.offset+a,delete this.styles[h][a]},_getLineStyle:function(h){var a=this._styleMap[h];return!!this.styles[a.line]},_setLineStyle:function(h){var a=this._styleMap[h];this.styles[a.line]={}},_wrapText:function(h,a){var e=[],r;for(this.isWrapping=!0,r=0;r<h.length;r++)e=e.concat(this._wrapLine(h[r],r,a));return this.isWrapping=!1,e},_measureWord:function(h,a,e){var r=0,t,n=!0;e=e||0;for(var o=0,i=h.length;o<i;o++){var l=this._getGraphemeBox(h[o],a,o+e,t,n);r+=l.kernedWidth,t=h[o]}return r},_wrapLine:function(h,a,e,Y){var t=0,n=this.splitByGrapheme,o=[],i=[],l=n?s.util.string.graphemeSplit(h):h.split(this._wordJoiners),u="",d=0,g=n?"":" ",v=0,m=0,y=0,w=!0,F=this._getWidthOfCharSpacing(),Y=Y||0;l.length===0&&l.push([]),e-=Y;for(var z=0;z<l.length;z++)u=n?l[z]:s.util.string.graphemeSplit(l[z]),v=this._measureWord(u,a,d),d+=u.length,t+=m+v-F,t>e&&!w?(o.push(i),i=[],t=v,w=!0):t+=F,!w&&!n&&i.push(g),i=i.concat(u),m=n?0:this._measureWord([g],a,d),d++,w=!1,v>y&&(y=v);return z&&o.push(i),y+Y>this.dynamicMinWidth&&(this.dynamicMinWidth=y-F+Y),o},isEndOfWrapping:function(h){return!this._styleMap[h+1]||this._styleMap[h+1].line!==this._styleMap[h].line},missingNewlineOffset:function(h){return this.splitByGrapheme?this.isEndOfWrapping(h)?1:0:1},_splitTextIntoLines:function(h){for(var a=s.Text.prototype._splitTextIntoLines.call(this,h),e=this._wrapText(a.lines,this.width),r=new Array(e.length),t=0;t<e.length;t++)r[t]=e[t].join("");return a.lines=r,a.graphemeLines=e,a},getMinWidth:function(){return Math.max(this.minWidth,this.dynamicMinWidth)},_removeExtraneousStyles:function(){var h={};for(var a in this._styleMap)this._textLines[a]&&(h[this._styleMap[a].line]=1);for(var a in this.styles)h[a]||delete this.styles[a]},toObject:function(h){return this.callSuper("toObject",["minWidth","splitByGrapheme"].concat(h))}}),s.Textbox.fromObject=function(h,a){var e=s.util.stylesFromArray(h.styles,h.text),r=Object.assign({},h,{styles:e});return s.Object._fromObject("Textbox",r,a,"text")}}(A),function(){var c=f.controlsUtils,s=c.scaleSkewCursorStyleHandler,h=c.scaleCursorStyleHandler,a=c.scalingEqually,e=c.scalingYOrSkewingX,r=c.scalingXOrSkewingY,t=c.scaleOrSkewActionName,n=f.Object.prototype.controls;if(n.ml=new f.Control({x:-.5,y:0,cursorStyleHandler:s,actionHandler:r,getActionName:t}),n.mr=new f.Control({x:.5,y:0,cursorStyleHandler:s,actionHandler:r,getActionName:t}),n.mb=new f.Control({x:0,y:.5,cursorStyleHandler:s,actionHandler:e,getActionName:t}),n.mt=new f.Control({x:0,y:-.5,cursorStyleHandler:s,actionHandler:e,getActionName:t}),n.tl=new f.Control({x:-.5,y:-.5,cursorStyleHandler:h,actionHandler:a}),n.tr=new f.Control({x:.5,y:-.5,cursorStyleHandler:h,actionHandler:a}),n.bl=new f.Control({x:-.5,y:.5,cursorStyleHandler:h,actionHandler:a}),n.br=new f.Control({x:.5,y:.5,cursorStyleHandler:h,actionHandler:a}),n.mtr=new f.Control({x:0,y:-.5,actionHandler:c.rotationWithSnapping,cursorStyleHandler:c.rotationStyleHandler,offsetY:-40,withConnection:!0,actionName:"rotate"}),f.Textbox){var o=f.Textbox.prototype.controls={};o.mtr=n.mtr,o.tr=n.tr,o.br=n.br,o.tl=n.tl,o.bl=n.bl,o.mt=n.mt,o.mb=n.mb,o.mr=new f.Control({x:.5,y:0,actionHandler:c.changeWidth,cursorStyleHandler:s,actionName:"resizing"}),o.ml=new f.Control({x:-.5,y:0,actionHandler:c.changeWidth,cursorStyleHandler:s,actionName:"resizing"})}}()})(dt);const Rt=""+new URL("close.CQGJFSAs.png",import.meta.url).href,Tt={1:{resolution:1,label:"9:16",width:253.125,height:450,ratio:450/1920},2:{resolution:2,label:"16:9",width:800,height:450,ratio:450/1080}};var Bt=(A=>(A.BACKGROUND="background",A.AVATAR="avatar",A.PROSPECT="prospect",A.MAPS="maps",A))(Bt||{}),Xt=(A=>(A.CAPTIONS="captions",A.TEXT="text",A))(Xt||{});const Gt=Dt({id:"canvas",state:()=>({id:void 0,isChangeData:!1,name:"未命名脚本",workspace:null,canvas:null,defaultSize:Tt[1],music:{},dub:{},canvasJson:{version:"",objects:[]},activeObject:null,voiceContent:{text:"",voice_url:"",type:1,voice_name:""},cover:""}),getters:{},actions:{setActiveObjectByType(A){var K,at,et,st,lt;if(((K=this.activeObject)==null?void 0:K.customType)===A)return;const f=(et=(at=this.canvas)==null?void 0:at.getObjects())==null?void 0:et.find(({customType:c})=>c===A);f&&((st=this.canvas)==null||st.setActiveObject(f),(lt=this.canvas)==null||lt.renderAll())},setZoom(A){if(!this.canvas)return;const f=this.defaultSize.width*A,K=this.defaultSize.height*A;this.canvas.setWidth(f),this.canvas.setHeight(K),this.canvas.setZoom(A)},changeSize(A){if(!this.canvas)return;this.defaultSize=A,this.workspace.clientWidth<this.defaultSize.width?this.workspace.style.height=`${this.workspace.clientWidth*this.defaultSize.height/this.defaultSize.width}px`:this.workspace.style.height=`${this.defaultSize.height}px`;const f=this.workspace.clientHeight/this.defaultSize.height;this.setZoom(f)},initObject(){if(!this.canvas)return;const A=this.canvas.getObjects(),f=this.getZoomCenter();A==null||A.forEach(K=>{var at,et;switch(K.customType){case"background":{K.set({scaleX:this.getScaleX(K.width),scaleY:this.getScaleY(K.height),left:f.left,top:f.top});break}case"text":{K.set({left:f.left,top:f.top,fontSize:this.calcFontSize((at=K.data)==null?void 0:at.fontSize)});break}case"captions":{K.set({top:f.top/4*7,left:f.left,fontSize:this.calcFontSize((et=K.data)==null?void 0:et.fontSize)});break}default:K.set({left:f.left,top:f.top})}})},initControl(){this.deleteControl()},deleteControl(){const A=this.canvas;if(!A)throw new Error("请先初始化canvas对象");const f=document.createElement("img");f.src=Rt;const K=34;function at(st,lt,c,s,h){st.save(),st.translate(lt,c),st.rotate(dt.fabric.util.degreesToRadians(h.angle)),st.drawImage(f,-K/2,-K/2,K,K),st.restore()}function et(st,lt){if(lt.action==="rotate")return!0;const c=A==null?void 0:A.getActiveObjects();return c&&(c.map(s=>A==null?void 0:A.remove(s)),A==null||A.requestRenderAll(),A==null||A.discardActiveObject()),!0}dt.fabric.Textbox.prototype.controls.del=new dt.fabric.Control({x:.5,y:-.5,offsetY:-K/4,offsetX:K/4,cursorStyle:"pointer",mouseUpHandler:et,render:at}),dt.fabric.Object.prototype.controls.del=new dt.fabric.Control({x:.5,y:-.5,offsetY:K/2,offsetX:-K/2,cursorStyle:"pointer",mouseUpHandler:et,render:at})},getScaleX(A){return this.getWidth()/A},getScaleY(A){return this.getHeight()/A},getCoverScale(A,f){return A/f<this.defaultSize.width/this.defaultSize.height?this.getScaleX(A):this.getScaleY(f)},getContainScale(A,f){return A/f<this.defaultSize.width/this.defaultSize.height?this.getScaleY(f):this.getScaleX(A)},initLoadFont(){var f;const A=(f=this.canvasJson.objects)==null?void 0:f.filter(K=>["textbox"].includes(K.type)).map(K=>new bt(K.fontFamily).load(null,1e5));return Promise.all(A)},async initCanvas(A,f){const{tabsState:K}=wt();this.workspace=f,dt.fabric.Object.prototype.set({borderColor:"#15E9FF",cornerColor:"#15E9FF",cornerStrokeColor:"white",borderOpacityWhenMoving:1,borderScaleFactor:1,cornerSize:10,cornerStyle:"circle",rotatingPointOffset:4,transparentCorners:!1}),await this.initData();const at=new dt.fabric.Canvas(A,{width:this.defaultSize.width,height:this.defaultSize.height});console.log(at),this.canvas=xt(at),this.canvas.preserveObjectStacking=!0,this.canvas.selection=!1,this.initControl(),this.initEvent(),await this.initLoadFont(),await this.setCanvasJson(this.canvasJson),this.setActiveObjectByType(K.value.current);const et=f.clientHeight/this.defaultSize.height;this.setZoom(et),setTimeout(()=>{this.isChangeData=!1})},initEvent(){this.canvas&&(this.canvas.on("after:render",()=>{const A=this.getCanvasJson();A&&(this.canvasJson=A)}),this.canvas.on("selection:created",()=>{this.changeActiveObject()}),this.canvas.on("selection:updated",()=>{this.changeActiveObject()}),this.canvas.on("selection:cleared",()=>{this.changeActiveObject()}))},changeActiveObject(){var K;const{changeTabs:A}=wt(),f=(K=this.canvas)==null?void 0:K.getActiveObject();f?(this.activeObject=xt(f),f.customType&&A(f.customType)):this.activeObject=null},setActiveObject(A){if(!this.canvas)throw new Error("请先初始化canvas对象");const f=this.canvas.getObjects().find(K=>K.id===A);f&&(this.canvas.setActiveObject(f),this.canvas.renderAll())},delObject(A){if(!this.canvas)throw new Error("请先初始化canvas对象");const f=this.canvas.getObjects().find(K=>K.id===A);f&&(this.canvas.remove(f),this.canvas.renderAll())},getCanvasJson(){if(this.canvas)return this.canvas.toJSON(["hasControls","hasBorders","scaleX","scaleY","originX","originY","angle","top","left","crossOrigin","data","selectable","id","customType","lockScalingY","lockScalingX","lockUniScaling","lockMovementX","lockMovementY","type","_controlsVisibility","name","editable"])},setCanvasJson(A){return new Promise((f,K)=>{this.canvas?this.canvas.loadFromJSON(A,()=>{var at;(at=this.canvas)==null||at.renderAll(),f()}):K()})},getZoomCenter(){if(!this.canvas)throw new Error("请先初始化canvas对象");const A=this.canvas.getCenter();for(const f in A)A[f]/=this.canvas.getZoom();return A},getTransformX(){if(!this.canvas)throw new Error("请先初始化canvas对象");return this.canvas.viewportTransform[4]/this.canvas.getZoom()},getWidth(){if(!this.canvas)throw new Error("请先初始化canvas对象");return this.canvas.getWidth()/this.canvas.getZoom()},getHeight(){if(!this.canvas)throw new Error("请先初始化canvas对象");return this.canvas.getHeight()/this.canvas.getZoom()},getTransformY(){if(!this.canvas)throw new Error("请先初始化canvas对象");return this.canvas.viewportTransform[5]/this.canvas.getZoom()},async addText(A,f="text",K={}){var a;if(!this.canvas)throw new Error("请先初始化canvas对象");const at=this.getZoomCenter(),{fontFamily:et,fill:st,stroke:lt,fontSize:c}=K;if(et){pt.loading("正在加载字体中，请稍等...");try{await new bt(et).load(null,100*1e3)}catch(e){console.log(e),pt.msgError("字体加载失败，请重试")}finally{pt.closeLoading()}}const s={name:"文本",id:St(),customType:f,left:at.left,top:at.top,fontSize:this.calcFontSize(c),selectable:!0,hasControls:!0,hasBorders:!0,fontWeight:400,fontFamily:et,fill:st,stroke:lt,strokeWidth:1,data:K,lockScalingY:!1,lockScalingX:!1,lockUniScaling:!1,textBackgroundColor:"",textAlign:"left",originX:"center",originY:"center",centeredScaling:!0},h=new dt.fabric.Textbox(A,s);switch(h.setControlsVisibility({bl:!1,br:!1,mb:!1,ml:!0,mr:!0,mt:!1,tl:!1,tr:!1,mtr:!1}),f){case"captions":{const r=this.canvas.getObjects().find(t=>t.customType==="captions");this.canvas.remove(r),h.set({name:"字幕",top:at.top/4*7,editable:!1,lockMovementX:!0});break}}this.canvas.add(h),(a=this.canvas)==null||a.setActiveObject(h)},addImage(A,f,K={}){if(!this.canvas)throw new Error("请先初始化canvas对象");const at={id:St(),customType:f,data:K,left:this.getWidth()/2-this.getTransformX(),top:this.getHeight()/2-this.getTransformY(),originX:"center",originY:"center",centeredScaling:!0,crossOrigin:""};dt.fabric.Image.fromURL(A,et=>{var st,lt,c,s,h,a,e,r;switch(f){case"background":{et.set({name:"背景",scaleX:this.getScaleX(et.width),scaleY:this.getScaleY(et.height),lockMovementX:!0,lockMovementY:!0,lockScalingX:!0,lockScalingY:!0,hasBorders:!1}),et.setControlsVisibility({bl:!1,br:!1,mb:!1,ml:!1,mr:!1,mt:!1,tl:!1,tr:!1,mtr:!1});break}case"avatar":{const t=this.getContainScale(et.width,et.height)-.02;et.set({name:"形象",scaleX:t,scaleY:t,hasBorders:!0}),et.setControlsVisibility({mb:!1,ml:!1,mr:!1,mt:!1,mtr:!1});break}case"maps":{let t=this.getContainScale(et.width,et.height)/3;t>1&&(t=1),et.set({name:"贴图",scaleX:t,scaleY:t}),et.setControlsVisibility({mb:!1,ml:!1,mr:!1,mt:!1,mtr:!1});break}case"prospect":{let t=this.getContainScale(et.width,et.height)/2;t>1&&(t=1),et.set({name:"前景",scaleX:t,scaleY:t}),et.setControlsVisibility({mb:!1,ml:!1,mr:!1,mt:!1,mtr:!1})}}if(f==="background"||f==="avatar"){const t=(st=this.canvas)==null?void 0:st.getObjects(),n=((lt=t==null?void 0:t[0])==null?void 0:lt.customType)==="background",o=t==null?void 0:t.find(i=>i.customType===f);o&&((c=this.canvas)==null||c.remove(o)),(s=this.canvas)==null||s.add(et),f==="background"||!n?(h=this.canvas)==null||h.sendToBack(et):(a=this.canvas)==null||a.moveTo(et,1)}else(e=this.canvas)==null||e.add(et);(r=this.canvas)==null||r.setActiveObject(et)},at)},calcFontSize(A){return this.defaultSize.ratio*A},transformData(){const A={id:this.id,name:this.name,resolution:this.defaultSize.resolution,canvas:mt(this.canvasJson),music:mt(this.music),dub:mt(this.dub),voice_content:mt(this.voiceContent),cover_picture_url:this.cover,texts:[],maps:[],prospect:[],captions:{status:0},background:{},avatar:{}};return this.canvasJson.objects.forEach((f,K)=>{var h,a,e,r,t,n,o,i,l,u,d,g;const at=f.width*f.scaleX,et=f.height*f.scaleY,st=at/this.getWidth(),lt=et/this.getHeight(),c=(f.left-at/2)/this.getWidth(),s=(f.top-et/2)/this.getHeight();switch(f.customType){case"avatar":{A.avatar={AvatarId:(h=f.data)==null?void 0:h.avatar_id,Height:lt,Width:st,X:c,Y:s,zIndex:K};break}case"background":{A.background={...mt(f.data),zIndex:K};break}case"captions":{A.captions={status:(a=f.data)==null?void 0:a.status,Font:(e=f.data)==null?void 0:e.fontFamily,FontColor:(r=f.data)==null?void 0:r.fill,FontSize:(t=f.data)==null?void 0:t.fontSize,Outline:1,OutlineColour:(n=f.data)==null?void 0:n.stroke,X:f.left/this.getWidth(),Y:f.top/this.getHeight(),Alignment:"Center",zIndex:K};break}case"text":{const{server_key:v="",type:m=""}=((o=f.data)==null?void 0:o.effect)||{};A.texts.push({AaiMotionInEffect:m=="in"?v:"",AaiMotionLoopEffect:m=="loop"?v:"",AaiMotionOutEffect:m=="out"?v:"",Font:(i=f.data)==null?void 0:i.fontFamily,FontColor:(l=f.data)==null?void 0:l.fill,FontSize:(u=f.data)==null?void 0:u.fontSize,Outline:1,OutlineColour:(d=f.data)==null?void 0:d.stroke,X:c,Y:s,Height:lt,Content:f.text,zIndex:K});break}case"maps":case"prospect":(g=A[f.customType])==null||g.push({X:c,Y:s,Height:lt,Width:st,...f.data,zIndex:K})}}),A},async savaOrComposite(A=1){const f=this.transformData(),K=await Mt({...f,action:A});this.isChangeData=!1,this.id=K.id},async putImgCover(){if(!this.canvas)throw new Error("请先初始化canvas对象");const A=this.canvas.toDataURL({format:"png",top:0,left:0,width:this.canvas.width,height:this.canvas.height}),f=It(A,"cover.png"),K=await Ft({file:f});this.cover=K.uri},async initData(){const f=yt().query.id;if(f)try{const K=await Lt({id:Number(f)}),{music:at={},dub:et={},canvas:st={},voice_content:lt=this.voiceContent,resolution:c=3}=JSON.parse(K.params);this.id=K.id,this.music=at,this.dub=et,this.name=K.name,this.voiceContent=lt,this.defaultSize=Tt[c]||{},this.canvasJson=st}catch{}}}});export{Bt as I,Xt as T,wt as a,Tt as c,Gt as u};
