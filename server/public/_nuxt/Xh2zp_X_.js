import{E as d,a as h,b as g,c as w}from"./DCaxswvP.js";import{j as v,cw as C,a as b,V as y,cQ as D,f as E}from"./C3HqF-ve.js";import{u as x,a as S}from"./XYEXaRmO.js";import{_ as L}from"./DQW8FjWS.js";import k from"./Bd_KmAtK.js";import{_ as B}from"./C6Qozryl.js";import R from"./Cd14pur_.js";import{l as V,c as r,M as $,N as z,Z as t,a0 as i,a2 as H,u as M}from"./Dp9aCaJ6.js";import"./Bb2-23m7.js";import"./CiYvFM4x.js";import"./DuiQQh4g.js";import"./DQM3eVRQ.js";import"./D5KDMvDa.js";import"./DlAUqK2U.js";import"./BJVCWakU.js";import"./D9-OzdG8.js";import"./C4e59zsN.js";import"./Df5PDZbu.js";import"./B2dGhy6n.js";import"./DH7aY8gH.js";import"./CLzx4hRx.js";import"./D0Qj8Spo.js";import"./9Bti1uB6.js";import"./DCTLXrZ8.js";/* empty css        *//* empty css        *//* empty css        */import"./CcPlX2kz.js";import"./D4IG-TzH.js";import"./Cv6HhfEG.js";import"./w4aZ_ZMl.js";import"./CSbbbz2F.js";import"./BGmmfkI4.js";import"./Dg2XwvBU.js";/* empty css        */import"./gtzK5o60.js";import"./Z8VNcbvf.js";import"./BFzR70Kc.js";import"./ZZ3Wnry7.js";import"./CNwggAt_.js";/* empty css        */import"./BRgo5q5W.js";import"./BF7Zsmtq.js";import"./fWRDI3BW.js";import"./COVldc8K.js";/* empty css        */import"./Db9qaj78.js";import"./BkngO3As.js";import"./DfZUM0y5.js";import"./C0rtsRzX.js";import"./Bpcqu5bh.js";import"./B8ufs3zL.js";import"./dogXonFS.js";import"./BPdEGeDo.js";import"./DlKZEFPo.js";import"./CMSty1OB.js";/* empty css        *//* empty css        *//* empty css        */import"./DP2rzg_V.js";import"./Ckq_Blbi.js";import"./DMI4YHhg.js";import"./yWpGL9Dq.js";import"./DhZeSc4G.js";import"./D1dHzZ6R.js";import"./cVs9qJ7N.js";import"./CVbdinWI.js";import"./DZZ7Wel0.js";import"./DqmLSLgY.js";import"./BlHb_8BP.js";import"./CXTt4p28.js";import"./l0sNRNKZ.js";import"./ChKCuCCG.js";import"./BuaeLNg1.js";import"./zboy2uUK.js";import"./27mOw3Er.js";/* empty css        */import"./B8amyNl3.js";const ao=V({__name:"design",setup(N){const n=v(),o=x(),{height:c}=C(),u=b(),{initTabs:l}=S();y(window,"beforeunload",m=>{o.isChangeData&&(m.preventDefault(),m.returnValue="内容已修改，确认离开页面吗？")}),D(async(m,f)=>{try{o.isChangeData&&n.isLogin&&await E.confirm("内容已修改，确认离开页面吗？")}catch{return!1}});const e=()=>{o.isChangeData=!0};return r(()=>o.canvasJson,e),r(()=>o.music,e),r(()=>o.dub,e),r(()=>o.voiceContent,e,{deep:!0}),r(()=>u.query,()=>{l()}),(m,f)=>{const p=d,a=h,_=g,s=w;return $(),z("div",{class:"overflow-x-auto",style:H({height:`${M(c)}px`})},[t(s,{class:"bg-page !min-w-[1200px] h-full"},{default:i(()=>[t(p,{height:"auto",style:{padding:"0"}},{default:i(()=>[t(L)]),_:1}),t(s,{class:"min-h-0"},{default:i(()=>[t(a,{width:"auto",style:{overflow:"visible"}},{default:i(()=>[t(k)]),_:1}),t(_,{style:{padding:"0"}},{default:i(()=>[t(B)]),_:1}),t(a,{width:"auto"},{default:i(()=>[t(R)]),_:1})]),_:1})]),_:1})],4)}}});export{ao as default};
