import{E as y}from"./2KyQdWL7.js";import{_ as V}from"./CDtCQ8p9.js";import{b as h}from"./C12kmceL.js";/* empty css        */import{l as w,m as v,M as o,N as s,O as e,_ as B,aq as I,u as A,a2 as S,Z as i,a0 as N,a7 as k,a5 as m}from"./uahP8ofS.js";import{_ as L}from"./DlAUqK2U.js";const b="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAIMSURBVFiF7ZaxTttQFIa/Y4ekFHet/AAMeYd6SDZgzwN0RV26IHWL1A4Va1XWPgA7GZOBvAOPgNLVNODknp/BJiVpBGlrtvySJfvonvt/9xxf+8JWW21Vo3r9SdLrT5K/ybHazL9PknC7O9AcGtPp4Xn/bb5JXlQXALPX3wwyIJu19gabVqI+gIJPiCsApOwu3ht0NoCoDeD8JLmO3LoYVyopsoTnIeqrQAVxh3WhqgRke/OnIWoFALg4Sa5nBV1V7RBku0Uy6Byvh6gdAOCin1wHpwu6wgApayXrIRbb8ONwsh+i6B0eEwL4DAgQqgsHL2Lw1RgEjxZj3YECgkMIUaq5vmhOw4PhgcvZND8cnf3eoo2HG8VRZs4Pdwcv2SRAYLJyYnNcIBmm0kwYyJEMVEKXMTAJLS84azTfDDrHOhydWQ4rLVgarJXUpQF/TLyUrfXhh4DBz8XTogIW/FJE7x8mV9k7JDDiKlVYVZVyVAzGolJCyKqKCZBSzD4vfMR4XuQHj1tQ26d4VUf9PI2Ihj5XW+X7MS7ymyVzeKFdcNTP07jBENSulrjW/EUAeqd5utNkaKINgHw8ba03rx2gd5qnAQ2N0lz4+FdjejB64s9YG0DvNE891lCiLQBpfMPT5rUCaMe/Uq0cNG758+bwaBv+r6x5+8FvX+2D0SymB5seSGrVvxzJttpqq3uRBCLzV/J9QgAAAABJRU5ErkJggg==",J={class:"xl:max-w-[1200px] flex justify-center mx-auto"},C=["to"],D={key:0,class:"mb-[10px]"},E={class:"text-2xl font-medium"},O={class:"my-4 text-sm h-[80px] leading-[20px] line-clamp-4"},X=w({__name:"entrance",props:{prop:{}},setup(d){const r=d,g=h(),_=v(()=>r.prop.data.filter(n=>n.isShow));return(n,a)=>{const u=y,x=V;return o(),s("div",J,[e("div",{class:"grid flex-wrap",style:S({"grid-template-columns":`repeat(${r.prop.showType}, minmax(0, 1fr))`})},[(o(!0),s(B,null,I(A(_),(t,f)=>{var l,p;return o(),s("div",{class:"flex-1 md:mb-[40px] mb-[20px]",key:f},[i(x,{to:{path:(l=t.link)==null?void 0:l.path,query:(p=t.link)==null?void 0:p.query}},{default:N(()=>{var c;return[e("div",{class:"chat-card h-full",to:(c=t.link)==null?void 0:c.path},[t.icon?(o(),s("div",D,[i(u,{class:"w-[58px] h-[58px] rounded-lg",src:A(g).getImageUrl(t.icon)},null,8,["src"])])):k("",!0),e("div",E,m(t.title),1),a[0]||(a[0]=e("div",{class:"line w-[100%] mt-4"},null,-1)),e("div",O,m(t.desc),1),a[1]||(a[1]=e("div",{class:"enter-btn mt-3"},[e("img",{src:b,class:"w-[32px] h-[32px]",alt:""})],-1))],8,C)]}),_:2},1032,["to"])])}),128))],4)])}}}),q=L(X,[["__scopeId","data-v-12fc9586"]]),F=Object.freeze(Object.defineProperty({__proto__:null,default:q},Symbol.toStringTag,{value:"Module"}));export{F as _};
