import{j as se,b as oe,d as i,E as ae,e as le,o as ne,p as re}from"./D726nzJl.js";import{E as ie}from"./vDgPRvwV.js";import{_ as ue}from"./BpYIl71c.js";import{E as me}from"./C-BkwxIh.js";import{E as ce}from"./D7NF1x92.js";/* empty css        */import"./DP2rzg_V.js";/* empty css        *//* empty css        *//* empty css        */import{l as de,m as q,b as f,c as _e,M as g,a1 as fe,a0 as p,O as r,Z as d,a6 as D,u as e,y as G,ai as ge,N as b,a7 as l,a4 as C}from"./Dp9aCaJ6.js";import{_ as pe}from"./DlAUqK2U.js";function ve(k){return $request.post({url:"/user_gift/gift",params:k})}function Qe(k){return $request.get({url:"/user_gift/records",params:k})}function ye(){return $request.get({url:"/user_gift/config"})}function be(k){return $request.get({url:"/user/getUserById",params:{user_sn:k}})}function ke(){return $request.get({url:"/user_gift/statistics"})}const xe={class:"w-full"},Ie={key:0,class:"mt-2 p-2 bg-gray-50 rounded flex items-center"},he={class:"ml-3 flex-1"},we={class:"font-medium"},Ee={class:"text-sm text-gray-500"},Ue={class:"w-full"},Ve={class:"mt-1 text-sm text-gray-500 flex justify-between"},Se={key:0},Ce={key:1},De={class:"p-3 bg-blue-50 rounded text-sm text-blue-700 mb-4"},Re={key:0,class:"text-center py-2"},Le={key:1,class:"space-y-1"},Te={key:0,class:"border-t pt-2 mt-2"},Ae={key:1},Be={key:2},Me={key:3},Ne={class:"dialog-footer"},A=20,F=6e4,B=500,$e=de({__name:"GiftModal",props:{modelValue:{type:Boolean,default:!1}},emits:["update:modelValue","success"],setup(k,{emit:H}){const c=se(),x=oe(),P=k,M=H,V=q({get:()=>P.modelValue,set:t=>M("update:modelValue",t)}),S=f(),m=f({to_user_id:0,gift_amount:1}),W={to_user_id:[{required:!0,message:"请选择接收用户",trigger:"blur"}],gift_amount:[{required:!0,message:"请输入赠送灵感值数量",trigger:"blur"},{type:"number",min:1,message:"赠送灵感值数量不能小于1",trigger:"blur"}]},R=f(!1),L=f(!1),I=f(""),v=f(null),n=f(null),y=f(null),h=f(0),w=f(0),j=q(()=>n.value?m.value.to_user_id>0&&m.value.gift_amount>0&&m.value.gift_amount<=c.userInfo.balance&&m.value.gift_amount>=n.value.min_gift_amount&&m.value.gift_amount<=n.value.max_gift_amount:!1);_e(V,t=>{if(t){if(!c.isLogin){i.error("请先登录"),E(),c.showLogin=!0;return}z()}else Q()});const z=async()=>{var t,s,_;try{console.log("开始加载赠送配置数据...");const[a,o]=await Promise.all([ye(),ke()]);if(console.log("配置API响应:",a),a&&a.data)n.value=a.data,console.log("配置数据加载成功:",n.value);else if(a)n.value=a,console.log("直接使用配置响应:",n.value);else throw console.warn("配置API返回空数据"),new Error("配置数据为空");y.value=o.data||o||null,console.log("所有数据加载完成")}catch(a){console.error("初始化数据失败:",a),console.error("错误详情:",{message:a.message,response:a.response,status:(t=a.response)==null?void 0:t.status,data:(s=a.response)==null?void 0:s.data}),typeof a=="string"&&a.includes("登录")?(i.error("登录已过期，请重新登录"),E()):((_=a.response)==null?void 0:_.status)===401?i.error("请先登录"):(i.error("加载数据失败: "+(a.message||a)),n.value={is_enable:1,min_gift_amount:1,max_gift_amount:1e3,daily_gift_limit:100,daily_receive_limit:500,gift_times_limit:10,receive_times_limit:20,friend_only:0,need_verify:0})}},K=()=>{const t=Date.now();return console.log("搜索频率检查:",{now:t,lastSearchTime:w.value,timeDiff:t-w.value,searchAttempts:h.value,minInterval:B,searchLimit:A,searchWindow:F}),t-w.value>F&&(console.log("重置搜索计数"),h.value=0),t-w.value<B?(console.log("搜索间隔过短"),i.warning(`搜索过于频繁，请等待${B/1e3}秒后再试`),!1):h.value>=A?(console.log("搜索次数超限"),i.warning(`搜索次数过多，已搜索${h.value}次，请1分钟后再试`),!1):(console.log("搜索频率检查通过"),!0)},O=t=>/^\d{8}$/.test(t),T=async()=>{var s,_,a;const t=I.value.trim();if(v.value=null,m.value.to_user_id=0,!!t){if(!c.isLogin){i.error("请先登录"),c.showLogin=!0;return}if(!O(t)){i.error("请输入正确的用户ID（8位数字）");return}if(t===c.userInfo.sn){i.error("不能给自己赠送");return}if(K()){L.value=!0,h.value++,w.value=Date.now();try{const o=await be(t);o&&o.id?(v.value=o,m.value.to_user_id=o.id,i.success("用户查找成功")):i.error("未查询到用户信息，请确认用户ID是否正确")}catch(o){console.error("搜索用户失败:",o),v.value=null,m.value.to_user_id=0;let u="查询失败，请稍后重试";if(typeof o=="string")u=o;else if(o!=null&&o.message)u=o.message;else if((_=(s=o==null?void 0:o.response)==null?void 0:s.data)!=null&&_.msg)u=o.response.data.msg;else if((a=o==null?void 0:o.response)!=null&&a.status)switch(o.response.status){case 400:u="请求参数错误";break;case 401:u="请先登录";break;case 403:u="没有权限执行此操作";break;case 404:u="未查询到用户信息，请确认用户ID是否正确";break;case 429:u="搜索过于频繁，请稍后再试";break;case 500:u="服务器错误，请稍后重试";break;default:u="查询失败，请稍后重试"}if(u.includes("登录")||u.includes("token")||u.includes("未登录")){i.error("登录已过期，请重新登录"),E();return}i.error(u)}finally{L.value=!1}}}},Z=()=>{v.value=null,m.value.to_user_id=0,I.value=""},J=async()=>{if(S.value){if(!c.isLogin){i.error("请先登录"),c.showLogin=!0;return}try{await S.value.validate()}catch{return}R.value=!0;try{const t={to_user_id:m.value.to_user_id,gift_amount:m.value.gift_amount},s=await ve(t);i.success("赠送成功！"),M("success",s),E(),c.getUser()}catch(t){console.error("赠送失败:",t),typeof t=="string"?(i.error(t),(t.includes("登录")||t.includes("token"))&&E()):i.error(t.message||"赠送失败，请重试")}finally{R.value=!1}}},E=()=>{V.value=!1},Q=()=>{m.value={to_user_id:0,gift_amount:1},v.value=null,I.value="",h.value=0,w.value=0,S.value&&S.value.resetFields()};return(t,s)=>{const _=ae,a=le,o=ie,u=ue,N=ne,X=me,Y=re,ee=ce;return g(),fe(ee,{modelValue:e(V),"onUpdate:modelValue":s[2]||(s[2]=U=>G(V)?V.value=U:null),title:"赠送灵感值",width:"500px","close-on-click-modal":!1,"destroy-on-close":!0},{footer:p(()=>[r("span",Ne,[d(_,{onClick:E},{default:p(()=>s[8]||(s[8]=[D("取消")])),_:1}),d(_,{type:"primary",onClick:J,loading:e(R),disabled:!e(j)},{default:p(()=>s[9]||(s[9]=[D(" 确认赠送 ")])),_:1},8,["loading","disabled"])])]),default:p(()=>[d(Y,{model:e(m),rules:W,ref_key:"formRef",ref:S,"label-width":"100px"},{default:p(()=>[d(N,{label:"接收用户",prop:"to_user_id"},{default:p(()=>[r("div",xe,[d(a,{modelValue:e(I),"onUpdate:modelValue":s[0]||(s[0]=U=>G(I)?I.value=U:null),placeholder:"请输入完整的用户ID（8位数字）",onBlur:T,onKeyup:ge(T,["enter"]),clearable:"",maxlength:"8"},{append:p(()=>[d(_,{onClick:T,loading:e(L)},{default:p(()=>s[3]||(s[3]=[D(" 搜索 ")])),_:1},8,["loading"])]),_:1},8,["modelValue"]),e(v)?(g(),b("div",Ie,[d(o,{size:40,src:e(v).avatar},null,8,["src"]),r("div",he,[r("div",we,l(e(v).nickname),1),r("div",Ee,"ID: "+l(e(v).sn),1)]),d(_,{onClick:Z,size:"small",text:""},{default:p(()=>[d(u,{name:"el-icon-Close"})]),_:1})])):C("",!0),r("div",{class:"mt-1 text-xs text-gray-500"},[s[4]||(s[4]=r("div",null,"• 请输入完整的用户ID，不支持模糊搜索",-1)),r("div",null,"• 为防止恶意搜索，每分钟最多可搜索"+l(A)+"次")])])]),_:1}),d(N,{label:"赠送灵感值数量",prop:"gift_amount"},{default:p(()=>{var U,$;return[r("div",Ue,[d(X,{modelValue:e(m).gift_amount,"onUpdate:modelValue":s[1]||(s[1]=te=>e(m).gift_amount=te),min:((U=e(n))==null?void 0:U.min_gift_amount)||1,max:Math.min((($=e(n))==null?void 0:$.max_gift_amount)||1e3,Math.floor(e(c).userInfo.balance)),precision:0,step:1,class:"w-full"},null,8,["modelValue","min","max"]),r("div",Ve,[r("span",null,"当前余额："+l(Math.floor(e(c).userInfo.balance))+" "+l(e(x).getTokenUnit),1),e(n)?(g(),b("span",Se,"单次限额："+l(e(n).min_gift_amount)+"-"+l(e(n).max_gift_amount),1)):(g(),b("span",Ce,"加载配置中..."))])])]}),_:1}),r("div",De,[s[7]||(s[7]=r("div",{class:"font-medium mb-2"},"赠送规则提示：",-1)),e(n)?(g(),b("div",Le,[r("div",null,"• 单次赠送范围："+l(e(n).min_gift_amount)+"-"+l(e(n).max_gift_amount)+" "+l(e(x).getTokenUnit),1),r("div",null,"• 每日赠送限额："+l(e(n).daily_gift_limit)+" "+l(e(x).getTokenUnit),1),r("div",null,"• 每日接收限额："+l(e(n).daily_receive_limit)+" "+l(e(x).getTokenUnit),1),r("div",null,"• 每日赠送次数："+l(e(n).gift_times_limit)+" 次",1),r("div",null,"• 每日接收次数："+l(e(n).receive_times_limit)+" 次",1),e(y)?(g(),b("div",Te,s[6]||(s[6]=[r("strong",null,"今日使用情况：",-1)]))):C("",!0),e(y)?(g(),b("div",Ae,"• 今日已赠送："+l(e(y).today_gift_amount||0)+" "+l(e(x).getTokenUnit)+"（"+l(e(y).today_gift_times||0)+" 次）",1)):C("",!0),e(y)?(g(),b("div",Be,"• 今日剩余额度："+l(e(y).remaining_daily_gift_limit||0)+" "+l(e(x).getTokenUnit),1)):C("",!0),e(y)?(g(),b("div",Me,"• 今日剩余次数："+l(e(n).gift_times_limit-(e(y).today_gift_times||0))+" 次",1)):C("",!0)])):(g(),b("div",Re,s[5]||(s[5]=[r("i",{class:"el-icon-loading"},null,-1),D(" 加载配置中... ")])))])]),_:1},8,["model"])]),_:1},8,["modelValue"])}}}),Xe=pe($e,[["__scopeId","data-v-74118731"]]);export{Xe as G,ke as a,ye as b,Qe as g};
