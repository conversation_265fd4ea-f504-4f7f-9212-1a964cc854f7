import{a as R,E as H}from"./B0taQFTv.js";import{j as N,b as T,E as U}from"./D726nzJl.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import{g as j,s as q}from"./DjwCd26w.js";import{P as A}from"./DaBGfXF7.js";import{u as D}from"./CcPlX2kz.js";import{l as O,j as P,b as B,r as I,m as _,M as r,N as d,Z as b,a0 as c,O as e,u as o,a6 as m,a7 as n,a4 as h,_ as Z,aq as G,a1 as J}from"./Dp9aCaJ6.js";import{_ as K}from"./DlAUqK2U.js";import"./GbXQDMM-.js";import"./DCTLXrZ8.js";import"./DxoS9eIh.js";import"./CJW7H0Ln.js";import"./Cv6HhfEG.js";import"./CmJNjzrM.js";import"./DcsXcc99.js";import"./DzhE9Pcm.js";import"./CjE8iZ8I.js";import"./D7NF1x92.js";import"./9Bti1uB6.js";/* empty css        */const Q={class:"share-popup"},W={key:0,class:"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg"},X={class:"flex items-start space-x-2"},Y={class:"text-sm text-red-700"},$={class:"bg-red-100 px-3 py-2 rounded-md"},ee={key:1,class:"mb-4 p-3 bg-amber-50 border border-amber-200 rounded-lg"},te={key:2,class:"mb-4 p-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg"},oe={class:"flex items-start space-x-3"},se={class:"flex-1"},ae={class:"text-sm text-green-700 leading-relaxed"},re={class:"mb-2"},ne={class:"bg-green-200 px-2 py-1 rounded font-bold text-green-900"},le={class:"text-xs text-green-600 bg-green-100 px-3 py-2 rounded-md mt-2"},ie={class:"h-[100px]"},de={class:"dialog-footer flex justify-center pb-2"},me=O({__name:"robot-share",emits:["success","close"],setup(pe,{expose:z,emit:S}){const V=N(),u=T(),y=S,f=P(),k=B([]),l=I({cate_id:"",id:""}),v=B(""),g=_(()=>{var s,t,a,p;return{...((t=(s=u.getSquareConfig)==null?void 0:s.robot_award)==null?void 0:t.revenue_config)||{},auto_audit:((p=(a=u.getSquareConfig)==null?void 0:a.robot_award)==null?void 0:p.auto_audit)||0}}),x=_(()=>u.getTokenUnit),C=_(()=>g.value.share_ratio||30),L=async()=>{try{const s=await j();s.unshift({name:"全部",id:""}),k.value=s}catch(s){console.log("获取视频分类失败=>",s)}},{lockFn:w,isLock:M}=D(async()=>{var s;await q(l),await V.getUser(),(s=f.value)==null||s.close(),y("success",l.id)}),E=()=>{y("close")};return z({open:(s,t)=>{var a;L(),(a=f.value)==null||a.open(),l.id=s,v.value=t||""}}),(s,t)=>{const a=H,p=R,F=U;return r(),d("div",Q,[b(A,{ref_key:"popupRef",ref:f,title:"分享至广场",async:!0,width:"400px",center:!0,cancelButtonText:"",confirmButtonText:"",appendToBody:!1,onConfirm:o(w),onClose:E},{footer:c(()=>[e("div",de,[b(F,{type:"primary",loading:o(M),class:"!rounded-md",onClick:o(w)},{default:c(()=>t[11]||(t[11]=[m(" 分享至广场 ")])),_:1},8,["loading","onClick"])])]),default:c(()=>[o(v)?(r(),d("div",W,[e("div",X,[t[3]||(t[3]=e("div",{class:"text-red-500 mt-0.5"},[e("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})])],-1)),e("div",Y,[t[1]||(t[1]=e("div",{class:"font-medium mb-1"},"上次审核拒绝原因",-1)),e("div",$,n(o(v)),1),t[2]||(t[2]=e("div",{class:"text-xs text-red-600 mt-2"}," 请根据拒绝原因调整智能体内容后重新提交 ",-1))])])])):h("",!0),o(g).auto_audit?h("",!0):(r(),d("div",ee,t[4]||(t[4]=[e("div",{class:"flex items-start space-x-2"},[e("div",{class:"text-amber-500 mt-0.5"},[e("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z","clip-rule":"evenodd"})])]),e("div",{class:"text-sm text-amber-700"},[e("div",{class:"font-medium mb-1"},"审核提醒"),e("div",null," 当前系统采用人工审核模式，提交后需要管理员审核通过才能在广场显示，请耐心等待审核结果。 ")])],-1)]))),o(g).is_enable?(r(),d("div",te,[e("div",oe,[t[10]||(t[10]=e("div",{class:"text-green-500 mt-0.5"},[e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z"}),e("path",{"fill-rule":"evenodd",d:"M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z","clip-rule":"evenodd"})])],-1)),e("div",se,[t[9]||(t[9]=e("div",{class:"text-base font-semibold text-green-800 mb-2"},"💰 分成收益说明",-1)),e("div",ae,[t[8]||(t[8]=e("div",{class:"mb-2"},[e("span",{class:"font-medium"},"🎯 收益机制："),m("分享智能体至广场后，其他用户每次使用您的智能体时，您都将获得分成收益！ ")],-1)),e("div",re,[t[5]||(t[5]=e("span",{class:"font-medium"},"💎 分成比例：",-1)),m("用户消耗"+n(o(x))+"的 ",1),e("span",ne,n(o(C))+"%",1),t[6]||(t[6]=m(" 将作为您的收益 "))]),e("div",le,[t[7]||(t[7]=e("span",{class:"font-medium"},"示例：",-1)),m("用户使用您的智能体消耗了100"+n(o(x))+"，您将获得"+n(Math.round(100*o(C)/100))+n(o(x))+"收益 ",1)])])])])])):h("",!0),e("div",ie,[b(p,{size:"large",class:"w-[360px]",modelValue:o(l).cate_id,"onUpdate:modelValue":t[0]||(t[0]=i=>o(l).cate_id=i),placeholder:"全部",style:{"--el-fill-color-blank":"#F7F7FB"}},{default:c(()=>[(r(!0),d(Z,null,G(o(k),i=>(r(),J(a,{key:i.id,label:i.name,value:i.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])]),_:1},8,["onConfirm"])])}}}),Te=K(me,[["__scopeId","data-v-cd200a54"]]);export{Te as default};
