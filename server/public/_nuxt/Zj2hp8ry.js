import{_ as N}from"./BClAlNto.js";import{h as L,e as $,s as B}from"./DkNxoV1Z.js";import{E as D}from"./BaKauqEV.js";import{_ as R}from"./C-HK5oo_.js";import{l as b,b as g,M as s,N as r,O as e,a5 as _,X as y,u as i,Z as a,a0 as u,a2 as j,V as z,a7 as A,aa as M,y as U,_ as h,aq as v,a3 as q}from"./uahP8ofS.js";import{_ as k}from"./DlAUqK2U.js";/* empty css        */import{E as F}from"./DZB2bx3q.js";import"./Txs22M_p.js";import"./D45YP0Si.js";import"./FN4AbcWJ.js";const O={class:"cursor-default"},T=b({__name:"index",props:{title:{type:String,default:""},length:{type:Number,default:0}},setup(d){const t=g(!0),c=()=>{t.value==!0?t.value=!1:t.value=!0};return(f,p)=>{const m=R;return s(),r("div",null,[e("div",{class:"flex items-center justify-between mt-[15px] text-info",onClick:c},[e("div",O,_(d.title),1),e("div",{class:y(["transition-transform rotate-",{"rotate-180":!i(t)}])},[a(m,{name:"el-icon-ArrowUp"})],2)]),a(M,null,{default:u(()=>[i(t)?(s(),r("div",{key:0,style:j({"max-height":d.length*110+"px"}),class:"dropDownList overflow-hidden"},[z(f.$slots,"menu",{},void 0,!0)],4)):A("",!0)]),_:3})])}}}),X=k(T,[["__scopeId","data-v-2b7032c7"]]),Z={class:"bg-body rounded-[12px] flex flex-col h-full overflow-hidden text-tx-primary",style:{width:"var(--aside-panel-width)"}},G={class:"flex items-center justify-around text-xl font-medium px-[16px] pt-[16px] cursor-pointer"},H={class:"px-[16px] pt-[16px] pb-[6px]"},J={class:"flex-1 min-h-0"},K={class:"px-[16px] pb-[16px]"},P=["onClick"],Q={class:"ml-2 flex-1"},W={class:"text-base font-bold role-name"},Y={class:"text-xs role-desc text-tx-placeholder line-clamp-1"},ee=b({__name:"role-sidebar",props:{sidebarList:{default:()=>[]},currentId:{default:()=>0},keyword:{default:""}},emits:["ontoggle","update:keyword"],setup(d,{emit:t}){const c=t,p=L(d,"keyword",c),m=g();return(x,o)=>{const w=N,C=$,I=D,V=X;return s(),r("div",Z,[e("div",G,[a(w,{to:"/dialogue/chat"},{default:u(()=>o[1]||(o[1]=[e("div",{class:"pb-[8px]"},"问答助手",-1)])),_:1}),o[2]||(o[2]=e("div",{class:"pb-[6px] text-primary border-solid border-b-[2px] border-primary"}," 角色助手 ",-1))]),e("div",H,[a(C,{class:"w-full leading-[32px] role-search",modelValue:i(p),"onUpdate:modelValue":o[0]||(o[0]=n=>U(p)?p.value=n:null),"prefix-icon":i(B),placeholder:"请输入关键词搜索"},null,8,["modelValue","prefix-icon"])]),e("div",J,[a(i(F),{class:"",ref_key:"sidebarRef",ref:m},{default:u(()=>[e("div",K,[(s(!0),r(h,null,v(x.sidebarList,(n,E)=>(s(),q(V,{title:n.name,length:n.skill.length,key:E},{menu:u(()=>[(s(!0),r(h,null,v(n.skill,(l,S)=>(s(),r("div",{class:y(["flex items-center mt-[15px] p-[10px] cursor-pointer rounded-[12px]",{"role-active":x.currentId==l.id}]),key:S,onClick:te=>c("ontoggle",l)},[a(I,{src:l.image,class:"w-[42px] h-[42px] rounded-[8px]"},null,8,["src"]),e("div",Q,[e("div",W,_(l.name),1),e("div",Y,_(l.describe),1)])],10,P))),128))]),_:2},1032,["title","length"]))),128))])]),_:1},512)])])}}}),me=k(ee,[["__scopeId","data-v-5ed71039"]]);export{me as default};
