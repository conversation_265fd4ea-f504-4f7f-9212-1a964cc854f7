import{_ as C}from"./B6IIPh85.js";import{bF as v,f as u,E as b}from"./ClNUxNV9.js";import{E as M}from"./CqanTtdS.js";import"./CxSV922q.js";import{r as d,a as B,b as P}from"./C5YZ-9ot.js";import{l as S,b as T,m as k,M as m,a1 as f,a0 as i,V as F,Z as I,a6 as U}from"./Dp9aCaJ6.js";const z=5*1024*1024,N=S({__name:"upload-button",props:{type:{},isParseContent:{type:Boolean},isOnlyParseContent:{type:Boolean}},emits:["on-success","on-failed"],setup(p,{emit:y}){const l={file:"*/*",image:"image/*",video:"video/*",audio:"audio/*"},o=p,c=y,a=T(!1),g=k(()=>l[o.type]??l.file);function _(){return Math.random().toString(36).substring(2,15)+Math.random().toString(36).substring(2,15)}async function w(e){try{if(e.file.size>z)throw new Error("文件大小不能超过5MB");return a.value=!0,o.isOnlyParseContent?{id:_().toString(),uri:"",type:0,name:e.file.name}:await v(o.type,{file:e.file,name:"file"})}catch(t){u.msgError(t instanceof Error?t.message:"上传失败")}finally{a.value=!1}}async function E(e,t){const n={id:e.id,url:e.uri,type:e.type,name:e.name,size:t.size,text:""};try{a.value=!0,(o.isParseContent||o.isOnlyParseContent)&&(n.text=await x(t.raw))}catch(s){const r=s instanceof Error?s.message:"文件解析错误";u.msgError(r)}finally{a.value=!1}c("on-success",n)}const x=async e=>{switch(e.name.split(".").pop()){case"md":case"txt":return await d(e);case"pdf":return await P(e);case"doc":case"docx":return await B(e);default:return await d(e)}};return(e,t)=>{const n=C,s=b,r=M;return m(),f(r,{ref:"uploadRef","show-file-list":!1,accept:g.value,multiple:!1,"on-success":E,"http-request":w,"on-error":h=>c("on-failed",h)},{trigger:i(()=>[e.$slots.default?F(e.$slots,"default",{key:0,isLoading:a.value}):(m(),f(s,{key:1,plain:"",class:"!rounded-[8px]",loading:a.value},{icon:i(()=>[I(n,{name:"el-icon-Upload"})]),default:i(()=>[t[0]||(t[0]=U(" 上传文件 "))]),_:1},8,["loading"]))]),_:3},8,["accept","on-error"])}}});export{N as _};
