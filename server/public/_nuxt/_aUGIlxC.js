import{l as R,e as b,o as y,p as k}from"./DkNxoV1Z.js";import{_ as q}from"./Fc7Bg_YF.js";import{P as U}from"./Dh9d3v4F.js";import"./DP2rzg_V.js";/* empty css        */import{a as B}from"./C6_W4ts7.js";import{l as C,j as f,b as E,s as I,M as P,a3 as D,a0 as s,V as F,Z as t,O as l,u as o}from"./uahP8ofS.js";const N={class:"w-[420px]"},A=C({__name:"add",emits:["success"],setup(S,{emit:_}){const c=_,v=R(),i=f(),a=E({name:"",avatar:"",image:""}),u=f(),g=I({name:[{required:!0,message:"请输入形象名称"}],avatar:[{required:!0,type:"string",message:"请选择形象头像"}],image:[{required:!0,type:"string",message:"请选择形象封面"}]}),V=async()=>{var e,m;await((e=u.value)==null?void 0:e.validate());const{id:n}=await B(a.value);(m=i.value)==null||m.close(),v.push({path:"/application/digital/edit",query:{id:n}}),c("success")};return(n,e)=>{const m=b,p=y,d=q,x=k,w=U;return P(),D(w,{ref_key:"popRef",ref:i,title:"创建形象",width:"500px",async:"",onConfirm:V},{trigger:s(()=>[F(n.$slots,"default")]),default:s(()=>[t(x,{class:"p-4",ref_key:"formRef",ref:u,model:o(a),"label-width":"100px",rules:o(g)},{default:s(()=>[t(p,{label:"形象名称",prop:"name"},{default:s(()=>[l("div",N,[t(m,{modelValue:o(a).name,"onUpdate:modelValue":e[0]||(e[0]=r=>o(a).name=r),placeholder:"请输入形象名称",clearable:""},null,8,["modelValue"])])]),_:1}),t(p,{label:"形象头像",prop:"avatar"},{default:s(()=>[l("div",null,[l("div",null,[t(d,{modelValue:o(a).avatar,"onUpdate:modelValue":e[1]||(e[1]=r=>o(a).avatar=r)},null,8,["modelValue"])]),e[3]||(e[3]=l("div",{class:"form-tips"},"建议尺寸：50*50px",-1))])]),_:1}),t(p,{label:"形象封面",prop:"image"},{default:s(()=>[l("div",null,[l("div",null,[t(d,{modelValue:o(a).image,"onUpdate:modelValue":e[2]||(e[2]=r=>o(a).image=r)},null,8,["modelValue"])]),e[4]||(e[4]=l("div",{class:"form-tips"},"建议尺寸：280px×187px",-1))])]),_:1})]),_:1},8,["model","rules"])]),_:3},512)}}});export{A as _};
