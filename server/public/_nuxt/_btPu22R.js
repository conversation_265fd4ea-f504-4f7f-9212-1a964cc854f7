import{E as H,a as J}from"./BNvvS5EC.js";import{_ as K}from"./BY8Moot3.js";import{E as W}from"./DVv83yww.js";import{E as Y,a as ee}from"./b4JHIKvk.js";import{h as j,b as te,bV as se}from"./C9xud4Fy.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import{P as le}from"./CWneeePX.js";import{u as oe}from"./DGQtBkeI.js";import{l as ie,j as de,b as ae,r as ne,m as F,c as P,M as o,N as i,a3 as S,a0 as x,_ as k,aq as C,u as r,O as n,a5 as _,y as R,a7 as y,X as V,Z as g,a4 as re}from"./uahP8ofS.js";import{_ as ce}from"./DlAUqK2U.js";const ue={class:"flex items-center"},pe={class:"my-1 flex items-center justify-between"},me={class:"flex items-center flex-1"},fe={class:"leading-6 mr-2"},xe={key:0,class:"text-[#23B571] font-medium bg-[#E3FFF2] px-[8px] py-[4px] rounded-[6px] text-xs leading-[20px]"},_e={key:1,class:"text-gray-500 text-xs leading-5"},ve={class:"line-clamp-1 flex-1 flex items-center"},ye={key:0},be={key:1,class:"text-[#a8abb2]"},ge={class:"flex-none ml-2 flex items-center"},he={class:"model-container"},Le={class:"flex items-center h-[46px] py-2"},ke=["src"],Ce={class:"mx-2 leading-[24px] mt-[2px] font-medium"},Me={key:1,class:"bg-[#E3FFF2] text-[#23B571] font-medium px-[8px] py-[4px] leading-[20px] rounded-[6px] text-xs"},we=["onClick"],$e={class:"flex items-center"},Ee={class:"mr-2 leading-6"},Fe={key:0,class:"text-[#23B571] font-medium bg-[#E3FFF2] px-[8px] py-[2px] rounded-[4px] text-xs leading-[16px]"},Pe={key:1,class:"text-gray-500 text-xs leading-5"},Se={key:0,class:"flex items-center"},Ve={key:1,class:"flex items-center"},Ae={key:0,class:"mt-3 p-3 bg-gradient-to-r from-blue-50 to-blue-100 border border-blue-300 rounded-lg text-blue-700",style:{"box-shadow":"0 2px 8px rgba(59, 130, 246, 0.1)"}},Be=ie({__name:"index",props:{id:{type:[String,Number],default:""},sub_id:{type:[String,Number],default:""},setDefault:{type:Boolean,default:!0},type:{type:String,default:"chatModels"},disabled:{type:Boolean,default:!1}},emits:["update:id","update:sub_id","update:modelConfig","update:config"],setup(m,{emit:T}){const h=T,l=m,u=j(l,"id",h),f=j(l,"sub_id",h),A=te(),M=de(),b=ae(-1),s=ne({modelList:[]}),U=F(()=>s.modelList.filter((e,t)=>t%2===0)),I=F(()=>s.modelList.filter((e,t)=>t%2!==0)),w=F(()=>!s.modelList||!Array.isArray(s.modelList)?{}:l.type==="chatModels"?s.modelList.flatMap(e=>e.models||[]).find(e=>e.id===f.value)||{}:s.modelList.find(e=>e.id===u.value)||{});P(()=>w.value,e=>{var d,c;h("update:modelConfig",e);const t=((c=(d=s.modelList.find(L=>L.id===u.value))==null?void 0:d.configs)==null?void 0:c[0])||{};h("update:configs",t)});const{suspense:q}=oe(["modelLists"],{queryFn:se,cacheTime:1e3}),z=async()=>{try{const{data:e}=await q();s.modelList=e[l.type]||[],console.log(`PC端获取${l.type}模型数据:`,s.modelList),$(),l.setDefault&&s.modelList.length>0&&O()}catch(e){console.log("PC端获取模型数据错误=>",e),s.modelList=[]}},$=()=>{if(!(!s.modelList||s.modelList.length===0))if(console.log(`PC端initSavedValues - type: ${l.type}, id: ${l.id}, sub_id: ${l.sub_id}`),console.log("PC端modelList:",s.modelList),l.type==="chatModels"){if(l.sub_id){for(const e of s.modelList)if(e.models&&e.models.some(t=>t.id===l.sub_id)){u.value=e.id,f.value=l.sub_id;const t=s.modelList.findIndex(d=>d.id===e.id);t!==-1&&(b.value=t),console.log(`PC端对话模型初始化: model=${e.id}, subModel=${l.sub_id}`);break}}}else if(l.id){const e=String(l.id),t=s.modelList.find(d=>String(d.id)===e);t?(u.value=t.id,console.log(`PC端${l.type}初始化成功:`,e,"->",t.alias||t.name)):(console.log(`PC端${l.type}初始化失败: 未找到ID为 ${e} 的模型`),console.log("PC端可用模型列表:",s.modelList.map(d=>({id:d.id,name:d.name,alias:d.alias}))))}else console.log(`PC端${l.type}初始化跳过: props.id为空`)},O=()=>{var t,d;if(!s.modelList||s.modelList.length===0)return;const e=s.modelList.findIndex(c=>c.is_default)||0;if(l.type==="chatModels"){const c=(d=(t=s.modelList[e])==null?void 0:t.models)==null?void 0:d[0];c&&(u.value=s.modelList[e].id,f.value=c.id,b.value=e)}else s.modelList[e]&&(u.value=s.modelList[e].id,b.value=e)},B=(e,t)=>e===0?t*2:t*2+1,G=(e,t)=>{const d=s.modelList[B(e,t)];return!d||!d.models?!1:d.models.some(c=>c.id===f.value)},Q=(e,t)=>{u.value=e,l.type==="chatModels"?f.value=t:f.value="",M.value.close()};return P(()=>[l.id,l.sub_id],()=>{s.modelList&&s.modelList.length>0&&$()}),P(()=>s.modelList,e=>{e&&e.length>0&&(console.log("PC端modelList更新，重新初始化已保存值"),$())},{immediate:!0}),z(),(e,t)=>{const d=H,c=J,L=K,N=W,X=Y,Z=ee;return o(),i("div",ue,[m.type==="vectorModels"||m.type==="rankingModels"?(o(),S(c,{key:0,class:"flex-1",modelValue:r(u),"onUpdate:modelValue":t[0]||(t[0]=a=>R(u)?u.value=a:null),filterable:"",disabled:m.disabled},{default:x(()=>[(o(!0),i(k,null,C(r(s).modelList,a=>(o(),S(d,{class:"!h-fit",value:a.id,key:a.id,label:a.alias||a.name},{default:x(()=>[n("div",pe,[n("div",me,[n("div",fe,_(a.alias||a.name),1),a.price=="0"||a.is_free?(o(),i("div",xe," 会员免费 ")):(o(),i("div",_e," 消耗"+_(a.price)+_(r(A).getTokenUnit)+"/1000字符 ",1))])])]),_:2},1032,["value","label"]))),128))]),_:1},8,["modelValue","disabled"])):y("",!0),m.type==="chatModels"||m.type==="vlModels"?(o(),i("div",{key:1,class:V(["select-input flex items-center justify-between flex-1 cursor-pointer rounded-[8px] w-[266px] h-[32px] px-[15px]",[r(f)?"":"text-tx-placeholder",m.disabled?"text-tx-placeholder cursor-no-drop bg-[--el-disabled-bg-color]":""]]),onClick:t[1]||(t[1]=a=>r(M).open())},[n("div",ve,[r(w).alias?(o(),i("span",ye,_(r(w).alias),1)):(o(),i("span",be,"请选择"))]),n("div",ge,[g(L,{name:"el-icon-ArrowDown"})])],2)):y("",!0),m.type==="chatModels"||m.type==="vlModels"?(o(),S(le,{key:2,ref_key:"popupRef",ref:M,width:"780px",title:"模型选择",customClass:"!rounded-[15px]"},{footer:x(()=>t[3]||(t[3]=[n("div",null,null,-1)])),default:x(()=>[g(N,{height:"50vh","max-height":"70vh"},{default:x(()=>[n("div",he,[g(Z,{"active-name":r(b),"onUpdate:activeName":t[2]||(t[2]=a=>R(b)?b.value=a:null),class:"flex flex-wrap justify-between",accordion:""},{default:x(()=>[(o(!0),i(k,null,C([r(U),r(I)],(a,E)=>(o(),i("div",{key:E},[(o(!0),i(k,null,C(a,(v,D)=>(o(),i("div",{key:v.id,class:"w-[350px] mt-[15px]"},[g(X,{class:V(["bg-[#f8f8f8] dark:bg-[#0d0e10] border border-solid border-[transparent]",{"el-collapse-item--active":G(E,D)}]),name:B(E,D)},{title:x(()=>[n("div",null,[n("div",Le,[v.logo?(o(),i("img",{key:0,src:v.logo,class:"w-[30px] h-[30px]",alt:"模型logo"},null,8,ke)):y("",!0),n("span",Ce,_(v.name),1),v.is_free?(o(),i("span",Me," 会员免费 ")):y("",!0)])])]),default:x(()=>[g(N,{height:"100%","max-height":"250px"},{default:x(()=>[(o(!0),i(k,null,C(v.models,p=>(o(),i("div",{key:p.id,class:V(["flex justify-between mb-[14px] px-[15px] cursor-pointer hover:text-primary",{"text-primary":r(f)===p.id}]),onClick:Ne=>Q(v.id,p.id)},[n("div",$e,[n("span",Ee,_(p.alias||"请选择"),1),p.alias&&(p.price=="0"||p.is_free)?(o(),i("span",Fe," 会员免费 ")):p.alias?(o(),i("span",Pe," 消耗"+_(p.price)+_(r(A).getTokenUnit)+"/1000字符 ",1)):y("",!0)]),r(f)===p.id?(o(),i("div",Se,[g(L,{name:"el-icon-CircleCheck",size:"20"})])):(o(),i("div",Ve,t[4]||(t[4]=[n("div",{class:"w-[18px] h-[18px] rounded-full border border-solid border-[#cacbd3]"},null,-1)])))],10,we))),128)),e.cItem&&e.cItem.vip_limit_info&&e.cItem.vip_limit_info.is_exceeded?(o(),i("div",Ae,t[5]||(t[5]=[n("div",{class:"font-semibold mb-2 flex items-center text-sm"},[n("span",{class:"text-blue-500 mr-2 text-base"},"💡"),re(" 会员免费额度提醒 ")],-1),n("div",{class:"text-xs leading-5 text-blue-600"}," 该模型短时间内使用频次过高，已明显超过正常使用需求，平台将对该模型免费使用进行限制，继续使用将正常扣费，建议您使用其他会员免费模型。此限制将于今日24:00解除，届时您可继续免费使用该模型。 ",-1)]))):y("",!0)]),_:2},1024)]),_:2},1032,["class","name"])]))),128))]))),128))]),_:1},8,["active-name"])])]),_:1})]),_:1},512)):y("",!0)])}}}),He=ce(Be,[["__scopeId","data-v-21a82443"]]);export{He as _};
