import{P as i}from"./2Cj0Rm6F.js";import{l as _,j as u,b as n,M as f,a3 as m,a0 as d,u as r,O as l,a5 as h}from"./uahP8ofS.js";const v={class:"whitespace-pre-wrap"},B=_({__name:"reply-popup",setup(w,{expose:c}){const o=u(),p=n(""),t=n("");return c({open:(a,s)=>{var e;p.value=s,(e=o.value)==null||e.open(),t.value=a}}),(a,s)=>{const e=i;return f(),m(e,{ref_key:"popRef",ref:o,width:"700px",title:r(p)},{default:d(()=>[l("div",null,[l("div",v,h(r(t)),1)])]),_:1},8,["title"])}}});export{B as _};
