import{_ as V}from"./D5KDMvDa.js";import{h as x,e as v,o as w,p as N,E as B}from"./C3HqF-ve.js";import{_ as C}from"./D-XwFUUB.js";import{E as g}from"./Dg2XwvBU.js";import"./DP2rzg_V.js";/* empty css        *//* empty css        */import{l as k,b as E,c as y,M as U,N as b,O as l,Z as t,a0 as s,u as n,a4 as m}from"./Dp9aCaJ6.js";import{_ as I}from"./DlAUqK2U.js";import"./zboy2uUK.js";import"./27mOw3Er.js";import"./yWpGL9Dq.js";import"./Bpcqu5bh.js";import"./D0Qj8Spo.js";import"./9Bti1uB6.js";import"./DCTLXrZ8.js";/* empty css        */const $={class:"manual-import"},D={class:"important-notice"},z={class:"notice-header"},F={class:"py-4"};const q=k({__name:"manual",props:{modelValue:{}},emits:["update:modelValue"],setup(r,{emit:d}){const o=x(r,"modelValue",d),p=E([]);return y(p,i=>{o.value.images=i.map(({url:e})=>e)}),(i,e)=>{const u=V,c=v,_=w,O=C,R=B,f=N,h=g;return U(),b("div",$,[l("div",D,[l("div",z,[t(u,{name:"el-icon-Warning",color:"#ff9900",size:"18"}),e[4]||(e[4]=l("span",{class:"notice-title"},"重要提示",-1))]),e[5]||(e[5]=l("div",{class:"notice-content"}," 为保护您的信息安全，请勿录入如姓名、身份证号、电话等个人隐私信息及任何工作秘密、商业秘密。本平台在未获得您同意的情况下，不会使用您的数据进行模型训练。 ",-1))]),t(h,null,{default:s(()=>[l("div",F,[t(f,{"label-width":"0px"},{default:s(()=>[t(_,null,{default:s(()=>[t(c,{modelValue:n(o).question,"onUpdate:modelValue":e[0]||(e[0]=a=>n(o).question=a),placeholder:"请输入问题"},null,8,["modelValue"])]),_:1}),t(_,null,{default:s(()=>[t(c,{modelValue:n(o).answer,"onUpdate:modelValue":e[1]||(e[1]=a=>n(o).answer=a),placeholder:"请输入问题答案，10000个字以内。",type:"textarea",resize:"none",rows:15,maxlength:"10000"},null,8,["modelValue"])]),_:1}),m("",!0),m("",!0)]),_:1})])]),_:1})])}}}),ae=I(q,[["__scopeId","data-v-4574d02d"]]);export{ae as default};
