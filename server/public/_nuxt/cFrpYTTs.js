import{E as y}from"./DH3BuQAR.js";import{a as w,E as R}from"./NEhbE7vl.js";import{f as k,E as q,p as I}from"./ClNUxNV9.js";/* empty css        *//* empty css        */import{_ as C}from"./CFq2wqJO.js";import{_ as N}from"./B1qejN0L.js";import{_ as U}from"./CpdnE52h.js";import{_ as j}from"./CyBaOjQh.js";import A from"./BaY3JvnQ.js";import{u as B}from"./Bo3PTL3c.js";import{c as $}from"./CeZA--ML.js";import{l as F,b as O,m as S,w as T,j as h,s as D,u as i,M,a1 as P,a0 as r,O as f,Z as t,y as s,a6 as Z,a4 as z}from"./Dp9aCaJ6.js";import{_ as G}from"./DlAUqK2U.js";import"./Cv6HhfEG.js";import"./DZESUSa0.js";import"./D9R3MaZj.js";import"./B6IIPh85.js";import"./CqanTtdS.js";import"./CxSV922q.js";import"./Cr7puf4F.js";import"./Cd8UtlNR.js";import"./Tedtu6ac.js";import"./DCTLXrZ8.js";import"./B1qK8f0i.js";import"./zRTrVFrw.js";import"./BbPTMigZ.js";import"./DkqMgBWM.js";import"./CCKuheBh.js";import"./C1aykdW0.js";import"./CqNjbiLO.js";import"./DlKZEFPo.js";import"./DP2rzg_V.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./l0sNRNKZ.js";import"./BNnETjxs.js";import"./g85fV0DF.js";import"./D6j_GvJh.js";import"./BYYVzU6A.js";import"./9Bti1uB6.js";import"./DIFvMdL6.js";import"./CQXeYJFv.js";import"./DMVjPTRc.js";import"./qQcR97T8.js";import"./C0R3uvOr.js";/* empty css        */import"./DCJ0qFa1.js";/* empty css        */import"./Du5U7mLs.js";import"./DrS9ZDPg.js";import"./DggqxWTi.js";/* empty css        *//* empty css        */import"./DJu6qumd.js";import"./DaDeVoIA.js";import"./BwQaTxf4.js";import"./BNsSO7uz.js";import"./hx-0JZAY.js";import"./CNgDMrD1.js";import"./D4cQUBDp.js";/* empty css        */import"./C6_W4ts7.js";import"./DN0R-r9F.js";const H={class:"flex-1 min-h-0"},J={class:"my-[15px] flex justify-center"},K=F({__name:"index",props:{modelValue:{}},emits:["success"],setup(_,{emit:c}){const b=_,g=c,e=O({}),V=S(()=>!!Object.keys(e.value).length);T(()=>{e.value=$(b.modelValue)});const u=h(),n=D({image:[{required:!0,type:"string",message:"请选择应用图标"}],name:[{required:!0,message:"请输入应用名称"}],model_id:[{required:!0,message:"请选择AI通道",trigger:["blur"]}],model_sub_id:[{required:!0,message:"请选择AI模型",trigger:["blur"]}],cate_id:[{required:!0,message:"请选择分类",trigger:["blur"]}],digital_id:[{required:!0,message:"请选择形象",trigger:["change"]},{validator(p,o,l){Number(o)===0&&l(new Error("请选择形象")),l()}}]}),v=async()=>{var p,o;try{await((p=u.value)==null?void 0:p.validate()),await B(e.value),g("success")}catch(l){for(const a in l){Object.keys(n).includes(a)&&k.msgError((o=l[a][0])==null?void 0:o.message);break}}};return(p,o)=>{const l=y,a=w,d=R,E=q,x=I;return i(V)?(M(),P(x,{key:0,ref_key:"formRef",ref:u,model:i(e),"label-width":"140px",rules:i(n),class:"app-edit flex flex-col"},{default:r(()=>[f("div",H,[t(d,{"model-value":"base"},{default:r(()=>[t(a,{label:"基本配置",name:"base"},{default:r(()=>[t(l,null,{default:r(()=>[t(C,{modelValue:i(e),"onUpdate:modelValue":o[0]||(o[0]=m=>s(e)?e.value=m:null)},null,8,["modelValue"])]),_:1})]),_:1}),t(a,{label:"AI模型/搜索配置",name:"search"},{default:r(()=>[t(l,null,{default:r(()=>[t(N,{modelValue:i(e),"onUpdate:modelValue":o[1]||(o[1]=m=>s(e)?e.value=m:null)},null,8,["modelValue"])]),_:1})]),_:1}),t(a,{label:"界面配置",name:"interface"},{default:r(()=>[t(l,null,{default:r(()=>[t(U,{modelValue:i(e),"onUpdate:modelValue":o[2]||(o[2]=m=>s(e)?e.value=m:null)},null,8,["modelValue"])]),_:1})]),_:1}),t(a,{label:"形象配置",name:"digital"},{default:r(()=>[t(l,null,{default:r(()=>[t(j,{modelValue:i(e),"onUpdate:modelValue":o[3]||(o[3]=m=>s(e)?e.value=m:null)},null,8,["modelValue"])]),_:1})]),_:1}),t(a,{label:"工作流配置",name:"flow"},{default:r(()=>[t(l,null,{default:r(()=>[t(A,{modelValue:i(e),"onUpdate:modelValue":o[4]||(o[4]=m=>s(e)?e.value=m:null)},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),f("div",J,[t(E,{type:"primary",onClick:v},{default:r(()=>o[5]||(o[5]=[Z(" 保存")])),_:1})])]),_:1},8,["model","rules"])):z("",!0)}}}),io=G(K,[["__scopeId","data-v-e8bd7a27"]]);export{io as default};
