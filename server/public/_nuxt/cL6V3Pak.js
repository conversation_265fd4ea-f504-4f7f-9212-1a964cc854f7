import{_ as rt}from"./B9NSz8IO.js";import{c as lt,_ as ct,a as dt,b as ut}from"./D9xDlWdi.js";import{_ as pt}from"./BY8Moot3.js";import{b as ft,a as vt,j as mt,cx as Ve,bx as _t,by as ht,u as yt,bB as X,bE as de,f as Y,E as gt}from"./C9xud4Fy.js";import{E as xt,a as bt}from"./b4JHIKvk.js";import{E as wt}from"./CKaLuefT.js";import{_ as kt}from"./BQBYw5b-.js";import{u as Rt}from"./CJotcpX_.js";import{l as Ct,j as z,b as h,ak as Et,r as ue,m as Ae,c as Tt,F as St,k as Lt,M as c,N as y,O as a,u as e,X as $e,a7 as _,a5 as T,Z as v,a0 as f,a4 as H,ab as qe,ac as Be,a2 as pe,a3 as w,_ as fe,aq as ve,as as It,n as zt}from"./uahP8ofS.js";import{u as jt}from"./2e9ulp1P.js";import{u as Ot,a as Pt}from"./MiioSZkP.js";import{u as Vt}from"./-Ctiqued.js";import{_ as At}from"./Con3asG-.js";import{_ as $t}from"./DjHpj1m1.js";import{B as qt,C as Bt,b as Ft,r as Dt,c as Ht,a as Nt,v as Mt,d as Ut}from"./BXWYK723.js";import{P as Wt}from"./CWneeePX.js";import{u as Jt}from"./BPUNRRf1.js";import{_ as Zt}from"./KFWgM_CI.js";import{useSessionFiles as Gt}from"./BkN_yJAT.js";import{E as Fe}from"./DVv83yww.js";import{_ as Kt}from"./DlAUqK2U.js";import"./CrYRPBPt.js";/* empty css        */import"./CVlyBbl9.js";import"./CC6TKTZR.js";import"./DWsmRjYd.js";import"./B7tOnmRj.js";import"./DjcM80p6.js";/* empty css        */import"./7EqMz3ZJ.js";import"./BgwqqoEm.js";import"./Cpg3PDWZ.js";import"./ABiOVlWA.js";import"./BD9OgGux.js";import"./DCTLXrZ8.js";/* empty css        */import"./DJEqh1lw.js";/* empty css        */import"./DwFObZc_.js";import"./DQUFgXGm.js";import"./WcH50Ugn.js";import"./RSfPJsXe.js";import"./C1JW1GZc.js";import"./VQGzo2mk.js";import"./DZa95GrR.js";import"./BwGb81kr.js";import"./DSHcXcee.js";import"./5nPgGenE.js";import"./DP2rzg_V.js";/* empty css        */import"./C07R3Lak.js";const De=""+new URL("user_avatar.B42E77Pp.png",import.meta.url).href,Qt={class:"h-full flex flex-col max-w-[720px] mx-auto bg-page rounded-[10px] overflow-hidden",style:{"box-shadow":"0px 5px 40px 0px rgba(0, 0, 0, 0.05)"}},Xt={class:"flex p-main items-center bg-body"},Yt=["src"],eo={class:"text-2xl line-clamp-1"},to={class:"text-tx-secondary line-clamp-1"},oo={class:"flex-1 min-h-0"},ao={ref:"containerRef",class:"h-full flex flex-col rounded relative"},so={class:"absolute top-0 left-0 w-full h-full flex flex-col z-10"},no={class:"flex-1 min-h-0"},io={class:"p-main"},ro={class:"my-[5px]"},lo={class:"bg-body"},co={class:"flex items-center h-12 px-3 bg-page rounded-lg max-w-xs line-clamp-1 overflow-hidden"},uo={key:0,class:"pb-[10px] text-center text-tx-regular"},po={key:1,class:"h-full relative"},fo=["width","height"],vo={class:"h-full flex justify-center items-center"},mo={class:"p-[20px] h-full flex relative z-10"},_o={class:"flex-1 h-full flex flex-col"},ho={class:"flex-1 min-h-0"},yo={class:"flex items-center cursor-pointer"},go={class:"text-xl flex-1 min-w-0 text-white"},xo={class:"h-full flex"},bo={class:"h-full flex flex-col items-center w-[160px] justify-end"},wo=["width","height","id"],ko={class:"text-xs text-white bg-[rgba(51,51,51,0.3)] py-[5px] px-[10px] rounded my-[10px]"},Ro={class:"w-[400px] h-full flex flex-col mr-[20px] pt-[100px]"},Co={class:"flex-1 min-h-0 bg-[rgba(0,0,0,0.5)] rounded-[20px] overflow-hidden flex flex-col"},Eo={class:"flex-1 min-h-0"},To={class:"py-4 px-[20px]"},So={key:1,class:"h-full flex justify-center text-tx-secondary items-center"},Lo={key:0,class:"flex justify-center items-center py-[10px]"},Io={class:"flex flex-col justify-center items-center"},zo={class:"flex justify-center mt-4"},jo={key:0,class:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40"},Oo={class:"bg-white rounded-[16rpx] p-[40rpx] shadow-xl max-w-[600rpx] w-[80vw] text-center"},Po={class:"text-[24rpx] text-gray-700 mb-[32rpx]"},Vo=Ct({__name:"[key]",async setup(Ao){let W,me;const J=ft();Jt();const q=Gt(),He=vt(),E=mt(),{copy:Ne}=Rt(),ee=z(),{key:k=""}=He.params,S=h(""),{height:_e,width:$o}=Ve(),{data:s}=([W,me]=Et(async()=>jt(async()=>{const o=await qt({apikey:k}),t=X("SHARE_CHAT_UNIQUE_ID","");if(!t.value){const[u]=await Bt({robot_id:o.robot.id},{authorization:k,password:S.value,identity:E.visitorId});t.value=u}return{...o,uniqueId:t.value}},{default(){return{robot:{}}}},"$89nQiJhyNZ")),W=await W,me(),W),te=z(),j=ue({_index:-1,robot_id:s.value.robot.id,record_id:-1,content:""}),Me=(o,t)=>{var u;j.record_id=o.id,j._index=t,(u=te.value)==null||u.open()},Ue=async()=>{var o;try{await Ht(j),(o=te.value)==null||o.close(),j.content="",g.value[j._index].is_feedback=1}catch(t){console.log("反馈提交失败-->",t)}},B=Ae(()=>s.value.chat_type===2&&s.value.robot.is_digital&&s.value.digital.id&&!s.value.digital.is_disable?2:1);J.isMobile&&B.value===2&&window.location.replace(`/mobile/packages/pages/digital_chat/share_chat?key=${k}`);const he=Ae(()=>{var o;return((o=s.value.menus)==null?void 0:o.map(t=>({keyword:t})))||[]}),N=async()=>{var t;const o=X(de,{});if(S.value=o.value[k]||"",s.value.pwd&&!S.value)return(t=ee.value)==null||t.open(),Promise.reject()},We=async o=>{var u;const t=X(de,{});S.value=o.password,t.value=Object.assign(t.value,{[k]:o.password}),(u=ee.value)==null||u.close(),Oe()},ye=()=>{const o=X(de,{});o.value=Object.assign(o.value,{[k]:""})},g=h([]);let oe=0;const ae=async()=>{try{const o=await Nt({share_apikey:k,identity:E.visitorId,page_size:25e3},{password:S.value,authorization:k,identity:E.visitorId});if(g.value=o.lists||[],setTimeout(()=>{M()}),B.value===2&&x.value==3){const t=g.value[g.value.length-1];t&&t.id!==oe&&(oe=t.id,Xe(oe))}}catch(o){return o=="访问密码错误!"&&(ye(),await N()),Promise.reject()}},ge=async()=>{await N(),await Y.confirm("确定清空记录？"),await Ft({},{password:S.value,authorization:k,identity:E.visitorId}),ae()};let d=null;const O=h(!1),Z=z(),G=async(o,t="input")=>{var b;if(await E.getFingerprint(),await N(),!o)return Y.msgError("请输入问题");if(O.value)return;O.value=!0,R(3);const u=Date.now(),n=q.files.value.map(i=>({name:i.name,type:"30",url:i.url}));g.value.push({type:1,content:o,files_plugin:n}),g.value.push({type:2,typing:!0,content:"",reasoning:"",key:u}),(b=Z.value)==null||b.setInputValue();const l=g.value.find(i=>i.key===u);d=Dt({question:o,unique_id:s.value.uniqueId,stream:!0,files:q.files.value.map(i=>({...i,type:"30"}))},{password:S.value,authorization:k,identity:E.visitorId}),d.addEventListener("reasoning",({data:i})=>{const{data:p,index:C}=i;l.reasoning||(l.reasoning=""),l.reasoning+=p}),d.addEventListener("chat",({data:i})=>{const{data:p,index:C}=i;l.content||(l.content=""),l.content+=p}),d.addEventListener("file",({data:i})=>{try{const p=JSON.parse(i.data);l.files=p}catch(p){console.error(p)}}),d.addEventListener("image",({data:i})=>{try{const p=JSON.parse(i.data);l.images=p}catch(p){console.error(p)}}),d.addEventListener("video",({data:i})=>{try{const p=JSON.parse(i.data);l.videos=p}catch(p){console.error(p)}}),d.addEventListener("close",()=>{l.typing=!1,O.value=!1,setTimeout(async()=>{await ae(),M()},1e3)}),d.addEventListener("error",i=>{var p,C,A;R(1),i.errorType==="connectError"&&Y.msgError("请求失败，请重试"),((p=i.data)==null?void 0:p.code)===1200&&(Y.msgError((C=i.data)==null?void 0:C.message),ye(),setTimeout(()=>{N()},10)),["connectError","responseError"].includes(i.errorType)&&(g.value.splice(g.value.length-2,2),t==="input"&&((A=Z.value)==null||A.setInputValue(o))),l.typing=!1,setTimeout(()=>{O.value=!1},200)})},K=z(),se=h(),M=async()=>{var t,u,n;const o=(u=(t=K.value)==null?void 0:t.wrapRef)==null?void 0:u.scrollHeight;(n=K.value)==null||n.setScrollTop(o)},{height:Je}=_t(se);ht(Je,()=>{O.value&&M()},{throttle:500,immediate:!0});const x=h(0),Ze=ue({0:"正在初始化对话...",1:"点击开始说话",2:"我在听，您请说...",3:"稍等，让我想一想",4:"正在回复中..."}),R=o=>{B.value===2&&(x.value=o)},xe=h(),F=h(!0),ne=h(!1),ie=h(0),P=h(!1),be=h(0),L=ue({id:"audio-canvas",width:80,height:40,minHeight:5,scale:2}),{render:Ge,stopRender:Ke,draw:qo}=Ot(L),{start:Qe,stop:re,isRecording:U,authorize:we,close:Bo,isOpen:Fo}=Pt({onstart(){R(2),clearTimeout(xe.value),P.value=!1,ie.value=Date.now()},async onstop(o){if(Ke(),P.value=!1,!ne.value){R(1);return}ne.value=!1,R(3);try{const t=await Mt({file:o.blob});if(!t.text){F.value&&V();return}G(t.text,"voice")}catch{F.value&&V()}},async ondata(o){var u;const t=Date.now();P.value&&Ge(o),o.powerLevel>=10&&(clearTimeout(be.value),x.value=2,P.value=!0,ie.value=t,be.value=setTimeout(()=>{ne.value=!0,clearTimeout(xe.value),Re(),re()},2e3)),t-ie.value>=((u=s.value.digital)==null?void 0:u.idle_time)*1e3&&(P.value||(et(),re()))}}),{play:ke,pause:Re,audioPlaying:Ce}=Vt({onstart(){x.value=4,le.value&&(le.value=!1)},onstop(){R(2),F.value?V():R(1)},onerror(){R(1)}}),Xe=async o=>{ke(async()=>await Ee({type:2,record_id:o}),!1)},V=async()=>{U.value||(await we(),Qe())},Ye=async()=>{if(x.value==4){Re(),V();return}x.value!=3&&(U.value?(F.value=!1,re(),R(1)):(F.value=!0,V()))},Ee=async o=>{try{const{url:t}=await Ut(o,{password:S.value,authorization:k,identity:E.visitorId});return t}catch{return R(1),Promise.reject()}},Q=h(""),le=h(!1),et=async()=>{if(!s.value.robot.is_digital||!s.value.digital.id||(Q.value||(Q.value=await Ee({type:3,record_id:s.value.robot.id})),!Q.value))return Promise.reject();le.value=!0;const o=Date.now();g.value.push({type:2,typing:!1,content:s.value.digital.idle_reply,key:o}),await zt(),M(),ke(Q.value,!1)};Tt(x,o=>{switch(o){case 2:V()}}),z();const ce=z(),{width:Te,height:Se}=Ve(),Le=z(),Ie=z(),ze=async o=>new Promise((t,u)=>{const n=document.createElement("video");n.src=o,n.preload="auto",n.loop=!0,n.muted=!0,n.autoplay=!1,n.playsInline=!0,n.play(),n.addEventListener("loadedmetadata",l=>{n.width=n.videoWidth,n.height=n.videoHeight,t(n)}),n.addEventListener("error",l=>{u(l)}),n.addEventListener("play",l=>{je()})}),je=()=>{if(!ce.value)return;const o=Te.value*2,t=Se.value*2,u=ce.value.getContext("2d");if(!u)return;const n=x.value===4?Ie.value:Le.value;if(!n)return;u.clearRect(0,0,o,t);const{videoHeight:l,videoWidth:b}=n;let i=0,p=0,C=b,A=l;if(b/l>=o/t){const I=o*l/t;i=(b-I)/2,C=I}else{const I=t*b/o;p=(l-I)/2,A=I}u.drawImage(n,i,p,C,A,0,0,o,t),requestAnimationFrame(je)},Oe=async()=>{if(await ae(),B.value==2){Le.value=await ze(s.value.digital.wide_stay_video),Ie.value=await ze(s.value.digital.wide_talk_video),F.value=!0;try{await we(),V()}catch{R(1)}setTimeout(()=>{M()},100)}};St(async()=>{await E.getFingerprint(),await N(),Oe()});const tt=()=>{d==null||d.removeEventListener("reasoning"),d==null||d.removeEventListener("chat"),d==null||d.removeEventListener("close"),d==null||d.removeEventListener("error"),d==null||d.abort()};Lt(()=>{tt()}),yt({title:s.value.name});const Pe=h(!1),ot=h("该模型短时间内使用频次过高，已明显超过正常使用需求，平台将对该模型免费使用进行限制，继续使用将正常扣费，建议您使用其他会员免费模型。此限制将于今日24:00解除，届时您可继续免费使用该模型。"),at=()=>{Pe.value=!1,window.location.reload()};return(o,t)=>{const u=rt,n=ct,l=dt,b=pt,i=gt,p=xt,C=bt,A=ut,I=lt,st=wt,nt=kt;return c(),y("div",null,[a("div",{class:"layout-bg",style:pe({height:`${e(_e)=="Infinity"?"100vh":e(_e)+"px"}`})},[e(B)===1?(c(),y("div",{key:0,class:$e(["h-full",{"p-main":!e(J).isMobile}])},[a("div",Qt,[a("div",Xt,[e(s).robot.image?(c(),y("img",{key:0,src:e(s).robot.image,class:"w-[40px] h-[40px] mr-[10px] flex-none rounded-full",alt:""},null,8,Yt)):_("",!0),a("div",null,[a("div",eo,T(e(s).robot.name),1),a("div",to,T(e(s).robot.intro),1)])]),a("div",oo,[v(st,{class:"h-full",content:e(J).getChatConfig.watermark,font:{color:"rgba(0,0,0,0.06)",fontSize:12}},{default:f(()=>{var m,$;return[a("div",ao,[a("div",so,[a("div",no,[v(e(Fe),{ref_key:"scrollbarRef",ref:K},{default:f(()=>[a("div",io,[a("div",{ref_key:"innerRef",ref:se},[e(s).robot.welcome_introducer?(c(),w(n,{key:0,class:"mb-[20px]",type:"left",avatar:`${e(s).robot.icons?e(s).robot.icons:e(s).robot.image}`,bg:"var(--el-bg-color)"},{default:f(()=>[v(u,{content:e(s).robot.welcome_introducer,onClickCustomLink:t[0]||(t[0]=r=>G(r,"link"))},null,8,["content"])]),_:1},8,["avatar"])):_("",!0),(c(!0),y(fe,null,ve(e(g),(r,D)=>(c(),y("div",{key:r.id+""+D,class:"mt-4"},[r.type==1?(c(),w(n,{key:0,type:"right",bg:"var(--el-color-primary)",color:"white",avatar:e(De)},{actions:f(()=>[a("div",ro,[v(i,{link:"",type:"info",onClick:it=>e(Ne)(r.content)},{icon:f(()=>[v(b,{name:"el-icon-CopyDocument"})]),default:f(()=>[t[4]||(t[4]=H(" 复制 "))]),_:2},1032,["onClick"])])]),default:f(()=>[v(l,{content:r.content,"files-plugin":r.files_plugin},null,8,["content","files-plugin"])]),_:2},1032,["avatar"])):_("",!0),r.type==2?(c(),w(n,{key:1,type:"left",time:r.create_time,avatar:`${e(s).robot.icons?e(s).robot.icons:e(s).robot.image}`,bg:"var(--el-bg-color)"},{outer_actions:f(()=>[r.create_time&&e(s).robot.is_show_feedback?(c(),w(i,{key:0,class:"ml-[52px] mt-2",style:{"--el-button-border-color":"transparent","--el-color-info-light-8":"transparent"},type:r.is_feedback?"info":"primary",plain:!0,bg:"",size:"small",disabled:r.is_feedback,onClick:it=>Me(r,D)},{default:f(()=>[H(T(r.is_feedback?"已反馈":"反馈"),1)]),_:2},1032,["type","disabled","onClick"])):_("",!0)]),default:f(()=>[r.reasoning?(c(),w(C,{key:0,"model-value":"the-chat-msg-collapse",class:"mb-2 the-chat-msg-collapse"},{default:f(()=>[v(p,{title:"深度思考",name:"the-chat-msg-collapse"},{default:f(()=>[v(l,{content:r.reasoning,class:"text-tx-secondary px-3 border-l-[3px] border-br-light"},null,8,["content"])]),_:2},1024)]),_:2},1024)):_("",!0),v(l,{content:String(r.content),type:"html",typing:r.typing,"show-copy":"","show-context":!!e(s).robot.is_show_context,"show-quote":!!e(s).robot.is_show_quote,"show-voice":e(J).getIsVoiceOpen,context:r.context,quotes:r.quotes,images:r.images,files:r.files,videos:r.videos,"record-id":r.id,"record-type":2,channel:e(k),"user-id":e(E).visitorId},null,8,["content","typing","show-context","show-quote","show-voice","context","quotes","images","files","videos","record-id","channel","user-id"])]),_:2},1032,["time","avatar"])):_("",!0)]))),128))],512)])]),_:1},512)]),a("div",lo,[v(I,{ref_key:"chatActionRef",ref:Z,loading:e(O),menus:e(he),"btn-color":"#f6f6f6",onEnter:G,onClear:ge,onPause:t[1]||(t[1]=r=>{var D;return(D=e(d))==null?void 0:D.abort()})},It({_:2},[e(s).robot.support_file?{name:"btn",fn:f(()=>[v(Zt,{class:"mr-3",type:"file","is-parse-content":!0,"is-only-parse-content":!0,onOnSuccess:e(q).addFile},null,8,["onOnSuccess"])]),key:"0"}:void 0,e(q).files.value.length?{name:"file-list",fn:f(()=>[(c(!0),y(fe,null,ve(e(q).files.value,r=>(c(),w(A,{key:r.id,onClose:D=>e(q).removeFile(r)},{default:f(()=>[a("div",co,T(r.name),1)]),_:2},1032,["onClose"]))),128))]),key:"1"}:void 0]),1032,["loading","menus"]),(m=e(s).robot)!=null&&m.copyright?(c(),y("div",uo,T(($=e(s).robot)==null?void 0:$.copyright),1)):_("",!0)])])],512)]}),_:1},8,["content"])])])],2)):_("",!0),e(B)===2?(c(),y("div",po,[a("canvas",{ref_key:"canvasRef",ref:ce,id:"digital-canvas",width:e(Te)*2,height:e(Se)*2},null,8,fo),H(" "+T(e(x))+" ",1),qe(a("div",vo,t[5]||(t[5]=[a("img",{class:"w-[400px]",src:At,alt:""},null,-1)]),512),[[Be,e(x)===0]]),qe(a("div",{class:"h-full",style:pe({background:e(s).robot.digital_bg})},[a("div",mo,[a("div",_o,[a("div",ho,[a("div",yo,[a("div",go,T(e(s).name),1)])]),t[6]||(t[6]=a("div",{class:"flex justify-center"},null,-1))]),a("div",xo,[a("div",bo,[a("div",{class:$e(["recorder gradient-button",{"recorder--stop":!e(U)&&!e(Ce)}]),onClick:Ye},[e(P)?(c(),y("canvas",{key:0,style:pe({width:`${e(L).width}px`,height:`${e(L).height}px`}),width:e(L).width*e(L).scale,height:e(L).height*e(L).scale,id:e(L).id},null,12,wo)):_("",!0),e(U)&&!e(P)?(c(),w(b,{key:1,name:"el-icon-Microphone",size:40})):e(Ce)?(c(),w(b,{key:2,name:"local-icon-pause",size:40})):e(U)?_("",!0):(c(),w(b,{key:3,name:"el-icon-Mute",size:40}))],2),a("div",ko,[a("div",null,T(e(Ze)[e(x)]),1)])]),a("div",Ro,[a("div",Co,[a("div",Eo,[e(g).length?(c(),w(e(Fe),{key:0,ref_key:"scrollbarRef",ref:K},{default:f(()=>[a("div",To,[a("div",{ref_key:"innerRef",ref:se},[(c(!0),y(fe,null,ve(e(g),(m,$)=>(c(),y("div",{key:m.id+""+$,class:"mt-4 sm:pb-[20px]"},[m.type==1?(c(),w(n,{key:0,type:"right",avatar:e(De),color:"white"},{default:f(()=>[v(l,{content:String(m.content)},null,8,["content"])]),_:2},1032,["avatar"])):_("",!0),m.type==2?(c(),w(n,{key:1,type:"left",time:m.create_time,avatar:e(s).robot.icons?e(s).robot.icons:e(s).robot.image,bg:"#fff"},{default:f(()=>[m.reasoning?(c(),w(C,{key:0,"model-value":"the-chat-msg-collapse",class:"mb-2 the-chat-msg-collapse"},{default:f(()=>[v(p,{title:"深度思考",name:"the-chat-msg-collapse"},{default:f(()=>[v(l,{content:m.reasoning,class:"text-tx-secondary px-3 border-l-[3px] border-br-light"},null,8,["content"])]),_:2},1024)]),_:2},1024)):_("",!0),v(l,{content:String(m.content),type:"html",typing:m.typing,images:m.images,files:m.files,videos:m.videos,"record-id":m.id,"record-type":2},null,8,["content","typing","images","files","videos","record-id"])]),_:2},1032,["time","avatar"])):_("",!0)]))),128))],512)])]),_:1},512)):(c(),y("div",So," 暂无聊天记录 "))]),e(O)?(c(),y("div",Lo,[v(i,{color:"#fff",round:"",onClick:t[2]||(t[2]=m=>{var $;return($=e(d))==null?void 0:$.abort()})},{default:f(()=>t[7]||(t[7]=[H(" 停止 ")])),_:1})])):_("",!0)]),a("div",null,[v(I,{ref_key:"chatActionRef",ref:Z,loading:[3,4].includes(e(x)),menus:e(he),"show-pause":!1,"show-clear":!1,onEnter:G},null,8,["loading","menus"])])]),a("div",Io,[a("div",{class:"gradient-button",onClick:ge},[v(b,{name:"local-icon-clear",size:24})])])])])],4),[[Be,e(x)!==0]])])):_("",!0)],4),v($t,{ref_key:"loginRef",ref:ee,onConfirm:We},null,512),v(Wt,{ref_key:"popupRef",ref:te,async:!0,width:"390",title:"问题反馈",appendToBody:!1,class:"feedback-pop"},{footer:f(()=>[a("div",zo,[v(i,{type:"primary",onClick:Ue},{default:f(()=>t[8]||(t[8]=[H(" 提交反馈 ")])),_:1})])]),default:f(()=>[v(nt,{modelValue:e(j).content,"onUpdate:modelValue":t[3]||(t[3]=m=>e(j).content=m),rows:"8",placeholder:"描述一下你遇到了什么问题"},null,8,["modelValue"])]),_:1},512),e(Pe)?(c(),y("div",jo,[a("div",Oo,[t[9]||(t[9]=a("div",{class:"font-bold text-[28rpx] mb-[24rpx] text-red-600 flex items-center justify-center"},[a("text",{class:"mr-[8rpx] text-[32rpx]"},"⚠️"),H(" 超额付费使用提醒 ")],-1)),a("div",Po,T(e(ot)),1),a("button",{class:"bg-blue-500 text-white rounded-[8rpx] px-[32rpx] py-[12rpx] text-[24rpx]",onClick:at},"我知道了")])])):_("",!0)])}}}),qa=Kt(Vo,[["__scopeId","data-v-14b91004"]]);export{qa as default};
