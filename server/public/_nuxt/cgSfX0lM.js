import{_ as r}from"./B6IIPh85.js";import l from"./hfFQGnFk.js";import{_ as i}from"./DmF3G-6j.js";import{useSearch as m}from"./DdZ-Pno0.js";import{l as c,M as p,a1 as _,a0 as e,Z as t,O as u,u as f}from"./Dp9aCaJ6.js";const S=c({__name:"suggestion",props:{lists:{default:()=>[]}},setup(d){const{launchSearch:o,result:x}=m();return(a,s)=>{const n=r;return p(),_(l,null,{title:e(()=>[t(n,{name:"el-icon-Search",size:16}),s[0]||(s[0]=u("span",{class:"text-2xl ml-1"}," 相关问题 ",-1))]),default:e(()=>[t(i,{lists:a.lists,prop:"text",onClickItem:f(o)},null,8,["lists","onClickItem"])]),_:1})}}});export{S as _};
