import{E as c}from"./DH3BuQAR.js";import{E as g}from"./hx-0JZAY.js";import{E as x}from"./LS8qfqy3.js";import{b as y,v as D}from"./ClNUxNV9.js";/* empty css        *//* empty css        */import{r as V,x as E,y as w,f as e,e as v}from"./CTLQW8rw.js";import L from"./C-awW5NS.js";import b from"./DjOw00ea.js";import{_ as k}from"./BCkmLKcS.js";import S from"./DqzeK2F-.js";import A from"./DB6hvLGR.js";import P from"./ymMAYdNY.js";import{D as z}from"./5cU5lAEb.js";import{DrawModeEnum as i}from"./tONJIxwY.js";import{l as U,F as B,u as o,M as p,N as a,Z as r,a0 as l,O as s,aa as M}from"./Dp9aCaJ6.js";import{_ as N}from"./DlAUqK2U.js";import"./CNgDMrD1.js";import"./zRTrVFrw.js";import"./D4cQUBDp.js";import"./67xbGseh.js";import"./BE7GAo-z.js";import"./B6IIPh85.js";import"./Tedtu6ac.js";import"./DCTLXrZ8.js";import"./l0sNRNKZ.js";/* empty css        */import"./Dr3yaPao.js";import"./D6j_GvJh.js";import"./BYYVzU6A.js";import"./9Bti1uB6.js";/* empty css        */import"./Rdahxiqq.js";import"./C0R3uvOr.js";/* empty css        */import"./CWzXXCLi.js";import"./B1qK8f0i.js";import"./B2RmFVzS.js";import"./mW1R_rAi.js";import"./Cga6GjQY.js";import"./Cpg3PDWZ.js";import"./C_z8SI9s.js";import"./CkTU2NpD.js";import"./Cd8UtlNR.js";import"./Cv6HhfEG.js";import"./Cr7puf4F.js";import"./BbPTMigZ.js";import"./DkqMgBWM.js";/* empty css        *//* empty css        */import"./1sgdir_0.js";import"./D0cj6m41.js";import"./DiqTVJuo.js";import"./DjwCd26w.js";import"./qQcR97T8.js";import"./CcPlX2kz.js";import"./C47JPtkS.js";import"./BVqQ2B7o.js";import"./BWdDF8rn.js";import"./1tHUlKgH.js";const q={key:0,class:"h-full flex-1 flex p-4 gap-4 draw_layout"},C={class:"bg-body w-[355px] p-4 flex flex-col gap-4 relative"},F={key:1,class:"h-full flex-1 flex p-4 gap-4 draw_layout justify-center items-center"},I=U({__name:"dalle",setup(O){const n=y();return B(()=>{V({draw_api:i.DALLE3,draw_model:i.DALLE3,action:"generate",prompt:"",negative_prompt:"",size:"1024x1024"}),E.model=i.DALLE3,w()}),(R,t)=>{const d=c,u=g,f=x,_=D;return o(n).config.switch.dalle3_status?(p(),a("div",q,[r(d,{class:"rounded-[12px] pb-[72px] bg-body"},{default:l(()=>[s("div",C,[r(L,{modelValue:o(e).prompt,"onUpdate:modelValue":t[0]||(t[0]=m=>o(e).prompt=m),model:o(i).DALLE3},null,8,["modelValue","model"]),r(S,{modelValue:o(e).size,"onUpdate:modelValue":t[1]||(t[1]=m=>o(e).size=m)},null,8,["modelValue"]),r(A,{modelValue:o(e).style,"onUpdate:modelValue":t[2]||(t[2]=m=>o(e).style=m)},null,8,["modelValue"]),r(P,{modelValue:o(e).quality,"onUpdate:modelValue":t[3]||(t[3]=m=>o(e).quality=m)},null,8,["modelValue"])]),r(k)]),_:1}),M(r(b,{"element-loading-text":"正在加载数据..."},null,512),[[_,o(v)]])])):(p(),a("div",F,[r(f,null,{icon:l(()=>[r(u,{class:"w-[100px] dark:opacity-60",src:o(z)},null,8,["src"])]),title:l(()=>t[4]||(t[4]=[s("div",{class:"text-info"},"绘画功能暂未开启",-1)])),_:1})]))}}}),Ho=N(I,[["__scopeId","data-v-9879be3f"]]);export{Ho as default};
