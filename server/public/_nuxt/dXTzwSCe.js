import{E as J,a as K}from"./C0aqmoaB.js";import{E as Q,a as X}from"./3BjIQFFf.js";import{j as ee,b as oe,cW as C,d3 as L,di as te,dj as S,dl as se,e as ae,o as le,E as ne,p as re,dt as ie}from"./Ct33iMSA.js";import{_ as me}from"./DejgKSRh.js";import{_ as de}from"./DxKRx1wF.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./DP2rzg_V.js";/* empty css        */import{u as pe}from"./CcPlX2kz.js";import{l as ue,m as y,j as R,r as ce,c as I,M as r,N as w,O as m,Z as l,a0 as n,_ as fe,aq as _e,u as s,a1 as c,a4 as f,a6 as _}from"./Dp9aCaJ6.js";import{_ as ge}from"./DlAUqK2U.js";import"./Cv6HhfEG.js";import"./DWZQK6lH.js";import"./iAKPM1CD.js";import"./DCTLXrZ8.js";import"./B4i2sXD1.js";import"./DXT6IpgZ.js";import"./DDmMBU6l.js";import"./t4HvZ20D.js";import"./VtiWddsO.js";import"./CxsMjoDo.js";const ye={class:"flex-1 flex flex-col"},xe={class:"flex flex-1 flex-col py-[20px] px-[30px] min-h-0"},we={class:"flex justify-center leading-5 w-[90px] pl-2.5 border-l border-br"},Ee={class:"flex justify-center"},Ve={key:0,class:"bg-[#f4f4f4] px-[20px] py-[15px] flex dark:bg-[#333]"},ke={class:"flex-1 text-tx-secondary"},be=["href"],ve=["href"],Ce=ue({__name:"index",setup(Le){const F=[{name:"手机号注册",type:"1"},{name:"邮箱注册",type:"2"}],x=y(()=>{var o;return((o=g.getLoginConfig)==null?void 0:o.register_way)||[]}),B=y(()=>F.filter(o=>x.value.includes(o.type))),E=ee(),{login:N,setUser:T,toggleShowLogin:U,setLoginPopupType:V}=E,d=R(),O={mobile:[{required:!0,message:"请输入手机号"}],email:[{required:!0,message:"请输入邮箱账号"},{type:"email",message:"请输入正确的邮箱账号"}],code:[{required:!0,message:"请输入验证码"}],password:[{required:!0,message:"请输入6-20位数字+字母或符号组合"},{min:6,max:20,message:"密码长度应为6-20"}],password_confirm:[{validator(o,e,p){e===""?p(new Error("请再次输入密码")):e!==t.password?p(new Error("两次输入的密码不一致")):p()}}]},t=ce({scene:"",mobile:"",code:"",email:"",password:"",password_confirm:""}),k=R(),j=async()=>{var o;t.scene==="1"?await q():P(),(o=k.value)==null||o.start()},q=async()=>{var o;await((o=d.value)==null?void 0:o.validateField(["mobile"])),await te({scene:S.REGISTER,mobile:t.mobile})},P=async()=>{var o;await((o=d.value)==null?void 0:o.validateField(["email"])),await se({scene:S.REGISTER,email:t.email})},g=oe(),W=y(()=>{var o;return((o=g.getLoginConfig)==null?void 0:o.register_sms_verify)===1}),A=y(()=>g.getLoginConfig.is_agreement===1),G=async()=>{var e;await((e=d.value)==null?void 0:e.validate());const o=await ie(t);!o.mobile&&g.getLoginConfig.coerce_mobile?(E.temToken=o.token,V(C.BIND_MOBILE)):(N(o.token),T(o),U(!1),location.reload())},{lockFn:$,isLock:h}=pe(G);return I(()=>t.scene,()=>{var o;t.password="",t.code="",t.password="",(o=d.value)==null||o.clearValidate()}),I(x,o=>{if(o.includes("1")){t.scene="1";return}const[e]=o;t.scene=e||""},{immediate:!0}),(o,e)=>{const p=K,M=J,D=Q,z=X,u=ae,i=le,Y=me,b=ne,Z=re,v=de;return r(),w("div",ye,[m("div",xe,[l(M,{modelValue:s(t).scene,"onUpdate:modelValue":e[0]||(e[0]=a=>s(t).scene=a)},{default:n(()=>[(r(!0),w(fe,null,_e(s(B),(a,H)=>(r(),c(p,{key:H,label:a.name,name:a.type},null,8,["label","name"]))),128))]),_:1},8,["modelValue"]),s(x).length?(r(),c(Z,{key:0,ref_key:"formRef",ref:d,class:"mt-[10px]",size:"large",model:s(t),rules:O},{default:n(()=>[s(t).scene==="1"?(r(),c(i,{key:0,prop:"mobile"},{default:n(()=>[l(u,{modelValue:s(t).mobile,"onUpdate:modelValue":e[1]||(e[1]=a=>s(t).mobile=a),placeholder:"请输入手机号"},{prepend:n(()=>[l(z,{"model-value":"+86",style:{width:"80px"}},{default:n(()=>[l(D,{label:"+86",value:"+86"})]),_:1})]),_:1},8,["modelValue"])]),_:1})):f("",!0),s(t).scene==="2"?(r(),c(i,{key:1,prop:"email"},{default:n(()=>[l(u,{modelValue:s(t).email,"onUpdate:modelValue":e[2]||(e[2]=a=>s(t).email=a),placeholder:"请输入邮箱账号"},null,8,["modelValue"])]),_:1})):f("",!0),s(W)?(r(),c(i,{key:2,prop:"code"},{default:n(()=>[l(u,{modelValue:s(t).code,"onUpdate:modelValue":e[3]||(e[3]=a=>s(t).code=a),placeholder:"请输入验证码"},{suffix:n(()=>[m("div",we,[l(Y,{ref_key:"verificationCodeRef",ref:k,onClickGet:j},null,512)])]),_:1},8,["modelValue"])]),_:1})):f("",!0),l(i,{prop:"password"},{default:n(()=>[l(u,{modelValue:s(t).password,"onUpdate:modelValue":e[4]||(e[4]=a=>s(t).password=a),type:"password","show-password":"",placeholder:"请输入6-20位数字+字母或符号组合"},null,8,["modelValue"])]),_:1}),l(i,{prop:"password_confirm"},{default:n(()=>[l(u,{modelValue:s(t).password_confirm,"onUpdate:modelValue":e[5]||(e[5]=a=>s(t).password_confirm=a),type:"password","show-password":"",placeholder:"请再次输入密码"},null,8,["modelValue"])]),_:1}),l(i,{class:"mt-[30px]"},{default:n(()=>[l(b,{class:"w-full",type:"primary",loading:s(h),onClick:s($)},{default:n(()=>e[7]||(e[7]=[_(" 注册 ")])),_:1},8,["loading","onClick"])]),_:1})]),_:1},8,["model"])):f("",!0),m("div",Ee,[e[9]||(e[9]=_(" 已有账号？ ")),l(b,{type:"primary",link:"",onClick:e[6]||(e[6]=a=>s(V)(s(C).LOGIN))},{default:n(()=>e[8]||(e[8]=[_(" 立即登录 ")])),_:1})])]),s(A)?(r(),w("div",Ve,[m("div",ke,[e[10]||(e[10]=_(" 您注册即同意 ")),l(v,{to:`/policy/${s(L).SERVICE}`,custom:""},{default:n(({href:a})=>[m("a",{class:"text-tx-primary",href:a,target:"_blank"}," 用户协议 ",8,be)]),_:1},8,["to"]),e[11]||(e[11]=_(" 和 ")),l(v,{class:"text-tx-primary",to:`/policy/${s(L).PRIVACY}`,custom:""},{default:n(({href:a})=>[m("a",{class:"text-tx-primary",href:a,target:"_blank"}," 隐私政策 ",8,ve)]),_:1},8,["to"])])])):f("",!0)])}}}),Ke=ge(Ce,[["__scopeId","data-v-3c8cc549"]]);export{Ke as default};
