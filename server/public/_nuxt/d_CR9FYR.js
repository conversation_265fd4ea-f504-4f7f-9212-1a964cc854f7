import{_ as G}from"./BpYIl71c.js";import{j as L,b as I,ds as D,d as O,E as P}from"./D726nzJl.js";import{E as q,a as A}from"./8HH1nzeK.js";import{_ as H}from"./tBqpKipO.js";/* empty css        *//* empty css        *//* empty css        */import"./l0sNRNKZ.js";/* empty css        *//* empty css        */import{u as J}from"./67xbGseh.js";import{_ as X}from"./tqtvavue.js";import{G as Z}from"./Y89rFoyH.js";import{l as K,b as v,m as V,j as Q,F as W,M as p,N as u,O as t,a7 as n,u as s,Z as a,a0 as i,a6 as S,aJ as Y,_ as ee,aq as te,a4 as w,X as M,y as F,a1 as oe,n as $}from"./Dp9aCaJ6.js";import{_ as se}from"./DlAUqK2U.js";import"./DxoS9eIh.js";import"./GbXQDMM-.js";import"./DCTLXrZ8.js";import"./DzhE9Pcm.js";import"./CmJNjzrM.js";import"./DcsXcc99.js";import"./t5rt8HsH.js";import"./CjE8iZ8I.js";import"./KdfGSRx1.js";import"./B0taQFTv.js";import"./CJW7H0Ln.js";import"./Cv6HhfEG.js";/* empty css        */import"./5pgbG-V6.js";import"./vDgPRvwV.js";import"./EetTZpAI.js";import"./DaBGfXF7.js";import"./D7NF1x92.js";import"./9Bti1uB6.js";/* empty css        */import"./DP2rzg_V.js";/* empty css        *//* empty css        *//* empty css        */import"./C-BkwxIh.js";import"./DlKZEFPo.js";/* empty css        */const ae={class:"p-[20px] flex bg-body rounded-[12px] flex-col h-full"},ne={class:"grid grid-cols-2 md:grid-cols-2 gap-4 bg-page py-[20px] rounded-lg flex-none"},le={class:"flex flex-col items-center justify-center"},ie={class:"font-medium text-[25px] text-[#0256FF]"},re={class:"mt-2"},pe={class:"flex flex-col items-center justify-center"},de={class:"font-medium text-[25px] text-[#0256FF]"},ce={class:"mt-4 flex justify-center"},me={class:"flex mt-4 flex-none"},ue={class:"p-[8px] flex justify-around bg-page rounded-[10px] font-medium"},fe=["onClick"],_e={class:"mt-4 flex-1 min-h-0 flex flex-col"},ve={class:"flex-1 min-h-0"},xe={class:"flex items-center space-x-2"},ge={key:0,class:"px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full"},be={class:"flex justify-end mt-4"},ye=K({__name:"balance",setup(he){const{userInfo:x}=L(),g=I(),f=v({type:1}),d=v(!1),b=V(()=>"数量"),_=v(!1),y=Q(),j=V(()=>[{name:`${g.getTokenUnit}明细`,type:1},{name:"智能体明细",type:2}]),h=l=>l&&l.includes("智能体分成收益"),E=async l=>{f.value.type=l,await $(),m()},{pager:c,getLists:m}=J({fetchFun:D,params:f.value}),z=async l=>{_.value=!0,await $(),y.value.open(l)},B=()=>{d.value=!0},N=()=>{m(),O.success("赠送成功！")};return W(()=>{m()}),(l,o)=>{const R=G,k=P,r=q,T=A,U=H;return p(),u("div",ae,[t("div",ne,[t("div",le,[t("div",ie,n(Math.floor(s(x).balance)),1),t("div",re,n(s(g).getTokenUnit)+"数量",1)]),t("div",pe,[t("div",de,n(s(x).robot_num),1),o[3]||(o[3]=t("div",{class:"mt-2"},"智能体",-1))])]),t("div",ce,[a(k,{type:"primary",size:"large",onClick:B},{default:i(()=>[a(R,{name:"local-icon-gift",size:"16px",class:"mr-2"}),o[4]||(o[4]=S(" 赠送他人灵感值 "))]),_:1})]),o[6]||(o[6]=Y('<div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg flex-none" data-v-962ec9e9><div class="flex items-start space-x-2" data-v-962ec9e9><div class="text-blue-500 mt-0.5" data-v-962ec9e9><svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" data-v-962ec9e9><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" data-v-962ec9e9></path></svg></div><div class="text-sm text-blue-700" data-v-962ec9e9><div class="font-medium" data-v-962ec9e9>智能体分成收益说明</div><div class="mt-1" data-v-962ec9e9> • 通过使用智能体广场的智能体可获得分成收益奖励<br data-v-962ec9e9> • 分成收益将在每天凌晨自动结算并发放到您的账户<br data-v-962ec9e9> • 具体分成比例和规则请查看智能体详情页面 </div></div></div></div>',1)),t("div",me,[t("div",ue,[(p(!0),u(ee,null,te(s(j),(e,C)=>(p(),u("div",{class:M([{isSelect:s(f).type==e.type},"px-[15px] md:px-[30px] py-[10px] cursor-pointer"]),key:C,onClick:ke=>E(e.type)},[t("span",null,n(e.name),1)],10,fe))),128))])]),t("div",_e,[t("div",ve,[a(T,{data:s(c).lists,height:"100%"},{default:i(()=>[a(r,{label:"订单编号",prop:"sn","min-width":"150"}),a(r,{label:"变动类型",prop:"change_type","min-width":"150"},{default:i(({row:e})=>[t("div",xe,[t("span",null,n(e.change_type),1),h(e.change_type)?(p(),u("span",ge," 分成收益 ")):w("",!0)])]),_:1}),a(r,{label:"智能体/应用名",prop:"robot_name","min-width":"150"},{default:i(({row:e})=>[t("div",null,n(e.robot_name||"-"),1)]),_:1}),a(r,{label:"操作时间",prop:"create_time","min-width":"150"}),a(r,{label:`变动${s(b)}`,prop:"change_amount","min-width":"100"},{default:i(({row:e})=>[t("div",{class:M({"text-danger":e.action==2,"text-green-600":h(e.change_type)&&e.action==1})},[t("span",null,n(e.action==1?"+":"-"),1),t("span",null,n(Math.floor(parseFloat(e.change_amount))),1)],2)]),_:1},8,["label"]),a(r,{label:"操作","min-width":"80"},{default:i(({row:e})=>[a(k,{onClick:C=>z(e.id),link:"",type:"primary"},{default:i(()=>o[5]||(o[5]=[S(" 详情 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),t("div",be,[a(U,{modelValue:s(c),"onUpdate:modelValue":o[0]||(o[0]=e=>F(c)?c.value=e:null),onChange:s(m),hideOnSinglePage:!1,background:!0},null,8,["modelValue","onChange"])])]),s(_)?(p(),oe(X,{key:0,type:s(b),ref_key:"popRef",ref:y,onClose:o[1]||(o[1]=e=>_.value=!1)},null,8,["type"])):w("",!0),a(Z,{modelValue:s(d),"onUpdate:modelValue":o[2]||(o[2]=e=>F(d)?d.value=e:null),onSuccess:N},null,8,["modelValue"])])}}}),ct=se(ye,[["__scopeId","data-v-962ec9e9"]]);export{ct as default};
