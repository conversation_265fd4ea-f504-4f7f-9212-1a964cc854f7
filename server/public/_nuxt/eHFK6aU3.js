import{a as f}from"./DGzblORL.js";import{_ as u}from"./qAS0yuWM.js";import{_ as x}from"./DcXpQgxG.js";import{l as y,b as e,c as h,F as v,M as t,N as n,O as p,_ as b,aq as g,u as o,a3 as k,a6 as C,a7 as B,n as q,X as w,a5 as N}from"./uahP8ofS.js";import{_ as T}from"./DlAUqK2U.js";import"./DY7E2jlx.js";import"./BFLg3Y4S.js";import"./Dt1L8u-1.js";import"./DCTLXrZ8.js";import"./CSRQmB5h.js";import"./SdKmR7yR.js";import"./D1hZ5aQ6.js";import"./C4BggPsu.js";import"./CY5Ghzht.js";import"./B9pCflRa.js";import"./v4tRJs8W.js";import"./0Hk8YYNq.js";import"./Cg6fN0Zt.js";import"./HrsfEhzV.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./l0sNRNKZ.js";import"./5SHXd8at.js";import"./CZC_C7nT.js";import"./DXdf2lbU.js";const $={class:"p-[20px] bg-body rounded-[12px] flex flex-col h-full"},D={class:"flex flex-none"},F={class:"p-[8px] flex bg-page rounded-[10px] font-medium"},L=["onClick"],M=y({__name:"index",setup(S){const m=f(),i=e("member"),c=e(u),r=e(!0),_=e([{name:"会员开通记录",type:"member"},{name:"充值记录",type:"recharge"}]);h(()=>m.query.time,async()=>{r.value=!1,await q(),r.value=!0});const l=a=>{i.value=a,c.value=a==="recharge"?u:x};return v(()=>{l(m.query.type||"member")}),(a,V)=>(t(),n("div",$,[p("div",D,[p("div",F,[(t(!0),n(b,null,g(o(_),(s,d)=>(t(),n("div",{class:w([{selectType:o(i)===s.type},"px-[30px] py-[10px] cursor-pointer"]),key:d,onClick:z=>l(s.type)},[p("span",null,N(s.name),1)],10,L))),128))])]),o(r)?(t(),k(C(o(c)),{key:0})):B("",!0)]))}}),ue=T(M,[["__scopeId","data-v-e66f7bae"]]);export{ue as default};
