import{_ as de}from"./DvrbA4QQ.js";import{b as me,E as re,o as ue,e as _e,p as pe,v as ce}from"./Ct33iMSA.js";import{E as fe,a as ge}from"./PnfXFZ_1.js";import{E as ve}from"./CawRhHrf.js";import{E as ye,a as be}from"./3BjIQFFf.js";import{a as xe,E as ke}from"./C0aqmoaB.js";import{E as he,a as Te}from"./Cz-ZBZVo.js";import{E as Ve}from"./DXT6IpgZ.js";import{E as Ee}from"./cZpipWaD.js";import{_ as Ce}from"./HaYB0y9G.js";import{E as Re}from"./SbrOwfyf.js";/* empty css        *//* empty css        */import"./DP2rzg_V.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./l0sNRNKZ.js";/* empty css        */import{u as Ue}from"./67xbGseh.js";import{g as Se,G as Ge,a as Me,b as we}from"./BaApeYZB.js";import{l as Ae,b as p,F as De,M as v,N as x,O as t,Z as o,a0 as n,a6 as c,a7 as a,u as e,a4 as B,y,aa as je,a1 as Ye,X as N}from"./Dp9aCaJ6.js";import{_ as Fe}from"./DlAUqK2U.js";import"./DOBoXv-W.js";import"./iAKPM1CD.js";import"./DCTLXrZ8.js";import"./B4i2sXD1.js";import"./DlKZEFPo.js";import"./t4HvZ20D.js";import"./CxsMjoDo.js";import"./DDmMBU6l.js";import"./Cv6HhfEG.js";import"./VtiWddsO.js";import"./DWZQK6lH.js";import"./BMo7Szn8.js";import"./BZXCTEAI.js";import"./9Bti1uB6.js";import"./BDl3LJB7.js";const Ie={class:"p-[20px] flex bg-body rounded-[12px] flex-col h-full"},Be={class:"flex justify-between items-center mb-4"},Ne={class:"mb-4 grid grid-cols-1 md:grid-cols-4 gap-4"},ze={class:"bg-page p-4 rounded-lg"},Pe={class:"text-2xl font-bold text-red-500"},$e={class:"bg-page p-4 rounded-lg"},Le={class:"text-2xl font-bold text-green-500"},Oe={class:"bg-page p-4 rounded-lg"},qe={class:"text-2xl font-bold text-red-500"},Xe={class:"bg-page p-4 rounded-lg"},Ze={class:"text-2xl font-bold text-green-500"},He={class:"bg-page p-4 rounded-lg mb-4"},Je={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Ke={key:0,class:"text-center py-2"},Qe={key:1,class:"space-y-2 text-sm text-gray-600"},We={key:0},et={class:"space-y-2 text-sm text-gray-600"},tt={class:"bg-page p-4 rounded-lg mb-4"},lt={class:"flex-1 min-h-0 flex flex-col"},st={class:"flex-1 min-h-0"},at={class:"flex items-center"},ot={class:"ml-3"},nt={class:"font-medium"},it={class:"text-sm text-gray-500"},dt={class:"flex justify-end mt-4"},mt={key:0,class:"space-y-4"},rt={class:"flex justify-between"},ut={class:"flex justify-between"},_t={class:"flex justify-between"},pt={class:"flex items-center"},ct={class:"ml-2"},ft={class:"flex justify-between"},gt={class:"flex justify-between"},vt={class:"flex justify-between"},yt=Ae({__name:"gift-records",setup(bt){const r=me(),z=p(!1),k=p("all"),S=p([]),h=p(!1),T=p(!1),i=p(null),V=p(null),u=p({type:"all",keyword:"",status:"",start_date:"",end_date:""}),m=p({monthSend:0,monthReceive:0,totalSend:0,totalReceive:0,dailyGiftAmount:0,dailyReceiveAmount:0,dailyGiftTimes:0,dailyReceiveTimes:0}),_=p(null),{pager:E,getLists:g}=Ue({fetchFun:Se,params:u.value}),P=d=>{u.value.type=d.name,g()},$=d=>{d?(u.value.start_date=d[0],u.value.end_date=d[1]):(u.value.start_date="",u.value.end_date="")},L=()=>{g()},O=()=>{u.value={type:k.value,keyword:"",status:"",start_date:"",end_date:""},V.value=null,g()},q=()=>{h.value=!0},X=()=>{g(),A()},M=d=>{i.value=d,T.value=!0},w=d=>({1:"成功",2:"失败",3:"已撤回"})[d]||"未知",A=async()=>{try{const{data:d}=await Me();m.value=d||{monthSend:0,monthReceive:0,totalSend:0,totalReceive:0,dailyGiftAmount:0,dailyReceiveAmount:0,dailyGiftTimes:0,dailyReceiveTimes:0}}catch(d){console.error("加载统计数据失败:",d),m.value={monthSend:0,monthReceive:0,totalSend:0,totalReceive:0,dailyGiftAmount:0,dailyReceiveAmount:0,dailyGiftTimes:0,dailyReceiveTimes:0}}},Z=async()=>{try{const{data:d}=await we();_.value=d}catch(d){console.error("加载配置失败:",d),_.value={is_enable:1,min_gift_amount:1,max_gift_amount:1e3,daily_gift_limit:100,daily_receive_limit:500,gift_times_limit:10,receive_times_limit:20,friend_only:0,need_verify:0}}};return De(()=>{g(),A(),Z()}),(d,l)=>{var j,Y,F,I;const H=de,b=re,J=fe,K=ge,Q=ve,C=ue,W=_e,R=ye,ee=be,te=pe,G=xe,le=ke,f=he,U=Ve,D=Ee,se=Te,ae=Ce,oe=Re,ne=ce;return v(),x("div",Ie,[t("div",Be,[l[9]||(l[9]=t("div",{class:"title font-medium text-xl"},"灵感赠送",-1)),o(b,{type:"primary",onClick:q},{default:n(()=>[o(H,{name:"local-icon-gift",size:"16px",class:"mr-2"}),l[8]||(l[8]=c(" 赠送他人灵感值 "))]),_:1})]),t("div",Ne,[t("div",ze,[l[10]||(l[10]=t("div",{class:"text-sm text-gray-500 mb-1"},"本月赠送",-1)),t("div",Pe,a(((j=e(m))==null?void 0:j.monthSend)||0)+" "+a(e(r).getTokenUnit),1)]),t("div",$e,[l[11]||(l[11]=t("div",{class:"text-sm text-gray-500 mb-1"},"本月接收",-1)),t("div",Le,a(((Y=e(m))==null?void 0:Y.monthReceive)||0)+" "+a(e(r).getTokenUnit),1)]),t("div",Oe,[l[12]||(l[12]=t("div",{class:"text-sm text-gray-500 mb-1"},"累计赠送",-1)),t("div",qe,a(((F=e(m))==null?void 0:F.totalSend)||0)+" "+a(e(r).getTokenUnit),1)]),t("div",Xe,[l[13]||(l[13]=t("div",{class:"text-sm text-gray-500 mb-1"},"累计接收",-1)),t("div",Ze,a(((I=e(m))==null?void 0:I.totalReceive)||0)+" "+a(e(r).getTokenUnit),1)])]),t("div",He,[o(K,{modelValue:e(S),"onUpdate:modelValue":l[0]||(l[0]=s=>y(S)?S.value=s:null)},{default:n(()=>[o(J,{title:"赠送规则说明",name:"rules"},{default:n(()=>[t("div",Je,[t("div",null,[l[15]||(l[15]=t("h4",{class:"font-medium text-gray-800 mb-3"},"基础规则",-1)),e(_)?(v(),x("div",Qe,[t("div",null,"• 单次赠送范围："+a(e(_).min_gift_amount)+"-"+a(e(_).max_gift_amount)+" "+a(e(r).getTokenUnit),1),t("div",null,"• 每日赠送限额："+a(e(_).daily_gift_limit)+" "+a(e(r).getTokenUnit),1),t("div",null,"• 每日接收限额："+a(e(_).daily_receive_limit)+" "+a(e(r).getTokenUnit),1),t("div",null,"• 每日赠送次数："+a(e(_).gift_times_limit)+" 次",1),t("div",null,"• 每日接收次数："+a(e(_).receive_times_limit)+" 次",1)])):(v(),x("div",Ke,l[14]||(l[14]=[t("i",{class:"el-icon-loading"},null,-1),c(" 加载配置中... ")])))]),e(m)&&e(_)?(v(),x("div",We,[l[16]||(l[16]=t("h4",{class:"font-medium text-gray-800 mb-3"},"今日使用情况",-1)),t("div",et,[t("div",null,"• 今日已赠送："+a(e(m).dailyGiftAmount||0)+" "+a(e(r).getTokenUnit)+"（"+a(e(m).dailyGiftTimes||0)+" 次）",1),t("div",null,"• 今日剩余额度："+a(Math.max(0,e(_).daily_gift_limit-(e(m).dailyGiftAmount||0)))+" "+a(e(r).getTokenUnit),1),t("div",null,"• 今日剩余次数："+a(Math.max(0,e(_).gift_times_limit-(e(m).dailyGiftTimes||0)))+" 次",1),t("div",null,"• 今日已接收："+a(e(m).dailyReceiveAmount||0)+" "+a(e(r).getTokenUnit)+"（"+a(e(m).dailyReceiveTimes||0)+" 次）",1)])])):B("",!0)])]),_:1})]),_:1},8,["modelValue"])]),t("div",tt,[o(te,{model:e(u),inline:""},{default:n(()=>[o(C,{label:"时间范围"},{default:n(()=>[o(Q,{modelValue:e(V),"onUpdate:modelValue":l[1]||(l[1]=s=>y(V)?V.value=s:null),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",onChange:$},null,8,["modelValue"])]),_:1}),o(C,{label:"用户搜索"},{default:n(()=>[o(W,{modelValue:e(u).keyword,"onUpdate:modelValue":l[2]||(l[2]=s=>e(u).keyword=s),placeholder:"搜索对方用户昵称",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),o(C,{label:"状态"},{default:n(()=>[o(ee,{modelValue:e(u).status,"onUpdate:modelValue":l[3]||(l[3]=s=>e(u).status=s),placeholder:"全部状态",clearable:""},{default:n(()=>[o(R,{label:"全部",value:""}),o(R,{label:"成功",value:1}),o(R,{label:"失败",value:2}),o(R,{label:"已撤回",value:3})]),_:1},8,["modelValue"])]),_:1}),o(C,null,{default:n(()=>[o(b,{type:"primary",onClick:L},{default:n(()=>l[17]||(l[17]=[c("搜索")])),_:1}),o(b,{onClick:O},{default:n(()=>l[18]||(l[18]=[c("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),o(le,{modelValue:e(k),"onUpdate:modelValue":l[4]||(l[4]=s=>y(k)?k.value=s:null),onTabClick:P,class:"mb-4"},{default:n(()=>[o(G,{label:"全部记录",name:"all"}),o(G,{label:"我的赠送",name:"send"}),o(G,{label:"我的接收",name:"receive"})]),_:1},8,["modelValue"]),t("div",lt,[t("div",st,[je((v(),Ye(se,{data:e(E).lists,height:"100%"},{default:n(()=>[o(f,{label:"流水号",prop:"gift_sn","min-width":"180"},{default:n(({row:s})=>[o(b,{type:"primary",link:"",onClick:ie=>M(s)},{default:n(()=>[c(a(s.gift_sn),1)]),_:2},1032,["onClick"])]),_:1}),o(f,{label:"类型",width:"80",align:"center"},{default:n(({row:s})=>[o(U,{type:s.type==="send"?"danger":"success"},{default:n(()=>[c(a(s.type==="send"?"赠送":"接收"),1)]),_:2},1032,["type"])]),_:1}),o(f,{label:"对方用户","min-width":"200"},{default:n(({row:s})=>[t("div",at,[o(D,{size:40,src:s.type==="send"?s.to_user_avatar:s.from_user_avatar},null,8,["src"]),t("div",ot,[t("div",nt,a(s.type==="send"?s.to_user_nickname:s.from_user_nickname),1),t("div",it," ID: "+a(s.type==="send"?s.to_user_sn||s.to_user_id:s.from_user_sn||s.from_user_id),1)])])]),_:1}),o(f,{label:"灵感值数量",prop:"gift_amount",width:"140",align:"right"},{default:n(({row:s})=>[t("div",{class:N(["text-lg font-medium",s.type==="send"?"text-red-500":"text-green-500"])},a(s.type==="send"?"-":"+")+a(Math.floor(parseFloat(s.gift_amount)))+" "+a(e(r).getTokenUnit),3)]),_:1}),o(f,{label:"状态",width:"100",align:"center"},{default:n(({row:s})=>[o(U,{type:s.status===1?"success":s.status===2?"danger":"warning"},{default:n(()=>[c(a(w(s.status)),1)]),_:2},1032,["type"])]),_:1}),o(f,{label:"时间",prop:"create_time",width:"180"}),o(f,{label:"操作",width:"80",align:"center"},{default:n(({row:s})=>[o(b,{type:"primary",link:"",onClick:ie=>M(s)},{default:n(()=>l[19]||(l[19]=[c(" 详情 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ne,e(z)]])]),t("div",dt,[o(ae,{modelValue:e(E),"onUpdate:modelValue":l[5]||(l[5]=s=>y(E)?E.value=s:null),onChange:e(g)},null,8,["modelValue","onChange"])])]),o(Ge,{modelValue:e(h),"onUpdate:modelValue":l[6]||(l[6]=s=>y(h)?h.value=s:null),onSuccess:X},null,8,["modelValue"]),o(oe,{modelValue:e(T),"onUpdate:modelValue":l[7]||(l[7]=s=>y(T)?T.value=s:null),title:"赠送详情",width:"500px"},{default:n(()=>[e(i)?(v(),x("div",mt,[t("div",rt,[l[20]||(l[20]=t("span",{class:"font-medium"},"流水号：",-1)),t("span",null,a(e(i).gift_sn),1)]),t("div",ut,[l[21]||(l[21]=t("span",{class:"font-medium"},"类型：",-1)),o(U,{type:e(i).type==="send"?"danger":"success"},{default:n(()=>[c(a(e(i).type==="send"?"赠送":"接收"),1)]),_:1},8,["type"])]),t("div",_t,[l[22]||(l[22]=t("span",{class:"font-medium"},"对方用户：",-1)),t("div",pt,[o(D,{size:32,src:e(i).type==="send"?e(i).to_user_avatar:e(i).from_user_avatar},null,8,["src"]),t("span",ct,a(e(i).type==="send"?e(i).to_user_nickname:e(i).from_user_nickname),1)])]),t("div",ft,[l[23]||(l[23]=t("span",{class:"font-medium"},"灵感值数量：",-1)),t("span",{class:N(["text-lg font-medium",e(i).type==="send"?"text-red-500":"text-green-500"])},a(e(i).type==="send"?"-":"+")+a(Math.floor(parseFloat(e(i).gift_amount)))+" "+a(e(r).getTokenUnit),3)]),t("div",gt,[l[24]||(l[24]=t("span",{class:"font-medium"},"状态：",-1)),o(U,{type:e(i).status===1?"success":e(i).status===2?"danger":"warning"},{default:n(()=>[c(a(w(e(i).status)),1)]),_:1},8,["type"])]),t("div",vt,[l[25]||(l[25]=t("span",{class:"font-medium"},"时间：",-1)),t("span",null,a(e(i).create_time),1)])])):B("",!0)]),_:1},8,["modelValue"])])}}}),il=Fe(yt,[["__scopeId","data-v-414d998b"]]);export{il as default};
