function t(e){return $request.get({url:"/kb.know/lists",params:e})}function r(e){return $request.get({url:"/kb.know/all",params:e})}function n(e){return $request.get({url:"/kb.know/detail",params:e})}function u(e){return $request.post({url:"/kb.know/add",params:e})}function s(e){return $request.post({url:"/kb.know/edit",params:e})}function a(e){return $request.get({url:"/kb.know/permissionStatus",params:e})}function l(e){return $request.post({url:"/kb.know/del",params:e})}function o(e){return $request.get({url:"/kb.know/files",params:e})}function i(e){return $request.post({url:"/kb.teach/import",params:e})}function k(e){return $request.get({url:"/kb.teach/datas",params:e})}function c(e){return $request.post({url:"/kb.know/fileRename",params:e})}function f(e){return $request.post({url:"/kb.know/fileRemove",params:e})}function p(e){return $request.post({url:"/kb.teach/qaRetry",params:e})}function w(e){return $request.post({url:"/kb.teach/tests",params:e})}function b(e){return $request.post({url:"/kb.teach/insert",params:e})}function d(e){return $request.post({url:"/kb.teach/update",params:e})}function q(e){return $request.post({url:"/kb.teach/reset",params:e})}function m(e){return $request.post({url:"/kb.teach/delete",params:e})}function $(e){return $request.get({url:"/kb.teach/detail",params:e})}function g(e){return $request.post({url:"/kb.teach/capture",params:e})}function D(e){return $request.post({url:"/kb.teach/detection",params:e})}function h(){return $request.get({url:"/kb.example/getAllExamples"})}function A(){return $request.get({url:"/kb.template/getAllTemplates"})}function K(e){return $request.get({url:"/kb.template/download",params:e})}export{u as a,n as b,l as c,f as d,p as e,o as f,i as g,q as h,k as i,m as j,s as k,D as l,c as m,h as n,d as o,b as p,$ as q,K as r,A as s,w as t,t as u,r as v,g as w,a as x};
