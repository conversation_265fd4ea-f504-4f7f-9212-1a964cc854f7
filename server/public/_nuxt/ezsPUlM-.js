import{g as B}from"./C9xud4Fy.js";var I={exports:{}};(function(P){(function(){function A(e,n){document.addEventListener?e.addEventListener("scroll",n,!1):e.attachEvent("scroll",n)}function M(e){document.body?e():document.addEventListener?document.addEventListener("DOMContentLoaded",function n(){document.removeEventListener("DOMContentLoaded",n),e()}):document.attachEvent("onreadystatechange",function n(){(document.readyState=="interactive"||document.readyState=="complete")&&(document.detachEvent("onreadystatechange",n),e())})}function b(e){this.g=document.createElement("div"),this.g.setAttribute("aria-hidden","true"),this.g.appendChild(document.createTextNode(e)),this.h=document.createElement("span"),this.i=document.createElement("span"),this.m=document.createElement("span"),this.j=document.createElement("span"),this.l=-1,this.h.style.cssText="max-width:none;display:inline-block;position:absolute;height:100%;width:100%;overflow:scroll;font-size:16px;",this.i.style.cssText="max-width:none;display:inline-block;position:absolute;height:100%;width:100%;overflow:scroll;font-size:16px;",this.j.style.cssText="max-width:none;display:inline-block;position:absolute;height:100%;width:100%;overflow:scroll;font-size:16px;",this.m.style.cssText="display:inline-block;width:200%;height:200%;font-size:16px;max-width:none;",this.h.appendChild(this.m),this.i.appendChild(this.j),this.g.appendChild(this.h),this.g.appendChild(this.i)}function g(e,n){e.g.style.cssText="max-width:none;min-width:20px;min-height:20px;display:inline-block;overflow:hidden;position:absolute;width:auto;margin:0;padding:0;top:-999px;white-space:nowrap;font-synthesis:none;font:"+n+";"}function D(e){var n=e.g.offsetWidth,t=n+100;return e.j.style.width=t+"px",e.i.scrollLeft=t,e.h.scrollLeft=e.h.scrollWidth+100,e.l!==n?(e.l=n,!0):!1}function C(e,n){function t(){var o=c;D(o)&&o.g.parentNode!==null&&n(o.l)}var c=e;A(e.h,t),A(e.i,t),D(e)}function N(e,n,t){n=n||{},t=t||window,this.family=e,this.style=n.style||"normal",this.weight=n.weight||"normal",this.stretch=n.stretch||"normal",this.context=t}var T=null,w=null,W=null,L=null;function O(e){return w===null&&(j(e)&&/Apple/.test(window.navigator.vendor)?(e=/AppleWebKit\/([0-9]+)(?:\.([0-9]+))(?:\.([0-9]+))/.exec(window.navigator.userAgent),w=!!e&&603>parseInt(e[1],10)):w=!1),w}function j(e){return L===null&&(L=!!e.document.fonts),L}function u(e,n){var t=e.style,c=e.weight;if(W===null){var o=document.createElement("div");try{o.style.font="condensed 100px sans-serif"}catch{}W=o.style.font!==""}return[t,c,W?e.stretch:"","100px",n].join(" ")}N.prototype.load=function(e,n){var t=this,c=e||"BESbswy",o=0,f=n||3e3,k=new Date().getTime();return new Promise(function(z,F){if(j(t.context)&&!O(t.context)){var S=new Promise(function(m,p){function r(){new Date().getTime()-k>=f?p(Error(""+f+"ms timeout exceeded")):t.context.document.fonts.load(u(t,'"'+t.family+'"'),c).then(function(h){1<=h.length?m():setTimeout(r,25)},p)}r()}),q=new Promise(function(m,p){o=setTimeout(function(){p(Error(""+f+"ms timeout exceeded"))},f)});Promise.race([q,S]).then(function(){clearTimeout(o),z(t)},F)}else M(function(){function m(){var i;(i=d!=-1&&l!=-1||d!=-1&&a!=-1||l!=-1&&a!=-1)&&((i=d!=l&&d!=a&&l!=a)||(T===null&&(i=/AppleWebKit\/([0-9]+)(?:\.([0-9]+))/.exec(window.navigator.userAgent),T=!!i&&(536>parseInt(i[1],10)||parseInt(i[1],10)===536&&11>=parseInt(i[2],10))),i=T&&(d==x&&l==x&&a==x||d==y&&l==y&&a==y||d==E&&l==E&&a==E)),i=!i),i&&(s.parentNode!==null&&s.parentNode.removeChild(s),clearTimeout(o),z(t))}function p(){if(new Date().getTime()-k>=f)s.parentNode!==null&&s.parentNode.removeChild(s),F(Error(""+f+"ms timeout exceeded"));else{var i=t.context.document.hidden;(i===!0||i===void 0)&&(d=r.g.offsetWidth,l=h.g.offsetWidth,a=v.g.offsetWidth,m()),o=setTimeout(p,50)}}var r=new b(c),h=new b(c),v=new b(c),d=-1,l=-1,a=-1,x=-1,y=-1,E=-1,s=document.createElement("div");s.dir="ltr",g(r,u(t,"sans-serif")),g(h,u(t,"serif")),g(v,u(t,"monospace")),s.appendChild(r.g),s.appendChild(h.g),s.appendChild(v.g),t.context.document.body.appendChild(s),x=r.g.offsetWidth,y=h.g.offsetWidth,E=v.g.offsetWidth,p(),C(r,function(i){d=i,m()}),g(r,u(t,'"'+t.family+'",sans-serif')),C(h,function(i){l=i,m()}),g(h,u(t,'"'+t.family+'",serif')),C(v,function(i){a=i,m()}),g(v,u(t,'"'+t.family+'",monospace'))})})},P.exports=N})()})(I);var _=I.exports;const H=B(_);export{H as F};
