import{E as H,a as J}from"./BkngO3As.js";import{_ as K}from"./D5KDMvDa.js";import{E as W}from"./Dg2XwvBU.js";import{E as Y,a as ee}from"./BFIriSjw.js";import{h as j,b as te,bV as se}from"./C3HqF-ve.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import{P as le}from"./BbazWion.js";import{u as oe}from"./DMI4YHhg.js";import{l as ie,j as de,b as ae,r as ne,m as P,c as S,M as l,N as i,a1 as V,a0 as _,_ as C,aq as M,u as a,O as r,a7 as m,y as T,a4 as g,X as B,Z as h,a6 as re}from"./Dp9aCaJ6.js";import{_ as ce}from"./DlAUqK2U.js";const pe={class:"flex items-center"},ue={class:"my-1 flex items-center justify-between"},me={class:"flex items-center flex-1"},fe={class:"leading-6 mr-2"},xe={key:0,class:"text-[#23B571] font-medium bg-[#E3FFF2] px-[8px] py-[4px] rounded-[6px] text-xs leading-[20px]"},_e={key:1,class:"text-gray-500 text-xs leading-5"},ye={class:"line-clamp-1 flex-1 flex items-center"},ge={key:0,class:"mr-2"},ve={key:1,class:"text-[#a8abb2]"},be={key:2,class:"text-[#23B571] font-medium bg-[#E3FFF2] px-[8px] py-[2px] rounded-[4px] text-xs leading-[16px]"},he={key:3,class:"text-gray-500 text-xs"},ke={class:"flex-none ml-2 flex items-center"},Le={class:"model-container"},Ce={class:"flex items-center h-[46px] py-2"},Me=["src"],we={class:"mx-2 leading-[24px] mt-[2px] font-medium"},Fe={key:1,class:"bg-[#E3FFF2] text-[#23B571] font-medium px-[8px] py-[4px] leading-[20px] rounded-[6px] text-xs"},Ee=["onClick"],$e={class:"flex items-center"},Pe={class:"mr-2 leading-6"},Se={key:0,class:"text-[#23B571] font-medium bg-[#E3FFF2] px-[8px] py-[2px] rounded-[4px] text-xs leading-[16px]"},Ve={key:1,class:"text-gray-500 text-xs leading-5"},Be={key:0,class:"flex items-center"},Ae={key:1,class:"flex items-center"},Ne={key:0,class:"mt-3 p-3 bg-gradient-to-r from-blue-50 to-blue-100 border border-blue-300 rounded-lg text-blue-700",style:{"box-shadow":"0 2px 8px rgba(59, 130, 246, 0.1)"}},De=ie({__name:"index",props:{id:{type:[String,Number],default:""},sub_id:{type:[String,Number],default:""},setDefault:{type:Boolean,default:!0},type:{type:String,default:"chatModels"},disabled:{type:Boolean,default:!1}},emits:["update:id","update:sub_id","update:modelConfig","update:config"],setup(f,{emit:U}){const k=U,o=f,p=j(o,"id",k),x=j(o,"sub_id",k),w=te(),F=de(),b=ae(-1),s=ne({modelList:[]}),R=P(()=>s.modelList.filter((e,t)=>t%2===0)),q=P(()=>s.modelList.filter((e,t)=>t%2!==0)),y=P(()=>!s.modelList||!Array.isArray(s.modelList)?{}:o.type==="chatModels"?s.modelList.flatMap(e=>e.models||[]).find(e=>e.id===x.value)||{}:s.modelList.find(e=>e.id===p.value)||{});S(()=>y.value,e=>{var d,c;k("update:modelConfig",e);const t=((c=(d=s.modelList.find(L=>L.id===p.value))==null?void 0:d.configs)==null?void 0:c[0])||{};k("update:configs",t)});const{suspense:z}=oe(["modelLists"],{queryFn:se,cacheTime:1e3}),I=async()=>{try{const{data:e}=await z();s.modelList=e[o.type]||[],console.log(`PC端获取${o.type}模型数据:`,s.modelList),E(),o.setDefault&&s.modelList.length>0&&O()}catch(e){console.log("PC端获取模型数据错误=>",e),s.modelList=[]}},E=()=>{if(!(!s.modelList||s.modelList.length===0))if(console.log(`PC端initSavedValues - type: ${o.type}, id: ${o.id}, sub_id: ${o.sub_id}`),console.log("PC端modelList:",s.modelList),o.type==="chatModels"){if(o.sub_id){for(const e of s.modelList)if(e.models&&e.models.some(t=>t.id===o.sub_id)){p.value=e.id,x.value=o.sub_id;const t=s.modelList.findIndex(d=>d.id===e.id);t!==-1&&(b.value=t),console.log(`PC端对话模型初始化: model=${e.id}, subModel=${o.sub_id}`);break}}}else if(o.id){const e=String(o.id),t=s.modelList.find(d=>String(d.id)===e);t?(p.value=t.id,console.log(`PC端${o.type}初始化成功:`,e,"->",t.alias||t.name)):(console.log(`PC端${o.type}初始化失败: 未找到ID为 ${e} 的模型`),console.log("PC端可用模型列表:",s.modelList.map(d=>({id:d.id,name:d.name,alias:d.alias}))))}else console.log(`PC端${o.type}初始化跳过: props.id为空`)},O=()=>{var t,d;if(!s.modelList||s.modelList.length===0)return;const e=s.modelList.findIndex(c=>c.is_default)||0;if(o.type==="chatModels"){const c=(d=(t=s.modelList[e])==null?void 0:t.models)==null?void 0:d[0];c&&(p.value=s.modelList[e].id,x.value=c.id,b.value=e)}else s.modelList[e]&&(p.value=s.modelList[e].id,b.value=e)},A=(e,t)=>e===0?t*2:t*2+1,G=(e,t)=>{const d=s.modelList[A(e,t)];return!d||!d.models?!1:d.models.some(c=>c.id===x.value)},Q=(e,t)=>{p.value=e,o.type==="chatModels"?x.value=t:x.value="",F.value.close()};return S(()=>[o.id,o.sub_id],()=>{s.modelList&&s.modelList.length>0&&E()}),S(()=>s.modelList,e=>{e&&e.length>0&&(console.log("PC端modelList更新，重新初始化已保存值"),E())},{immediate:!0}),I(),(e,t)=>{const d=H,c=J,L=K,N=W,X=Y,Z=ee;return l(),i("div",pe,[f.type==="vectorModels"||f.type==="rankingModels"?(l(),V(c,{key:0,class:"flex-1",modelValue:a(p),"onUpdate:modelValue":t[0]||(t[0]=n=>T(p)?p.value=n:null),filterable:"",disabled:f.disabled},{default:_(()=>[(l(!0),i(C,null,M(a(s).modelList,n=>(l(),V(d,{class:"!h-fit",value:n.id,key:n.id,label:n.alias||n.name},{default:_(()=>[r("div",ue,[r("div",me,[r("div",fe,m(n.alias||n.name),1),n.price=="0"||n.is_free?(l(),i("div",xe," 会员免费 ")):(l(),i("div",_e," 消耗"+m(n.price)+m(a(w).getTokenUnit)+"/1000字符 ",1))])])]),_:2},1032,["value","label"]))),128))]),_:1},8,["modelValue","disabled"])):g("",!0),f.type==="chatModels"||f.type==="vlModels"?(l(),i("div",{key:1,class:B(["select-input flex items-center justify-between flex-1 cursor-pointer rounded-[8px] w-[266px] h-[32px] px-[15px]",[a(x)?"":"text-tx-placeholder",f.disabled?"text-tx-placeholder cursor-no-drop bg-[--el-disabled-bg-color]":""]]),onClick:t[1]||(t[1]=n=>a(F).open())},[r("div",ye,[a(y).alias?(l(),i("span",ge,m(a(y).alias),1)):(l(),i("span",ve,"请选择")),a(y).alias&&(a(y).price=="0"||a(y).is_free)?(l(),i("span",be," 会员免费 ")):a(y).alias?(l(),i("span",he," 消耗"+m(a(y).price)+m(a(w).getTokenUnit)+"/1000字符 ",1)):g("",!0)]),r("div",ke,[h(L,{name:"el-icon-ArrowDown"})])],2)):g("",!0),f.type==="chatModels"||f.type==="vlModels"?(l(),V(le,{key:2,ref_key:"popupRef",ref:F,width:"780px",title:"模型选择",customClass:"!rounded-[15px]"},{footer:_(()=>t[3]||(t[3]=[r("div",null,null,-1)])),default:_(()=>[h(N,{height:"50vh","max-height":"70vh"},{default:_(()=>[r("div",Le,[h(Z,{"active-name":a(b),"onUpdate:activeName":t[2]||(t[2]=n=>T(b)?b.value=n:null),class:"flex flex-wrap justify-between",accordion:""},{default:_(()=>[(l(!0),i(C,null,M([a(R),a(q)],(n,$)=>(l(),i("div",{key:$},[(l(!0),i(C,null,M(n,(v,D)=>(l(),i("div",{key:v.id,class:"w-[350px] mt-[15px]"},[h(X,{class:B(["bg-[#f8f8f8] dark:bg-[#0d0e10] border border-solid border-[transparent]",{"el-collapse-item--active":G($,D)}]),name:A($,D)},{title:_(()=>[r("div",null,[r("div",Ce,[v.logo?(l(),i("img",{key:0,src:v.logo,class:"w-[30px] h-[30px]",alt:"模型logo"},null,8,Me)):g("",!0),r("span",we,m(v.name),1),v.is_free?(l(),i("span",Fe," 会员免费 ")):g("",!0)])])]),default:_(()=>[h(N,{height:"100%","max-height":"250px"},{default:_(()=>[(l(!0),i(C,null,M(v.models,u=>(l(),i("div",{key:u.id,class:B(["flex justify-between mb-[14px] px-[15px] cursor-pointer hover:text-primary",{"text-primary":a(x)===u.id}]),onClick:je=>Q(v.id,u.id)},[r("div",$e,[r("span",Pe,m(u.alias||"请选择"),1),u.alias&&(u.price=="0"||u.is_free)?(l(),i("span",Se," 会员免费 ")):u.alias?(l(),i("span",Ve," 消耗"+m(u.price)+m(a(w).getTokenUnit)+"/1000字符 ",1)):g("",!0)]),a(x)===u.id?(l(),i("div",Be,[h(L,{name:"el-icon-CircleCheck",size:"20"})])):(l(),i("div",Ae,t[4]||(t[4]=[r("div",{class:"w-[18px] h-[18px] rounded-full border border-solid border-[#cacbd3]"},null,-1)])))],10,Ee))),128)),e.cItem.vip_limit_info&&e.cItem.vip_limit_info.is_exceeded?(l(),i("div",Ne,t[5]||(t[5]=[r("div",{class:"font-semibold mb-2 flex items-center text-sm"},[r("span",{class:"text-blue-500 mr-2 text-base"},"💡"),re(" 会员免费额度提醒 ")],-1),r("div",{class:"text-xs leading-5 text-blue-600"}," 该模型短时间内使用频次过高，已明显超过正常使用需求，平台将对该模型免费使用进行限制，继续使用将正常扣费，建议您使用其他会员免费模型。此限制将于今日24:00解除，届时您可继续免费使用该模型。 ",-1)]))):g("",!0)]),_:2},1024)]),_:2},1032,["class","name"])]))),128))]))),128))]),_:1},8,["active-name"])])]),_:1})]),_:1},512)):g("",!0)])}}}),Ke=ce(De,[["__scopeId","data-v-f4886555"]]);export{Ke as _};
