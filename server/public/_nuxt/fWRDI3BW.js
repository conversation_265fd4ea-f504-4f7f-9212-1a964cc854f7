import{E as G,a as R}from"./D4IG-TzH.js";import{a as U,b as j}from"./ZZ3Wnry7.js";import{_ as F}from"./CNwggAt_.js";import{E as J}from"./C4e59zsN.js";import{E as K}from"./BGmmfkI4.js";import{E as M}from"./Dg2XwvBU.js";import{cF as q,cG as P,f as X}from"./C3HqF-ve.js";/* empty css        *//* empty css        *//* empty css        */import{u as E}from"./gtzK5o60.js";import{u as Z,I as C,c as H}from"./XYEXaRmO.js";import{e as Q}from"./Z8VNcbvf.js";import{l as W,r as Y,ak as B,m as w,M as l,N as p,O as s,Z as d,a0 as m,_ as g,aq as b,u as a,a1 as V,a4 as ee,a6 as te,a7 as ae,X as z}from"./Dp9aCaJ6.js";import{_ as oe}from"./DlAUqK2U.js";import"./Cv6HhfEG.js";import"./w4aZ_ZMl.js";import"./D5KDMvDa.js";import"./Df5PDZbu.js";import"./B2dGhy6n.js";import"./DH7aY8gH.js";import"./Bb2-23m7.js";import"./CiYvFM4x.js";import"./DuiQQh4g.js";import"./DQM3eVRQ.js";const se={class:"avatar-select h-full flex flex-col"},ne={class:"px-main"},re={class:"pt-[5px]"},le={class:"flex-1 min-h-0"},ie={class:"p-main"},ce={key:0,class:"flex flex-wrap mx-[-7px]"},pe=["onClick"],de={class:"absolute inset-0"},me=W({__name:"background",async setup(ue){let n,u;const i=Z(),v=[{type:1,label:"竖版背景"},{type:2,label:"横版背景"}],r=Y({type:1,category_id:0}),{data:N}=([n,u]=B(()=>E(()=>q(),{lazy:!0},"$kOs1tBC3ke")),n=await n,u(),n),{data:f,refresh:x,pending:T}=([n,u]=B(()=>E(()=>P(r),{lazy:!0},"$92jT89Nd84")),n=await n,u(),n),I=async o=>{var t,c,_;if(((t=h.value)==null?void 0:t.type)!==i.defaultSize.resolution){const y=H[(c=h.value)==null?void 0:c.type];(_=i.getCanvasJson())!=null&&_.objects.length&&await X.confirm("是否确认更改画布尺寸？当前画面所有设置将被重置且无法恢复"),i.changeSize(y),i.initObject()}i.addImage(o.url,C.BACKGROUND,o)},h=w(()=>v.find(o=>o.type===r.type)),S=w(()=>{var t;const o=(t=i.canvasJson.objects)==null?void 0:t.find(c=>c.customType===C.BACKGROUND);return o!=null&&o.data?o.data:null});return(o,t)=>{const c=R,_=G,y=j,D=U,L=F,O=J,$=K,A=M;return l(),p("div",se,[s("div",ne,[s("div",re,[d(_,{modelValue:a(r).type,"onUpdate:modelValue":t[0]||(t[0]=e=>a(r).type=e),onTabChange:t[1]||(t[1]=e=>{f.value=[],a(x)()})},{default:m(()=>[(l(),p(g,null,b(v,e=>d(c,{key:e.type,label:e.label,name:e.type},null,8,["label","name"])),64))]),_:1},8,["modelValue"])]),d(L,{"default-height":42,class:"my-[-5px]"},{default:m(()=>[d(D,{modelValue:a(r).category_id,"onUpdate:modelValue":t[2]||(t[2]=e=>a(r).category_id=e),class:"el-radio-group-margin",onChange:t[3]||(t[3]=e=>a(x)())},{default:m(()=>[(l(!0),p(g,null,b(a(N),e=>(l(),V(y,{key:e.id,label:e.id},{default:m(()=>[te(ae(e.name),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1})]),s("div",le,[d(A,null,{default:m(()=>[s("div",ie,[a(f).length?(l(),p("div",ce,[(l(!0),p(g,null,b(a(f),e=>{var k;return l(),p("div",{key:e.id,class:"w-[50%]"},[s("div",{class:"px-[7px] mb-[14px]",onClick:_e=>I(e)},[s("div",{class:z(["border border-solid border-br-light rounded-md p-[10px] cursor-pointer",{"!border-primary":((k=a(S))==null?void 0:k.id)==e.id}])},[s("div",null,[s("div",{class:z(["pic-wrap h-0 relative",{"pt-[177%]":a(r).type==1,"pt-[56.3%]":a(r).type==2}])},[s("div",de,[d(O,{src:e.url,class:"w-full h-full",fit:"contain",lazy:""},null,8,["src"])])],2)])],2)],8,pe)])}),128))])):a(T)?ee("",!0):(l(),V($,{key:1,image:a(Q),description:"暂无数据～"},null,8,["image"]))])]),_:1})])])}}}),Ue=oe(me,[["__scopeId","data-v-c2d2a601"]]);export{Ue as default};
