import{E as f}from"./2KyQdWL7.js";import{E as d}from"./BqWxzGsb.js";import{_ as x}from"./C12kmceL.js";/* empty css        */import{u as y}from"./CM_SPFYb.js";import k from"./By3LWgTm.js";import g from"./EbO5csYZ.js";import{e as h}from"./DDS8GZ-a.js";import{useAiPPTStore as P}from"./DINDpfEH.js";import{l as v,ak as w,M as t,N as e,Z as r,a0 as i,O as a,u as m,a3 as l}from"./uahP8ofS.js";import"./B5_1O_mx.js";import"./CeHUJVAt.js";import"./i9Efl4hL.js";import"./BAooD3NP.js";import"./DlAUqK2U.js";import"./CBI7ecvA.js";import"./DCTLXrZ8.js";import"./uHdmwmuG.js";import"./DL-C_KWg.js";import"./l0sNRNKZ.js";/* empty css        *//* empty css        */import"./5Kxl5xpx.js";import"./BnhkBVfO.js";import"./BD9OgGux.js";/* empty css        */import"./BfGcwPP1.js";import"./R2n930gq.js";import"./CTKr7N4V.js";import"./Cq-dlMe8.js";/* empty css        */import"./CWOFGv9Q.js";const E={class:"h-full p-4"},B={key:0,class:"h-full rounded-[15px] bg-body"},C={key:1,class:"h-full flex-1 flex p-4 justify-center items-center"},pt=v({__name:"index",async setup(N){let s,p;const o=P();return[s,p]=w(()=>y(()=>o.getPPTConfig(),"$D6uS58pp2g")),await s,p(),(A,n)=>{const c=f,_=d,u=x;return t(),e("div",null,[r(u,{name:"default"},{default:i(()=>[a("div",E,[m(o).config.status>0?(t(),e("div",B,[m(o).showOutline?(t(),l(g,{key:0})):(t(),l(k,{key:1}))])):(t(),e("div",C,[r(_,null,{icon:i(()=>[r(c,{class:"w-[150px] dark:opacity-60",src:m(h)},null,8,["src"])]),title:i(()=>n[0]||(n[0]=[a("div",{class:"text-info"},"功能暂未开启",-1)])),_:1})]))])]),_:1})])}}});export{pt as default};
