import{E as g}from"./hx-0JZAY.js";import{E as w}from"./LS8qfqy3.js";import{a as v,_ as B}from"./ClNUxNV9.js";/* empty css        */import{u as E}from"./BnLJcfTV.js";import N from"./CQl545gy.js";import{_ as C}from"./CAApPcNC.js";import{useSearch as S}from"./DdZ-Pno0.js";import{e as A}from"./BADzJxKA.js";import{l as I,ak as R,c as j,M as t,N as r,Z as m,a0 as i,u as e,_ as V,a1 as c,O as Z}from"./Dp9aCaJ6.js";import"./CNgDMrD1.js";import"./zRTrVFrw.js";import"./D4cQUBDp.js";import"./DH3BuQAR.js";/* empty css        */import"./DmF3G-6j.js";import"./B6IIPh85.js";import"./DlAUqK2U.js";import"./BPeYb9Dg.js";import"./Tedtu6ac.js";import"./DCTLXrZ8.js";import"./CWzXXCLi.js";import"./D6j_GvJh.js";import"./BYYVzU6A.js";import"./9Bti1uB6.js";import"./B1qK8f0i.js";import"./l0sNRNKZ.js";/* empty css        *//* empty css        *//* empty css        */import"./CBqby79w.js";import"./C-mBvDUD.js";import"./BLV0QRdm.js";import"./DM_JDu0G.js";import"./jUuEzr2R.js";import"./Cq2NhlyP.js";import"./CraaJdZW.js";import"./Cmv72rat.js";import"./B_j7Mcw4.js";import"./DU4nMuh8.js";import"./CcPlX2kz.js";import"./DQ4F_Nwy.js";import"./DTSvt-82.js";import"./DDB-P7g7.js";import"./BfrYKVxU.js";import"./DZESUSa0.js";import"./CSu8YG5J.js";import"./CkTU2NpD.js";import"./Cd8UtlNR.js";import"./Cv6HhfEG.js";import"./Cr7puf4F.js";import"./BbPTMigZ.js";import"./DkqMgBWM.js";/* empty css        */import"./cgSfX0lM.js";import"./hfFQGnFk.js";import"./CQXeYJFv.js";import"./DMVjPTRc.js";import"./Ds5UaVSh.js";import"./BnVTSZ1I.js";import"./vSSOhih4.js";import"./BG65BRUc.js";import"./DggqxWTi.js";/* empty css        */const $={key:1,class:"h-full flex-1 flex p-4 justify-center items-center"},Kt=I({__name:"index",async setup(q){let p,s;const l=v(),{showSearchResult:a,config:u,getConfig:_,getSearchInfo:f,options:d,result:y}=S();return[p,s]=R(()=>E(()=>_(),"$mZBhG8hzNj")),await p,s(),j(()=>l.query.id,o=>{o?y.value.id<0&&f(o):(d.value.ask="",a.value=!1)},{immediate:!0}),(o,n)=>{const h=g,k=w,x=B;return t(),r("div",null,[m(x,{name:"default"},{default:i(()=>[e(u).status>0?(t(),r(V,{key:0},[e(a)?(t(),c(C,{key:0})):(t(),c(N,{key:1}))],64)):(t(),r("div",$,[m(k,null,{icon:i(()=>[m(h,{class:"w-[150px] dark:opacity-60",src:e(A)},null,8,["src"])]),title:i(()=>n[0]||(n[0]=[Z("div",{class:"text-info"},"功能暂未开启",-1)])),_:1})]))]),_:1})])}}});export{Kt as default};
