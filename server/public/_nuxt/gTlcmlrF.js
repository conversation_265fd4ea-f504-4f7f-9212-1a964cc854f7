import{K as tt,X as L,b9 as et,O as nt,P as at}from"./Ct33iMSA.js";import{l as U,m as s,j as G,b as ot,F as rt,c as lt,o as st,M as it,N as ct,V as ut,a2 as ft}from"./Dp9aCaJ6.js";const dt=tt({zIndex:{type:Number,default:9},rotate:{type:Number,default:-22},width:Number,height:Number,image:String,content:{type:L([String,Array]),default:"Element Plus"},font:{type:L(Object)},gap:{type:L(Array),default:()=>[100,100]},offset:{type:L(Array)}});function pt(r){return r.replace(/([A-Z])/g,"-$1").toLowerCase()}function vt(r){return Object.keys(r).map(n=>`${pt(n)}: ${r[n]};`).join(" ")}function gt(){return window.devicePixelRatio||1}const mt=(r,n)=>{let u=!1;return r.removedNodes.length&&n&&(u=Array.from(r.removedNodes).includes(n)),r.type==="attributes"&&r.target===n&&(u=!0),u},K=3;function X(r,n,u=1){const l=document.createElement("canvas"),g=l.getContext("2d"),m=r*u,B=n*u;return l.setAttribute("width",`${m}px`),l.setAttribute("height",`${B}px`),g.save(),[g,l,m,B]}function ht(){function r(n,u,l,g,m,B,R,F){const[h,T,x,A]=X(g,m,l);if(n instanceof HTMLImageElement)h.drawImage(n,0,0,x,A);else{const{color:d,fontSize:w,fontStyle:I,fontWeight:E,fontFamily:Z,textAlign:q,textBaseline:J}=B,Y=Number(w)*l;h.font=`${I} normal ${E} ${Y}px/${m}px ${Z}`,h.fillStyle=d,h.textAlign=q,h.textBaseline=J;const j=Array.isArray(n)?n:[n];j==null||j.forEach((D,Q)=>{h.fillText(D??"",x/2,Q*(Y+K*l))})}const b=Math.PI/180*Number(u),H=Math.max(g,m),[P,O,p]=X(H,H,l);P.translate(p/2,p/2),P.rotate(b),x>0&&A>0&&P.drawImage(T,-x/2,-A/2);function c(d,w){const I=d*Math.cos(b)-w*Math.sin(b),E=d*Math.sin(b)+w*Math.cos(b);return[I,E]}let y=0,W=0,C=0,N=0;const z=x/2,_=A/2;[[0-z,0-_],[0+z,0-_],[0+z,0+_],[0-z,0+_]].forEach(([d,w])=>{const[I,E]=c(d,w);y=Math.min(y,I),W=Math.max(W,I),C=Math.min(C,E),N=Math.max(N,E)});const t=y+p/2,e=C+p/2,a=W-y,f=N-C,v=R*l,M=F*l,k=(a+v)*2,S=f+M,[$,o]=X(k,S);function i(d=0,w=0){$.drawImage(O,t,e,a,f,d,w,a,f)}return i(),i(a+v,-f/2-M/2),i(a+v,+f/2+M/2),[o.toDataURL(),k/l,S/l]}return r}const xt=U({name:"ElWatermark"}),bt=U({...xt,props:dt,setup(r){const n=r,u={position:"relative"},l=s(()=>{var t,e;return(e=(t=n.font)==null?void 0:t.color)!=null?e:"rgba(0,0,0,.15)"}),g=s(()=>{var t,e;return(e=(t=n.font)==null?void 0:t.fontSize)!=null?e:16}),m=s(()=>{var t,e;return(e=(t=n.font)==null?void 0:t.fontWeight)!=null?e:"normal"}),B=s(()=>{var t,e;return(e=(t=n.font)==null?void 0:t.fontStyle)!=null?e:"normal"}),R=s(()=>{var t,e;return(e=(t=n.font)==null?void 0:t.fontFamily)!=null?e:"sans-serif"}),F=s(()=>{var t,e;return(e=(t=n.font)==null?void 0:t.textAlign)!=null?e:"center"}),h=s(()=>{var t,e;return(e=(t=n.font)==null?void 0:t.textBaseline)!=null?e:"top"}),T=s(()=>n.gap[0]),x=s(()=>n.gap[1]),A=s(()=>T.value/2),b=s(()=>x.value/2),H=s(()=>{var t,e;return(e=(t=n.offset)==null?void 0:t[0])!=null?e:A.value}),P=s(()=>{var t,e;return(e=(t=n.offset)==null?void 0:t[1])!=null?e:b.value}),O=()=>{const t={zIndex:n.zIndex,position:"absolute",left:0,top:0,width:"100%",height:"100%",pointerEvents:"none",backgroundRepeat:"repeat"};let e=H.value-A.value,a=P.value-b.value;return e>0&&(t.left=`${e}px`,t.width=`calc(100% - ${e}px)`,e=0),a>0&&(t.top=`${a}px`,t.height=`calc(100% - ${a}px)`,a=0),t.backgroundPosition=`${e}px ${a}px`,t},p=G(null),c=G(),y=ot(!1),W=()=>{c.value&&(c.value.remove(),c.value=void 0)},C=(t,e)=>{var a;p.value&&c.value&&(y.value=!0,c.value.setAttribute("style",vt({...O(),backgroundImage:`url('${t}')`,backgroundSize:`${Math.floor(e)}px`})),(a=p.value)==null||a.append(c.value),setTimeout(()=>{y.value=!1}))},N=t=>{let e=120,a=64;const f=n.image,v=n.content,M=n.width,k=n.height;if(!f&&t.measureText){t.font=`${Number(g.value)}px ${R.value}`;const S=Array.isArray(v)?v:[v],$=S.map(o=>{const i=t.measureText(o);return[i.width,i.fontBoundingBoxAscent!==void 0?i.fontBoundingBoxAscent+i.fontBoundingBoxDescent:i.actualBoundingBoxAscent+i.actualBoundingBoxDescent]});e=Math.ceil(Math.max(...$.map(o=>o[0]))),a=Math.ceil(Math.max(...$.map(o=>o[1])))*S.length+(S.length-1)*K}return[M??e,k??a]},z=ht(),_=()=>{const e=document.createElement("canvas").getContext("2d"),a=n.image,f=n.content,v=n.rotate;if(e){c.value||(c.value=document.createElement("div"));const M=gt(),[k,S]=N(e),$=o=>{const[i,d]=z(o||"",v,M,k,S,{color:l.value,fontSize:g.value,fontStyle:B.value,fontWeight:m.value,fontFamily:R.value,textAlign:F.value,textBaseline:h.value},T.value,x.value);C(i,d)};if(a){const o=new Image;o.onload=()=>{$(o)},o.onerror=()=>{$(f)},o.crossOrigin="anonymous",o.referrerPolicy="no-referrer",o.src=a}else $(f)}};return rt(()=>{_()}),lt(()=>n,()=>{_()},{deep:!0,flush:"post"}),st(()=>{W()}),et(p,t=>{y.value||t.forEach(e=>{mt(e,c.value)&&(W(),_())})},{attributes:!0,subtree:!0,childList:!0}),(t,e)=>(it(),ct("div",{ref_key:"containerRef",ref:p,style:ft([u])},[ut(t.$slots,"default")],4))}});var yt=nt(bt,[["__file","watermark.vue"]]);const St=at(yt);export{St as E};
