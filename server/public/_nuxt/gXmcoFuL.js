import{_ as m}from"./BBAMXcBI.js";import{_ as p}from"./BbMsFDMv.js";import{b as s}from"./DkNxoV1Z.js";import{l as a,M as l,N as n,Z as t,X as c,u as r,O as i,V as _}from"./uahP8ofS.js";import{_ as d}from"./DlAUqK2U.js";import"./BClAlNto.js";import"./DzplivRi.js";import"./C-HK5oo_.js";import"./BTEYzK2M.js";/* empty css        */import"./CDJpNYev.js";import"./42pOGOdp.js";import"./C_YLeEJc.js";import"./C1DmYkVe.js";import"./DCTLXrZ8.js";import"./DNxQvvN3.js";import"./DZB2bx3q.js";import"./BD9OgGux.js";/* empty css        *//* empty css        */import"./D87wJRsE.js";import"./DKxmiVxP.js";import"./BQO0pc5y.js";import"./Cyd8FdCj.js";import"./BaKauqEV.js";import"./Txs22M_p.js";import"./D45YP0Si.js";import"./FN4AbcWJ.js";/* empty css        */import"./Zi9TlVvP.js";import"./CgMcgcz-.js";import"./DNOhY0JR.js";import"./DuEYT86R.js";import"./BGIqOOM2.js";/* empty css        */import"./DP2rzg_V.js";import"./BfGcwPP1.js";const f={class:"layout-header h-full flex items-center"},u={class:"flex-1 min-w-0"},g={class:""},h=a({__name:"index",setup(x){const o=s();return(e,v)=>(l(),n("div",f,[t(m,{class:c("mr-[50px]"),logo:r(o).getWebsiteConfig.pc_logo,title:r(o).getWebsiteConfig.pc_name},null,8,["logo","title"]),i("div",u,[i("div",g,[_(e.$slots,"default",{},void 0,!0)])]),t(p,{class:"ml-auto"})]))}}),io=d(h,[["__scopeId","data-v-a4fd1a58"]]);export{io as default};
