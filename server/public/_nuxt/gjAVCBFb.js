import{_ as q}from"./BAooD3NP.js";import{h as D,p as N,f as k,e as U,o as z,E as L}from"./C12kmceL.js";import"./DP2rzg_V.js";/* empty css        */import{u as $}from"./BfGcwPP1.js";import{w as M}from"./CzIVhTAp.js";import{l as O,b as W,M as r,N as m,O as t,Z as o,a0 as i,_ as y,aq as V,u as c,y as H,a4 as x,a5 as g,a3 as P}from"./uahP8ofS.js";import{_ as R}from"./DlAUqK2U.js";const S={class:"web-page"},T={class:"important-notice mb-4"},Z={class:"notice-header"},j={class:"py-4"},A={class:"flex-1"},G={class:"mb-2 text-tx-primary font-medium text-lg"},J=O({__name:"web-page",props:{modelValue:{}},emits:["update:modelValue"],setup(E,{emit:b}){const l=D(E,"modelValue",b),a=W(""),w=async n=>{await k.confirm(`确定删除：${n.name}？`);const e=l.value.indexOf(n);e!==-1&&l.value.splice(e,1)},{lockFn:B,isLock:C}=$(async()=>{if(!a.value)return k.msgError("请输入网页链接");const n=await M({url:a.value.split(`
`).filter(Boolean)});l.value=[...n.map(e=>({data:[{a:"",q:e.content}],path:"",name:e.url})),...l.value],a.value=""});return(n,e)=>{const d=q,p=U,_=z,f=L,F=N;return r(),m("div",S,[t("div",T,[t("div",Z,[o(d,{name:"el-icon-Warning",color:"#ff9900",size:"18"}),e[1]||(e[1]=t("span",{class:"notice-title"},"重要提示",-1))]),e[2]||(e[2]=t("div",{class:"notice-content"}," 为保护您的信息安全，请勿录入如姓名、身份证号、电话等个人隐私信息及任何工作秘密、商业秘密。本平台在未获得您同意的情况下，不会使用您的数据进行模型训练。 ",-1))]),t("div",j,[o(F,null,{default:i(()=>[o(_,null,{default:i(()=>[t("div",A,[o(p,{modelValue:c(a),"onUpdate:modelValue":e[0]||(e[0]=s=>H(a)?a.value=s:null),placeholder:"请输入要解析的网页链接，添加多个请按回车键分隔",type:"textarea",resize:"none",rows:6},null,8,["modelValue"])])]),_:1}),o(_,null,{default:i(()=>[o(f,{type:"primary",loading:c(C),onClick:c(B)},{default:i(()=>e[3]||(e[3]=[x(" 解析 ")])),_:1},8,["loading","onClick"])]),_:1})]),_:1}),t("div",null,[(r(!0),m(y,null,V(c(l),(s,v)=>(r(),m("div",{key:v,class:"mb-4"},[t("div",G,[x(" #"+g(v+1)+" "+g(s.name)+" ",1),o(f,{link:"",type:"primary"},{default:i(()=>[o(d,{name:"el-icon-Delete",onClick:u=>w(s)},null,8,["onClick"])]),_:2},1024)]),(r(!0),m(y,null,V(s.data,(u,h)=>(r(),P(p,{key:h,modelValue:u.q,"onUpdate:modelValue":I=>u.q=I,placeholder:"文件内容，空内容会自动省略",type:"textarea",resize:"none",rows:15},null,8,["modelValue","onUpdate:modelValue"]))),128))]))),128))])])])}}}),se=R(J,[["__scopeId","data-v-f382808a"]]);export{se as default};
