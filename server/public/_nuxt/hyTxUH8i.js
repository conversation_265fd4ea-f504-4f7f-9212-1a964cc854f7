import{a as f}from"./Ct33iMSA.js";import{_ as u}from"./GT6hEAJd.js";import{_ as x}from"./BzqCcLlP.js";import{l as y,b as e,c as h,F as v,M as t,N as n,O as p,_ as b,aq as g,u as o,a1 as k,a3 as C,a4 as B,n as q,X as w,a7 as N}from"./Dp9aCaJ6.js";import{_ as T}from"./DlAUqK2U.js";import"./Cz-ZBZVo.js";import"./B4i2sXD1.js";import"./iAKPM1CD.js";import"./DCTLXrZ8.js";import"./VtiWddsO.js";import"./DDmMBU6l.js";import"./t4HvZ20D.js";import"./BMo7Szn8.js";import"./CxsMjoDo.js";import"./HaYB0y9G.js";import"./BZXCTEAI.js";import"./3BjIQFFf.js";import"./DXT6IpgZ.js";import"./Cv6HhfEG.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./l0sNRNKZ.js";import"./5SHXd8at.js";import"./67xbGseh.js";import"./DXdf2lbU.js";const $={class:"p-[20px] bg-body rounded-[12px] flex flex-col h-full"},D={class:"flex flex-none"},F={class:"p-[8px] flex bg-page rounded-[10px] font-medium"},L=["onClick"],M=y({__name:"index",setup(S){const m=f(),i=e("member"),c=e(u),r=e(!0),_=e([{name:"会员开通记录",type:"member"},{name:"充值记录",type:"recharge"}]);h(()=>m.query.time,async()=>{r.value=!1,await q(),r.value=!0});const l=a=>{i.value=a,c.value=a==="recharge"?u:x};return v(()=>{l(m.query.type||"member")}),(a,V)=>(t(),n("div",$,[p("div",D,[p("div",F,[(t(!0),n(b,null,g(o(_),(s,d)=>(t(),n("div",{class:w([{selectType:o(i)===s.type},"px-[30px] py-[10px] cursor-pointer"]),key:d,onClick:z=>l(s.type)},[p("span",null,N(s.name),1)],10,L))),128))])]),o(r)?(t(),k(C(o(c)),{key:0})):B("",!0)]))}}),ue=T(M,[["__scopeId","data-v-e66f7bae"]]);export{ue as default};
