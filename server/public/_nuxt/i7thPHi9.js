import{_ as d}from"./C5LOqlzt.js";import{cn as _}from"./C3HqF-ve.js";import{l as h,r as V,M as l,N as t,Z as f,O as s,_ as v,aq as b,u as c,X as r,a7 as o}from"./Dp9aCaJ6.js";import{_ as w}from"./DlAUqK2U.js";import"./D5KDMvDa.js";import"./BPWdBU3q.js";import"./DfZUM0y5.js";import"./DCTLXrZ8.js";import"./Ded2KV7I.js";import"./Dg2XwvBU.js";import"./9Bti1uB6.js";/* empty css        *//* empty css        */const L={class:"mt-[15px]"},z={class:"flex justify-between flex-wrap"},k=["onClick"],y={class:"flex justify-center items-center mt-[10px] h-[20px]"},j={class:"text-base text-[#101010] dark:text-white mt-[4px] size-scale"},C={class:"text-xs text-[#798696] dark:text-white mt-[4px] size-name"},B=h({__name:"mj-picture-size",props:{modelValue:{default:"1:1"}},emits:["update:modelValue"],setup(p,{emit:i}){const n=i,u=p,{modelValue:a}=_(u,n),x=V({lists:[{name:"头像图",scaleLabel:"1:1",scaleValue:"1:1",class:"w-[20px] h-[20px]"},{name:"媒体配图",scaleLabel:"3:4",scaleValue:"3:4",class:"w-[15px] h-[20px]"},{name:"文章配图",scaleLabel:"4:3",scaleValue:"4:3",class:"w-[20px] h-[15px]"},{name:"宣传海报",scaleLabel:"9:16",scaleValue:"9:16",class:"w-[13px] h-[20px]"},{name:"电脑壁纸",scaleLabel:"16:9",scaleValue:"16:9",class:"w-[20px] h-[12px]"},{name:"手机壁纸",scaleLabel:"1:2",scaleValue:"1:2",class:"w-[12px] h-[20px]"},{name:"横版名片",scaleLabel:"3:2",scaleValue:"3:2",class:"w-[20px] h-[14px]"},{name:"小红书图",scaleLabel:"2:3",scaleValue:"2:3",class:"w-[13px] h-[20px]"}]});return a.value="1:1",(M,N)=>(l(),t("div",L,[f(d,{title:"图片尺寸",tips:"指定生成图像的宽高比",required:""}),s("div",z,[(l(!0),t(v,null,b(c(x).lists,(e,m)=>(l(),t("div",{key:m,class:r(["picture-size cursor-pointer text-center hover:text-primary",{"picture-size-active":c(a)==(e==null?void 0:e.scaleValue),"picture-size-disable":!(e!=null&&e.scaleValue)}]),onClick:S=>a.value=e.scaleValue},[s("div",y,[s("div",{class:r(["rect",e.class])},null,2)]),s("div",j,o(e.scaleLabel),1),s("div",C,o(e.name),1)],10,k))),128))])]))}}),H=w(B,[["__scopeId","data-v-7588a305"]]);export{H as default};
