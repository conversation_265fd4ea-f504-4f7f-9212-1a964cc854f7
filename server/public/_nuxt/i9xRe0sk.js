import{_ as u}from"./B2RmFVzS.js";import{_}from"./B6IIPh85.js";import{cn as f}from"./ClNUxNV9.js";import{_ as v}from"./Dr3yaPao.js";import{l as g,M as a,N as l,Z as t,O as o,_ as h,aq as x,X as j,u as C,a7 as y}from"./Dp9aCaJ6.js";const V=""+new URL("mj.BVSeH6C7.png",import.meta.url).href,k=""+new URL("nj.DRE2TxC_.png",import.meta.url).href,b={class:"grid grid-cols-2 gap-4"},w=["onClick"],B={class:"relative rounded-[12px] overflow-hidden"},L={class:"text-hidden-2 text-center"},F=g({__name:"mj-model",props:{modelValue:{default:""}},emits:["update:modelValue"],setup(n,{emit:r}){const c=r,i=n,{modelValue:s}=f(i,c),d=[{value:"mj",title:"真实感强",cover:V},{value:"niji",title:"卡通动漫",cover:k}];return(M,N)=>{const m=u,p=_;return a(),l("div",null,[t(v,{title:"模型选择",required:"",tips:"指定midjourney的渲染模型"}),o("div",b,[(a(),l(h,null,x(d,(e,$)=>o("div",{key:e.cover,class:"flex flex-col gap-2",onClick:I=>s.value=e.value},[o("div",B,[t(m,{class:"rounded-[12px] overflow-hidden bg-[var(--el-bg-color-page)]",src:e.cover,fit:"cover",ratio:[144,100]},null,8,["src"]),o("div",{class:j(["absolute top-0 left-0 bg-[rgba(0,0,0,0.4)] w-full h-full flex justify-center items-center transition-opacity opacity-0",{"opacity-100":e.value===C(s)}])},[t(p,{name:"el-icon-CircleCheckFilled",size:20,color:"#fff"})],2)]),o("div",L,y(e.title),1)],8,w)),64))])])}}});export{F as _};
