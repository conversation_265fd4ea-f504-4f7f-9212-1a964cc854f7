import{E as N,a as R}from"./CCKuheBh.js";import{bF as I,f as U,o as j,e as D,E as S,p as A,v as M}from"./ClNUxNV9.js";import{_ as z}from"./B6IIPh85.js";import{E as G}from"./CqanTtdS.js";/* empty css        */import"./DP2rzg_V.js";import"./CxSV922q.js";/* empty css        */import{u as L}from"./BZsihFLC.js";import{l as O,j as T,b as Z,M as i,N as C,Z as n,a0 as a,u as t,a6 as u,a1 as m,a4 as p,O as l,aa as $,a7 as x}from"./Dp9aCaJ6.js";const q={class:"bg-white rounded-[10px] p-main h-[420px]"},H={class:"flex-1"},J={class:"el-upload__text flex items-center justify-center"},K={key:0,class:"mt-[10px]"},P={class:"flex flex-col items-center justify-center"},Q={class:"flex mt-[10px] items-center"},W={class:"scale-90"},X=["src"],y=".wav,.mp3",de=O({__name:"center-setting",setup(Y){const o=L(),f=T(),r=Z(!1),E=c=>{},g=async({raw:c})=>{var e;try{if(c){r.value=!0;const s=await I("audio",{file:c,data:{use_type:2}});o.voiceContent.voice_url=s.uri,o.voiceContent.voice_name=s.name}}catch(s){U.msgError(s)}finally{r.value=!1,(e=f.value)==null||e.clearFiles()}},k=()=>{o.voiceContent.voice_url="",o.voiceContent.voice_name=""};return(c,e)=>{const s=N,V=R,d=j,w=D,v=z,h=G,b=S,B=A,F=M;return i(),C("div",q,[n(B,null,{default:a(()=>[n(d,{label:"播报内容"},{default:a(()=>[n(V,{modelValue:t(o).voiceContent.type,"onUpdate:modelValue":e[0]||(e[0]=_=>t(o).voiceContent.type=_),onChange:E},{default:a(()=>[n(s,{label:1},{default:a(()=>e[2]||(e[2]=[u("文本输入")])),_:1}),n(s,{label:2},{default:a(()=>e[3]||(e[3]=[u("音频输入")])),_:1})]),_:1},8,["modelValue"])]),_:1}),t(o).voiceContent.type==1?(i(),m(d,{key:0},{default:a(()=>[n(w,{modelValue:t(o).voiceContent.text,"onUpdate:modelValue":e[1]||(e[1]=_=>t(o).voiceContent.text=_),type:"textarea",rows:15,resize:"none",placeholder:"请输入播报内容..."},null,8,["modelValue"])]),_:1})):p("",!0),t(o).voiceContent.type==2?(i(),m(d,{key:1},{default:a(()=>[l("div",H,[$((i(),m(h,{ref_key:"uploadRef",ref:f,drag:"","on-change":g,"auto-upload":!1,"show-file-list":!1,accept:y},{default:a(()=>[l("div",J,[n(v,{name:"el-icon-Upload"}),e[4]||(e[4]=u(" 拖拽文件至此，或点击")),e[5]||(e[5]=l("em",null," 选择文件 ",-1))]),l("div",{class:"el-upload__text"}," 音频支持："+x(y)+"格式，时长不超过30分钟， 大小不超过50MB ")]),_:1})),[[F,t(r)]]),t(o).voiceContent.voice_url?(i(),C("div",K,[l("div",P,[l("div",null,x(t(o).voiceContent.voice_name),1),l("div",Q,[l("div",W,[l("audio",{src:t(o).voiceContent.voice_url,controls:""},null,8,X)]),n(b,{link:"",onClick:k},{default:a(()=>[n(v,{name:"el-icon-Delete"})]),_:1})])])])):p("",!0)])]),_:1})):p("",!0)]),_:1})])}}});export{de as _};
