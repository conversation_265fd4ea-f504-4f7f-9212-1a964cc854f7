import{E as K,a as N}from"./DFv4Fkzk.js";import{j as U,dw as j,p as P,cX as T,dj as q,dk as G,dx as M,e as $,o as D,E as z}from"./C12kmceL.js";import{_ as W}from"./BJZSygq8.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./DP2rzg_V.js";/* empty css        */import{l as X,j as b,r as Z,m as L,M as r,N as A,O as f,Z as l,a0 as s,u as o,ai as _,a3 as i,a7 as d,a4 as y}from"./uahP8ofS.js";import{u as H}from"./BfGcwPP1.js";const J={class:"pt-[10px]"},Q={class:"flex justify-center leading-5 w-[90px] pl-2.5 border-l border-br"},Y={class:"flex"},h={class:"flex-1 flex"},ue=X({__name:"mobile-login",setup(ee){const p=U(),k=b(),S={mobile:[{required:!0,message:"请输入手机号"}],password:[{required:!0,message:"请输入密码"}],code:[{required:!0,message:"请输入验证码"}]},t=Z({code:"",mobile:"",password:"",scene:1,terminal:j}),v=L(()=>t.scene===3),w=L(()=>t.scene===1),E=a=>{t.scene=a},x=b(),F=async()=>{var a,e;await((a=k.value)==null?void 0:a.validateField(["account"])),await q({scene:G.LOGIN,mobile:t.mobile}),(e=x.value)==null||e.start()},{lockFn:V,isLock:C}=H(async()=>{var e;await((e=k.value)==null?void 0:e.validate());const a=await M(t);p.login(a.token),location.reload(),await p.getUser(),p.toggleShowLogin(!1)}),m=()=>{C.value||V()};return(a,e)=>{const B=K,O=N,g=$,u=D,R=W,c=z,I=P;return r(),A("div",J,[f("div",null,[l(I,{ref_key:"formRef",ref:k,size:"large",model:o(t),rules:S,onKeyup:_(m,["enter"])},{default:s(()=>[l(u,{prop:"mobile"},{default:s(()=>[l(g,{modelValue:o(t).mobile,"onUpdate:modelValue":e[0]||(e[0]=n=>o(t).mobile=n),placeholder:"请输入手机号",onKeyup:_(m,["enter"])},{prepend:s(()=>[l(O,{"model-value":"+86",style:{width:"80px"}},{default:s(()=>[l(B,{label:"+86",value:"+86"})]),_:1})]),_:1},8,["modelValue"])]),_:1}),o(w)?(r(),i(u,{key:0,prop:"password"},{default:s(()=>[l(g,{modelValue:o(t).password,"onUpdate:modelValue":e[1]||(e[1]=n=>o(t).password=n),type:"password","show-password":"",placeholder:"请输入密码",onKeyup:_(m,["enter"])},null,8,["modelValue"])]),_:1})):d("",!0),o(v)?(r(),i(u,{key:1,prop:"code"},{default:s(()=>[l(g,{modelValue:o(t).code,"onUpdate:modelValue":e[2]||(e[2]=n=>o(t).code=n),placeholder:"请输入验证码",onKeyup:_(m,["enter"])},{suffix:s(()=>[f("div",Q,[l(R,{ref_key:"verificationCodeRef",ref:x,onClickGet:F},null,512)])]),_:1},8,["modelValue"])]),_:1})):d("",!0),f("div",Y,[f("div",h,[o(w)?(r(),i(c,{key:0,type:"primary",link:"",onClick:e[3]||(e[3]=n=>E(3))},{default:s(()=>e[6]||(e[6]=[y(" 手机验证码登录 ")])),_:1})):d("",!0),o(v)?(r(),i(c,{key:1,type:"primary",link:"",onClick:e[4]||(e[4]=n=>E(1))},{default:s(()=>e[7]||(e[7]=[y(" 手机密码登录 ")])),_:1})):d("",!0)]),o(w)?(r(),i(c,{key:0,link:"",onClick:e[5]||(e[5]=n=>o(p).setLoginPopupType(o(T).FORGOT_PWD_MOBILE))},{default:s(()=>e[8]||(e[8]=[y(" 忘记密码？ ")])),_:1})):d("",!0)]),l(u,{class:"my-[30px]"},{default:s(()=>[l(c,{class:"w-full",type:"primary",loading:o(C),onClick:o(V)},{default:s(()=>e[9]||(e[9]=[y(" 登录 ")])),_:1},8,["loading","onClick"])]),_:1})]),_:1},8,["model"])])])}}});export{ue as _};
