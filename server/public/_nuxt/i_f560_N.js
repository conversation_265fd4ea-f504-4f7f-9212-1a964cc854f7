import{E as u}from"./DggqxWTi.js";import{l as d,b as p,c as v,M as f,N as x,O as l,a7 as n,Z as b,u as V}from"./Dp9aCaJ6.js";import{_}from"./DlAUqK2U.js";import"./ClNUxNV9.js";import"./Cr7puf4F.js";const g={class:"flex-1"},h={class:"font-medium text-tx-primary"},N={class:"text-tx-placeholder text-xs mt-2"},k=d({__name:"permission-option",props:{label:String,description:String,value:Number,modelValue:Number},emits:["change"],setup(e,{emit:i}){const t=e,r=i,a=p(t.modelValue);v(()=>t.modelValue,o=>{a.value=o});const c=()=>{r("change",t.value)};return(o,s)=>(f(),x("div",{class:"flex items-center p-4 hover:bg-page rounded-xl",onClick:c},[l("div",g,[l("div",h,n(e.label),1),l("div",N,n(e.description),1)]),b(V(u),{modelValue:a.value,"onUpdate:modelValue":s[0]||(s[0]=m=>a.value=m),"true-value":e.value,label:"",size:"large",class:"ml-2"},null,8,["modelValue","true-value"])]))}}),y=_(k,[["__scopeId","data-v-bfb962ae"]]);export{y as default};
