import{E as t}from"./DH3BuQAR.js";import"./ClNUxNV9.js";/* empty css        */import e from"./DGmTSp-m.js";import{_ as l}from"./VpqWy_ul.js";import{_ as c}from"./iPm__QIQ.js";import{l as m,M as r,N as _,O as o,Z as s,a0 as n}from"./Dp9aCaJ6.js";const i={class:"h-full flex flex-col"},p={class:"flex-1 min-h-0 max-w-[800px] w-full mx-auto"},f={class:"p-main"},b=m({__name:"index",setup(x){return(d,u)=>{const a=t;return r(),_("div",i,[o("div",p,[s(a,null,{default:n(()=>[o("div",f,[s(l),s(e,{class:"mt-[16px]"}),s(c,{class:"mt-[16px]"})])]),_:1})])])}}});export{b as _};
