import{E as Y,a as ee}from"./3BjIQFFf.js";import{j as te,l as oe,b as se,e as re,E as ne,v as ae,f as h}from"./Ct33iMSA.js";import{_ as le}from"./DvrbA4QQ.js";import{E as ie}from"./DXT6IpgZ.js";import{E as pe}from"./iAKPM1CD.js";import{E as de}from"./Cs34LOgA.js";import{_ as ce}from"./CuM_MOHA.js";import{_ as me}from"./DxKRx1wF.js";import{E as ue}from"./B4i2sXD1.js";import{E as xe}from"./CLVsM8Qg.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./l0sNRNKZ.js";/* empty css        */import{l as fe,b as B,j as _e,r as T,ak as ge,M as r,N as a,O as t,Z as s,a0 as n,u as p,a6 as x,a9 as m,aa as A,_ as R,aq as be,a4 as w,a1 as f,a7 as ve,n as ye}from"./Dp9aCaJ6.js";import{u as he}from"./CcPlX2kz.js";import{u as ke}from"./DuO6be6_.js";import{_ as we}from"./CtW6tOCV.js";import{u as Ce}from"./B77oJvju.js";import Se from"./Dtz219N4.js";import{_ as Ee}from"./DlAUqK2U.js";import"./Cv6HhfEG.js";import"./DDmMBU6l.js";import"./t4HvZ20D.js";import"./VtiWddsO.js";import"./CxsMjoDo.js";import"./DCTLXrZ8.js";import"./CbLH9y7N.js";import"./9Bti1uB6.js";import"./BHaz_wmF.js";import"./BCfv4qMP.js";import"./BPaXy7Em.js";/* empty css        */import"./Bo3PTL3c.js";import"./DjwCd26w.js";import"./kzeq9hfp.js";import"./SbrOwfyf.js";/* empty css        */const Re={class:"h-full flex flex-col"},Le={class:"flex px-[20px] py-[16px]"},Ne={class:"flex items-center flex-none ml-auto"},$e={class:"flex-1 min-h-0"},Ve={class:"px-[20px]","infinite-scroll-distance":"50"},Ie={key:0},Pe={class:"flex flex-wrap items-stretch mx-[-10px]"},qe={class:"w-1/4 2xl:w-1/5 sm:mb-[20px] mb-[10px] app-item"},ze={class:"flex px-[15px] py-[12px]"},je={class:"flex-1 text-tx-secondary"},Be=["onClick"],Te=["onClick"],Ae=["onClick"],De={key:1,class:"flex items-center rounded-[12px] px-[12px] py-[10px] cursor-not-allowed opacity-60"},Ue=["onClick"],Fe=["onClick"],Me=["onClick"],Oe={class:"px-[15px] flex flex-col items-center text-center flex-1"},Ze=["src"],Ge={class:"text-2xl mt-[6px] mb-[12px]"},He={class:"text-tx-secondary leading-5 h-[60px] line-clamp-3"},Je={class:"flex mt-4 items-center border-t border-solid border-br-light"},Ke={class:"flex-1 text-tx-regular border-r border-solid border-br-light flex items-center justify-center h-[50px] cursor-pointer hover:text-primary"},Qe={key:1},We=fe({__name:"robot",async setup(Xe){let L,N;const c=te(),C=oe(),$=se(),_=Ce(),S=B(!1),V=_e(null),D=B([]),I=async(i,e,b)=>{if(e===1){h.msgError("智能体正在审核中，请等待审核完成");return}if(e===2){h.msgError("智能体已在广场中，请先取消发布再重新分享");return}e===3&&h.msg("智能体之前被拒绝，正在重新提交审核"),S.value=!0,await ye(),V.value.open(i,b)},U=i=>{D.value.push(i),_.getRobot(),k()},{isLock:F,lockFn:P}=he(async()=>{if(!c.isLogin)return c.toggleShowLogin();if(c.userInfo.robot_num<=0)return $.getIsShowRecharge?(await h.confirm("智能体数量已用完，请前往充值"),C.push({path:"/user/recharge"})):h.msgError("智能体数量已用完。请联系客服增加"),Promise.reject();const i=await _.addRobot();c.getUser(),C.push({path:"/application/robot/setting",query:{id:i}})}),g=T({type:" ",keyword:""}),l=T({pageNo:1,count:0,pageSize:15,lists:[]}),E=async()=>{const i=await _.getRobot({...g,page_no:l.pageNo,page_size:l.pageSize});l.count=i.count,l.pageNo===1&&(l.lists=[]),l.lists.push(...i.lists)},M=()=>{c.isLogin&&l.count>=l.pageNo*l.pageSize&&(l.pageNo++,E())},k=()=>{l.pageNo=1,E()};[L,N]=ge(()=>ke(()=>E(),{lazy:!0},"$Iyd8pwcMuC")),await L,N();const u=async(i,e)=>{switch(i){case"delete":await _.delRobot(e.id),c.getUser(),k();break;case"release":case"dialogue":C.push({path:"/application/robot/setting",query:{id:e.id,currentTab:i}});break;case"share":I(e.id,e.share_status);break;case"cancelPublic":await _.cancelShareRobot(e.id),k();break;case"resubmit":I(e.id,e.share_status,e.verify_result);break}};return(i,e)=>{const b=Y,O=ee,Z=re,q=ne,d=le,v=ie,G=pe,H=de,J=ce,z=me,K=ue,Q=ae,W=xe;return r(),a("div",Re,[t("div",Le,[e[6]||(e[6]=t("div",{class:"font-medium text-xl"},"我的智能体",-1)),t("div",Ne,[e[5]||(e[5]=t("div",{class:"flex-none mr-[10px]"},"筛选",-1)),s(O,{class:"!w-[100px] flex-none",modelValue:p(g).type,"onUpdate:modelValue":e[0]||(e[0]=o=>p(g).type=o)},{default:n(()=>[s(b,{label:"全部",value:" "}),s(b,{label:"公开",value:1}),s(b,{label:"私有",value:0})]),_:1},8,["modelValue"]),s(Z,{class:"mx-[10px]",modelValue:p(g).keyword,"onUpdate:modelValue":e[1]||(e[1]=o=>p(g).keyword=o),placeholder:"请输入"},null,8,["modelValue"]),t("div",null,[s(q,{onClick:m(k,["stop"]),type:"primary"},{default:n(()=>e[4]||(e[4]=[x("搜索")])),_:1})])])]),t("div",$e,[s(K,null,{default:n(()=>[A((r(),a("div",Ve,[p(c).isLogin?(r(),a("div",Ie,[t("div",Pe,[t("div",qe,[A((r(),a("div",{class:"mx-[10px] bg-body h-full rounded-[12px] p-[20px] overflow-hidden flex flex-col justify-center items-center cursor-pointer min-h-[200px]",onClick:e[2]||(e[2]=(...o)=>p(P)&&p(P)(...o))},[s(d,{name:"el-icon-Plus",size:24}),e[7]||(e[7]=t("div",{class:"mt-[10px]"},"新增智能体",-1))])),[[Q,p(F)]])]),(r(!0),a(R,null,be(p(l).lists,(o,X)=>(r(),a("div",{key:X,class:"w-1/4 2xl:w-1/5 sm:mb-[20px] mb-[10px] app-item"},[s(z,{to:{path:"/application/robot/setting",query:{id:o.id}},class:"mx-[10px] bg-body h-full rounded-[12px] overflow-hidden relative flex flex-col"},{default:n(()=>[t("div",ze,[t("div",je,[o.share_status===2?(r(),f(v,{key:0,type:"success"},{default:n(()=>e[8]||(e[8]=[x("已上架")])),_:1})):o.share_status===1?(r(),f(v,{key:1,type:"warning"},{default:n(()=>e[9]||(e[9]=[x("审核中")])),_:1})):o.share_status===3?(r(),f(G,{key:2,content:"审核拒绝"+(o.verify_result?"："+o.verify_result:""),placement:"top"},{default:n(()=>[s(v,{type:"danger"},{default:n(()=>e[10]||(e[10]=[x("已拒绝")])),_:1})]),_:2},1032,["content"])):(r(),a(R,{key:3},[o.is_public?(r(),f(v,{key:0,type:"warning"},{default:n(()=>e[11]||(e[11]=[x("公开")])),_:1})):(r(),f(v,{key:1,type:"primary"},{default:n(()=>e[12]||(e[12]=[x("私有")])),_:1}))],64))]),s(H,{placement:"bottom",trigger:"hover",offset:"12",teleported:!0,"show-arrow":!1,transition:"custom-popover","popper-class":"cursor-pointer",width:190},{reference:n(()=>[s(q,{link:"",class:"el-dropdown-link"},{default:n(()=>[s(d,{name:"el-icon-MoreFilled"})]),_:1})]),default:n(()=>{var j;return[t("div",{class:"flex items-center dark:hover:bg-[rgba(255,255,255,0.05)] hover:bg-[rgba(0,0,0,0.05)] rounded-[12px] px-[12px] py-[10px]",onClick:m(y=>u("release",o),["stop"])},[s(d,{name:"el-icon-Position"}),e[13]||(e[13]=t("span",{class:"ml-2"},"发布智能体",-1))],8,Be),t("div",{class:"flex items-center dark:hover:bg-[rgba(255,255,255,0.05)] hover:bg-[rgba(0,0,0,0.05)] rounded-[12px] px-[12px] py-[10px]",onClick:m(y=>u("dialogue",o),["stop"])},[s(d,{name:"el-icon-ChatDotRound"}),e[14]||(e[14]=t("span",{class:"ml-2"},"对话数据",-1))],8,Te),(j=p($).getSquareConfig.robot_award)!=null&&j.is_open?(r(),a(R,{key:0},[o.share_status===2?(r(),a("div",{key:0,class:"flex items-center dark:hover:bg-[rgba(255,255,255,0.05)] hover:bg-[rgba(0,0,0,0.05)] rounded-[12px] px-[12px] py-[10px]",onClick:m(y=>u("cancelPublic",o),["stop"])},[s(d,{name:"el-icon-Share"}),e[15]||(e[15]=t("span",{class:"ml-2"},"取消发布至广场",-1))],8,Ae)):o.share_status===1?(r(),a("div",De,[s(d,{name:"el-icon-Clock"}),e[16]||(e[16]=t("span",{class:"ml-2"},"审核中",-1))])):o.share_status===3?(r(),a("div",{key:2,class:"flex items-center dark:hover:bg-[rgba(255,255,255,0.05)] hover:bg-[rgba(0,0,0,0.05)] rounded-[12px] px-[12px] py-[10px]",onClick:m(y=>u("resubmit",o),["stop"])},[s(d,{name:"el-icon-RefreshRight"}),e[17]||(e[17]=t("span",{class:"ml-2"},"重新提交",-1))],8,Ue)):(r(),a("div",{key:3,class:"flex items-center dark:hover:bg-[rgba(255,255,255,0.05)] hover:bg-[rgba(0,0,0,0.05)] rounded-[12px] px-[12px] py-[10px]",onClick:m(y=>u("share",o),["stop"])},[s(d,{name:"el-icon-Share"}),e[18]||(e[18]=t("span",{class:"ml-2"},"发布至广场",-1))],8,Fe))],64)):w("",!0),t("div",{class:"flex items-center dark:hover:bg-[rgba(255,255,255,0.05)] hover:bg-[rgba(0,0,0,0.05)] rounded-[12px] px-[12px] py-[10px]",onClick:m(y=>u("delete",o),["stop"])},[s(d,{name:"el-icon-Delete"}),e[19]||(e[19]=t("span",{class:"ml-2"},"删除",-1))],8,Me)]}),_:2},1024)]),t("div",Oe,[t("img",{class:"flex-none w-[74px] h-[74px] rounded-full",src:o.image,alt:""},null,8,Ze),t("div",Ge,[s(J,{content:o.name},null,8,["content"])]),t("div",He,ve(o.intro||"这个智能体还没介绍呢～"),1)]),t("div",Je,[t("div",Ke,[s(d,{name:"el-icon-Position",size:18}),e[20]||(e[20]=t("div",{class:"ml-[8px]"}," 设置智能体 ",-1))]),s(z,{to:{path:"/application/chat",query:{id:o.id}},class:"flex-1 text-tx-regular flex items-center justify-center h-[50px] cursor-pointer hover:text-primary"},{default:n(()=>[s(d,{name:"el-icon-ChatLineSquare",size:18}),e[21]||(e[21]=t("div",{class:"ml-[8px]"},"开始对话",-1))]),_:2},1032,["to"])])]),_:2},1032,["to"])]))),128))])])):w("",!0),p(c).isLogin?w("",!0):(r(),a("div",Qe,[s(we)]))])),[[W,M]])]),_:1})]),p(S)?(r(),f(Se,{key:0,ref_key:"shareRef",ref:V,onClose:e[3]||(e[3]=o=>S.value=!1),onSuccess:U},null,512)):w("",!0)])}}}),At=Ee(We,[["__scopeId","data-v-acc6d051"]]);export{At as default};
