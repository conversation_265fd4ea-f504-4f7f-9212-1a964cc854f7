import{E as f,a as h}from"./BfDOk1wq.js";import{b as x,v as g}from"./C12kmceL.js";/* empty css        *//* empty css        *//* empty css        */import"./l0sNRNKZ.js";/* empty css        *//* empty css        */import{b}from"./DXdf2lbU.js";import{l as v,b as m,F as y,M as i,N as o,O as t,ab as k,u as d,a3 as N,a0 as s,Z as l,a5 as n,a7 as B,a4 as C}from"./uahP8ofS.js";const E={class:"mt-4 flex-1 flex flex-col min-h-0"},V={class:"flex-1 min-h-0"},w={class:"flex items-center"},L={class:"flex items-center"},T={class:"flex items-center"},D={class:"flex items-center"},M={class:"flex items-center"},S={class:"flex items-center"},z={key:0},A={key:1},F={class:"flex flex-col"},O={key:0,class:"text-warning"},X=v({__name:"member",setup(P){x();const c=m([]),_=m(!1),p=async()=>{_.value=!0;try{c.value=await b()}finally{_.value=!1}};return y(()=>{p()}),(R,Z)=>{const a=h,r=f,u=g;return i(),o("div",E,[t("div",V,[k((i(),N(r,{height:"100%",size:"large",data:d(c)},{default:s(()=>[l(a,{label:"订单编号",prop:"order_sn",width:"170"},{default:s(({row:e})=>[t("div",w,[t("span",null,n(e.order_sn||"-"),1)])]),_:1}),l(a,{label:"会员等级",prop:"name","min-width":"130"},{default:s(({row:e})=>[t("div",L,[t("span",null,n(e.package_name||e.name||"-"),1)])]),_:1}),l(a,{label:"会员时长",prop:"original_package_long_time","min-width":"100"},{default:s(({row:e})=>[t("div",T,[t("span",null,n(e.original_package_long_time||"-"),1)])]),_:1}),l(a,{label:"有效期至",prop:"package_long_time","min-width":"160"},{default:s(({row:e})=>[t("div",D,[t("span",null,n(e.package_long_time||"-"),1)])]),_:1}),l(a,{label:"购买方式",prop:"channel_text","min-width":"100"}),l(a,{label:"支付方式",prop:"pay_way_text","min-width":"100"},{default:s(({row:e})=>[t("div",M,[t("span",null,n(e.pay_way_text||"-"),1)])]),_:1}),l(a,{label:"实付金额","min-width":"100"},{default:s(({row:e})=>[t("div",S,[e.order_amount?(i(),o("span",z,"¥"+n(e.order_amount),1)):(i(),o("span",A,"-"))])]),_:1}),l(a,{label:"支付状态",prop:"pay_status_text","min-width":"100"},{default:s(({row:e})=>[t("div",F,[t("span",null,n(e.pay_status_text||"-"),1),e.refund_status==1?(i(),o("span",O,"已退款")):B("",!0)])]),_:1}),l(a,{label:"支付/操作时间",prop:"pay_time_text","min-width":"160"},{default:s(({row:e})=>[C(n((e==null?void 0:e.pay_time_text)||(e==null?void 0:e.create_time)),1)]),_:1})]),_:1},8,["data"])),[[u,d(_)]])])])}}});export{X as _};
