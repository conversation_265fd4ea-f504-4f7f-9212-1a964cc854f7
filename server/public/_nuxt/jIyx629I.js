import{E as f}from"./hx-0JZAY.js";import{E as d}from"./LS8qfqy3.js";import{_ as x}from"./ClNUxNV9.js";/* empty css        */import{u as y}from"./BnLJcfTV.js";import k from"./DNwtshTM.js";import g from"./DJU-3omp.js";import{e as h}from"./BADzJxKA.js";import{useAiPPTStore as P}from"./BVrk0Zka.js";import{l as v,ak as w,M as t,N as e,Z as r,a0 as i,O as a,u as m,a1 as l}from"./Dp9aCaJ6.js";import"./CNgDMrD1.js";import"./zRTrVFrw.js";import"./D4cQUBDp.js";import"./B6IIPh85.js";import"./DlAUqK2U.js";import"./Tedtu6ac.js";import"./DCTLXrZ8.js";import"./CWzXXCLi.js";import"./DH3BuQAR.js";import"./l0sNRNKZ.js";/* empty css        *//* empty css        */import"./SDGVr5K_.js";import"./C0R3uvOr.js";import"./9Bti1uB6.js";/* empty css        */import"./CcPlX2kz.js";import"./R2n930gq.js";import"./DQ4F_Nwy.js";import"./B1qK8f0i.js";/* empty css        */import"./CraaJdZW.js";const E={class:"h-full p-4"},B={key:0,class:"h-full rounded-[15px] bg-body"},C={key:1,class:"h-full flex-1 flex p-4 justify-center items-center"},pt=v({__name:"index",async setup(N){let s,p;const o=P();return[s,p]=w(()=>y(()=>o.getPPTConfig(),"$D6uS58pp2g")),await s,p(),(A,n)=>{const c=f,_=d,u=x;return t(),e("div",null,[r(u,{name:"default"},{default:i(()=>[a("div",E,[m(o).config.status>0?(t(),e("div",B,[m(o).showOutline?(t(),l(g,{key:0})):(t(),l(k,{key:1}))])):(t(),e("div",C,[r(_,null,{icon:i(()=>[r(c,{class:"w-[150px] dark:opacity-60",src:m(h)},null,8,["src"])]),title:i(()=>n[0]||(n[0]=[a("div",{class:"text-info"},"功能暂未开启",-1)])),_:1})]))])]),_:1})])}}});export{pt as default};
