import{o as l}from"./D726nzJl.js";import"./DP2rzg_V.js";import{l as m,a8 as p,M as c,a1 as i,a0 as a,O as s,a7 as _,V as d,al as f,am as u,u as h}from"./Dp9aCaJ6.js";const x={class:"font-bold text-tx-primary"},b={class:"flex-1 min-w-0 overflow-hidden"},k=m({__name:"form-wrap",props:{label:{}},setup(e){const o=p(),t=e;return(r,w)=>{const n=l;return c(),i(n,f(u(h(o))),{label:a(()=>[s("span",x,_(t.label),1)]),default:a(()=>[s("div",b,[d(r.$slots,"default")])]),_:3},16)}}});export{k as _};
