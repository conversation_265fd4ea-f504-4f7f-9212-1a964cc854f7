import{_ as x}from"./D5KDMvDa.js";import{l as g,a as v,b,j as k,h as y}from"./C3HqF-ve.js";import{l as S,b as p,c as V,M as c,N as u,O as o,_ as z,aq as B,u as m,aa as C,ab as I,X as M,Z as N,a7 as R}from"./Dp9aCaJ6.js";import{_ as $}from"./DlAUqK2U.js";const j={class:"h-full relative"},D={class:"w-[200px] bg-body rounded-[12px] h-full px-[16px] py-[20px]"},P={class:"mt-[10px]"},q=["onClick"],A={class:"ml-[10px]"},E=S({__name:"sidePop",props:{modelValue:{type:Boolean,default:!1}},emits:["update:modelValue"],setup(h,{emit:d}){const _=g(),f=v(),e=b();k();const l=p(0);y(h,"modelValue",d);const a=p([{name:"充值中心",icon:"chongzhi",path:"/user/recharge",show:(e==null?void 0:e.getIsShowRecharge)||!1},{name:"会员中心",icon:"open_vip",path:"/user/member",show:(e==null?void 0:e.getIsShowMember)||!1},{name:"推广返现",icon:"distribution",path:"/user/promotion/distribution",show:!0},{name:"任务奖励",icon:"task_reward",path:"/user/task_reward",show:!0},{name:"购买记录",icon:"goumaijilu",path:"/user/record",show:!0},{name:"我的作品",icon:"my_works",path:"/user/works",show:!0},{name:"余额明细",icon:"yuemingxi",path:"/user/balance",show:!0},{name:"灵感赠送",icon:"gift",path:"/user/gift-records",show:!0},{name:"消息通知",icon:"notice",path:"/user/notification",show:!0},{name:"个人信息",icon:"gerenzhongxin",path:"/user/center",show:!0}]),w=s=>{_.push(a.value[s].path)};return V(()=>f.path,s=>{console.log(s);const t=a.value.findIndex(n=>s.includes(n.path));l.value=t},{immediate:!0}),(s,t)=>{const n=x;return c(),u("div",j,[o("div",D,[t[0]||(t[0]=o("div",{class:"text-base"},"个人中心",-1)),o("div",P,[(c(!0),u(z,null,B(m(a),(r,i)=>C((c(),u("div",{class:"py-[10px] cursor-pointer",key:i,onClick:O=>w(i)},[o("div",{class:M([{isSelect:m(l)==i},"flex items-center h-[40px] leading-[40px] w-full pl-[15px]"])},[N(n,{name:`local-icon-${r.icon}`,size:"16px"},null,8,["name"]),o("span",A,R(r.name),1)],2)],8,q)),[[I,r.show]])),128))])])])}}}),H=$(E,[["__scopeId","data-v-f30d9180"]]);export{H as default};
