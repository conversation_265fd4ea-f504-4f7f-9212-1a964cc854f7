import{j as K,b as O,dw as T,p as P,cX as V,dl as U,dk as j,dx as M,e as q,o as D,E as G}from"./C12kmceL.js";import{_ as $}from"./BJZSygq8.js";import"./DP2rzg_V.js";/* empty css        */import{l as A,j as B,r as X,m as F,M as i,N as z,O as u,Z as n,a0 as l,u as o,ai as c,a3 as f,a7 as y,a4 as _}from"./uahP8ofS.js";import{u as W}from"./BfGcwPP1.js";const Z={class:"pt-[10px]"},H={class:"flex justify-center leading-5 w-[90px] pl-2.5 border-l border-br"},J={class:"flex justify-end"},Q={class:"flex-1 flex"},ne=A({__name:"mailbox-login",setup(Y){const r=K(),I=O(),k=B(),S={email:[{required:!0,message:"请输入邮箱账号"},{type:"email",message:"请输入正确的邮箱账号"}],password:[{required:!0,message:"请输入密码"}],code:[{required:!0,message:"请输入验证码"}]},t=X({code:"",email:"",password:"",scene:4,terminal:T}),w=F(()=>t.scene===4),v=F(()=>t.scene===2),E=s=>{t.scene=s},x=B(),b=async()=>{var s,e;await((s=k.value)==null?void 0:s.validateField(["email"])),await U({scene:j.LOGIN,email:t.email}),(e=x.value)==null||e.start()},{lockFn:C,isLock:L}=W(async()=>{var e;await((e=k.value)==null?void 0:e.validate());const s=await M(t);!s.mobile&&I.getLoginConfig.coerce_mobile?(r.temToken=s.token,r.setLoginPopupType(V.BIND_MOBILE)):(r.login(s.token),r.setUser(s),r.toggleShowLogin(!1),location.reload())}),d=()=>{L.value||C()};return(s,e)=>{const g=q,m=D,N=$,p=G,R=P;return i(),z("div",Z,[u("div",null,[n(R,{ref_key:"formRef",ref:k,size:"large",model:o(t),rules:S,onKeyup:c(d,["enter"])},{default:l(()=>[n(m,{prop:"email"},{default:l(()=>[n(g,{modelValue:o(t).email,"onUpdate:modelValue":e[0]||(e[0]=a=>o(t).email=a),placeholder:"请输入邮箱账号",onKeyup:c(d,["enter"])},null,8,["modelValue"])]),_:1}),o(v)?(i(),f(m,{key:0,prop:"password"},{default:l(()=>[n(g,{modelValue:o(t).password,"onUpdate:modelValue":e[1]||(e[1]=a=>o(t).password=a),type:"password","show-password":"",placeholder:"请输入密码",onKeyup:c(d,["enter"])},null,8,["modelValue"])]),_:1})):y("",!0),o(w)?(i(),f(m,{key:1,prop:"code"},{default:l(()=>[n(g,{modelValue:o(t).code,"onUpdate:modelValue":e[2]||(e[2]=a=>o(t).code=a),placeholder:"请输入验证码",onKeyup:c(d,["enter"])},{suffix:l(()=>[u("div",H,[n(N,{ref_key:"verificationCodeRef",ref:x,onClickGet:b},null,512)])]),_:1},8,["modelValue"])]),_:1})):y("",!0),u("div",J,[u("div",Q,[o(v)?(i(),f(p,{key:0,type:"primary",link:"",onClick:e[3]||(e[3]=a=>E(4))},{default:l(()=>e[6]||(e[6]=[_(" 邮箱验证码登录 ")])),_:1})):y("",!0),o(w)?(i(),f(p,{key:1,type:"primary",link:"",onClick:e[4]||(e[4]=a=>E(2))},{default:l(()=>e[7]||(e[7]=[_(" 邮箱密码登录 ")])),_:1})):y("",!0)]),n(p,{link:"",onClick:e[5]||(e[5]=a=>o(r).setLoginPopupType(o(V).FORGOT_PWD_MAILBOX))},{default:l(()=>e[8]||(e[8]=[_(" 忘记密码？ ")])),_:1})]),n(m,{class:"my-[30px]"},{default:l(()=>[n(p,{class:"w-full",type:"primary",loading:o(L),onClick:o(C)},{default:l(()=>e[9]||(e[9]=[_(" 登录 ")])),_:1},8,["loading","onClick"])]),_:1})]),_:1},8,["model"])])])}}});export{ne as _};
