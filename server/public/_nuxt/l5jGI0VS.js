import{_ as m}from"./D5KDMvDa.js";import{j as d,b as x,E as f}from"./C3HqF-ve.js";import{_ as u}from"./CLzx4hRx.js";import{E as g}from"./Dg2XwvBU.js";/* empty css        */import{l as h,M as v,N as E,Z as e,a0 as o,O as t,a7 as n,u as a,a6 as y}from"./Dp9aCaJ6.js";import{_ as S}from"./DlAUqK2U.js";const b=""+new URL("avatar_example.DnuyDEq4.png",import.meta.url).href,w={class:"h-full"},B={class:"p-main"},N={class:"sm:p-[30px] lg:p-[40px] xl:p-[60px]"},C={class:"flex flex-col items-center justify-center"},k={class:"text-[32px] font-medium"},A={class:"max-w-[850px] mt-[24px] text-center text-lg"},D={class:"mt-[40px]"},I={class:"p-main bg-white shadow-[0_0_16px_#006cff0f] rounded-2xl flex flex-col items-center"},V={class:"text-tx-regular"},j={class:"my-[20px]"},L=h({__name:"index",setup(R){const r=d(),i=x();return(U,s)=>{const l=m,_=f,c=u,p=g;return v(),E("div",w,[e(p,null,{default:o(()=>[t("div",B,[t("div",N,[t("div",C,[t("h1",k,n(a(i).getAvatarConfig.title),1),t("p",A,n(a(i).getAvatarConfig.intro),1),t("div",D,[t("div",I,[s[1]||(s[1]=t("img",{class:"w-[260px] h-[220px]",src:b,alt:""},null,-1)),s[2]||(s[2]=t("div",{class:"text-2xl my-[10px]"},"我的帐户",-1)),t("div",V," 剩余："+n(a(r).userInfo.video_num||0)+"分钟 ",1),t("div",j,[e(c,{to:"/digital_human/aside/video_compositing"},{default:o(()=>[e(_,{type:"primary",class:"enter-btn hover-to-right",round:""},{default:o(()=>[s[0]||(s[0]=y(" 前往制作 ")),e(l,{class:"ml-[5px] target",size:"18",name:"el-icon-Right"})]),_:1})]),_:1})])])])])])])]),_:1})])}}}),F=S(L,[["__scopeId","data-v-acf39e1b"]]);export{F as default};
