import{E as f}from"./CVlyBbl9.js";import{E as d}from"./7Ri6TXvs.js";import{_ as x}from"./C9xud4Fy.js";/* empty css        */import{u as y}from"./2e9ulp1P.js";import k from"./CL_tdqnV.js";import g from"./BcnlU_ef.js";import{e as h}from"./BWWzPGxX.js";import{useAiPPTStore as P}from"./Cs2z699j.js";import{l as v,ak as w,M as t,N as e,Z as r,a0 as i,O as a,u as m,a3 as l}from"./uahP8ofS.js";import"./CC6TKTZR.js";import"./DWsmRjYd.js";import"./B7tOnmRj.js";import"./BY8Moot3.js";import"./DlAUqK2U.js";import"./9Ov0gw5P.js";import"./DCTLXrZ8.js";import"./CjijhATn.js";import"./DVv83yww.js";import"./l0sNRNKZ.js";/* empty css        *//* empty css        */import"./Bsgzp6Ot.js";import"./ABiOVlWA.js";import"./BD9OgGux.js";/* empty css        */import"./BfGcwPP1.js";import"./R2n930gq.js";import"./izUSXXoL.js";import"./CrYRPBPt.js";/* empty css        */import"./C1JW1GZc.js";const E={class:"h-full p-4"},B={key:0,class:"h-full rounded-[15px] bg-body"},C={key:1,class:"h-full flex-1 flex p-4 justify-center items-center"},pt=v({__name:"index",async setup(N){let s,p;const o=P();return[s,p]=w(()=>y(()=>o.getPPTConfig(),"$D6uS58pp2g")),await s,p(),(A,n)=>{const c=f,_=d,u=x;return t(),e("div",null,[r(u,{name:"default"},{default:i(()=>[a("div",E,[m(o).config.status>0?(t(),e("div",B,[m(o).showOutline?(t(),l(g,{key:0})):(t(),l(k,{key:1}))])):(t(),e("div",C,[r(_,null,{icon:i(()=>[r(c,{class:"w-[150px] dark:opacity-60",src:m(h)},null,8,["src"])]),title:i(()=>n[0]||(n[0]=[a("div",{class:"text-info"},"功能暂未开启",-1)])),_:1})]))])]),_:1})])}}});export{pt as default};
