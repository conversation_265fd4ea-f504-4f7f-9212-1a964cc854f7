import{cn as V,o as v}from"./C3HqF-ve.js";import"./DP2rzg_V.js";import{l as h,m as w,M as a,a1 as b,a0 as r,O as s,N as n,aq as z,u as i,_ as k,X as p,a7 as y}from"./Dp9aCaJ6.js";import{_ as C}from"./DlAUqK2U.js";const B={class:"flex-1 min-w-0 overflow-hidden"},g={class:"flex flex-wrap mx-[-6px] mb-[-10px]"},q=["onClick"],E={class:"flex justify-center items-center h-[20px]"},F={class:"text-base text-[#101010] dark:text-white mt-[4px] size-scale"},I=h({__name:"video-size",props:{modelValue:{default:"1:1"},filters:{default:()=>[]}},emits:["update:modelValue"],setup(u,{emit:d}){const x=d,l=u,{modelValue:t}=V(l,x),_=w(()=>[{scaleValue:"1:1",class:"w-[20px] h-[20px]"},{scaleValue:"3:4",class:"w-[15px] h-[20px]"},{scaleValue:"4:3",class:"w-[20px] h-[15px]"},{scaleValue:"9:16",class:"w-[13px] h-[20px]"},{scaleValue:"16:9",class:"w-[20px] h-[12px]"}].filter(c=>l.filters.includes(c.scaleValue)));return(c,o)=>{const f=v;return a(),b(f,{prop:"scale",required:""},{label:r(()=>o[0]||(o[0]=[s("span",{class:"font-bold text-tx-primary"}," 生成尺寸 ",-1)])),default:r(()=>[s("div",B,[s("div",g,[(a(!0),n(k,null,z(i(_),(e,m)=>(a(),n("div",{key:m,class:"w-[33.3%] px-[6px]"},[s("div",{class:p(["picture-size cursor-pointer text-center hover:text-primary",{"picture-size-active":i(t)==(e==null?void 0:e.scaleValue),"picture-size-disable":!(e!=null&&e.scaleValue)}]),onClick:M=>t.value=e.scaleValue},[s("div",E,[s("div",{class:p(["rect",e.class])},null,2)]),s("div",F,y(e.scaleValue),1)],10,q)]))),128))])])]),_:1})}}}),D=C(I,[["__scopeId","data-v-1b8ae8bb"]]);export{D as default};
