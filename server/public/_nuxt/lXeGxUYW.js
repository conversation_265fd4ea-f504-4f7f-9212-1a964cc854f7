import{co as B,e as C,o as U,p as h,E as z}from"./D726nzJl.js";import{_ as D}from"./BpYIl71c.js";import{_ as F}from"./CJ8CVceb.js";import{E as I}from"./D7NF1x92.js";import"./DP2rzg_V.js";/* empty css        *//* empty css        */import{c as M}from"./qffJON56.js";import{l as N,j as $,m as j,M as q,a1 as O,a0 as t,O as r,Z as o,a6 as c,u as n,y as w}from"./Dp9aCaJ6.js";const P={class:"flex-1"},T={class:"dialog-footer"},W=N({__name:"add-menu",props:{show:{type:Boolean},type:{},data:{}},emits:["update:show","update:data","confirm"],setup(y,{emit:v}){const V=y,p=v,u=$(),{show:m,data:l}=B(V,p),i=j({get(){return l.value.images.map(a=>({url:a}))},set(a){l.value.images=a.map(e=>e.url)}}),g={keyword:[{required:!0,message:"请输入关键词"}]},x=async()=>{var a;await((a=u.value)==null?void 0:a.validate()),p("confirm",M(l.value)),m.value=!1};return(a,e)=>{const f=C,d=U,b=D,k=F,E=h,_=z,R=I;return q(),O(R,{modelValue:n(m),"onUpdate:modelValue":e[4]||(e[4]=s=>w(m)?m.value=s:null),title:"添加菜单",width:"640px","destroy-on-close":!0},{footer:t(()=>[r("span",T,[o(_,{onClick:e[3]||(e[3]=s=>m.value=!1)},{default:t(()=>e[6]||(e[6]=[c("取消")])),_:1}),o(_,{type:"primary",onClick:x},{default:t(()=>e[7]||(e[7]=[c(" 确定 ")])),_:1})])]),default:t(()=>[r("div",null,[o(E,{ref_key:"formRef",ref:u,model:n(l),rules:g,"label-width":"100px",disabled:a.type==="view"},{default:t(()=>[o(d,{label:"关键词",prop:"keyword"},{default:t(()=>[o(f,{modelValue:n(l).keyword,"onUpdate:modelValue":e[0]||(e[0]=s=>n(l).keyword=s),placeholder:"请输入关键词",clearable:"",maxlength:20,"show-word-limit":!0},null,8,["modelValue"])]),_:1}),o(d,{label:"回复内容",prop:"content"},{default:t(()=>[o(f,{modelValue:n(l).content,"onUpdate:modelValue":e[1]||(e[1]=s=>n(l).content=s),placeholder:"请输入回复内容",type:"textarea",autosize:{minRows:8,maxRows:8},clearable:"",maxlength:3e3,"show-word-limit":!0,resize:"none"},null,8,["modelValue"])]),_:1}),o(d,{label:"上传图片",prop:"image"},{default:t(()=>[r("div",P,[r("div",null,[o(k,{files:n(i),"onUpdate:files":e[2]||(e[2]=s=>w(i)?i.value=s:null),type:"image","list-type":"picture-card",limit:9,multiple:"","show-file-list":""},{default:t(()=>[o(b,{name:"el-icon-Plus",size:20})]),_:1},8,["files"])]),e[5]||(e[5]=r("div",{class:"form-tips"},"最多支持上传 9 张图",-1))])]),_:1})]),_:1},8,["model","disabled"])])]),_:1},8,["modelValue"])}}});export{W as _};
