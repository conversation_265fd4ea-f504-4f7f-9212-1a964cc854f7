import{E as _}from"./CtupuXNp.js";import{cn as h}from"./DGzblORL.js";/* empty css        *//* empty css        */import{_ as f}from"./B5ZbZMlB.js";import{l as v,r as V,M as t,N as l,Z as w,O as s,_ as b,aq as L,u as c,a3 as z,a0 as k,X as o,a5 as r}from"./uahP8ofS.js";import{_ as y}from"./DlAUqK2U.js";import"./Dt1L8u-1.js";import"./DCTLXrZ8.js";import"./CWcG_iO5.js";import"./BFLg3Y4S.js";import"./BD9OgGux.js";import"./PVV9uz0C.js";const C={class:"mt-[15px]"},g={class:"flex justify-between flex-wrap"},B=["onClick"],S={class:"flex justify-center items-center mt-[10px] h-[20px]"},E={class:"text-base text-[#101010] dark:text-white mt-[4px] size-scale"},N={class:"text-xs text-[#798696] dark:text-white mt-[4px] size-name"},$=v({__name:"sd-picture-size",props:{modelValue:{default:"512x512"}},emits:["update:modelValue"],setup(p,{emit:n}){const i=n,x=p,{modelValue:a}=h(x,i),u=V({lists:[{name:"头像图",scaleLabel:"1:1",scaleValue:"512x512",class:"w-[20px] h-[20px]"},{name:"媒体配图",scaleLabel:"3:4",scaleValue:"1024x1365",class:"w-[15px] h-[20px]"},{name:"文章配图",scaleLabel:"4:3",scaleValue:"1365x1024",class:"w-[20px] h-[15px]"},{name:"宣传海报",scaleLabel:"9:16",scaleValue:"720x1280",class:"w-[13px] h-[20px]"},{name:"电脑壁纸",scaleLabel:"16:9",scaleValue:"1920x1080",class:"w-[20px] h-[12px]"},{name:"手机壁纸",scaleLabel:"1:2",scaleValue:"720x1440",class:"w-[12px] h-[20px]"},{name:"横版名片",scaleLabel:"3:2",scaleValue:"960x640",class:"w-[20px] h-[14px]"},{name:"小红书图",scaleLabel:"2:3",scaleValue:"800x1200",class:"w-[13px] h-[20px]"}]});return a.value="512x512",(j,q)=>{const m=_;return t(),l("div",C,[w(f,{title:"图片尺寸",tips:"分辨率越高生成结果越慢，服务器配置比较低时可能会出现绘画失败、超时、缓慢等情况",required:""}),s("div",g,[(t(!0),l(b,null,L(c(u).lists,(e,d)=>(t(),z(m,{key:d,placement:"bottom","show-arrow":!1,transition:"custom-popover",width:150,trigger:"hover",content:`分辨率：${e.scaleValue}px`},{reference:k(()=>[s("div",{class:o(["picture-size cursor-pointer text-center hover:text-primary",{"picture-size-active":c(a)==(e==null?void 0:e.scaleValue),"picture-size-disable":!(e!=null&&e.scaleValue)}]),onClick:M=>a.value=e.scaleValue},[s("div",S,[s("div",{class:o(["rect",e.class])},null,2)]),s("div",E,r(e.scaleLabel),1),s("div",N,r(e.name),1)],10,B)]),_:2},1032,["content"]))),128))])])}}}),R=y($,[["__scopeId","data-v-ee835ec6"]]);export{R as default};
