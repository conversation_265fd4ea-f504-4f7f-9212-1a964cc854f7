import{_ as a}from"./CLzx4hRx.js";import{l as n,M as t,a1 as i,a0 as r,N as s,O as p,a4 as o,a7 as c}from"./Dp9aCaJ6.js";const _={key:0},m=["src"],d={key:1,class:"font-bold ml-[10px] text-[16px] line-clamp-1"},x=n({__name:"title-logo",props:{logo:{},title:{}},setup(u){return(e,g)=>{const l=a;return e.logo||e.title?(t(),i(l,{key:0,to:"/",class:"flex items-center title-logo px-[10px]"},{default:r(()=>[e.logo?(t(),s("div",_,[p("img",{class:"w-[34px] h-[34px]",src:e.logo},null,8,m)])):o("",!0),e.title?(t(),s("div",d,c(e.title),1)):o("",!0)]),_:1})):o("",!0)}}});export{x as _};
