import{a as v}from"./Dz_s4yBT.js";import{b as d,a as w}from"./DGzblORL.js";/* empty css        */import"./l0sNRNKZ.js";/* empty css        */import P from"./C3yWuphZ.js";import{l as M,m as s,M as c,N as m,Z as N,a0 as g,_ as k,aq as x,a3 as A,u as h}from"./uahP8ofS.js";import{_ as y}from"./DlAUqK2U.js";import"./HP80a3uV.js";import"./Dt1L8u-1.js";import"./DCTLXrZ8.js";import"./CY5Ghzht.js";import"./BNsATNM9.js";import"./D7Zbvkmx.js";import"./BZfRKX0z.js";const B={class:"menu"},I=M({__name:"nav",props:{isHome:{type:Boolean}},setup(L){const i=d(),u=s(()=>{var e;return((e=i.pageAside.nav)==null?void 0:e.filter(t=>Number(t.is_show)===1))||[]}),l=s(()=>i.pageAside.showNavIcon),a=w(),p=s(()=>{const e=a.path==="/"?a.path:a.path.replace(/\/$/,"");return a.meta.parentPath||a.meta.activePath||e}),n=e=>{const t=e.link.path;if(t.startsWith("http://")||t.startsWith("https://"))try{return new URL(t).pathname}catch{return console.warn("无法解析菜单URL:",t),t}return t},f=e=>{const t=p.value,r=n(e);return!!(t===r||t.startsWith(r+"/"))},_=()=>{const e=p.value;for(const t of u.value){const r=n(t);if(e===r||e.startsWith(r+"/"))return r}return e};return(e,t)=>{const r=v;return c(),m("div",B,[N(r,{"default-active":_()},{default:g(()=>[(c(!0),m(k,null,x(h(u),o=>(c(),A(P,{key:o.id,item:o,showName:!0,"is-show-icon":h(l),path:n(o),"is-active":f(o)},null,8,["item","is-show-icon","path","is-active"]))),128))]),_:1},8,["default-active"])])}}}),z=y(I,[["__scopeId","data-v-c6663a48"]]);export{z as default};
