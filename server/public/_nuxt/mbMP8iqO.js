import{_ as ce}from"./DH8JVbik.js";import{_ as pe}from"./CN8DIg3d.js";import{a as me,b as fe,j as ve,l as ge,cz as ye,f as E,a1 as he,_ as _e}from"./D726nzJl.js";import{u as R}from"./CEZIOyk0.js";import{u as xe}from"./CcPlX2kz.js";import we from"./Dobl_ujz.js";import{C as Se,a as Ce}from"./BFynY0vw.js";import Ie from"./D98WSnGD.js";import{b as be,e as ke,c as Le}from"./B_1915px.js";import{g as Ee,a as Re,b as Me}from"./BdjxQtGL.js";import{u as ze}from"./BkzMQo12.js";import{c as O}from"./qffJON56.js";import{l as De,b as v,j as Ne,q as Ve,r as G,ak as M,c as H,k as $e,M as z,N as D,Z as S,a0 as K,O as g,_ as J,aq as qe,u as n,y as N,X as Ue,a7 as Fe,a4 as Te,n as Ae}from"./Dp9aCaJ6.js";import{_ as Oe}from"./DlAUqK2U.js";import"./B0taQFTv.js";import"./GbXQDMM-.js";import"./DCTLXrZ8.js";import"./DxoS9eIh.js";import"./CJW7H0Ln.js";import"./Cv6HhfEG.js";import"./CmJNjzrM.js";import"./DcsXcc99.js";import"./DzhE9Pcm.js";import"./CjE8iZ8I.js";import"./BpYIl71c.js";import"./BHChID2I.js";import"./DyqYHYmh.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./DaBGfXF7.js";import"./D7NF1x92.js";import"./9Bti1uB6.js";/* empty css        */import"./D8hXTSyI.js";import"./DP2rzg_V.js";/* empty css        */import"./DsYfyJ0C.js";/* empty css        */import"./t5rt8HsH.js";import"./CUc0yGc4.js";import"./B6eARkLB.js";import"./Bb2-23m7.js";import"./BOaJ3oTb.js";import"./DouuYAvM.js";import"./l0sNRNKZ.js";import"./CGUG-TI3.js";import"./BljqH1wT.js";import"./D12SYOqE.js";import"./CBTxQWUq.js";import"./BXMCmyua.js";import"./BA9ELCiR.js";import"./DU8ualjj.js";import"./DnE_5Yhr.js";/* empty css        */const Y=(x,a="yyyy-mm-dd")=>{x||(x=Number(new Date)),x.toString().length===10&&(x*=1e3);const d=new Date(x);let f;const w={"y+":d.getFullYear().toString(),"m+":(d.getMonth()+1).toString(),"d+":d.getDate().toString(),"h+":d.getHours().toString(),"M+":d.getMinutes().toString(),"s+":d.getSeconds().toString()};for(const y in w)f=new RegExp(`(${y})`).exec(a),f&&(a=a.replace(f[1],f[1].length===1?w[y]:w[y].padStart(f[1].length,"0")));return a},He={class:"flex flex-col h-full"},Ke={class:"px-[16px] pt-[16px] round-[12px]"},je={class:"flex flex-wrap gap-y-2 my-swiper category-lists"},Be=["onClick"],Pe={class:"flex-1 min-h-0 flex"},Xe={class:"py-[16px] pl-[16px]"},Ze={class:"flex-1 min-w-0 p-4"},Ge={class:"h-full flex bg-body rounded-[12px]"},Je={class:"h-full border-r border-solid border-br-light"},Ye={class:"flex-1 min-w-0"},Qe=De({__name:"produce",async setup(x){let a,d;const f=v(""),w=me();ze();const y=v(!0),j=v([]),u=v({}),V=fe(),Q=ve(),$=ge(),{cateId:W,modelId:ee}=w.query,C=v("current"),I=Ne({});Ve("chatModel",I);const i=G({cateId:W||"0",modelId:ee,modelKey:""}),te=async e=>{i.cateId=e,await B();const[t]=T.value||[];$.replace({path:"",query:{cateId:e,modelId:t==null?void 0:t.id}})},{data:q,pending:We,refresh:et}=([a,d]=M(()=>R(()=>Ee(),{default(){return[]},lazy:!0},"$3iMOmfmgi4")),a=await a,d(),a);v(!1),v(0);let U=null;const F=e=>{try{U==null||U.slideTo(e)}catch(t){console.error(t)}},{data:T,refresh:B}=([a,d]=M(()=>R(()=>Re({keyword:f.value,category_id:i.cateId,page_size:999}),{lazy:!0},"$VSSSR0UZJy")),a=await a,d(),a);ye(f,e=>{B()},{debounce:500});const oe=async()=>{await E.confirm("确定清空创作记录？"),await be({type:2,other_id:i.modelId}),l.value=[],k()},{data:b,refresh:ae}=([a,d]=M(()=>R(()=>Me({id:i.modelId}),{default(){return{}},transform(e){return e},lazy:!0},"$dzxelPzSXF")),a=await a,d(),a),c=G({pageNo:1,count:0,pageSize:15,lists:[]}),{refresh:A,pending:re}=([a,d]=M(()=>R(()=>ke({other_id:i.modelId||0,page_size:c.pageSize,page_no:c.pageNo,type:2}),{default(){return[]},transform(e){c.count=e.count;const t=e.lists.map(s=>{let p="";if(he(s.ask)){const _=(s==null?void 0:s.ask[0])||{};p=`${_.title}：${_.value}`}else p=s.ask;return{...s,title:p}});return c.pageNo===1&&(c.lists=[]),c.lists.push(...t),t},lazy:!0},"$T4LGrdB7sx")),a=await a,d(),a),k=async()=>{Object.assign(c,{pageNo:1,count:0,pageSize:15,lists:[]}),await A()},le=()=>{re.value||c.count>=c.pageNo*c.pageSize&&(c.pageNo++,A())},ne=e=>{console.log("model",e),i.modelId=e.id,k()},{lockFn:se}=xe(async e=>{e.extra&&(u.value=e.extra,await Ae(),X())}),ie=()=>{var e,t;(t=(e=b.value)==null?void 0:e.form)==null||t.forEach(s=>{s.props.placeholder&&!s.props.defaultValue&&(u.value[s.props.field]=s.props.placeholder)})},de=()=>{var e;(e=b.value)==null||e.form.forEach(t=>{t.props.defaultValue?u.value[t.props.field]=O(t.props.defaultValue):u.value[t.props.field]=void 0})},ue=()=>{y.value?(y.value=!1,j.value=[]):(j.value=q.value.map(e=>e.id),y.value=!0)},l=v([]),P=()=>{var t;const e=((t=b.value)==null?void 0:t.form[0])||{};return`${e.props.title}：${u.value[e.props.field]||""}`},h=v(!1);let r;const X=async()=>{if(!h.value){C.value="current",h.value=!0,l.value=[],F(1);try{r=Le({other_id:i.modelId,question:u.value,type:2,creation_type:Ce.Normal,model:i.modelKey}),r.addEventListener("close",async()=>{Q.getUser(),setTimeout(async()=>{h.value=!1,await k();const e=l.value.length;l.value[e-1].id=c.lists[0].id},200)}),r.addEventListener("error",async e=>{var t;if(((t=e.data)==null?void 0:t.code)===1100){V.getIsShowRecharge?(await E.confirm(`${V.getTokenUnit}数量已用完，请前往充值`),$.push("/user/recharge")):E.msgError(`${V.getTokenUnit}数量已用完。请联系客服增加`);return}e.errorType==="connectError"&&E.msgError("请求失败，请重试"),setTimeout(()=>{h.value=!1,F(0)},200)}),r.addEventListener("reasoning",({data:e})=>{const{id:t,event:s,data:p,code:_,index:m}=e;let o=l.value.findIndex(L=>L.id===t);o===-1&&(l.value.push({create_time:Y(Date.now(),"yyyy-mm-dd hh:MM:ss"),title:u.value.question?u.value.question:P(),reply:[],extra:O(u.value),id:t}),o=l.value.length-1),p&&(l.value[o].reply[m]||(l.value[o].reply[m]=""),l.value[o].reply[m]+=p)}),r.addEventListener("chat",({data:e})=>{const{id:t,event:s,data:p,code:_,index:m}=e;let o=l.value.findIndex(L=>L.id===t);o===-1&&(l.value.push({create_time:Y(Date.now(),"yyyy-mm-dd hh:MM:ss"),title:u.value.question?u.value.question:P(),reply:[],extra:O(u.value),id:t}),o=l.value.length-1),p&&(l.value[o].reply[m]||(l.value[o].reply[m]=""),l.value[o].reply[m]+=p)}),r.addEventListener("finish",({data:e})=>{const{data:t,index:s,id:p}=e,_=l.value.findIndex(m=>m.id===p);t&&(l.value[_].reply[s]+=t),de()})}catch{h.value=!1}}};H(q,e=>{y.value=!1,ue()},{immediate:!0});const Z=()=>{r==null||r.removeEventListener("close"),r==null||r.removeEventListener("chat"),r==null||r.removeEventListener("error"),r==null||r.removeEventListener("finish"),r==null||r.abort(),h.value=!1};return H(()=>w.query,({cateId:e,modelId:t})=>{i.cateId=e,i.modelId=t,l.value=[],C.value="current",F(0),Z(),t?(ae(),k()):b.value={}}),H(()=>T.value,e=>{e.length&&i.modelId==null&&$.replace({path:"",query:{cateId:i.cateId,modelId:e[0].id}})},{deep:!0}),$e(()=>{Z()}),(e,t)=>{const s=ce,p=pe,_=_e;return z(),D("div",null,[S(_,{name:"default"},{default:K(()=>{var m;return[g("div",He,[g("div",Ke,[g("div",je,[(z(!0),D(J,null,qe(n(q),(o,L)=>(z(),D(J,null,[Object.keys(o).includes("name")?(z(),D("div",{key:0,class:Ue(["category-item",{"is-active":n(i).cateId==o.id}]),onClick:tt=>te(o.id)},Fe(o.name),11,Be)):Te("",!0)],64))),256))])]),g("div",Pe,[g("div",Xe,[S(Ie,{modelValue:n(f),"onUpdate:modelValue":t[0]||(t[0]=o=>N(f)?f.value=o:null),"model-state":n(i),"current-model-list":((m=n(T))==null?void 0:m.lists)||[],onSelect:ne},null,8,["modelValue","model-state","current-model-list"])]),g("div",Ze,[g("div",Ge,[g("div",Je,[S(we,{modelValue:n(u),"onUpdate:modelValue":t[3]||(t[3]=o=>N(u)?u.value=o:null),class:"h-full 2xl:w-[400px] xl:w-[350px] lg:w-[320px] w-[250px]","model-data":n(b),loading:n(h),onInsert:ie,onCreate:X},{actions:K(()=>[S(s,{class:"w-full my-[10px]",sub_id:n(i).modelKey,"onUpdate:sub_id":t[1]||(t[1]=o=>n(i).modelKey=o),modelConfig:n(I),"onUpdate:modelConfig":t[2]||(t[2]=o=>N(I)?I.value=o:null)},null,8,["sub_id","modelConfig"])]),_:1},8,["modelValue","model-data","loading"])]),g("div",Ye,[S(p,null,{default:K(()=>[S(Se,{current:n(C),"onUpdate:current":t[4]||(t[4]=o=>N(C)?C.value=o:null),chatModel:n(I),"current-creation-history":n(l),loading:n(h),"page-info":n(c),onLoad:le,onClean:oe,onRewrite:n(se),onRefresh:n(A)},null,8,["current","chatModel","current-creation-history","loading","page-info","onRewrite","onRefresh"])]),_:1})])])])])])]}),_:1})])}}}),ro=Oe(Qe,[["__scopeId","data-v-c15da807"]]);export{ro as default};
