import{_ as G}from"./sBOlCGQa.js";import{aa as H,aX as Z,bJ as M,bs as Q,s as W,cp as Y,bH as ee,ch as le,cq as te,cr as se,bI as oe,d as F,J as ae,E as T,e as A,cs as ne,h as ie,o as de}from"./C9xud4Fy.js";import{E as ue,a as re}from"./BNvvS5EC.js";import{E as me,a as pe}from"./3uKaP3Ll.js";import{E as _e}from"./VnZBizJD.js";import{E as ce}from"./B291DSSP.js";import"./DP2rzg_V.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./l0sNRNKZ.js";import{u as fe}from"./CzIVhTAp.js";import{_ as ve}from"./NqAC7czs.js";import{t as xe}from"./BXWYK723.js";import{E as be}from"./CrYRPBPt.js";import{E as ge}from"./ABiOVlWA.js";/* empty css        */import{l as J,b as V,m as K,F as Ve,M as m,N as p,Z as l,a0 as s,O as t,u as a,a4 as _,a5 as x,a7 as E,_ as D,aq as B,X as O,a3 as L,j as ke,n as ye}from"./uahP8ofS.js";import{_ as we}from"./DlAUqK2U.js";import{u as Ee}from"./B5j_tNK8.js";function Ce(){return $request.get({url:"/robot.roleExample/getAllRoleExamples"})}const $e={class:"role-example-selector"},Re={key:0,class:"selected-indicator"},Ue={class:"dialog-content"},he={class:"search-bar"},Se={class:"flex h-[520px] mt-4"},Ie={class:"w-[220px] border-r border-gray-200 pr-4"},qe={class:"category-header"},ze={class:"category-list"},De=["onClick"],Be={class:"category-content"},Le={class:"category-name"},Ne={class:"flex-1 pl-4"},Me={class:"example-header"},Fe={key:0,class:"example-count"},Ke={key:0,class:"loading-container"},Oe={key:1,class:"empty-container"},Te={class:"empty-text"},Ae={key:2,class:"example-list"},Je=["onClick"],Pe={class:"example-header-info"},Xe={class:"example-title"},je={class:"example-actions"},Ge={key:0,class:"example-description"},He={class:"example-content"},Ze={class:"content-preview"},Qe={class:"content-length"},We={class:"dialog-footer"},Ye={class:"footer-info"},el={key:0,class:"selected-info"},ll={class:"footer-actions"},tl=J({__name:"index",emits:["select"],setup(N,{emit:h}){const $=h,b=V(!1),R=V(!1),k=V(""),n=V([]),y=V(null),g=V(null),c=V(null),S=K(()=>{if(!y.value)return[];const d=n.value.find(o=>o.id===y.value);return(d==null?void 0:d.examples)||[]}),v=K(()=>{let d=S.value;if(k.value.trim()){const o=k.value.toLowerCase();d=d.filter(u=>u.title.toLowerCase().includes(o)||u.content.toLowerCase().includes(o)||u.description&&u.description.toLowerCase().includes(o))}return d}),e=d=>{var u;const o=n.value.find(w=>w.id===d);return((u=o==null?void 0:o.examples)==null?void 0:u.length)||0},U=d=>{y.value=d,g.value=null,c.value=null,k.value=""},f=d=>{g.value=d.id,c.value=d},C=()=>{g.value=null,c.value=null},I=()=>{c.value&&($("select",c.value),b.value=!1,F.success({message:"角色示例选择成功",type:"success",duration:2e3}))},q=async()=>{try{R.value=!0;const d=await Ce();n.value=d||[],n.value.length>0&&(y.value=n.value[0].id)}catch(d){console.error("获取角色示例失败:",d),F.error("获取角色示例失败")}finally{R.value=!1}};return Ve(()=>{q()}),(d,o)=>{const u=ae,w=T,z=be,i=A,P=ne,X=ge;return m(),p("div",$e,[l(w,{type:"primary",icon:a(Z),onClick:o[0]||(o[0]=r=>b.value=!0),size:"small",class:"example-selector-btn"},{default:s(()=>[o[4]||(o[4]=t("span",{class:"btn-text"},"选择角色示例",-1)),l(u,{class:"btn-icon"},{default:s(()=>[l(a(H))]),_:1})]),_:1},8,["icon"]),c.value?(m(),p("div",Re,[l(z,{type:"success",size:"small",closable:"",onClose:C},{default:s(()=>[l(u,null,{default:s(()=>[l(a(M))]),_:1}),_(" 已选择："+x(c.value.title),1)]),_:1})])):E("",!0),l(X,{modelValue:b.value,"onUpdate:modelValue":o[3]||(o[3]=r=>b.value=r),title:"选择角色示例",width:"900px","close-on-click-modal":!1,class:"example-dialog"},{footer:s(()=>[t("div",We,[t("div",Ye,[c.value?(m(),p("span",el,[l(u,null,{default:s(()=>[l(a(Q))]),_:1}),_(" 已选择："+x(c.value.title),1)])):E("",!0)]),t("div",ll,[l(w,{onClick:o[2]||(o[2]=r=>b.value=!1),size:"large"},{default:s(()=>o[8]||(o[8]=[_("取消")])),_:1}),l(w,{type:"primary",onClick:I,disabled:!c.value,size:"large",class:"confirm-btn"},{default:s(()=>[l(u,null,{default:s(()=>[l(a(M))]),_:1}),o[9]||(o[9]=_(" 确定选择 "))]),_:1},8,["disabled"])])])]),default:s(()=>[t("div",Ue,[t("div",he,[l(i,{modelValue:k.value,"onUpdate:modelValue":o[1]||(o[1]=r=>k.value=r),placeholder:"搜索角色示例...","prefix-icon":a(W),clearable:"",class:"search-input"},null,8,["modelValue","prefix-icon"])]),t("div",Se,[t("div",Ie,[t("div",qe,[l(u,null,{default:s(()=>[l(a(Y))]),_:1}),o[5]||(o[5]=t("span",null,"示例分类",-1))]),t("div",ze,[(m(!0),p(D,null,B(n.value,r=>(m(),p("div",{key:r.id,class:O(["category-item",y.value===r.id?"category-active":""]),onClick:j=>U(r.id)},[t("div",Be,[t("span",Le,x(r.name),1),l(P,{value:e(r.id),class:"category-badge",max:99},null,8,["value"])])],10,De))),128))])]),t("div",Ne,[t("div",Me,[l(u,null,{default:s(()=>[l(a(ee))]),_:1}),o[6]||(o[6]=t("span",null,"角色示例",-1)),v.value.length?(m(),p("span",Fe," ("+x(v.value.length)+" 个) ",1)):E("",!0)]),R.value?(m(),p("div",Ke,[l(u,{class:"is-loading loading-icon"},{default:s(()=>[l(a(le))]),_:1}),o[7]||(o[7]=t("div",{class:"loading-text"},"加载中...",-1))])):v.value.length===0?(m(),p("div",Oe,[l(u,{class:"empty-icon"},{default:s(()=>[l(a(te))]),_:1}),t("div",Te,x(k.value?"未找到匹配的角色示例":"暂无角色示例"),1)])):(m(),p("div",Ae,[(m(!0),p(D,null,B(v.value,r=>(m(),p("div",{key:r.id,class:O(["example-item",g.value===r.id?"example-selected":""]),onClick:j=>f(r)},[t("div",Pe,[t("div",Xe,[l(u,{class:"title-icon"},{default:s(()=>[l(a(se))]),_:1}),_(" "+x(r.title),1)]),t("div",je,[g.value===r.id?(m(),L(u,{key:0,class:"selected-icon"},{default:s(()=>[l(a(oe))]),_:1})):E("",!0)])]),r.description?(m(),p("div",Ge,x(r.description),1)):E("",!0),t("div",He,[t("div",Ze,x(r.content),1),t("div",Qe,x(r.content.length)+" 字符 ",1)])],10,Je))),128))]))])])])]),_:1},8,["modelValue"])])}}}),sl=we(tl,[["__scopeId","data-v-eceaf629"]]),ol={class:"pt-[10px]"},al={class:"w-80"},nl={class:"w-80"},il={class:"flex"},dl={class:"w-80"},ul={class:"ml-2 flex items-center"},rl={class:"flex-1 min-w-0"},ml={class:"flex"},pl={class:"w-80"},_l={class:"ml-3 flex items-start"},cl={class:"flex w-[400px]"},Nl=J({__name:"base-config",props:{modelValue:{}},emits:["update:modelValue"],setup(N,{emit:h}){const $=ke(),b=V(!1),n=ie(N,"modelValue",h),{optionsData:y,refresh:g}=Ee({knowledge:{api:fe,params:{page_type:0},transformData(v){return v.lists||[]}},robotCategory:{api:xe}}),c=async()=>{b.value=!0,await ye(),$.value.open()},S=v=>{n.value.roles_prompt=v.content};return(v,e)=>{const U=G,f=de,C=A,I=ue,q=re,d=T,o=me,u=pe,w=_e,z=ce;return m(),p("div",ol,[l(f,{label:"智能体图标",prop:"image"},{default:s(()=>[t("div",null,[t("div",null,[l(U,{modelValue:a(n).image,"onUpdate:modelValue":e[0]||(e[0]=i=>a(n).image=i)},null,8,["modelValue"])]),e[11]||(e[11]=t("div",{class:"form-tips"},"建议尺寸：240*240px",-1))])]),_:1}),l(f,{label:"智能体名称",prop:"name"},{default:s(()=>[t("div",al,[l(C,{modelValue:a(n).name,"onUpdate:modelValue":e[1]||(e[1]=i=>a(n).name=i),placeholder:"请输入智能体名称",clearable:""},null,8,["modelValue"])])]),_:1}),l(f,{label:"简介",prop:"intro"},{default:s(()=>[t("div",nl,[l(C,{modelValue:a(n).intro,"onUpdate:modelValue":e[2]||(e[2]=i=>a(n).intro=i),placeholder:"请简单描述下给你的智能体",type:"textarea",autosize:{minRows:3,maxRows:6},maxlength:200,"show-word-limit":"",clearable:""},null,8,["modelValue"])])]),_:1}),l(f,{label:"关联知识库",prop:"kb_ids"},{default:s(()=>[t("div",null,[t("div",il,[t("div",dl,[l(q,{modelValue:a(n).kb_ids,"onUpdate:modelValue":e[3]||(e[3]=i=>a(n).kb_ids=i),placeholder:"请选择关联知识库",clearable:"",multiple:""},{default:s(()=>[(m(!0),p(D,null,B(a(y).knowledge,i=>(m(),L(I,{key:i.id,label:`${i.name}`,value:String(i.id)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),t("div",ul,[l(d,{type:"primary",link:"",onClick:c},{default:s(()=>e[12]||(e[12]=[_(" 新增知识库 ")])),_:1}),e[14]||(e[14]=t("span",{class:"px-1"},"|",-1)),l(d,{type:"primary",link:"",onClick:a(g)},{default:s(()=>e[13]||(e[13]=[_(" 刷新 ")])),_:1},8,["onClick"])])]),e[15]||(e[15]=t("div",{class:"form-tips"},"需选择同一种训练模型的知识库",-1))])]),_:1}),l(f,{label:"角色设定",prop:"roles_prompt"},{default:s(()=>[t("div",rl,[t("div",ml,[t("div",pl,[l(C,{modelValue:a(n).roles_prompt,"onUpdate:modelValue":e[4]||(e[4]=i=>a(n).roles_prompt=i),placeholder:"请输入角色设定",type:"textarea",autosize:{minRows:4,maxRows:6},clearable:""},null,8,["modelValue"])]),t("div",_l,[l(sl,{onSelect:S})])]),e[16]||(e[16]=t("div",{class:"form-tips"},' 引导应用的聊天方向，该内容会被固定在上下文的开头。可点击"选择角色示例"快速填入预设的角色设定内容。 ',-1))])]),_:1}),l(f,{label:"对话图标"},{default:s(()=>[t("div",null,[l(U,{modelValue:a(n).icons,"onUpdate:modelValue":e[5]||(e[5]=i=>a(n).icons=i),"exclude-domain":!1,"can-close":!0},null,8,["modelValue"]),e[17]||(e[17]=t("div",{class:"form-tips"}," 不设置的话，对话图标默认拿智能体封面 ",-1))])]),_:1}),l(f,{label:"对话上下文",prop:"is_show_context"},{default:s(()=>[t("div",null,[l(u,{modelValue:a(n).is_show_context,"onUpdate:modelValue":e[6]||(e[6]=i=>a(n).is_show_context=i)},{default:s(()=>[l(o,{label:1},{default:s(()=>e[18]||(e[18]=[_(" 显示 ")])),_:1}),l(o,{label:0},{default:s(()=>e[19]||(e[19]=[_(" 关闭 ")])),_:1})]),_:1},8,["modelValue"]),e[20]||(e[20]=t("div",{class:"form-tips"},"在前台显示对话上下文，默认显示",-1))])]),_:1}),l(f,{label:"引用内容",prop:"is_show_quote"},{default:s(()=>[t("div",null,[l(u,{modelValue:a(n).is_show_quote,"onUpdate:modelValue":e[7]||(e[7]=i=>a(n).is_show_quote=i)},{default:s(()=>[l(o,{label:1},{default:s(()=>e[21]||(e[21]=[_(" 显示 ")])),_:1}),l(o,{label:0},{default:s(()=>e[22]||(e[22]=[_(" 关闭 ")])),_:1})]),_:1},8,["modelValue"]),e[23]||(e[23]=t("div",{class:"form-tips"},"在前台显示引用内容，默认显示",-1))])]),_:1}),l(f,{label:"反馈按钮",prop:"is_show_feedback"},{default:s(()=>[t("div",null,[l(u,{modelValue:a(n).is_show_feedback,"onUpdate:modelValue":e[8]||(e[8]=i=>a(n).is_show_feedback=i)},{default:s(()=>[l(o,{label:1},{default:s(()=>e[24]||(e[24]=[_(" 显示 ")])),_:1}),l(o,{label:0},{default:s(()=>e[25]||(e[25]=[_(" 关闭 ")])),_:1})]),_:1},8,["modelValue"]),e[26]||(e[26]=t("div",{class:"form-tips"},"在前台显示引用内容，默认显示",-1))])]),_:1}),l(f,{label:"问答相似问题推荐",prop:"related_issues_num",required:""},{default:s(()=>[t("div",null,[t("div",cl,[l(w,{modelValue:a(n).related_issues_num,"onUpdate:modelValue":e[9]||(e[9]=i=>a(n).related_issues_num=i),min:0,max:10},null,8,["modelValue"]),l(z,{modelValue:a(n).related_issues_num,"onUpdate:modelValue":e[10]||(e[10]=i=>a(n).related_issues_num=i),min:0,max:10,class:"ml-4 w-[180px]"},null,8,["modelValue"])]),e[27]||(e[27]=t("div",{class:"form-tips"},"作用于智能体对话问题推荐，填0对话问题推荐不生效",-1))])]),_:1}),b.value?(m(),L(ve,{key:0,ref_key:"addkbPopRef",ref:$,onSuccess:a(g)},null,8,["onSuccess"])):E("",!0)])}}});export{Nl as _};
