import{_ as d}from"./DzWd4x2z.js";import{_}from"./DCg4AEg6.js";import{a as c,_ as u}from"./ClNUxNV9.js";import{u as f}from"./CmPjtti1.js";import{l as x,m as h,M as C,N as b,Z as i,a0 as v,O as e,u as o}from"./Dp9aCaJ6.js";import"./DH3BuQAR.js";import"./B6IIPh85.js";import"./DlAUqK2U.js";/* empty css        */import"./BNsSO7uz.js";import"./DTSvt-82.js";import"./CVgoAVKM.js";import"./B1qK8f0i.js";/* empty css        */import"./hx-0JZAY.js";import"./CNgDMrD1.js";import"./zRTrVFrw.js";import"./D4cQUBDp.js";import"./scKBn7ss.js";/* empty css        */import"./mW1R_rAi.js";import"./Cga6GjQY.js";import"./Cpg3PDWZ.js";import"./C0R3uvOr.js";import"./9Bti1uB6.js";import"./DCTLXrZ8.js";/* empty css        */import"./BviKld3d.js";import"./Bb2-23m7.js";import"./B2UFrjaV.js";import"./DwFObZc_.js";import"./B_1915px.js";import"./D0cj6m41.js";import"./Bg2e09vA.js";import"./CraaJdZW.js";import"./CqanTtdS.js";import"./CxSV922q.js";import"./CeZA--ML.js";import"./Cr7puf4F.js";import"./CQXeYJFv.js";import"./DMVjPTRc.js";import"./DiZwe6ND.js";import"./DDaEm-_F.js";import"./BnLJcfTV.js";import"./D8MEmB8I.js";import"./Bo3PTL3c.js";import"./qQcR97T8.js";import"./_E68iMkU.js";import"./C5YZ-9ot.js";const I={class:"h-full flex"},S={class:"flex h-full p-[16px]"},V={class:"h-full pr-[16px] py-[16px] flex-1 min-w-0"},k={class:"h-full flex flex-col bg-body rounded-lg"},Vo=x({__name:"chat",setup(y){const t=f(),m=c(),s=h(()=>m.query.id);return(E,r)=>{const p=d,n=_,l=u;return C(),b("div",null,[i(l,{name:"default"},{default:v(()=>[e("div",I,[e("div",S,[i(p,{modelValue:o(t).sessionId,"onUpdate:modelValue":r[0]||(r[0]=a=>o(t).sessionId=a),data:o(t).sessionLists,onAdd:o(t).sessionAdd,onEdit:o(t).sessionEdit,onDelete:o(t).sessionDelete,onClear:o(t).sessionClear,onClickItem:o(t).setSessionSelect},null,8,["modelValue","data","onAdd","onEdit","onDelete","onClear","onClickItem"])]),e("div",V,[e("div",k,[i(n,{"robot-id":o(s)},null,8,["robot-id"])])])])]),_:1})])}}});export{Vo as default};
