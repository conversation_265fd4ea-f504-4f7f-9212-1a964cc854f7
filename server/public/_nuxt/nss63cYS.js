import{_ as V}from"./D5KDMvDa.js";import{cz as v,E as X}from"./C3HqF-ve.js";import{E as j}from"./BbTp37Y3.js";import"./BPdEGeDo.js";import"./l0sNRNKZ.js";/* empty css        */import{b as K}from"./Bu_nKEGp.js";import{m as $,o as W,c as N,l as A,M as b,N as E,O as o,u as t,_ as F,a7 as C,a4 as z,Z as _,a0 as S}from"./Dp9aCaJ6.js";import{_ as G}from"./DlAUqK2U.js";const U=e=>{let n=parseInt(String(e/60)),a=Math.round(e%60)+"";const c=":";return n==0?n="00":n<10&&(n="0"+n),a.length==1&&(a="0"+a),n+c+a},J=()=>{const e=v(()=>new Audio,"$DJSMRhR2S7"),n=v(()=>0,"$GS6UDXwhYk"),a=v(()=>0,"$PUrgbX7hfL"),c=v(()=>!1,"$0vj7SXLRdo"),d=v(()=>new Map,"$8PwyUKyd2j"),p=v(()=>-1,"$1LKcW0IhcI"),l=v(()=>[],"$ff4BoCp0CW"),i=async()=>{const s=await K({page_type:0,status:2});d.value.clear(),s.lists.forEach((r,u)=>{d.value.set(r.id,{...r,index:u})}),l.value=s.lists},k=s=>{d.value.clear(),s.forEach((r,u)=>{d.value.set(r.id,{...r,index:u})}),l.value=s},I=()=>{e.value.paused||(c.value=!0)},M=()=>{c.value?g():x()},g=()=>{e.value.pause()},x=()=>{e.value.play()},f=()=>{c.value=!1},B=()=>{f()},m=()=>{a.value=e.value.duration},y=()=>{n.value=e.value.currentTime},h=s=>{e.value.currentTime=s},w=s=>{p.value=s,setTimeout(()=>{x()})},T=s=>{let r=L.value.index;r===void 0&&(r=0);let u=r+s;u<=0&&(u=0),u>=l.value.length-1&&(u=l.value.length-1),c.value=!1;const R=l.value[u];w(R.id)},D=$(()=>U(a.value)),O=$(()=>U(n.value)),L=$(()=>d.value.get(p.value)||{});W(()=>{g(),f(),a.value=0,e.value.src=""}),e.value.onplay||(e.value.onloadedmetadata=m,e.value.onplay=I,e.value.onpause=f,e.value.ontimeupdate=y,e.value.onseeking=y,e.value.onended=f,e.value.onerror=B);const P=()=>{const s=d.value.get(p.value);s&&e.value.src!==s.audio_url&&(e.value.src=s.audio_url)};return N(p,s=>{s==-1?(e.value.src="",a.value=0):P()}),N(l,P),{audioCtx:e,currentTime:n,duration:a,durationTrans:D,currentTimeTrans:O,currentId:p,currentMusic:L,playing:c,musicList:l,pause:g,play:x,getMusic:i,setMusic:k,setCurrentTime:h,prevOrNext:T,togglePlay:M,setCurrentId:w}},Y={class:"flex items-center p-[13px]"},Z=["src"],q={class:"flex-1 min-w-0 ml-[15px]"},H={class:"font-bold line-clamp-1"},Q={key:0,class:"line-clamp-1 text-tx-secondary text-sm"},ee={class:"mx-[20px] flex items-center"},te={class:"text-tx-regular"},se={class:"mx-[20px]"},ne={class:"text-tx-regular"},ae={class:"flex items-center text-tx-secondary mx-[30px]"},oe={class:"w-[50px]"},le={class:"mx-[20px] w-[250px]"},ie={class:"w-[50px]"},ce=A({__name:"player",emits:["title"],setup(e,{emit:n}){const a=n,{playing:c,duration:d,musicList:p,currentId:l,currentMusic:i,currentTime:k,currentTimeTrans:I,setCurrentTime:M,durationTrans:g,prevOrNext:x,togglePlay:f}=J();return(B,m)=>{const y=V,h=X,w=j;return b(),E("div",Y,[o("div",{class:"flex items-center w-[200px] h-[55px] cursor-pointer",onClick:m[0]||(m[0]=T=>a("title",t(i)))},[t(i).title?(b(),E(F,{key:0},[o("img",{src:t(i).image_url,class:"w-[55px] h-full rounded-[8px]"},null,8,Z),o("div",q,[o("div",H,C(t(i).title),1),t(i).style_desc?(b(),E("div",Q,C(t(i).style_desc),1)):z("",!0)])],64)):z("",!0)]),o("div",ee,[o("div",te,[_(h,{link:"",disabled:t(l)<=0||t(i).index<=0,onClick:m[1]||(m[1]=T=>t(x)(-1))},{icon:S(()=>[_(y,{name:"local-icon-up",size:24})]),_:1},8,["disabled"])]),o("div",se,[_(h,{type:"primary",circle:"",onClick:t(f),disabled:t(l)<=0},{icon:S(()=>[_(y,{name:`local-icon-${t(c)?"pause1":"play"}`,size:14},null,8,["name"])]),_:1},8,["onClick","disabled"])]),o("div",ne,[_(h,{link:"",disabled:t(l)<=0||t(i).index>=t(p).length-1,onClick:m[2]||(m[2]=T=>t(x)(1))},{icon:S(()=>[_(y,{name:"local-icon-down",size:24})]),_:1},8,["disabled"])])]),o("div",ae,[o("div",oe,C(t(I)),1),o("div",le,[_(w,{"model-value":t(k),disabled:!t(i).audio_url,size:"small","show-tooltip":!1,min:0,max:t(d),onInput:t(M)},null,8,["model-value","disabled","max","onInput"])]),o("div",ie,C(t(g)),1)])])}}}),ye=G(ce,[["__scopeId","data-v-53183250"]]);export{ye as _,J as u};
