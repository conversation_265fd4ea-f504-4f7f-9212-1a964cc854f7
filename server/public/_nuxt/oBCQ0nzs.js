import{E as c}from"./EetTZpAI.js";import{E as w}from"./DuMn-nmf.js";import{h as V,o as g,e as x}from"./D726nzJl.js";import"./DP2rzg_V.js";import{l as b,M as u,N as d,Z as e,a0 as n,O as p,u as t,_ as k,a4 as I}from"./Dp9aCaJ6.js";import{_ as v}from"./DlAUqK2U.js";const D={class:"flow-config pt-[10px]"},E=b({__name:"flow-config",props:{modelValue:{}},emits:["update:modelValue"],setup(f,{emit:m}){const l=V(f,"modelValue",m);return(C,o)=>{const _=c,r=w,i=g,s=x;return u(),d("div",D,[e(_,{title:"注意事项",type:"warning",closable:!1,class:"mb-[20px]"},{default:n(()=>o[5]||(o[5]=[p("ol",{class:"list-decimal pl-[20px]"},[p("li",null,"coze工作流配置地址：https://www.coze.cn （工作空间->资源库）"),p("li",null,"启用后对话时将使用coze工作流作为响应结果，相似问题输出仍使用AI模型输出"),p("li",null,"coze工作流设置时输入变量名需为“input”,输出变量名需为“output_text”或“output_image”"),p("li",null,"输出变量“output_text”对应为输出文本内容，输出变量“output_image”对应为输出图片资源，目前仅支持单图")],-1)])),_:1}),e(i,{label:"启用 coze 工作流",prop:"flow_status"},{default:n(()=>[p("div",null,[e(r,{modelValue:t(l).flow_status,"onUpdate:modelValue":o[0]||(o[0]=a=>t(l).flow_status=a),"active-value":1,"inactive-value":0,"inline-prompt":"","active-text":"开","inactive-text":"关"},null,8,["modelValue"])])]),_:1}),t(l).flow_status===1?(u(),d(k,{key:0},[e(i,{label:"应用 ID",prop:"flow_config.app_id"},{default:n(()=>[e(s,{class:"!w-[320px]",modelValue:t(l).flow_config.app_id,"onUpdate:modelValue":o[1]||(o[1]=a=>t(l).flow_config.app_id=a),placeholder:"请输入应用 ID"},null,8,["modelValue"])]),_:1}),e(i,{label:"工作流 ID",prop:"flow_config.workflow_id"},{default:n(()=>[e(s,{class:"!w-[320px]",modelValue:t(l).flow_config.workflow_id,"onUpdate:modelValue":o[2]||(o[2]=a=>t(l).flow_config.workflow_id=a),placeholder:"请输入工作流 ID"},null,8,["modelValue"])]),_:1}),e(i,{label:"智能体 ID",prop:"flow_config.bot_id"},{default:n(()=>[e(s,{class:"!w-[320px]",modelValue:t(l).flow_config.bot_id,"onUpdate:modelValue":o[3]||(o[3]=a=>t(l).flow_config.bot_id=a),placeholder:"请输入智能体 ID"},null,8,["modelValue"])]),_:1}),e(i,{label:"token",prop:"flow_config.api_token"},{default:n(()=>[e(s,{class:"!w-[320px]",modelValue:t(l).flow_config.api_token,"onUpdate:modelValue":o[4]||(o[4]=a=>t(l).flow_config.api_token=a),placeholder:"token"},null,8,["modelValue"])]),_:1})],64)):I("",!0)])}}}),O=v(E,[["__scopeId","data-v-bdeafcb5"]]);export{O as default};
