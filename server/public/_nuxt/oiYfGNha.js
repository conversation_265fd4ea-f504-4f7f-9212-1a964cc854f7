import{K as D,X as Z,a4 as le,U as J,M as I,a0 as oe,O as re,cj as ge,ck as Ne,J as F,a9 as Te,aa as Pe,T as Ce,af as L,aX as $e,ap as ie,aT as ae,Z as we,cl as se,P as Se,Q as Ee}from"./DGzblORL.js";import{c as k}from"./HrsfEhzV.js";import{l as A,i as Q,b as g,c as z,n as G,M as ce,N as ue,X as de,u as x,a2 as xe,a as K,m as V,F as be,Y as Be,Z as b,q as ke,V as Y,H as Oe,$ as Re,r as ze,k as Ae,ab as Me,ac as Fe,a7 as Le}from"./uahP8ofS.js";import{u as Ve}from"./BDY5qoU8.js";const U=Symbol("tabsRootContextKey"),De=D({tabs:{type:Z(<PERSON>rray),default:()=>le([])}}),fe="ElTabBar",Ie=A({name:fe}),Ke=A({...Ie,props:De,setup(e,{expose:o}){const m=e,O=K(),c=Q(U);c||J(fe,"<el-tabs><el-tab-bar /></el-tabs>");const t=I("tabs"),$=g(),B=g(),u=()=>{let i=0,P=0;const d=["top","bottom"].includes(c.props.tabPosition)?"width":"height",n=d==="width"?"x":"y",E=n==="x"?"left":"top";return m.tabs.every(a=>{var v,C;const N=(C=(v=O.parent)==null?void 0:v.refs)==null?void 0:C[`tab-${a.uid}`];if(!N)return!1;if(!a.active)return!0;i=N[`offset${k(E)}`],P=N[`client${k(d)}`];const w=window.getComputedStyle(N);return d==="width"&&(m.tabs.length>1&&(P-=Number.parseFloat(w.paddingLeft)+Number.parseFloat(w.paddingRight)),i+=Number.parseFloat(w.paddingLeft)),!1}),{[d]:`${P}px`,transform:`translate${k(n)}(${i}px)`}},f=()=>B.value=u();return z(()=>m.tabs,async()=>{await G(),f()},{immediate:!0}),oe($,()=>f()),o({ref:$,update:f}),(i,P)=>(ce(),ue("div",{ref_key:"barRef",ref:$,class:de([x(t).e("active-bar"),x(t).is(x(c).props.tabPosition)]),style:xe(B.value)},null,6))}});var Ue=re(Ke,[["__file","tab-bar.vue"]]);const Xe=D({panes:{type:Z(Array),default:()=>le([])},currentName:{type:[String,Number],default:""},editable:Boolean,type:{type:String,values:["card","border-card",""],default:""},stretch:Boolean}),qe={tabClick:(e,o,m)=>m instanceof Event,tabRemove:(e,o)=>o instanceof Event},ne="ElTabNav",He=A({name:ne,props:Xe,emits:qe,setup(e,{expose:o,emit:m}){const O=K(),c=Q(U);c||J(ne,"<el-tabs><tab-nav /></el-tabs>");const t=I("tabs"),$=ge(),B=Ne(),u=g(),f=g(),i=g(),P=g(),d=g(!1),n=g(0),E=g(!1),a=g(!0),v=V(()=>["top","bottom"].includes(c.props.tabPosition)?"width":"height"),C=V(()=>({transform:`translate${v.value==="width"?"X":"Y"}(-${n.value}px)`})),N=()=>{if(!u.value)return;const l=u.value[`offset${k(v.value)}`],r=n.value;if(!r)return;const s=r>l?r-l:0;n.value=s},w=()=>{if(!u.value||!f.value)return;const l=f.value[`offset${k(v.value)}`],r=u.value[`offset${k(v.value)}`],s=n.value;if(l-s<=r)return;const y=l-s>r*2?s+r:l-r;n.value=y},M=async()=>{const l=f.value;if(!d.value||!i.value||!u.value||!l)return;await G();const r=i.value.querySelector(".is-active");if(!r)return;const s=u.value,y=["top","bottom"].includes(c.props.tabPosition),_=r.getBoundingClientRect(),h=s.getBoundingClientRect(),S=y?l.offsetWidth-h.width:l.offsetHeight-h.height,T=n.value;let p=T;y?(_.left<h.left&&(p=T-(h.left-_.left)),_.right>h.right&&(p=T+_.right-h.right)):(_.top<h.top&&(p=T-(h.top-_.top)),_.bottom>h.bottom&&(p=T+(_.bottom-h.bottom))),p=Math.max(p,0),n.value=Math.min(p,S)},ee=()=>{var l;if(!f.value||!u.value)return;e.stretch&&((l=P.value)==null||l.update());const r=f.value[`offset${k(v.value)}`],s=u.value[`offset${k(v.value)}`],y=n.value;s<r?(d.value=d.value||{},d.value.prev=y,d.value.next=y+s<r,r-y<s&&(n.value=r-s)):(d.value=!1,y>0&&(n.value=0))},me=l=>{const r=l.code,{up:s,down:y,left:_,right:h}=L;if(![s,y,_,h].includes(r))return;const S=Array.from(l.currentTarget.querySelectorAll("[role=tab]:not(.is-disabled)")),T=S.indexOf(l.target);let p;r===_||r===s?T===0?p=S.length-1:p=T-1:T<S.length-1?p=T+1:p=0,S[p].focus({preventScroll:!0}),S[p].click(),te()},te=()=>{a.value&&(E.value=!0)},X=()=>E.value=!1;return z($,l=>{l==="hidden"?a.value=!1:l==="visible"&&setTimeout(()=>a.value=!0,50)}),z(B,l=>{l?setTimeout(()=>a.value=!0,50):a.value=!1}),oe(i,ee),be(()=>setTimeout(()=>M(),0)),Be(()=>ee()),o({scrollToActiveTab:M,removeFocus:X}),z(()=>e.panes,()=>O.update(),{flush:"post",deep:!0}),()=>{const l=d.value?[b("span",{class:[t.e("nav-prev"),t.is("disabled",!d.value.prev)],onClick:N},[b(F,null,{default:()=>[b(Te,null,null)]})]),b("span",{class:[t.e("nav-next"),t.is("disabled",!d.value.next)],onClick:w},[b(F,null,{default:()=>[b(Pe,null,null)]})])]:null,r=e.panes.map((s,y)=>{var _,h,S,T;const p=s.uid,q=s.props.disabled,H=(h=(_=s.props.name)!=null?_:s.index)!=null?h:`${y}`,j=!q&&(s.isClosable||e.editable);s.index=`${y}`;const he=j?b(F,{class:"is-icon-close",onClick:R=>m("tabRemove",s,R)},{default:()=>[b(Ce,null,null)]}):null,ye=((T=(S=s.slots).label)==null?void 0:T.call(S))||s.props.label,_e=!q&&s.active?0:-1;return b("div",{ref:`tab-${p}`,class:[t.e("item"),t.is(c.props.tabPosition),t.is("active",s.active),t.is("disabled",q),t.is("closable",j),t.is("focus",E.value)],id:`tab-${H}`,key:`tab-${p}`,"aria-controls":`pane-${H}`,role:"tab","aria-selected":s.active,tabindex:_e,onFocus:()=>te(),onBlur:()=>X(),onClick:R=>{X(),m("tabClick",s,H,R)},onKeydown:R=>{j&&(R.code===L.delete||R.code===L.backspace)&&m("tabRemove",s,R)}},[ye,he])});return b("div",{ref:i,class:[t.e("nav-wrap"),t.is("scrollable",!!d.value),t.is(c.props.tabPosition)]},[l,b("div",{class:t.e("nav-scroll"),ref:u},[b("div",{class:[t.e("nav"),t.is(c.props.tabPosition),t.is("stretch",e.stretch&&["top","bottom"].includes(c.props.tabPosition))],ref:f,style:C.value,role:"tablist",onKeydown:me},[e.type?null:b(Ue,{ref:P,tabs:[...e.panes]},null),r])])])}}}),je=D({type:{type:String,values:["card","border-card",""],default:""},closable:Boolean,addable:Boolean,modelValue:{type:[String,Number]},editable:Boolean,tabPosition:{type:String,values:["top","right","bottom","left"],default:"top"},beforeLeave:{type:Z(Function),default:()=>!0},stretch:Boolean}),W=e=>Oe(e)||we(e),We={[ie]:e=>W(e),tabClick:(e,o)=>o instanceof Event,tabChange:e=>W(e),edit:(e,o)=>["remove","add"].includes(o),tabRemove:e=>W(e),tabAdd:()=>!0},Ye=A({name:"ElTabs",props:je,emits:We,setup(e,{emit:o,slots:m,expose:O}){var c;const t=I("tabs"),{children:$,addChild:B,removeChild:u}=Ve(K(),"ElTabPane"),f=g(),i=g((c=e.modelValue)!=null?c:"0"),P=async(a,v=!1)=>{var C,N,w;if(!(i.value===a||ae(a)))try{await((C=e.beforeLeave)==null?void 0:C.call(e,a,i.value))!==!1&&(i.value=a,v&&(o(ie,a),o("tabChange",a)),(w=(N=f.value)==null?void 0:N.removeFocus)==null||w.call(N))}catch{}},d=(a,v,C)=>{a.props.disabled||(P(v,!0),o("tabClick",a,C))},n=(a,v)=>{a.props.disabled||ae(a.props.name)||(v.stopPropagation(),o("edit",a.props.name,"remove"),o("tabRemove",a.props.name))},E=()=>{o("edit",void 0,"add"),o("tabAdd")};return z(()=>e.modelValue,a=>P(a)),z(i,async()=>{var a;await G(),(a=f.value)==null||a.scrollToActiveTab()}),ke(U,{props:e,currentName:i,registerPane:B,unregisterPane:u}),O({currentName:i}),()=>{const a=m["add-icon"],v=e.editable||e.addable?b("span",{class:t.e("new-tab"),tabindex:"0",onClick:E,onKeydown:w=>{w.code===L.enter&&E()}},[a?Y(m,"add-icon"):b(F,{class:t.is("icon-plus")},{default:()=>[b($e,null,null)]})]):null,C=b("div",{class:[t.e("header"),t.is(e.tabPosition)]},[v,b(He,{ref:f,currentName:i.value,editable:e.editable,type:e.type,panes:$.value,stretch:e.stretch,onTabClick:d,onTabRemove:n},null)]),N=b("div",{class:t.e("content")},[Y(m,"default")]);return b("div",{class:[t.b(),t.m(e.tabPosition),{[t.m("card")]:e.type==="card",[t.m("border-card")]:e.type==="border-card"}]},[...e.tabPosition!=="bottom"?[C,N]:[N,C]])}}}),Ze=D({label:{type:String,default:""},name:{type:[String,Number]},closable:Boolean,disabled:Boolean,lazy:Boolean}),Je=["id","aria-hidden","aria-labelledby"],ve="ElTabPane",Qe=A({name:ve}),Ge=A({...Qe,props:Ze,setup(e){const o=e,m=K(),O=Re(),c=Q(U);c||J(ve,"usage: <el-tabs><el-tab-pane /></el-tabs/>");const t=I("tab-pane"),$=g(),B=V(()=>o.closable||c.props.closable),u=se(()=>{var n;return c.currentName.value===((n=o.name)!=null?n:$.value)}),f=g(u.value),i=V(()=>{var n;return(n=o.name)!=null?n:$.value}),P=se(()=>!o.lazy||f.value||u.value);z(u,n=>{n&&(f.value=!0)});const d=ze({uid:m.uid,slots:O,props:o,paneName:i,active:u,index:$,isClosable:B});return be(()=>{c.registerPane(d)}),Ae(()=>{c.unregisterPane(d.uid)}),(n,E)=>x(P)?Me((ce(),ue("div",{key:0,id:`pane-${x(i)}`,class:de(x(t).b()),role:"tabpanel","aria-hidden":!x(u),"aria-labelledby":`tab-${x(i)}`},[Y(n.$slots,"default")],10,Je)),[[Fe,x(u)]]):Le("v-if",!0)}});var pe=re(Ge,[["__file","tab-pane.vue"]]);const nt=Se(Ye,{TabPane:pe}),lt=Ee(pe);export{nt as E,lt as a};
