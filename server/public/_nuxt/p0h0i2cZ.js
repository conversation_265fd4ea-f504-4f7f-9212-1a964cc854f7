import{l as h,$ as N,b as V,m as p,M as a,a3 as l,a0 as u,u as e,aa as $,ab as D,ac as I,O as k,X as o,a6 as M,a7 as n,N as i,V as C,a4 as b,a5 as f,Z as A,_ as O}from"./uahP8ofS.js";import{K as P,ag as z,ct as S,M as F,J as g,O as J,cu as K,P as X}from"./DGzblORL.js";const Z=["light","dark"],_=P({title:{type:String,default:""},description:{type:String,default:""},type:{type:String,values:z(S),default:"info"},closable:{type:Boolean,default:!0},closeText:{type:String,default:""},showIcon:Boolean,center:Boolean,effect:{type:String,values:Z,default:"light"}}),j={close:r=>r instanceof MouseEvent},q=h({name:"<PERSON><PERSON><PERSON><PERSON>"}),G=h({...q,props:_,emits:j,setup(r,{emit:w}){const c=r,{Close:E}=K,d=N(),t=F("alert"),m=V(!0),y=p(()=>S[c.type]),B=p(()=>[t.e("icon"),{[t.is("big")]:!!c.description||!!d.default}]),T=p(()=>({"with-description":c.description||d.default})),v=s=>{m.value=!1,w("close",s)};return(s,L)=>(a(),l($,{name:e(t).b("fade"),persisted:""},{default:u(()=>[D(k("div",{class:o([e(t).b(),e(t).m(s.type),e(t).is("center",s.center),e(t).is(s.effect)]),role:"alert"},[s.showIcon&&e(y)?(a(),l(e(g),{key:0,class:o(e(B))},{default:u(()=>[(a(),l(M(e(y))))]),_:1},8,["class"])):n("v-if",!0),k("div",{class:o(e(t).e("content"))},[s.title||s.$slots.title?(a(),i("span",{key:0,class:o([e(t).e("title"),e(T)])},[C(s.$slots,"title",{},()=>[b(f(s.title),1)])],2)):n("v-if",!0),s.$slots.default||s.description?(a(),i("p",{key:1,class:o(e(t).e("description"))},[C(s.$slots,"default",{},()=>[b(f(s.description),1)])],2)):n("v-if",!0),s.closable?(a(),i(O,{key:2},[s.closeText?(a(),i("div",{key:0,class:o([e(t).e("close-btn"),e(t).is("customed")]),onClick:v},f(s.closeText),3)):(a(),l(e(g),{key:1,class:o(e(t).e("close-btn")),onClick:v},{default:u(()=>[A(e(E))]),_:1},8,["class"]))],64)):n("v-if",!0)],2)],2),[[I,m.value]])]),_:3},8,["name"]))}});var H=J(G,[["__file","alert.vue"]]);const U=X(H);export{U as E};
