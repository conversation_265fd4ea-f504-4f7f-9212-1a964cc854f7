import{E as p}from"./CUc0yGc4.js";import{co as d}from"./D726nzJl.js";import{l as u,M as i,N as c,Z as f,u as g,y as _}from"./Dp9aCaJ6.js";const b={class:"p-1 bg-[var(--el-bg-color-page)] rounded-[12px]",style:{"--el-border-radius-base":"12px"}},k=u({__name:"draw-type",props:{modelValue:{default:"txt2img"}},emits:["update:modelValue"],setup(l,{emit:t}){const a=t,s=l,{modelValue:e}=d(s,a),n=[{label:"文生图",value:"txt2img"},{label:"图生图",value:"img2img"}];return(V,o)=>{const m=p;return i(),c("div",b,[f(m,{block:!1,class:"w-full h-[36px] !bg-[transparent]",modelValue:g(e),"onUpdate:modelValue":o[0]||(o[0]=r=>_(e)?e.value=r:null),options:n},null,8,["modelValue"])])}}});export{k as _};
