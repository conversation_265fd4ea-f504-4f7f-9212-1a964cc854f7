import{h as q,e as U,o as D,p as R,E as g}from"./Ct33iMSA.js";import{_ as $}from"./DvrbA4QQ.js";import{_ as z}from"./BOmaQ_iX.js";import"./DP2rzg_V.js";/* empty css        */import{P as F}from"./kzeq9hfp.js";import{l as I,j as f,M as p,a1 as h,a0 as i,Z as s,u as o,a4 as m,N as V,_ as w}from"./Dp9aCaJ6.js";import"./DlAUqK2U.js";import"./5L1sP24H.js";import"./BuZ1OskF.js";import"./CL661lr-.js";import"./DDmMBU6l.js";import"./SbrOwfyf.js";import"./9Bti1uB6.js";import"./DCTLXrZ8.js";/* empty css        */const te=I({__name:"edit-qa",props:{modelValue:{},title:{},disabled:{type:Boolean,default:!1}},emits:["update:modelValue","confirm"],setup(x,{expose:y,emit:v}){const N=x,d=v,_=f(),c=f(),e=q(N,"modelValue",d),b={question:[{validator(t,l,a,r,u){l?a():e.value.type===1?a("请输入内容"):e.value.type===2&&a("请输入问题")}}],answer:[{validator(t,l,a,r,u){l?a():a("请输入答案")}}]},E=()=>{var t;(t=c.value)==null||t.open()},k=()=>{var t;(t=c.value)==null||t.close()},C=async()=>{var t;await((t=_.value)==null?void 0:t.validate()),d("confirm")};return y({open:E,close:k}),(t,l)=>{const a=U,r=D,u=$,P=z,M=g,B=R;return p(),h(F,{ref_key:"popupRef",ref:c,title:t.title,width:"800px","destroy-on-close":!0,async:"",onConfirm:C},{default:i(()=>[s(B,{ref_key:"formRef",ref:_,model:o(e),rules:b,"label-width":"100px",disabled:t.disabled},{default:i(()=>[o(e).type===1?(p(),h(r,{key:0,label:"内容",prop:"question"},{default:i(()=>[s(a,{modelValue:o(e).question,"onUpdate:modelValue":l[0]||(l[0]=n=>o(e).question=n),placeholder:"请输入内容",type:"textarea",resize:"none",rows:20,clearable:""},null,8,["modelValue"])]),_:1})):m("",!0),o(e).type===2?(p(),V(w,{key:1},[s(r,{label:"提问问题",prop:"question"},{default:i(()=>[s(a,{modelValue:o(e).question,"onUpdate:modelValue":l[1]||(l[1]=n=>o(e).question=n),placeholder:"请输入问题",type:"textarea",resize:"none",rows:6,maxlength:"600","show-word-limit":"",clearable:""},null,8,["modelValue"])]),_:1}),s(r,{label:"问题答案",prop:"answer"},{default:i(()=>[s(a,{modelValue:o(e).answer,"onUpdate:modelValue":l[2]||(l[2]=n=>o(e).answer=n),placeholder:"请输入答案",type:"textarea",resize:"none",rows:20,clearable:""},null,8,["modelValue"])]),_:1}),o(e).type===2&&o(e).method===1?(p(),V(w,{key:0},[m("",!0),m("",!0)],64)):m("",!0)],64)):m("",!0)]),_:1},8,["model","disabled"])]),_:1},8,["title"])}}});export{te as default};
