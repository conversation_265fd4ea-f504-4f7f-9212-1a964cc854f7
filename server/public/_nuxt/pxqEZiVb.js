import{_}from"./BpYIl71c.js";import{h as u,e as f,o as x,p as V}from"./D726nzJl.js";import"./DP2rzg_V.js";/* empty css        */import{l as v,M as E,N as h,O as e,Z as t,a0 as n,u as a}from"./Dp9aCaJ6.js";import{_ as I}from"./DlAUqK2U.js";const F={class:"manual-import"},w={class:"important-notice"},B={class:"notice-header"},N={class:"py-4 flex flex-col"},g={class:"flex-1 min-w-0"},k=v({__name:"manual-doc",props:{modelValue:{}},emits:["update:modelValue"],setup(l,{emit:m}){const s=u(l,"modelValue",m);return(z,o)=>{const i=_,c=f,r=x,p=V;return E(),h("div",F,[e("div",w,[e("div",B,[t(i,{name:"el-icon-Warning",color:"#ff9900",size:"18"}),o[1]||(o[1]=e("span",{class:"notice-title"},"重要提示",-1))]),o[2]||(o[2]=e("div",{class:"notice-content"}," 为保护您的信息安全，请勿录入如姓名、身份证号、电话等个人隐私信息及任何工作秘密、商业秘密。本平台在未获得您同意的情况下，不会使用您的数据进行模型训练。 ",-1))]),e("div",N,[t(p,null,{default:n(()=>[t(r,null,{default:n(()=>[e("div",g,[t(c,{modelValue:a(s).question,"onUpdate:modelValue":o[0]||(o[0]=d=>a(s).question=d),placeholder:"请输入文本内容，10000个字以内。",type:"textarea",resize:"none",rows:15,maxlength:"10000"},null,8,["modelValue"])])]),_:1})]),_:1})])])}}}),Z=I(k,[["__scopeId","data-v-15e0d1c8"]]);export{Z as default};
