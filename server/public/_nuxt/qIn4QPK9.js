import{E as m}from"./B7GyCei5.js";import{cn as u}from"./DGzblORL.js";import{l as d,M as c,N as i,Z as f,u as _,y as b}from"./uahP8ofS.js";const V={class:"p-1 bg-[var(--el-bg-color-page)] rounded-[12px]",style:{"--el-border-radius-base":"12px"}},k=d({__name:"video-type",props:{modelValue:{default:1}},emits:["update:modelValue"],setup(l,{emit:s}){const t=s,a=l,{modelValue:e}=u(a,t),n=[{label:"文生视频",value:1},{label:"图生视频",value:2}];return(g,o)=>{const p=m;return c(),i("div",V,[f(p,{block:!1,class:"w-full h-[36px] !bg-[transparent]",modelValue:_(e),"onUpdate:modelValue":o[0]||(o[0]=r=>b(e)?e.value=r:null),options:n},null,8,["modelValue"])])}}});export{k as _};
