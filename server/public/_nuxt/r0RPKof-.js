import{h as Ht,o as Wt,e as Gt,E as Lt}from"./C12kmceL.js";import{E as zt}from"./BQyknIqG.js";import{_ as Vt}from"./BAooD3NP.js";import{a as jt,E as Ut}from"./BfDOk1wq.js";import"./DP2rzg_V.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./l0sNRNKZ.js";/* empty css        */import{_ as $t}from"./DbvtO8_t.js";import{c as qt}from"./Cr9cZ-Xs.js";import{l as Zt,r as Kt,j as Qt,F as Jt,n as mt,M as Me,N as Fe,Z as I,a0 as H,u as j,O as z,a4 as se,a7 as Ke}from"./uahP8ofS.js";const en=""+new URL("robot_copyright.B2pMs7mY.png",import.meta.url).href;/**!
 * Sortable 1.15.0
 * <AUTHOR>   <<EMAIL>>
 * <AUTHOR>    <<EMAIL>>
 * @license MIT
 */function gt(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(n);e&&(o=o.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),t.push.apply(t,o)}return t}function Z(n){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?gt(Object(t),!0).forEach(function(o){tn(n,o,t[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(t)):gt(Object(t)).forEach(function(o){Object.defineProperty(n,o,Object.getOwnPropertyDescriptor(t,o))})}return n}function He(n){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?He=function(e){return typeof e}:He=function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},He(n)}function tn(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function J(){return J=Object.assign||function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(n[o]=t[o])}return n},J.apply(this,arguments)}function nn(n,e){if(n==null)return{};var t={},o=Object.keys(n),i,r;for(r=0;r<o.length;r++)i=o[r],!(e.indexOf(i)>=0)&&(t[i]=n[i]);return t}function on(n,e){if(n==null)return{};var t=nn(n,e),o,i;if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);for(i=0;i<r.length;i++)o=r[i],!(e.indexOf(o)>=0)&&Object.prototype.propertyIsEnumerable.call(n,o)&&(t[o]=n[o])}return t}var rn="1.15.0";function Q(n){if(typeof window<"u"&&window.navigator)return!!navigator.userAgent.match(n)}var ee=Q(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),Pe=Q(/Edge/i),vt=Q(/firefox/i),Ce=Q(/safari/i)&&!Q(/chrome/i)&&!Q(/android/i),Tt=Q(/iP(ad|od|hone)/i),Ct=Q(/chrome/i)&&Q(/android/i),Ot={capture:!1,passive:!1};function _(n,e,t){n.addEventListener(e,t,!ee&&Ot)}function y(n,e,t){n.removeEventListener(e,t,!ee&&Ot)}function Ve(n,e){if(e){if(e[0]===">"&&(e=e.substring(1)),n)try{if(n.matches)return n.matches(e);if(n.msMatchesSelector)return n.msMatchesSelector(e);if(n.webkitMatchesSelector)return n.webkitMatchesSelector(e)}catch{return!1}return!1}}function an(n){return n.host&&n!==document&&n.host.nodeType?n.host:n.parentNode}function $(n,e,t,o){if(n){t=t||document;do{if(e!=null&&(e[0]===">"?n.parentNode===t&&Ve(n,e):Ve(n,e))||o&&n===t)return n;if(n===t)break}while(n=an(n))}return null}var bt=/\s+/g;function W(n,e,t){if(n&&e)if(n.classList)n.classList[t?"add":"remove"](e);else{var o=(" "+n.className+" ").replace(bt," ").replace(" "+e+" "," ");n.className=(o+(t?" "+e:"")).replace(bt," ")}}function p(n,e,t){var o=n&&n.style;if(o){if(t===void 0)return document.defaultView&&document.defaultView.getComputedStyle?t=document.defaultView.getComputedStyle(n,""):n.currentStyle&&(t=n.currentStyle),e===void 0?t:t[e];!(e in o)&&e.indexOf("webkit")===-1&&(e="-webkit-"+e),o[e]=t+(typeof t=="string"?"":"px")}}function ge(n,e){var t="";if(typeof n=="string")t=n;else do{var o=p(n,"transform");o&&o!=="none"&&(t=o+" "+t)}while(!e&&(n=n.parentNode));var i=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return i&&new i(t)}function xt(n,e,t){if(n){var o=n.getElementsByTagName(e),i=0,r=o.length;if(t)for(;i<r;i++)t(o[i],i);return o}return[]}function q(){var n=document.scrollingElement;return n||document.documentElement}function x(n,e,t,o,i){if(!(!n.getBoundingClientRect&&n!==window)){var r,a,l,s,u,h,c;if(n!==window&&n.parentNode&&n!==q()?(r=n.getBoundingClientRect(),a=r.top,l=r.left,s=r.bottom,u=r.right,h=r.height,c=r.width):(a=0,l=0,s=window.innerHeight,u=window.innerWidth,h=window.innerHeight,c=window.innerWidth),(e||t)&&n!==window&&(i=i||n.parentNode,!ee))do if(i&&i.getBoundingClientRect&&(p(i,"transform")!=="none"||t&&p(i,"position")!=="static")){var f=i.getBoundingClientRect();a-=f.top+parseInt(p(i,"border-top-width")),l-=f.left+parseInt(p(i,"border-left-width")),s=a+r.height,u=l+r.width;break}while(i=i.parentNode);if(o&&n!==window){var b=ge(i||n),g=b&&b.a,w=b&&b.d;b&&(a/=w,l/=g,c/=g,h/=w,s=a+h,u=l+c)}return{top:a,left:l,bottom:s,right:u,width:c,height:h}}}function wt(n,e,t){for(var o=re(n,!0),i=x(n)[e];o;){var r=x(o)[t],a=void 0;if(a=i>=r,!a)return o;if(o===q())break;o=re(o,!1)}return!1}function ve(n,e,t,o){for(var i=0,r=0,a=n.children;r<a.length;){if(a[r].style.display!=="none"&&a[r]!==m.ghost&&(o||a[r]!==m.dragged)&&$(a[r],t.draggable,n,!1)){if(i===e)return a[r];i++}r++}return null}function ft(n,e){for(var t=n.lastElementChild;t&&(t===m.ghost||p(t,"display")==="none"||e&&!Ve(t,e));)t=t.previousElementSibling;return t||null}function V(n,e){var t=0;if(!n||!n.parentNode)return-1;for(;n=n.previousElementSibling;)n.nodeName.toUpperCase()!=="TEMPLATE"&&n!==m.clone&&(!e||Ve(n,e))&&t++;return t}function yt(n){var e=0,t=0,o=q();if(n)do{var i=ge(n),r=i.a,a=i.d;e+=n.scrollLeft*r,t+=n.scrollTop*a}while(n!==o&&(n=n.parentNode));return[e,t]}function ln(n,e){for(var t in n)if(n.hasOwnProperty(t)){for(var o in e)if(e.hasOwnProperty(o)&&e[o]===n[t][o])return Number(t)}return-1}function re(n,e){if(!n||!n.getBoundingClientRect)return q();var t=n,o=!1;do if(t.clientWidth<t.scrollWidth||t.clientHeight<t.scrollHeight){var i=p(t);if(t.clientWidth<t.scrollWidth&&(i.overflowX=="auto"||i.overflowX=="scroll")||t.clientHeight<t.scrollHeight&&(i.overflowY=="auto"||i.overflowY=="scroll")){if(!t.getBoundingClientRect||t===document.body)return q();if(o||e)return t;o=!0}}while(t=t.parentNode);return q()}function sn(n,e){if(n&&e)for(var t in e)e.hasOwnProperty(t)&&(n[t]=e[t]);return n}function Qe(n,e){return Math.round(n.top)===Math.round(e.top)&&Math.round(n.left)===Math.round(e.left)&&Math.round(n.height)===Math.round(e.height)&&Math.round(n.width)===Math.round(e.width)}var Oe;function It(n,e){return function(){if(!Oe){var t=arguments,o=this;t.length===1?n.call(o,t[0]):n.apply(o,t),Oe=setTimeout(function(){Oe=void 0},e)}}}function un(){clearTimeout(Oe),Oe=void 0}function At(n,e,t){n.scrollLeft+=e,n.scrollTop+=t}function Pt(n){var e=window.Polymer,t=window.jQuery||window.Zepto;return e&&e.dom?e.dom(n).cloneNode(!0):t?t(n).clone(!0)[0]:n.cloneNode(!0)}var L="Sortable"+new Date().getTime();function dn(){var n=[],e;return{captureAnimationState:function(){if(n=[],!!this.options.animation){var o=[].slice.call(this.el.children);o.forEach(function(i){if(!(p(i,"display")==="none"||i===m.ghost)){n.push({target:i,rect:x(i)});var r=Z({},n[n.length-1].rect);if(i.thisAnimationDuration){var a=ge(i,!0);a&&(r.top-=a.f,r.left-=a.e)}i.fromRect=r}})}},addAnimationState:function(o){n.push(o)},removeAnimationState:function(o){n.splice(ln(n,{target:o}),1)},animateAll:function(o){var i=this;if(!this.options.animation){clearTimeout(e),typeof o=="function"&&o();return}var r=!1,a=0;n.forEach(function(l){var s=0,u=l.target,h=u.fromRect,c=x(u),f=u.prevFromRect,b=u.prevToRect,g=l.rect,w=ge(u,!0);w&&(c.top-=w.f,c.left-=w.e),u.toRect=c,u.thisAnimationDuration&&Qe(f,c)&&!Qe(h,c)&&(g.top-c.top)/(g.left-c.left)===(h.top-c.top)/(h.left-c.left)&&(s=cn(g,f,b,i.options)),Qe(c,h)||(u.prevFromRect=h,u.prevToRect=c,s||(s=i.options.animation),i.animate(u,g,c,s)),s&&(r=!0,a=Math.max(a,s),clearTimeout(u.animationResetTimer),u.animationResetTimer=setTimeout(function(){u.animationTime=0,u.prevFromRect=null,u.fromRect=null,u.prevToRect=null,u.thisAnimationDuration=null},s),u.thisAnimationDuration=s)}),clearTimeout(e),r?e=setTimeout(function(){typeof o=="function"&&o()},a):typeof o=="function"&&o(),n=[]},animate:function(o,i,r,a){if(a){p(o,"transition",""),p(o,"transform","");var l=ge(this.el),s=l&&l.a,u=l&&l.d,h=(i.left-r.left)/(s||1),c=(i.top-r.top)/(u||1);o.animatingX=!!h,o.animatingY=!!c,p(o,"transform","translate3d("+h+"px,"+c+"px,0)"),this.forRepaintDummy=fn(o),p(o,"transition","transform "+a+"ms"+(this.options.easing?" "+this.options.easing:"")),p(o,"transform","translate3d(0,0,0)"),typeof o.animated=="number"&&clearTimeout(o.animated),o.animated=setTimeout(function(){p(o,"transition",""),p(o,"transform",""),o.animated=!1,o.animatingX=!1,o.animatingY=!1},a)}}}}function fn(n){return n.offsetWidth}function cn(n,e,t,o){return Math.sqrt(Math.pow(e.top-n.top,2)+Math.pow(e.left-n.left,2))/Math.sqrt(Math.pow(e.top-t.top,2)+Math.pow(e.left-t.left,2))*o.animation}var ce=[],Je={initializeByDefault:!0},Ne={mount:function(e){for(var t in Je)Je.hasOwnProperty(t)&&!(t in e)&&(e[t]=Je[t]);ce.forEach(function(o){if(o.pluginName===e.pluginName)throw"Sortable: Cannot mount plugin ".concat(e.pluginName," more than once")}),ce.push(e)},pluginEvent:function(e,t,o){var i=this;this.eventCanceled=!1,o.cancel=function(){i.eventCanceled=!0};var r=e+"Global";ce.forEach(function(a){t[a.pluginName]&&(t[a.pluginName][r]&&t[a.pluginName][r](Z({sortable:t},o)),t.options[a.pluginName]&&t[a.pluginName][e]&&t[a.pluginName][e](Z({sortable:t},o)))})},initializePlugins:function(e,t,o,i){ce.forEach(function(l){var s=l.pluginName;if(!(!e.options[s]&&!l.initializeByDefault)){var u=new l(e,t,e.options);u.sortable=e,u.options=e.options,e[s]=u,J(o,u.defaults)}});for(var r in e.options)if(e.options.hasOwnProperty(r)){var a=this.modifyOption(e,r,e.options[r]);typeof a<"u"&&(e.options[r]=a)}},getEventProperties:function(e,t){var o={};return ce.forEach(function(i){typeof i.eventProperties=="function"&&J(o,i.eventProperties.call(t[i.pluginName],e))}),o},modifyOption:function(e,t,o){var i;return ce.forEach(function(r){e[r.pluginName]&&r.optionListeners&&typeof r.optionListeners[t]=="function"&&(i=r.optionListeners[t].call(e[r.pluginName],o))}),i}};function hn(n){var e=n.sortable,t=n.rootEl,o=n.name,i=n.targetEl,r=n.cloneEl,a=n.toEl,l=n.fromEl,s=n.oldIndex,u=n.newIndex,h=n.oldDraggableIndex,c=n.newDraggableIndex,f=n.originalEvent,b=n.putSortable,g=n.extraEventProperties;if(e=e||t&&t[L],!!e){var w,R=e.options,M="on"+o.charAt(0).toUpperCase()+o.substr(1);window.CustomEvent&&!ee&&!Pe?w=new CustomEvent(o,{bubbles:!0,cancelable:!0}):(w=document.createEvent("Event"),w.initEvent(o,!0,!0)),w.to=a||t,w.from=l||t,w.item=i||t,w.clone=r,w.oldIndex=s,w.newIndex=u,w.oldDraggableIndex=h,w.newDraggableIndex=c,w.originalEvent=f,w.pullMode=b?b.lastPutMode:void 0;var C=Z(Z({},g),Ne.getEventProperties(o,e));for(var X in C)w[X]=C[X];t&&t.dispatchEvent(w),R[M]&&R[M].call(e,w)}}var pn=["evt"],F=function(e,t){var o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},i=o.evt,r=on(o,pn);Ne.pluginEvent.bind(m)(e,t,Z({dragEl:d,parentEl:T,ghostEl:v,rootEl:D,nextEl:fe,lastDownEl:We,cloneEl:S,cloneHidden:ie,dragStarted:De,putSortable:A,activeSortable:m.active,originalEvent:i,oldIndex:me,oldDraggableIndex:xe,newIndex:G,newDraggableIndex:oe,hideGhostForTarget:Ft,unhideGhostForTarget:Rt,cloneNowHidden:function(){ie=!0},cloneNowShown:function(){ie=!1},dispatchSortableEvent:function(l){k({sortable:t,name:l,originalEvent:i})}},r))};function k(n){hn(Z({putSortable:A,cloneEl:S,targetEl:d,rootEl:D,oldIndex:me,oldDraggableIndex:xe,newIndex:G,newDraggableIndex:oe},n))}var d,T,v,D,fe,We,S,ie,me,G,xe,oe,Re,A,pe=!1,je=!1,Ue=[],ue,U,et,tt,_t,Et,De,he,Ie,Ae=!1,Xe=!1,Ge,N,nt=[],lt=!1,$e=[],Ze=typeof document<"u",Ye=Tt,Dt=Pe||ee?"cssFloat":"float",mn=Ze&&!Ct&&!Tt&&"draggable"in document.createElement("div"),Nt=function(){if(Ze){if(ee)return!1;var n=document.createElement("x");return n.style.cssText="pointer-events:auto",n.style.pointerEvents==="auto"}}(),kt=function(e,t){var o=p(e),i=parseInt(o.width)-parseInt(o.paddingLeft)-parseInt(o.paddingRight)-parseInt(o.borderLeftWidth)-parseInt(o.borderRightWidth),r=ve(e,0,t),a=ve(e,1,t),l=r&&p(r),s=a&&p(a),u=l&&parseInt(l.marginLeft)+parseInt(l.marginRight)+x(r).width,h=s&&parseInt(s.marginLeft)+parseInt(s.marginRight)+x(a).width;if(o.display==="flex")return o.flexDirection==="column"||o.flexDirection==="column-reverse"?"vertical":"horizontal";if(o.display==="grid")return o.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(r&&l.float&&l.float!=="none"){var c=l.float==="left"?"left":"right";return a&&(s.clear==="both"||s.clear===c)?"vertical":"horizontal"}return r&&(l.display==="block"||l.display==="flex"||l.display==="table"||l.display==="grid"||u>=i&&o[Dt]==="none"||a&&o[Dt]==="none"&&u+h>i)?"vertical":"horizontal"},gn=function(e,t,o){var i=o?e.left:e.top,r=o?e.right:e.bottom,a=o?e.width:e.height,l=o?t.left:t.top,s=o?t.right:t.bottom,u=o?t.width:t.height;return i===l||r===s||i+a/2===l+u/2},vn=function(e,t){var o;return Ue.some(function(i){var r=i[L].options.emptyInsertThreshold;if(!(!r||ft(i))){var a=x(i),l=e>=a.left-r&&e<=a.right+r,s=t>=a.top-r&&t<=a.bottom+r;if(l&&s)return o=i}}),o},Mt=function(e){function t(r,a){return function(l,s,u,h){var c=l.options.group.name&&s.options.group.name&&l.options.group.name===s.options.group.name;if(r==null&&(a||c))return!0;if(r==null||r===!1)return!1;if(a&&r==="clone")return r;if(typeof r=="function")return t(r(l,s,u,h),a)(l,s,u,h);var f=(a?l:s).options.group.name;return r===!0||typeof r=="string"&&r===f||r.join&&r.indexOf(f)>-1}}var o={},i=e.group;(!i||He(i)!="object")&&(i={name:i}),o.name=i.name,o.checkPull=t(i.pull,!0),o.checkPut=t(i.put),o.revertClone=i.revertClone,e.group=o},Ft=function(){!Nt&&v&&p(v,"display","none")},Rt=function(){!Nt&&v&&p(v,"display","")};Ze&&!Ct&&document.addEventListener("click",function(n){if(je)return n.preventDefault(),n.stopPropagation&&n.stopPropagation(),n.stopImmediatePropagation&&n.stopImmediatePropagation(),je=!1,!1},!0);var de=function(e){if(d){e=e.touches?e.touches[0]:e;var t=vn(e.clientX,e.clientY);if(t){var o={};for(var i in e)e.hasOwnProperty(i)&&(o[i]=e[i]);o.target=o.rootEl=t,o.preventDefault=void 0,o.stopPropagation=void 0,t[L]._onDragOver(o)}}},bn=function(e){d&&d.parentNode[L]._isOutsideThisEl(e.target)};function m(n,e){if(!(n&&n.nodeType&&n.nodeType===1))throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(n));this.el=n,this.options=e=J({},e),n[L]=this;var t={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(n.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return kt(n,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(a,l){a.setData("Text",l.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:m.supportPointer!==!1&&"PointerEvent"in window&&!Ce,emptyInsertThreshold:5};Ne.initializePlugins(this,n,t);for(var o in t)!(o in e)&&(e[o]=t[o]);Mt(e);for(var i in this)i.charAt(0)==="_"&&typeof this[i]=="function"&&(this[i]=this[i].bind(this));this.nativeDraggable=e.forceFallback?!1:mn,this.nativeDraggable&&(this.options.touchStartThreshold=1),e.supportPointer?_(n,"pointerdown",this._onTapStart):(_(n,"mousedown",this._onTapStart),_(n,"touchstart",this._onTapStart)),this.nativeDraggable&&(_(n,"dragover",this),_(n,"dragenter",this)),Ue.push(this.el),e.store&&e.store.get&&this.sort(e.store.get(this)||[]),J(this,dn())}m.prototype={constructor:m,_isOutsideThisEl:function(e){!this.el.contains(e)&&e!==this.el&&(he=null)},_getDirection:function(e,t){return typeof this.options.direction=="function"?this.options.direction.call(this,e,t,d):this.options.direction},_onTapStart:function(e){if(e.cancelable){var t=this,o=this.el,i=this.options,r=i.preventOnFilter,a=e.type,l=e.touches&&e.touches[0]||e.pointerType&&e.pointerType==="touch"&&e,s=(l||e).target,u=e.target.shadowRoot&&(e.path&&e.path[0]||e.composedPath&&e.composedPath()[0])||s,h=i.filter;if(Cn(o),!d&&!(/mousedown|pointerdown/.test(a)&&e.button!==0||i.disabled)&&!u.isContentEditable&&!(!this.nativeDraggable&&Ce&&s&&s.tagName.toUpperCase()==="SELECT")&&(s=$(s,i.draggable,o,!1),!(s&&s.animated)&&We!==s)){if(me=V(s),xe=V(s,i.draggable),typeof h=="function"){if(h.call(this,e,s,this)){k({sortable:t,rootEl:u,name:"filter",targetEl:s,toEl:o,fromEl:o}),F("filter",t,{evt:e}),r&&e.cancelable&&e.preventDefault();return}}else if(h&&(h=h.split(",").some(function(c){if(c=$(u,c.trim(),o,!1),c)return k({sortable:t,rootEl:c,name:"filter",targetEl:s,fromEl:o,toEl:o}),F("filter",t,{evt:e}),!0}),h)){r&&e.cancelable&&e.preventDefault();return}i.handle&&!$(u,i.handle,o,!1)||this._prepareDragStart(e,l,s)}}},_prepareDragStart:function(e,t,o){var i=this,r=i.el,a=i.options,l=r.ownerDocument,s;if(o&&!d&&o.parentNode===r){var u=x(o);if(D=r,d=o,T=d.parentNode,fe=d.nextSibling,We=o,Re=a.group,m.dragged=d,ue={target:d,clientX:(t||e).clientX,clientY:(t||e).clientY},_t=ue.clientX-u.left,Et=ue.clientY-u.top,this._lastX=(t||e).clientX,this._lastY=(t||e).clientY,d.style["will-change"]="all",s=function(){if(F("delayEnded",i,{evt:e}),m.eventCanceled){i._onDrop();return}i._disableDelayedDragEvents(),!vt&&i.nativeDraggable&&(d.draggable=!0),i._triggerDragStart(e,t),k({sortable:i,name:"choose",originalEvent:e}),W(d,a.chosenClass,!0)},a.ignore.split(",").forEach(function(h){xt(d,h.trim(),ot)}),_(l,"dragover",de),_(l,"mousemove",de),_(l,"touchmove",de),_(l,"mouseup",i._onDrop),_(l,"touchend",i._onDrop),_(l,"touchcancel",i._onDrop),vt&&this.nativeDraggable&&(this.options.touchStartThreshold=4,d.draggable=!0),F("delayStart",this,{evt:e}),a.delay&&(!a.delayOnTouchOnly||t)&&(!this.nativeDraggable||!(Pe||ee))){if(m.eventCanceled){this._onDrop();return}_(l,"mouseup",i._disableDelayedDrag),_(l,"touchend",i._disableDelayedDrag),_(l,"touchcancel",i._disableDelayedDrag),_(l,"mousemove",i._delayedDragTouchMoveHandler),_(l,"touchmove",i._delayedDragTouchMoveHandler),a.supportPointer&&_(l,"pointermove",i._delayedDragTouchMoveHandler),i._dragStartTimer=setTimeout(s,a.delay)}else s()}},_delayedDragTouchMoveHandler:function(e){var t=e.touches?e.touches[0]:e;Math.max(Math.abs(t.clientX-this._lastX),Math.abs(t.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){d&&ot(d),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var e=this.el.ownerDocument;y(e,"mouseup",this._disableDelayedDrag),y(e,"touchend",this._disableDelayedDrag),y(e,"touchcancel",this._disableDelayedDrag),y(e,"mousemove",this._delayedDragTouchMoveHandler),y(e,"touchmove",this._delayedDragTouchMoveHandler),y(e,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(e,t){t=t||e.pointerType=="touch"&&e,!this.nativeDraggable||t?this.options.supportPointer?_(document,"pointermove",this._onTouchMove):t?_(document,"touchmove",this._onTouchMove):_(document,"mousemove",this._onTouchMove):(_(d,"dragend",this),_(D,"dragstart",this._onDragStart));try{document.selection?Le(function(){document.selection.empty()}):window.getSelection().removeAllRanges()}catch{}},_dragStarted:function(e,t){if(pe=!1,D&&d){F("dragStarted",this,{evt:t}),this.nativeDraggable&&_(document,"dragover",bn);var o=this.options;!e&&W(d,o.dragClass,!1),W(d,o.ghostClass,!0),m.active=this,e&&this._appendGhost(),k({sortable:this,name:"start",originalEvent:t})}else this._nulling()},_emulateDragOver:function(){if(U){this._lastX=U.clientX,this._lastY=U.clientY,Ft();for(var e=document.elementFromPoint(U.clientX,U.clientY),t=e;e&&e.shadowRoot&&(e=e.shadowRoot.elementFromPoint(U.clientX,U.clientY),e!==t);)t=e;if(d.parentNode[L]._isOutsideThisEl(e),t)do{if(t[L]){var o=void 0;if(o=t[L]._onDragOver({clientX:U.clientX,clientY:U.clientY,target:e,rootEl:t}),o&&!this.options.dragoverBubble)break}e=t}while(t=t.parentNode);Rt()}},_onTouchMove:function(e){if(ue){var t=this.options,o=t.fallbackTolerance,i=t.fallbackOffset,r=e.touches?e.touches[0]:e,a=v&&ge(v,!0),l=v&&a&&a.a,s=v&&a&&a.d,u=Ye&&N&&yt(N),h=(r.clientX-ue.clientX+i.x)/(l||1)+(u?u[0]-nt[0]:0)/(l||1),c=(r.clientY-ue.clientY+i.y)/(s||1)+(u?u[1]-nt[1]:0)/(s||1);if(!m.active&&!pe){if(o&&Math.max(Math.abs(r.clientX-this._lastX),Math.abs(r.clientY-this._lastY))<o)return;this._onDragStart(e,!0)}if(v){a?(a.e+=h-(et||0),a.f+=c-(tt||0)):a={a:1,b:0,c:0,d:1,e:h,f:c};var f="matrix(".concat(a.a,",").concat(a.b,",").concat(a.c,",").concat(a.d,",").concat(a.e,",").concat(a.f,")");p(v,"webkitTransform",f),p(v,"mozTransform",f),p(v,"msTransform",f),p(v,"transform",f),et=h,tt=c,U=r}e.cancelable&&e.preventDefault()}},_appendGhost:function(){if(!v){var e=this.options.fallbackOnBody?document.body:D,t=x(d,!0,Ye,!0,e),o=this.options;if(Ye){for(N=e;p(N,"position")==="static"&&p(N,"transform")==="none"&&N!==document;)N=N.parentNode;N!==document.body&&N!==document.documentElement?(N===document&&(N=q()),t.top+=N.scrollTop,t.left+=N.scrollLeft):N=q(),nt=yt(N)}v=d.cloneNode(!0),W(v,o.ghostClass,!1),W(v,o.fallbackClass,!0),W(v,o.dragClass,!0),p(v,"transition",""),p(v,"transform",""),p(v,"box-sizing","border-box"),p(v,"margin",0),p(v,"top",t.top),p(v,"left",t.left),p(v,"width",t.width),p(v,"height",t.height),p(v,"opacity","0.8"),p(v,"position",Ye?"absolute":"fixed"),p(v,"zIndex","100000"),p(v,"pointerEvents","none"),m.ghost=v,e.appendChild(v),p(v,"transform-origin",_t/parseInt(v.style.width)*100+"% "+Et/parseInt(v.style.height)*100+"%")}},_onDragStart:function(e,t){var o=this,i=e.dataTransfer,r=o.options;if(F("dragStart",this,{evt:e}),m.eventCanceled){this._onDrop();return}F("setupClone",this),m.eventCanceled||(S=Pt(d),S.removeAttribute("id"),S.draggable=!1,S.style["will-change"]="",this._hideClone(),W(S,this.options.chosenClass,!1),m.clone=S),o.cloneId=Le(function(){F("clone",o),!m.eventCanceled&&(o.options.removeCloneOnHide||D.insertBefore(S,d),o._hideClone(),k({sortable:o,name:"clone"}))}),!t&&W(d,r.dragClass,!0),t?(je=!0,o._loopId=setInterval(o._emulateDragOver,50)):(y(document,"mouseup",o._onDrop),y(document,"touchend",o._onDrop),y(document,"touchcancel",o._onDrop),i&&(i.effectAllowed="move",r.setData&&r.setData.call(o,i,d)),_(document,"drop",o),p(d,"transform","translateZ(0)")),pe=!0,o._dragStartId=Le(o._dragStarted.bind(o,t,e)),_(document,"selectstart",o),De=!0,Ce&&p(document.body,"user-select","none")},_onDragOver:function(e){var t=this.el,o=e.target,i,r,a,l=this.options,s=l.group,u=m.active,h=Re===s,c=l.sort,f=A||u,b,g=this,w=!1;if(lt)return;function R(Ee,Yt){F(Ee,g,Z({evt:e,isOwner:h,axis:b?"vertical":"horizontal",revert:a,dragRect:i,targetRect:r,canSort:c,fromSortable:f,target:o,completed:C,onMove:function(pt,Bt){return Be(D,t,d,i,pt,x(pt),e,Bt)},changed:X},Yt))}function M(){R("dragOverAnimationCapture"),g.captureAnimationState(),g!==f&&f.captureAnimationState()}function C(Ee){return R("dragOverCompleted",{insertion:Ee}),Ee&&(h?u._hideClone():u._showClone(g),g!==f&&(W(d,A?A.options.ghostClass:u.options.ghostClass,!1),W(d,l.ghostClass,!0)),A!==g&&g!==m.active?A=g:g===m.active&&A&&(A=null),f===g&&(g._ignoreWhileAnimating=o),g.animateAll(function(){R("dragOverAnimationComplete"),g._ignoreWhileAnimating=null}),g!==f&&(f.animateAll(),f._ignoreWhileAnimating=null)),(o===d&&!d.animated||o===t&&!o.animated)&&(he=null),!l.dragoverBubble&&!e.rootEl&&o!==document&&(d.parentNode[L]._isOutsideThisEl(e.target),!Ee&&de(e)),!l.dragoverBubble&&e.stopPropagation&&e.stopPropagation(),w=!0}function X(){G=V(d),oe=V(d,l.draggable),k({sortable:g,name:"change",toEl:t,newIndex:G,newDraggableIndex:oe,originalEvent:e})}if(e.preventDefault!==void 0&&e.cancelable&&e.preventDefault(),o=$(o,l.draggable,t,!0),R("dragOver"),m.eventCanceled)return w;if(d.contains(e.target)||o.animated&&o.animatingX&&o.animatingY||g._ignoreWhileAnimating===o)return C(!1);if(je=!1,u&&!l.disabled&&(h?c||(a=T!==D):A===this||(this.lastPutMode=Re.checkPull(this,u,d,e))&&s.checkPut(this,u,d,e))){if(b=this._getDirection(e,o)==="vertical",i=x(d),R("dragOverValid"),m.eventCanceled)return w;if(a)return T=D,M(),this._hideClone(),R("revert"),m.eventCanceled||(fe?D.insertBefore(d,fe):D.appendChild(d)),C(!0);var E=ft(t,l.draggable);if(!E||En(e,b,this)&&!E.animated){if(E===d)return C(!1);if(E&&t===e.target&&(o=E),o&&(r=x(o)),Be(D,t,d,i,o,r,e,!!o)!==!1)return M(),E&&E.nextSibling?t.insertBefore(d,E.nextSibling):t.appendChild(d),T=t,X(),C(!0)}else if(E&&_n(e,b,this)){var Y=ve(t,0,l,!0);if(Y===d)return C(!1);if(o=Y,r=x(o),Be(D,t,d,i,o,r,e,!1)!==!1)return M(),t.insertBefore(d,Y),T=t,X(),C(!0)}else if(o.parentNode===t){r=x(o);var P=0,ae,be=d.parentNode!==t,B=!gn(d.animated&&d.toRect||i,o.animated&&o.toRect||r,b),we=b?"top":"left",te=wt(o,"top","top")||wt(d,"top","top"),ye=te?te.scrollTop:void 0;he!==o&&(ae=r[we],Ae=!1,Xe=!B&&l.invertSwap||be),P=Dn(e,o,r,b,B?1:l.swapThreshold,l.invertedSwapThreshold==null?l.swapThreshold:l.invertedSwapThreshold,Xe,he===o);var K;if(P!==0){var le=V(d);do le-=P,K=T.children[le];while(K&&(p(K,"display")==="none"||K===v))}if(P===0||K===o)return C(!1);he=o,Ie=P;var _e=o.nextElementSibling,ne=!1;ne=P===1;var ke=Be(D,t,d,i,o,r,e,ne);if(ke!==!1)return(ke===1||ke===-1)&&(ne=ke===1),lt=!0,setTimeout(yn,30),M(),ne&&!_e?t.appendChild(d):o.parentNode.insertBefore(d,ne?_e:o),te&&At(te,0,ye-te.scrollTop),T=d.parentNode,ae!==void 0&&!Xe&&(Ge=Math.abs(ae-x(o)[we])),X(),C(!0)}if(t.contains(d))return C(!1)}return!1},_ignoreWhileAnimating:null,_offMoveEvents:function(){y(document,"mousemove",this._onTouchMove),y(document,"touchmove",this._onTouchMove),y(document,"pointermove",this._onTouchMove),y(document,"dragover",de),y(document,"mousemove",de),y(document,"touchmove",de)},_offUpEvents:function(){var e=this.el.ownerDocument;y(e,"mouseup",this._onDrop),y(e,"touchend",this._onDrop),y(e,"pointerup",this._onDrop),y(e,"touchcancel",this._onDrop),y(document,"selectstart",this)},_onDrop:function(e){var t=this.el,o=this.options;if(G=V(d),oe=V(d,o.draggable),F("drop",this,{evt:e}),T=d&&d.parentNode,G=V(d),oe=V(d,o.draggable),m.eventCanceled){this._nulling();return}pe=!1,Xe=!1,Ae=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),st(this.cloneId),st(this._dragStartId),this.nativeDraggable&&(y(document,"drop",this),y(t,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),Ce&&p(document.body,"user-select",""),p(d,"transform",""),e&&(De&&(e.cancelable&&e.preventDefault(),!o.dropBubble&&e.stopPropagation()),v&&v.parentNode&&v.parentNode.removeChild(v),(D===T||A&&A.lastPutMode!=="clone")&&S&&S.parentNode&&S.parentNode.removeChild(S),d&&(this.nativeDraggable&&y(d,"dragend",this),ot(d),d.style["will-change"]="",De&&!pe&&W(d,A?A.options.ghostClass:this.options.ghostClass,!1),W(d,this.options.chosenClass,!1),k({sortable:this,name:"unchoose",toEl:T,newIndex:null,newDraggableIndex:null,originalEvent:e}),D!==T?(G>=0&&(k({rootEl:T,name:"add",toEl:T,fromEl:D,originalEvent:e}),k({sortable:this,name:"remove",toEl:T,originalEvent:e}),k({rootEl:T,name:"sort",toEl:T,fromEl:D,originalEvent:e}),k({sortable:this,name:"sort",toEl:T,originalEvent:e})),A&&A.save()):G!==me&&G>=0&&(k({sortable:this,name:"update",toEl:T,originalEvent:e}),k({sortable:this,name:"sort",toEl:T,originalEvent:e})),m.active&&((G==null||G===-1)&&(G=me,oe=xe),k({sortable:this,name:"end",toEl:T,originalEvent:e}),this.save()))),this._nulling()},_nulling:function(){F("nulling",this),D=d=T=v=fe=S=We=ie=ue=U=De=G=oe=me=xe=he=Ie=A=Re=m.dragged=m.ghost=m.clone=m.active=null,$e.forEach(function(e){e.checked=!0}),$e.length=et=tt=0},handleEvent:function(e){switch(e.type){case"drop":case"dragend":this._onDrop(e);break;case"dragenter":case"dragover":d&&(this._onDragOver(e),wn(e));break;case"selectstart":e.preventDefault();break}},toArray:function(){for(var e=[],t,o=this.el.children,i=0,r=o.length,a=this.options;i<r;i++)t=o[i],$(t,a.draggable,this.el,!1)&&e.push(t.getAttribute(a.dataIdAttr)||Tn(t));return e},sort:function(e,t){var o={},i=this.el;this.toArray().forEach(function(r,a){var l=i.children[a];$(l,this.options.draggable,i,!1)&&(o[r]=l)},this),t&&this.captureAnimationState(),e.forEach(function(r){o[r]&&(i.removeChild(o[r]),i.appendChild(o[r]))}),t&&this.animateAll()},save:function(){var e=this.options.store;e&&e.set&&e.set(this)},closest:function(e,t){return $(e,t||this.options.draggable,this.el,!1)},option:function(e,t){var o=this.options;if(t===void 0)return o[e];var i=Ne.modifyOption(this,e,t);typeof i<"u"?o[e]=i:o[e]=t,e==="group"&&Mt(o)},destroy:function(){F("destroy",this);var e=this.el;e[L]=null,y(e,"mousedown",this._onTapStart),y(e,"touchstart",this._onTapStart),y(e,"pointerdown",this._onTapStart),this.nativeDraggable&&(y(e,"dragover",this),y(e,"dragenter",this)),Array.prototype.forEach.call(e.querySelectorAll("[draggable]"),function(t){t.removeAttribute("draggable")}),this._onDrop(),this._disableDelayedDragEvents(),Ue.splice(Ue.indexOf(this.el),1),this.el=e=null},_hideClone:function(){if(!ie){if(F("hideClone",this),m.eventCanceled)return;p(S,"display","none"),this.options.removeCloneOnHide&&S.parentNode&&S.parentNode.removeChild(S),ie=!0}},_showClone:function(e){if(e.lastPutMode!=="clone"){this._hideClone();return}if(ie){if(F("showClone",this),m.eventCanceled)return;d.parentNode==D&&!this.options.group.revertClone?D.insertBefore(S,d):fe?D.insertBefore(S,fe):D.appendChild(S),this.options.group.revertClone&&this.animate(d,S),p(S,"display",""),ie=!1}}};function wn(n){n.dataTransfer&&(n.dataTransfer.dropEffect="move"),n.cancelable&&n.preventDefault()}function Be(n,e,t,o,i,r,a,l){var s,u=n[L],h=u.options.onMove,c;return window.CustomEvent&&!ee&&!Pe?s=new CustomEvent("move",{bubbles:!0,cancelable:!0}):(s=document.createEvent("Event"),s.initEvent("move",!0,!0)),s.to=e,s.from=n,s.dragged=t,s.draggedRect=o,s.related=i||e,s.relatedRect=r||x(e),s.willInsertAfter=l,s.originalEvent=a,n.dispatchEvent(s),h&&(c=h.call(u,s,a)),c}function ot(n){n.draggable=!1}function yn(){lt=!1}function _n(n,e,t){var o=x(ve(t.el,0,t.options,!0)),i=10;return e?n.clientX<o.left-i||n.clientY<o.top&&n.clientX<o.right:n.clientY<o.top-i||n.clientY<o.bottom&&n.clientX<o.left}function En(n,e,t){var o=x(ft(t.el,t.options.draggable)),i=10;return e?n.clientX>o.right+i||n.clientX<=o.right&&n.clientY>o.bottom&&n.clientX>=o.left:n.clientX>o.right&&n.clientY>o.top||n.clientX<=o.right&&n.clientY>o.bottom+i}function Dn(n,e,t,o,i,r,a,l){var s=o?n.clientY:n.clientX,u=o?t.height:t.width,h=o?t.top:t.left,c=o?t.bottom:t.right,f=!1;if(!a){if(l&&Ge<u*i){if(!Ae&&(Ie===1?s>h+u*r/2:s<c-u*r/2)&&(Ae=!0),Ae)f=!0;else if(Ie===1?s<h+Ge:s>c-Ge)return-Ie}else if(s>h+u*(1-i)/2&&s<c-u*(1-i)/2)return Sn(e)}return f=f||a,f&&(s<h+u*r/2||s>c-u*r/2)?s>h+u/2?1:-1:0}function Sn(n){return V(d)<V(n)?1:-1}function Tn(n){for(var e=n.tagName+n.className+n.src+n.href+n.textContent,t=e.length,o=0;t--;)o+=e.charCodeAt(t);return o.toString(36)}function Cn(n){$e.length=0;for(var e=n.getElementsByTagName("input"),t=e.length;t--;){var o=e[t];o.checked&&$e.push(o)}}function Le(n){return setTimeout(n,0)}function st(n){return clearTimeout(n)}Ze&&_(document,"touchmove",function(n){(m.active||pe)&&n.cancelable&&n.preventDefault()});m.utils={on:_,off:y,css:p,find:xt,is:function(e,t){return!!$(e,t,e,!1)},extend:sn,throttle:It,closest:$,toggleClass:W,clone:Pt,index:V,nextTick:Le,cancelNextTick:st,detectDirection:kt,getChild:ve};m.get=function(n){return n[L]};m.mount=function(){for(var n=arguments.length,e=new Array(n),t=0;t<n;t++)e[t]=arguments[t];e[0].constructor===Array&&(e=e[0]),e.forEach(function(o){if(!o.prototype||!o.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(o));o.utils&&(m.utils=Z(Z({},m.utils),o.utils)),Ne.mount(o)})};m.create=function(n,e){return new m(n,e)};m.version=rn;var O=[],Se,ut,dt=!1,it,rt,qe,Te;function On(){function n(){this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0};for(var e in this)e.charAt(0)==="_"&&typeof this[e]=="function"&&(this[e]=this[e].bind(this))}return n.prototype={dragStarted:function(t){var o=t.originalEvent;this.sortable.nativeDraggable?_(document,"dragover",this._handleAutoScroll):this.options.supportPointer?_(document,"pointermove",this._handleFallbackAutoScroll):o.touches?_(document,"touchmove",this._handleFallbackAutoScroll):_(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(t){var o=t.originalEvent;!this.options.dragOverBubble&&!o.rootEl&&this._handleAutoScroll(o)},drop:function(){this.sortable.nativeDraggable?y(document,"dragover",this._handleAutoScroll):(y(document,"pointermove",this._handleFallbackAutoScroll),y(document,"touchmove",this._handleFallbackAutoScroll),y(document,"mousemove",this._handleFallbackAutoScroll)),St(),ze(),un()},nulling:function(){qe=ut=Se=dt=Te=it=rt=null,O.length=0},_handleFallbackAutoScroll:function(t){this._handleAutoScroll(t,!0)},_handleAutoScroll:function(t,o){var i=this,r=(t.touches?t.touches[0]:t).clientX,a=(t.touches?t.touches[0]:t).clientY,l=document.elementFromPoint(r,a);if(qe=t,o||this.options.forceAutoScrollFallback||Pe||ee||Ce){at(t,this.options,l,o);var s=re(l,!0);dt&&(!Te||r!==it||a!==rt)&&(Te&&St(),Te=setInterval(function(){var u=re(document.elementFromPoint(r,a),!0);u!==s&&(s=u,ze()),at(t,i.options,u,o)},10),it=r,rt=a)}else{if(!this.options.bubbleScroll||re(l,!0)===q()){ze();return}at(t,this.options,re(l,!1),!1)}}},J(n,{pluginName:"scroll",initializeByDefault:!0})}function ze(){O.forEach(function(n){clearInterval(n.pid)}),O=[]}function St(){clearInterval(Te)}var at=It(function(n,e,t,o){if(e.scroll){var i=(n.touches?n.touches[0]:n).clientX,r=(n.touches?n.touches[0]:n).clientY,a=e.scrollSensitivity,l=e.scrollSpeed,s=q(),u=!1,h;ut!==t&&(ut=t,ze(),Se=e.scroll,h=e.scrollFn,Se===!0&&(Se=re(t,!0)));var c=0,f=Se;do{var b=f,g=x(b),w=g.top,R=g.bottom,M=g.left,C=g.right,X=g.width,E=g.height,Y=void 0,P=void 0,ae=b.scrollWidth,be=b.scrollHeight,B=p(b),we=b.scrollLeft,te=b.scrollTop;b===s?(Y=X<ae&&(B.overflowX==="auto"||B.overflowX==="scroll"||B.overflowX==="visible"),P=E<be&&(B.overflowY==="auto"||B.overflowY==="scroll"||B.overflowY==="visible")):(Y=X<ae&&(B.overflowX==="auto"||B.overflowX==="scroll"),P=E<be&&(B.overflowY==="auto"||B.overflowY==="scroll"));var ye=Y&&(Math.abs(C-i)<=a&&we+X<ae)-(Math.abs(M-i)<=a&&!!we),K=P&&(Math.abs(R-r)<=a&&te+E<be)-(Math.abs(w-r)<=a&&!!te);if(!O[c])for(var le=0;le<=c;le++)O[le]||(O[le]={});(O[c].vx!=ye||O[c].vy!=K||O[c].el!==b)&&(O[c].el=b,O[c].vx=ye,O[c].vy=K,clearInterval(O[c].pid),(ye!=0||K!=0)&&(u=!0,O[c].pid=setInterval((function(){o&&this.layer===0&&m.active._onTouchMove(qe);var _e=O[this.layer].vy?O[this.layer].vy*l:0,ne=O[this.layer].vx?O[this.layer].vx*l:0;typeof h=="function"&&h.call(m.dragged.parentNode[L],ne,_e,n,qe,O[this.layer].el)!=="continue"||At(O[this.layer].el,ne,_e)}).bind({layer:c}),24))),c++}while(e.bubbleScroll&&f!==s&&(f=re(f,!1)));dt=u}},30),Xt=function(e){var t=e.originalEvent,o=e.putSortable,i=e.dragEl,r=e.activeSortable,a=e.dispatchSortableEvent,l=e.hideGhostForTarget,s=e.unhideGhostForTarget;if(t){var u=o||r;l();var h=t.changedTouches&&t.changedTouches.length?t.changedTouches[0]:t,c=document.elementFromPoint(h.clientX,h.clientY);s(),u&&!u.el.contains(c)&&(a("spill"),this.onSpill({dragEl:i,putSortable:o}))}};function ct(){}ct.prototype={startIndex:null,dragStart:function(e){var t=e.oldDraggableIndex;this.startIndex=t},onSpill:function(e){var t=e.dragEl,o=e.putSortable;this.sortable.captureAnimationState(),o&&o.captureAnimationState();var i=ve(this.sortable.el,this.startIndex,this.options);i?this.sortable.el.insertBefore(t,i):this.sortable.el.appendChild(t),this.sortable.animateAll(),o&&o.animateAll()},drop:Xt};J(ct,{pluginName:"revertOnSpill"});function ht(){}ht.prototype={onSpill:function(e){var t=e.dragEl,o=e.putSortable,i=o||this.sortable;i.captureAnimationState(),t.parentNode&&t.parentNode.removeChild(t),i.animateAll()},drop:Xt};J(ht,{pluginName:"removeOnSpill"});m.mount(new On);m.mount(ht,ct);const xn={class:"pt-[10px]"},In={class:"w-[600px]"},An={class:"w-[600px]"},Pn={class:"form-tips flex items-center"},Nn={class:"flex-1 min-w-0"},kn={class:"max-w-[600px]"},Mn={class:"move-icon cursor-move"},Fn={key:0},Rn={key:1},Xn={key:2},Yn={class:"mt-4"},to=Zt({__name:"interface-config",props:{modelValue:{}},emits:["update:modelValue"],setup(n,{emit:e}){const i=Ht(n,"modelValue",e),r=Kt({show:!1,type:"add",data:{},index:0}),a=Qt(),l=()=>{const c=a.value.$el.querySelector(".el-table__body tbody");m.create(c,{animation:150,handle:".move-icon",onEnd:({newIndex:f,oldIndex:b})=>{const g=i.value.menus,w=g.splice(b,1)[0];g.splice(f,0,w),i.value.menus=[],mt(()=>{i.value.menus=g})}})},s=(c="add",f={images:[],content:"",keyword:""})=>{var b;r.show=!0,r.type=c,r.data=qt(f),r.index=(b=i.value.menus)==null?void 0:b.indexOf(f)},u=c=>{var f;(f=i.value.menus)==null||f.splice(c,1)},h=c=>{var f,b,g;switch(r.type){case"add":((f=i.value.menus)==null?void 0:f.length)<3&&((b=i.value.menus)==null||b.push(c));break;case"edit":r.index!==-1&&((g=i.value.menus)==null||g.splice(r.index,1,c));break}};return Jt(async()=>{await mt(),l()}),(c,f)=>{const b=Gt,g=Wt,w=zt,R=Vt,M=jt,C=Lt,X=Ut;return Me(),Fe("div",xn,[I(g,{label:"欢迎语",prop:"welcome_introducer"},{default:H(()=>[z("div",In,[I(b,{modelValue:j(i).welcome_introducer,"onUpdate:modelValue":f[0]||(f[0]=E=>j(i).welcome_introducer=E),placeholder:"",type:"textarea",autosize:{minRows:8,maxRows:8},clearable:"",resize:"none"},null,8,["modelValue"]),f[5]||(f[5]=z("div",{class:"form-tips"},[se(" 打开聊天窗口后会主动发送，添加双井号可添加提问示例，例如：#帮我写一则关于xxx的文案# "),z("br"),se(" 多个问题请用回车换行 ")],-1))])]),_:1}),I(g,{label:"底部标识",prop:"copyright"},{default:H(()=>[z("div",An,[I(b,{modelValue:j(i).copyright,"onUpdate:modelValue":f[1]||(f[1]=E=>j(i).copyright=E),placeholder:"",clearable:""},null,8,["modelValue"]),z("div",Pn,[f[8]||(f[8]=se(" 不填写不显示 ")),I(w,{placement:"top-start",width:"auto","show-arrow":!1,transition:"custom-popover",trigger:"hover"},{reference:H(()=>f[6]||(f[6]=[z("span",{class:"text-primary ml-[10px]"},"查看示例 ",-1)])),default:H(()=>[f[7]||(f[7]=z("img",{src:en,alt:"",class:"w-[250px] h-[310px] scale-110"},null,-1))]),_:1})])])]),_:1}),I(g,{label:"菜单设置"},{default:H(()=>[z("div",Nn,[z("div",kn,[I(X,{size:"large",data:j(i).menus,ref_key:"tableRef",ref:a,"row-key":"id"},{default:H(()=>[I(M,{width:"50"},{default:H(()=>[z("div",Mn,[I(R,{name:"el-icon-Rank"})])]),_:1}),I(M,{label:"关键词",prop:"keyword","min-width":"120"}),I(M,{label:"回复内容","min-width":"120"},{default:H(({row:E})=>{var Y,P;return[E.content?(Me(),Fe("span",Fn," 文字 ")):Ke("",!0),E.content&&((Y=E.images)!=null&&Y.length)?(Me(),Fe("span",Rn," + ")):Ke("",!0),(P=E.images)!=null&&P.length?(Me(),Fe("span",Xn," 图片 ")):Ke("",!0)]}),_:1}),I(M,{label:"操作",width:"200"},{default:H(({$index:E,row:Y})=>[I(C,{type:"primary",link:"",onClick:P=>s("view",Y)},{default:H(()=>f[9]||(f[9]=[se(" 查看 ")])),_:2},1032,["onClick"]),I(C,{type:"primary",link:"",onClick:P=>s("edit",Y)},{default:H(()=>f[10]||(f[10]=[se(" 编辑 ")])),_:2},1032,["onClick"]),I(C,{type:"danger",link:"",onClick:P=>u(E)},{default:H(()=>f[11]||(f[11]=[se(" 删除 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),z("div",Yn,[I(C,{type:"primary",disabled:j(i).menus.length>=3,onClick:f[2]||(f[2]=E=>s())},{default:H(()=>f[12]||(f[12]=[se(" +添加菜单 ")])),_:1},8,["disabled"])]),f[13]||(f[13]=z("div",{class:"form-tips"}," 用户点击菜单后，将回复对应内容。此类消息不消耗余额。 ",-1))])]),_:1}),I($t,{show:j(r).show,"onUpdate:show":f[3]||(f[3]=E=>j(r).show=E),type:j(r).type,data:j(r).data,"onUpdate:data":f[4]||(f[4]=E=>j(r).data=E),onConfirm:h},null,8,["show","type","data"])])}}});export{to as _};
