import{b as R,o as S,p as z,dn as I}from"./Ct33iMSA.js";import{E as M}from"./DJ8m2TZq.js";import{E as O}from"./cZpipWaD.js";import{E as U}from"./C_Ttdr_y.js";import{E as Z,a as q}from"./Cz-ZBZVo.js";import{P as G}from"./kzeq9hfp.js";import"./DP2rzg_V.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./l0sNRNKZ.js";/* empty css        *//* empty css        */import{l as H,j as J,b as N,M as m,a1 as j,a0 as a,Z as l,a6 as _,a7 as i,u as s,O as e,N as v,a4 as u,_ as K,n as L}from"./Dp9aCaJ6.js";const Q={class:"text-green-600 font-medium"},W={class:"flex items-center space-x-3"},X={class:"font-medium"},Y={class:"flex items-center space-x-3"},ee={class:"space-y-2"},te={class:"flex justify-between"},se={class:"font-medium"},oe={class:"flex justify-between"},le={class:"font-medium text-blue-600"},ne={class:"flex justify-between"},ae={class:"font-medium text-green-600"},ie={class:"flex justify-between text-sm text-gray-500"},re={class:"space-y-1"},_e={class:"text-sm"},pe={key:0},me={class:"text-sm"},ue={key:0,class:"mt-[-10px]"},Pe=H({__name:"recordDetailPop",props:{type:{type:Number,default:-1}},emits:["close"],setup(C,{expose:D,emit:de}){const T=R(),b=J(),x=N(-1),t=N({}),A=async()=>{t.value=await I({id:x.value})};return D({open:async d=>{b.value.open(),x.value=d,await L(),A()}}),(d,o)=>{const r=S,B=M,y=O,P=U,V=z,f=Z,$=q,F=G;return m(),j(F,{ref_key:"popRef",ref:b,width:"600px",onClose:o[0]||(o[0]=c=>d.$emit("close"))},{default:a(()=>{var c,g,k;return[l(V,{"label-width":"90px","label-position":"left"},{default:a(()=>{var w,h,E;return[l(r,{label:"订单编号"},{default:a(()=>{var n;return[_(i((n=s(t))==null?void 0:n.sn),1)]}),_:1}),l(r,{label:"用户信息"},{default:a(()=>{var n,p;return[_(i((p=(n=s(t))==null?void 0:n.user)==null?void 0:p.nickname),1)]}),_:1}),l(r,{label:"操作时间"},{default:a(()=>{var n;return[_(i((n=s(t))==null?void 0:n.create_time),1)]}),_:1}),l(r,{label:"应用名"},{default:a(()=>{var n;return[_(i(((n=s(t))==null?void 0:n.robot_name)||"-"),1)]}),_:1}),l(r,{label:"变动类型"},{default:a(()=>{var n;return[_(i((n=s(t))==null?void 0:n.change_type),1)]}),_:1}),l(r,{label:`变动${C.type}`},{default:a(()=>{var n,p;return[e("span",null,i(((n=s(t))==null?void 0:n.action)==1?"+":"-"),1),_(i((p=s(t))==null?void 0:p.change_amount),1)]}),_:1},8,["label"]),(w=s(t))!=null&&w.revenue_info?(m(),v(K,{key:0},[l(B,{"content-position":"left"},{default:a(()=>o[1]||(o[1]=[_("分成收益详情")])),_:1}),l(r,{label:"收益说明"},{default:a(()=>[e("div",Q,i(s(t).revenue_info.revenue_desc),1)]),_:1}),l(r,{label:"智能体信息"},{default:a(()=>[e("div",W,[l(y,{size:40,src:s(t).revenue_info.robot_image,class:"flex-shrink-0"},null,8,["src"]),e("div",null,[e("div",X,i(s(t).revenue_info.robot_name),1),o[2]||(o[2]=e("div",{class:"text-sm text-gray-500"},"来自智能体广场",-1))])])]),_:1}),l(r,{label:"分享者"},{default:a(()=>[e("div",Y,[l(y,{size:32,src:s(t).revenue_info.sharer_avatar,class:"flex-shrink-0"},null,8,["src"]),e("span",null,i(s(t).revenue_info.sharer_nickname),1)])]),_:1}),l(r,{label:"分成详情"},{default:a(()=>[e("div",ee,[e("div",te,[o[3]||(o[3]=e("span",null,"总消耗电力值：",-1)),e("span",se,i(s(t).revenue_info.total_cost),1)]),e("div",oe,[o[4]||(o[4]=e("span",null,"分成比例：",-1)),e("span",le,i(s(t).revenue_info.share_ratio)+"%",1)]),e("div",ne,[o[5]||(o[5]=e("span",null,"您的收益：",-1)),e("span",ae,"+"+i(s(t).revenue_info.share_amount),1)]),e("div",ie,[o[6]||(o[6]=e("span",null,"平台保留：",-1)),e("span",null,i(s(t).revenue_info.platform_amount),1)])])]),_:1}),l(r,{label:"结算信息"},{default:a(()=>[e("div",re,[e("div",null,[o[7]||(o[7]=e("span",{class:"text-sm text-gray-500"},"结算方式：",-1)),e("span",_e,i(s(t).revenue_info.settle_type_desc),1)]),s(t).revenue_info.settle_time?(m(),v("div",pe,[o[8]||(o[8]=e("span",{class:"text-sm text-gray-500"},"结算时间：",-1)),e("span",me,i(s(t).revenue_info.settle_time),1)])):u("",!0)])]),_:1}),l(P,{title:"收益说明",type:"info",closable:!1,"show-icon":""},{description:a(()=>o[9]||(o[9]=[e("div",{class:"text-sm"},[_(" • 通过使用智能体广场的智能体可获得分成收益"),e("br"),_(" • 分成收益将在每天凌晨自动结算并发放"),e("br"),_(" • 分成比例由智能体分享者设置决定 ")],-1)])),_:1})],64)):u("",!0),(E=(h=s(t))==null?void 0:h.flows)!=null&&E.length?(m(),j(r,{key:1,label:"扣费明细"})):u("",!0)]}),_:1}),(g=(c=s(t))==null?void 0:c.flows)!=null&&g.length?(m(),v("div",ue,[l($,{data:(k=s(t))==null?void 0:k.flows},{default:a(()=>[l(f,{prop:"name",label:"模块名称"}),l(f,{prop:"model",label:"AI模型"}),l(f,{prop:"total_price",label:`消耗${s(T).getTokenUnit}`},null,8,["label"])]),_:1},8,["data"])])):u("",!0)]}),_:1},512)}}});export{Pe as _};
