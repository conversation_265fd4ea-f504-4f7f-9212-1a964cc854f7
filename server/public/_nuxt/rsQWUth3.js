import{E as h}from"./DnE_5Yhr.js";import{_ as y}from"./BpYIl71c.js";import{co as g,o as w}from"./D726nzJl.js";/* empty css        */import"./DP2rzg_V.js";import{l as k,M as a,a1 as V,a0 as c,O as e,N as i,aq as b,_ as C,Z as r,u as d,a4 as I,a7 as B}from"./Dp9aCaJ6.js";const E={class:"flex-1 overflow-hidden"},N={class:"flex flex-wrap mx-[-6px]"},S=["onClick"],F={class:"pt-[100%] relative h-0 rounded-[12px] overflow-hidden"},L={class:"absolute inset-0 left-0 top-0"},M={key:0,class:"absolute bg-[var(--el-overlay-color-lighter)] inset-0 left-0 top-0 flex items-center justify-center text-white"},$={class:"text-center text-xs"},G=k({__name:"video-style",props:{modelValue:{default:()=>[]},styleList:{}},emits:["update:modelValue"],setup(m,{emit:u}){const p=u,_=m,{modelValue:t}=g(_,p),f=o=>{const l=t.value.findIndex(n=>n===o);l!==-1?t.value.splice(l,1):t.value.push(o)};return(o,l)=>{const n=h,x=y,v=w;return a(),V(v,null,{label:c(()=>l[0]||(l[0]=[e("div",{class:"w-full flex items-center"},[e("span",{class:"font-bold text-tx-primary flex-1"}," 选择风格 ")],-1)])),default:c(()=>[e("div",E,[e("div",N,[(a(!0),i(C,null,b(o.styleList,s=>(a(),i("div",{class:"w-[25%] px-[6px]",key:s.id},[e("div",{class:"h-full cursor-pointer",onClick:j=>f(s.id)},[e("div",F,[e("div",L,[r(n,{src:s.image,class:"h-full w-full"},null,8,["src"])]),d(t).includes(s.id)||d(t).includes(String(s.id))?(a(),i("div",M,[r(x,{name:"el-icon-SuccessFilled",size:20})])):I("",!0)]),e("div",$,B(s.name),1)],8,S)]))),128))])])]),_:1})}}});export{G as _};
