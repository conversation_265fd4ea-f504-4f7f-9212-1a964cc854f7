import{E as V}from"./B4i2sXD1.js";import{_ as B}from"./CuM_MOHA.js";import{_ as D}from"./DvrbA4QQ.js";import{E as L}from"./Cs34LOgA.js";import{_ as T}from"./CmwY1WGM.js";import{_ as N}from"./CN8DIg3d.js";import{a as M,l as O,b as P}from"./Ct33iMSA.js";/* empty css        *//* empty css        *//* empty css        */import{u as U}from"./DuO6be6_.js";import z from"./rfakkO9Z.js";import{_ as F}from"./CQ6ugP7V.js";import{_ as J}from"./BzX8M4zl.js";import{g as Z}from"./Bo3PTL3c.js";import{u as j}from"./B77oJvju.js";import{l as G,b as d,ak as H,c as K,M as a,N as f,Z as m,a0 as s,O as e,u as o,y as w,_ as Q,aq as W,a1 as h,a4 as y,a7 as X}from"./Dp9aCaJ6.js";import{_ as Y}from"./DlAUqK2U.js";import"./iAKPM1CD.js";import"./DCTLXrZ8.js";import"./l0sNRNKZ.js";import"./CbLH9y7N.js";import"./9Bti1uB6.js";import"./fF1yfb5-.js";import"./DOBoXv-W.js";import"./CxsMjoDo.js";/* empty css        */import"./C0aqmoaB.js";import"./Cv6HhfEG.js";import"./DWZQK6lH.js";/* empty css        */import"./DslESG0G.js";import"./CyyBcapk.js";import"./5L1sP24H.js";import"./BuZ1OskF.js";import"./CL661lr-.js";import"./DDmMBU6l.js";import"./3BjIQFFf.js";import"./DXT6IpgZ.js";import"./t4HvZ20D.js";import"./VtiWddsO.js";import"./BP61DoVF.js";import"./BHQFQfA3.js";import"./BDl3LJB7.js";import"./DlKZEFPo.js";import"./DP2rzg_V.js";/* empty css        *//* empty css        *//* empty css        */import"./BNnETjxs.js";import"./BAFBxC6W.js";import"./CMmFsH5J.js";import"./PnfXFZ_1.js";import"./kzeq9hfp.js";import"./SbrOwfyf.js";/* empty css        */import"./DTZFElWG.js";import"./Du5U7mLs.js";import"./CKTXp_fO.js";import"./DTrEzz9A.js";import"./Cz-ZBZVo.js";import"./BMo7Szn8.js";/* empty css        *//* empty css        */import"./VifW0IM0.js";import"./BOmaQ_iX.js";import"./Ba3KKPP1.js";import"./Cp9epHeJ.js";import"./DxKRx1wF.js";import"./BPaXy7Em.js";import"./BCfv4qMP.js";import"./BHaz_wmF.js";/* empty css        */import"./C6_W4ts7.js";import"./DmHustuG.js";import"./C_Ttdr_y.js";import"./BownwAu9.js";import"./BUn54r69.js";import"./DJ8m2TZq.js";import"./HaYB0y9G.js";import"./BZXCTEAI.js";/* empty css        *//* empty css        */import"./D16flgaF.js";import"./67xbGseh.js";import"./FgcQZDvz.js";import"./C7pFHulk.js";import"./DEUV9nh4.js";import"./DmGK8OfV.js";import"./CcPlX2kz.js";import"./Dp1MVQvw.js";import"./b5dYIutT.js";import"./BjeoGP1y.js";import"./QtpRY950.js";import"./CSt9LTZT.js";import"./DDmlxYr9.js";import"./GiT_E_F_.js";import"./BMN1ditj.js";import"./ll_jwVuV.js";import"./BLxRo6nR.js";import"./Dr_Xa85m.js";import"./BVEJZaAX.js";import"./BelJuF0-.js";import"./C53WLuoX.js";import"./CwtuiZRe.js";import"./CeoT0ppp.js";import"./PIZLygzx.js";import"./BFbcoQee.js";import"./Cpg3PDWZ.js";const tt={class:"h-full flex"},ot=["onClick"],rt=["src"],et={class:"ml-[8px] line-clamp-1"},it={class:"flex items-center cursor-pointer"},pt={class:"text-xl flex-1 min-w-0"},mt={class:"sm:h-full py-[16px] pr-[16px] flex flex-col sm:flex-row flex-1 min-w-0"},nt={class:"sm:h-full flex-1 min-w-0 min-h-0 bg-body rounded-[12px]"},at=G({__name:"setting",async setup(st){let c,x;const i=M(),u=O();P();const v=j();v.getRobot();const _=d(!1),l=d(i.query.id),{data:b,refresh:k}=([c,x]=H(()=>U(()=>Z({id:l.value}),{transform(t){return(t==null?void 0:t.category_id)===0&&(t.category_id=""),t},default(){return{}},lazy:!0},"$3nIwi7TB6J")),c=await c,x(),c),p=d("edit"),q=[{name:"智能体设置",icon:"el-icon-Setting",key:"edit"},{name:"发布智能体",key:"release",icon:"el-icon-Position"},{name:"对话数据",key:"dialogue",icon:"el-icon-ChatDotRound"},{name:"立即对话",key:"chat",icon:"el-icon-ChatLineRound"}],S=t=>{switch(t){case"chat":u.push({path:"/application/chat",query:{id:l.value}});break;default:u.replace({path:i.path,query:{...i.query,currentTab:t}})}},C=async t=>{_.value=!1,t!=i.query.id&&(l.value=t,await k(),u.replace({path:i.path,query:{...i.query,id:t}}))};return K(()=>i.query,t=>{p.value=t.currentTab||"edit"},{immediate:!0}),(t,n)=>{const g=V,R=B,A=D,E=L,$=T,I=N;return a(),f("div",tt,[m($,{modelValue:o(p),"onUpdate:modelValue":[n[1]||(n[1]=r=>w(p)?p.value=r:null),S],"menu-list":q,"back-path":"/application/layout/robot"},{title:s(()=>[e("div",null,[m(E,{placement:"bottom",width:180,trigger:"click","show-arrow":!1,transition:"custom-popover",visible:o(_),"onUpdate:visible":n[0]||(n[0]=r=>w(_)?_.value=r:null)},{reference:s(()=>[e("div",it,[e("div",pt,[m(R,{content:o(b).name,teleported:!0,effect:"light"},null,8,["content"])]),m(A,{name:"el-icon-ArrowDown"})])]),default:s(()=>[e("div",null,[m(g,{style:{height:"250px"}},{default:s(()=>[(a(!0),f(Q,null,W(o(v).robotLists,r=>(a(),f("div",{class:"flex items-center leading-10 cursor-pointer hover:bg-primary-light-9 px-[10px] my-[5px] rounded-[12px] hover:text-primary",key:r.id,onClick:lt=>C(r.id)},[e("img",{class:"rounded-[50%] w-[28px] h-[28px] flex-none",src:r.image,alt:""},null,8,rt),e("div",et,X(r.name),1)],8,ot))),128))]),_:1})])]),_:1},8,["visible"])])]),_:1},8,["modelValue"]),e("div",mt,[e("div",nt,[o(p)==="edit"?(a(),h(z,{key:0,"model-value":o(b),onSuccess:n[2]||(n[2]=r=>o(u).push("/application/layout/robot"))},null,8,["model-value"])):y("",!0),m(I,null,{default:s(()=>[o(p)==="release"?(a(),h(g,{key:0},{default:s(()=>[m(F,{"app-id":o(l)},null,8,["app-id"])]),_:1})):y("",!0)]),_:1}),o(p)==="dialogue"?(a(),h(J,{key:1,"app-id":o(l)},null,8,["app-id"])):y("",!0)])])])}}}),_r=Y(at,[["__scopeId","data-v-21d9b61a"]]);export{_r as default};
