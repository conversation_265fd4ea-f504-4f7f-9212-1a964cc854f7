import{a as p,E as n}from"./Dt1L8u-1.js";import{V as f}from"./DGzblORL.js";import"./l0sNRNKZ.js";/* empty css        */import{l as c,j as u,b as d,M as m,N as v,Z as w,a0 as y,W as h,u as _,O as b,a5 as x,a2 as g}from"./uahP8ofS.js";const k={class:"overflow-tooltip"},C=c({__name:"index",props:{...p,teleported:{type:Boolean,default:!1},placement:{type:String,default:"top"},overflowType:{type:String,default:"ellipsis"},line:{type:Number,default:1}},setup(a){const r=a,e=u(),t=d(!1);return f(e,"mouseenter",()=>{var o,s,l,i;if(r.disabled){t.value=!0;return}((o=e.value)==null?void 0:o.scrollWidth)>((s=e.value)==null?void 0:s.offsetWidth)||((l=e.value)==null?void 0:l.scrollHeight)>((i=e.value)==null?void 0:i.offsetHeight)?t.value=!1:t.value=!0}),(o,s)=>{const l=n;return m(),v("div",k,[w(l,h(r,{"popper-class":"overflow-tooltip-popper whitespace-pre-wrap",disabled:_(t)}),{default:y(()=>[b("div",{ref_key:"textRef",ref:e,class:"overflow-text line-clamp-1",style:g({textOverflow:a.overflowType,"-webkit-line-clamp":a.line})},x(o.content),5)]),_:1},16,["disabled"])])}}});export{C as _};
