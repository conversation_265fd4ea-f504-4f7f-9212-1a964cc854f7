import{f as ns}from"./D726nzJl.js";const rs=window.setInterval;/*!
 * html2canvas 1.4.1 <https://html2canvas.hertzen.com>
 * Copyright (c) 2022 <PERSON><PERSON> <https://hertzen.com>
 * Released under MIT License
 *//*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var pt=function(e,A){return pt=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,t){n.__proto__=t}||function(n,t){for(const r in t)Object.prototype.hasOwnProperty.call(t,r)&&(n[r]=t[r])},pt(e,A)};function nA(e,A){if(typeof A!="function"&&A!==null)throw new TypeError("Class extends value "+String(A)+" is not a constructor or null");pt(e,A);function n(){this.constructor=e}e.prototype=A===null?Object.create(A):(n.prototype=A.prototype,new n)}var It=function(){return It=Object.assign||function(A){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(const s in n)Object.prototype.hasOwnProperty.call(n,s)&&(A[s]=n[s])}return A},It.apply(this,arguments)};function k(e,A,n,t){function r(s){return s instanceof n?s:new n(function(B){B(s)})}return new(n||(n=Promise))(function(s,B){function o(a){try{c(t.next(a))}catch(Q){B(Q)}}function i(a){try{c(t.throw(a))}catch(Q){B(Q)}}function c(a){a.done?s(a.value):r(a.value).then(o,i)}c((t=t.apply(e,[])).next())})}function P(e,A){let n={label:0,sent:function(){if(s[0]&1)throw s[1];return s[1]},trys:[],ops:[]},t,r,s,B;return B={next:o(0),throw:o(1),return:o(2)},typeof Symbol=="function"&&(B[Symbol.iterator]=function(){return this}),B;function o(c){return function(a){return i([c,a])}}function i(c){if(t)throw new TypeError("Generator is already executing.");for(;n;)try{if(t=1,r&&(s=c[0]&2?r.return:c[0]?r.throw||((s=r.return)&&s.call(r),0):r.next)&&!(s=s.call(r,c[1])).done)return s;switch(r=0,s&&(c=[c[0]&2,s.value]),c[0]){case 0:case 1:s=c;break;case 4:return n.label++,{value:c[1],done:!1};case 5:n.label++,r=c[1],c=[0];continue;case 7:c=n.ops.pop(),n.trys.pop();continue;default:if(s=n.trys,!(s=s.length>0&&s[s.length-1])&&(c[0]===6||c[0]===2)){n=0;continue}if(c[0]===3&&(!s||c[1]>s[0]&&c[1]<s[3])){n.label=c[1];break}if(c[0]===6&&n.label<s[1]){n.label=s[1],s=c;break}if(s&&n.label<s[2]){n.label=s[2],n.ops.push(c);break}s[2]&&n.ops.pop(),n.trys.pop();continue}c=A.call(e,n)}catch(a){c=[6,a],r=0}finally{t=s=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}}function ue(e,A,n){if(arguments.length===2)for(var t=0,r=A.length,s;t<r;t++)(s||!(t in A))&&(s||(s=Array.prototype.slice.call(A,0,t)),s[t]=A[t]);return e.concat(s||A)}const wA=function(){function e(A,n,t,r){this.left=A,this.top=n,this.width=t,this.height=r}return e.prototype.add=function(A,n,t,r){return new e(this.left+A,this.top+n,this.width+t,this.height+r)},e.fromClientRect=function(A,n){return new e(n.left+A.windowBounds.left,n.top+A.windowBounds.top,n.width,n.height)},e.fromDOMRectList=function(A,n){const t=Array.from(n).find(function(r){return r.width!==0});return t?new e(t.left+A.windowBounds.left,t.top+A.windowBounds.top,t.width,t.height):e.EMPTY},e.EMPTY=new e(0,0,0,0),e}(),Ye=function(e,A){return wA.fromClientRect(e,A.getBoundingClientRect())},ss=function(e){const A=e.body,n=e.documentElement;if(!A||!n)throw new Error("Unable to get document size");const t=Math.max(Math.max(A.scrollWidth,n.scrollWidth),Math.max(A.offsetWidth,n.offsetWidth),Math.max(A.clientWidth,n.clientWidth)),r=Math.max(Math.max(A.scrollHeight,n.scrollHeight),Math.max(A.offsetHeight,n.offsetHeight),Math.max(A.clientHeight,n.clientHeight));return new wA(0,0,t,r)},We=function(e){const A=[];let n=0;const t=e.length;for(;n<t;){const r=e.charCodeAt(n++);if(r>=55296&&r<=56319&&n<t){const s=e.charCodeAt(n++);(s&64512)===56320?A.push(((r&1023)<<10)+(s&1023)+65536):(A.push(r),n--)}else A.push(r)}return A},O=function(){const e=[];for(let s=0;s<arguments.length;s++)e[s]=arguments[s];if(String.fromCodePoint)return String.fromCodePoint.apply(String,e);const A=e.length;if(!A)return"";const n=[];let t=-1,r="";for(;++t<A;){let s=e[t];s<=65535?n.push(s):(s-=65536,n.push((s>>10)+55296,s%1024+56320)),(t+1===A||n.length>16384)&&(r+=String.fromCharCode.apply(String,n),n.length=0)}return r},An="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Bs=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(let e=0;e<An.length;e++)Bs[An.charCodeAt(e)]=e;const en="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",zA=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(let e=0;e<en.length;e++)zA[en.charCodeAt(e)]=e;const os=function(e){let A=e.length*.75,n=e.length,t,r=0,s,B,o,i;e[e.length-1]==="="&&(A--,e[e.length-2]==="="&&A--);const c=typeof ArrayBuffer<"u"&&typeof Uint8Array<"u"&&typeof Uint8Array.prototype.slice<"u"?new ArrayBuffer(A):new Array(A),a=Array.isArray(c)?c:new Uint8Array(c);for(t=0;t<n;t+=4)s=zA[e.charCodeAt(t)],B=zA[e.charCodeAt(t+1)],o=zA[e.charCodeAt(t+2)],i=zA[e.charCodeAt(t+3)],a[r++]=s<<2|B>>4,a[r++]=(B&15)<<4|o>>2,a[r++]=(o&3)<<6|i&63;return c},is=function(e){const A=e.length,n=[];for(let t=0;t<A;t+=2)n.push(e[t+1]<<8|e[t]);return n},cs=function(e){const A=e.length,n=[];for(let t=0;t<A;t+=4)n.push(e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]);return n},DA=5,Yt=11,nt=2,as=Yt-DA,Ar=65536>>DA,Qs=1<<DA,rt=Qs-1,gs=1024>>DA,ws=Ar+gs,ls=ws,us=32,Cs=ls+us,fs=65536>>Yt,Us=1<<as,Fs=Us-1,tn=function(e,A,n){return e.slice?e.slice(A,n):new Uint16Array(Array.prototype.slice.call(e,A,n))},hs=function(e,A,n){return e.slice?e.slice(A,n):new Uint32Array(Array.prototype.slice.call(e,A,n))},ds=function(e,A){const n=os(e),t=Array.isArray(n)?cs(n):new Uint32Array(n),r=Array.isArray(n)?is(n):new Uint16Array(n),s=24,B=tn(r,s/2,t[4]/2),o=t[5]===2?tn(r,(s+t[4])/2):hs(t,Math.ceil((s+t[4])/4));return new Es(t[0],t[1],t[2],t[3],B,o)};var Es=function(){function e(A,n,t,r,s,B){this.initialValue=A,this.errorValue=n,this.highStart=t,this.highValueIndex=r,this.index=s,this.data=B}return e.prototype.get=function(A){let n;if(A>=0){if(A<55296||A>56319&&A<=65535)return n=this.index[A>>DA],n=(n<<nt)+(A&rt),this.data[n];if(A<=65535)return n=this.index[Ar+(A-55296>>DA)],n=(n<<nt)+(A&rt),this.data[n];if(A<this.highStart)return n=Cs-fs+(A>>Yt),n=this.index[n],n+=A>>DA&Fs,n=this.index[n],n=(n<<nt)+(A&rt),this.data[n];if(A<=1114111)return this.data[this.highValueIndex]}return this.errorValue},e}();const nn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Hs=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(let e=0;e<nn.length;e++)Hs[nn.charCodeAt(e)]=e;const ps="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",rn=50,Is=1,er=2,tr=3,ys=4,ms=5,sn=7,nr=8,Bn=9,FA=10,yt=11,on=12,mt=13,Ks=14,$A=15,Kt=16,Ce=17,WA=18,Ls=19,cn=20,Lt=21,ZA=22,st=23,OA=24,q=25,Ae=26,ee=27,MA=28,bs=29,bA=30,xs=31,fe=32,Ue=33,bt=34,xt=35,Dt=36,ae=37,Tt=38,Te=39,Se=40,Bt=41,rr=42,Ds=43,Ts=[9001,65288],sr="!",H="×",Fe="÷",St=ds(ps),aA=[bA,Dt],Ot=[Is,er,tr,ms],Br=[FA,nr],an=[ee,Ae],Ss=Ot.concat(Br),Qn=[Tt,Te,Se,bt,xt],Os=[$A,mt],Ms=function(e,A){A===void 0&&(A="strict");const n=[],t=[],r=[];return e.forEach(function(s,B){let o=St.get(s);if(o>rn?(r.push(!0),o-=rn):r.push(!1),["normal","auto","loose"].indexOf(A)!==-1&&[8208,8211,12316,12448].indexOf(s)!==-1)return t.push(B),n.push(Kt);if(o===ys||o===yt){if(B===0)return t.push(B),n.push(bA);const i=n[B-1];return Ss.indexOf(i)===-1?(t.push(t[B-1]),n.push(i)):(t.push(B),n.push(bA))}if(t.push(B),o===xs)return n.push(A==="strict"?Lt:ae);if(o===rr||o===bs)return n.push(bA);if(o===Ds)return s>=131072&&s<=196605||s>=196608&&s<=262141?n.push(ae):n.push(bA);n.push(o)}),[t,n,r]},ot=function(e,A,n,t){const r=t[n];if(Array.isArray(e)?e.indexOf(r)!==-1:e===r)for(var s=n;s<=t.length;){s++;var B=t[s];if(B===A)return!0;if(B!==FA)break}if(r===FA)for(var s=n;s>0;){s--;const i=t[s];if(Array.isArray(e)?e.indexOf(i)!==-1:e===i){let c=n;for(;c<=t.length;){c++;var B=t[c];if(B===A)return!0;if(B!==FA)break}}if(i!==FA)break}return!1},gn=function(e,A){let n=e;for(;n>=0;){const t=A[n];if(t===FA)n--;else return t}return 0},Gs=function(e,A,n,t,r){if(n[t]===0)return H;const s=t-1;if(Array.isArray(r)&&r[s]===!0)return H;const B=s-1,o=s+1,i=A[s],c=B>=0?A[B]:0,a=A[o];if(i===er&&a===tr)return H;if(Ot.indexOf(i)!==-1)return sr;if(Ot.indexOf(a)!==-1||Br.indexOf(a)!==-1)return H;if(gn(s,A)===nr)return Fe;if(St.get(e[s])===yt||(i===fe||i===Ue)&&St.get(e[o])===yt||i===sn||a===sn||i===Bn||[FA,mt,$A].indexOf(i)===-1&&a===Bn||[Ce,WA,Ls,OA,MA].indexOf(a)!==-1||gn(s,A)===ZA||ot(st,ZA,s,A)||ot([Ce,WA],Lt,s,A)||ot(on,on,s,A))return H;if(i===FA)return Fe;if(i===st||a===st)return H;if(a===Kt||i===Kt)return Fe;if([mt,$A,Lt].indexOf(a)!==-1||i===Ks||c===Dt&&Os.indexOf(i)!==-1||i===MA&&a===Dt||a===cn||aA.indexOf(a)!==-1&&i===q||aA.indexOf(i)!==-1&&a===q||i===ee&&[ae,fe,Ue].indexOf(a)!==-1||[ae,fe,Ue].indexOf(i)!==-1&&a===Ae||aA.indexOf(i)!==-1&&an.indexOf(a)!==-1||an.indexOf(i)!==-1&&aA.indexOf(a)!==-1||[ee,Ae].indexOf(i)!==-1&&(a===q||[ZA,$A].indexOf(a)!==-1&&A[o+1]===q)||[ZA,$A].indexOf(i)!==-1&&a===q||i===q&&[q,MA,OA].indexOf(a)!==-1)return H;if([q,MA,OA,Ce,WA].indexOf(a)!==-1)for(var Q=s;Q>=0;){var g=A[Q];if(g===q)return H;if([MA,OA].indexOf(g)!==-1)Q--;else break}if([ee,Ae].indexOf(a)!==-1)for(var Q=[Ce,WA].indexOf(i)!==-1?B:s;Q>=0;){var g=A[Q];if(g===q)return H;if([MA,OA].indexOf(g)!==-1)Q--;else break}if(Tt===i&&[Tt,Te,bt,xt].indexOf(a)!==-1||[Te,bt].indexOf(i)!==-1&&[Te,Se].indexOf(a)!==-1||[Se,xt].indexOf(i)!==-1&&a===Se||Qn.indexOf(i)!==-1&&[cn,Ae].indexOf(a)!==-1||Qn.indexOf(a)!==-1&&i===ee||aA.indexOf(i)!==-1&&aA.indexOf(a)!==-1||i===OA&&aA.indexOf(a)!==-1||aA.concat(q).indexOf(i)!==-1&&a===ZA&&Ts.indexOf(e[o])===-1||aA.concat(q).indexOf(a)!==-1&&i===WA)return H;if(i===Bt&&a===Bt){let l=n[s],w=1;for(;l>0&&(l--,A[l]===Bt);)w++;if(w%2!==0)return H}return i===fe&&a===Ue?H:Fe},Rs=function(e,A){A||(A={lineBreak:"normal",wordBreak:"normal"});let n=Ms(e,A.lineBreak),t=n[0],r=n[1],s=n[2];(A.wordBreak==="break-all"||A.wordBreak==="break-word")&&(r=r.map(function(o){return[q,bA,rr].indexOf(o)!==-1?ae:o}));const B=A.wordBreak==="keep-all"?s.map(function(o,i){return o&&e[i]>=19968&&e[i]<=40959}):void 0;return[t,r,B]},Vs=function(){function e(A,n,t,r){this.codePoints=A,this.required=n===sr,this.start=t,this.end=r}return e.prototype.slice=function(){return O.apply(void 0,this.codePoints.slice(this.start,this.end))},e}(),Ns=function(e,A){const n=We(e),t=Rs(n,A),r=t[0],s=t[1],B=t[2],o=n.length;let i=0,c=0;return{next:function(){if(c>=o)return{done:!0,value:null};let a=H;for(;c<o&&(a=Gs(n,s,r,++c,B))===H;);if(a!==H||c===o){const Q=new Vs(n,a,i,c);return i=c,{value:Q,done:!1}}return{done:!0,value:null}}}},_s=1,vs=2,we=4,wn=8,Ge=10,ln=47,se=92,Ps=9,Xs=32,he=34,qA=61,ks=35,Js=36,Ys=37,de=39,Ee=40,jA=41,Ws=95,W=45,Zs=33,qs=60,js=62,zs=64,$s=91,AB=93,eB=61,tB=123,He=63,nB=125,un=124,rB=126,sB=128,Cn=65533,it=42,xA=43,BB=44,oB=58,iB=59,Qe=46,cB=0,aB=8,QB=11,gB=14,wB=31,lB=127,sA=-1,or=48,ir=97,cr=101,uB=102,CB=117,fB=122,ar=65,Qr=69,gr=70,UB=85,FB=90,X=function(e){return e>=or&&e<=57},hB=function(e){return e>=55296&&e<=57343},GA=function(e){return X(e)||e>=ar&&e<=gr||e>=ir&&e<=uB},dB=function(e){return e>=ir&&e<=fB},EB=function(e){return e>=ar&&e<=FB},HB=function(e){return dB(e)||EB(e)},pB=function(e){return e>=sB},pe=function(e){return e===Ge||e===Ps||e===Xs},Re=function(e){return HB(e)||pB(e)||e===Ws},fn=function(e){return Re(e)||X(e)||e===W},IB=function(e){return e>=cB&&e<=aB||e===QB||e>=gB&&e<=wB||e===lB},UA=function(e,A){return e!==se?!1:A!==Ge},Ie=function(e,A,n){return e===W?Re(A)||UA(A,n):Re(e)?!0:!!(e===se&&UA(e,A))},ct=function(e,A,n){return e===xA||e===W?X(A)?!0:A===Qe&&X(n):X(e===Qe?A:e)},yB=function(e){let A=0,n=1;(e[A]===xA||e[A]===W)&&(e[A]===W&&(n=-1),A++);const t=[];for(;X(e[A]);)t.push(e[A++]);const r=t.length?parseInt(O.apply(void 0,t),10):0;e[A]===Qe&&A++;const s=[];for(;X(e[A]);)s.push(e[A++]);const B=s.length,o=B?parseInt(O.apply(void 0,s),10):0;(e[A]===Qr||e[A]===cr)&&A++;let i=1;(e[A]===xA||e[A]===W)&&(e[A]===W&&(i=-1),A++);const c=[];for(;X(e[A]);)c.push(e[A++]);const a=c.length?parseInt(O.apply(void 0,c),10):0;return n*(r+o*Math.pow(10,-B))*Math.pow(10,i*a)},mB={type:2},KB={type:3},LB={type:4},bB={type:13},xB={type:8},DB={type:21},TB={type:9},SB={type:10},OB={type:11},MB={type:12},GB={type:14},ye={type:23},RB={type:1},VB={type:25},NB={type:24},_B={type:26},vB={type:27},PB={type:28},XB={type:29},kB={type:31},Mt={type:32},wr=function(){function e(){this._value=[]}return e.prototype.write=function(A){this._value=this._value.concat(We(A))},e.prototype.read=function(){const A=[];let n=this.consumeToken();for(;n!==Mt;)A.push(n),n=this.consumeToken();return A},e.prototype.consumeToken=function(){const A=this.consumeCodePoint();switch(A){case he:return this.consumeStringToken(he);case ks:var n=this.peekCodePoint(0),t=this.peekCodePoint(1),r=this.peekCodePoint(2);if(fn(n)||UA(t,r)){const w=Ie(n,t,r)?vs:_s;var s=this.consumeName();return{type:5,value:s,flags:w}}break;case Js:if(this.peekCodePoint(0)===qA)return this.consumeCodePoint(),bB;break;case de:return this.consumeStringToken(de);case Ee:return mB;case jA:return KB;case it:if(this.peekCodePoint(0)===qA)return this.consumeCodePoint(),GB;break;case xA:if(ct(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case BB:return LB;case W:var B=A,o=this.peekCodePoint(0),i=this.peekCodePoint(1);if(ct(B,o,i))return this.reconsumeCodePoint(A),this.consumeNumericToken();if(Ie(B,o,i))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();if(o===W&&i===js)return this.consumeCodePoint(),this.consumeCodePoint(),NB;break;case Qe:if(ct(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case ln:if(this.peekCodePoint(0)===it)for(this.consumeCodePoint();;){let w=this.consumeCodePoint();if(w===it&&(w=this.consumeCodePoint(),w===ln))return this.consumeToken();if(w===sA)return this.consumeToken()}break;case oB:return _B;case iB:return vB;case qs:if(this.peekCodePoint(0)===Zs&&this.peekCodePoint(1)===W&&this.peekCodePoint(2)===W)return this.consumeCodePoint(),this.consumeCodePoint(),VB;break;case zs:var c=this.peekCodePoint(0),a=this.peekCodePoint(1),Q=this.peekCodePoint(2);if(Ie(c,a,Q)){var s=this.consumeName();return{type:7,value:s}}break;case $s:return PB;case se:if(UA(A,this.peekCodePoint(0)))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();break;case AB:return XB;case eB:if(this.peekCodePoint(0)===qA)return this.consumeCodePoint(),xB;break;case tB:return OB;case nB:return MB;case CB:case UB:var g=this.peekCodePoint(0),l=this.peekCodePoint(1);return g===xA&&(GA(l)||l===He)&&(this.consumeCodePoint(),this.consumeUnicodeRangeToken()),this.reconsumeCodePoint(A),this.consumeIdentLikeToken();case un:if(this.peekCodePoint(0)===qA)return this.consumeCodePoint(),TB;if(this.peekCodePoint(0)===un)return this.consumeCodePoint(),DB;break;case rB:if(this.peekCodePoint(0)===qA)return this.consumeCodePoint(),SB;break;case sA:return Mt}return pe(A)?(this.consumeWhiteSpace(),kB):X(A)?(this.reconsumeCodePoint(A),this.consumeNumericToken()):Re(A)?(this.reconsumeCodePoint(A),this.consumeIdentLikeToken()):{type:6,value:O(A)}},e.prototype.consumeCodePoint=function(){const A=this._value.shift();return typeof A>"u"?-1:A},e.prototype.reconsumeCodePoint=function(A){this._value.unshift(A)},e.prototype.peekCodePoint=function(A){return A>=this._value.length?-1:this._value[A]},e.prototype.consumeUnicodeRangeToken=function(){const A=[];let n=this.consumeCodePoint();for(;GA(n)&&A.length<6;)A.push(n),n=this.consumeCodePoint();let t=!1;for(;n===He&&A.length<6;)A.push(n),n=this.consumeCodePoint(),t=!0;if(t){const B=parseInt(O.apply(void 0,A.map(function(o){return o===He?or:o})),16);var r=parseInt(O.apply(void 0,A.map(function(o){return o===He?gr:o})),16);return{type:30,start:B,end:r}}const s=parseInt(O.apply(void 0,A),16);if(this.peekCodePoint(0)===W&&GA(this.peekCodePoint(1))){this.consumeCodePoint(),n=this.consumeCodePoint();const B=[];for(;GA(n)&&B.length<6;)B.push(n),n=this.consumeCodePoint();var r=parseInt(O.apply(void 0,B),16);return{type:30,start:s,end:r}}else return{type:30,start:s,end:s}},e.prototype.consumeIdentLikeToken=function(){const A=this.consumeName();return A.toLowerCase()==="url"&&this.peekCodePoint(0)===Ee?(this.consumeCodePoint(),this.consumeUrlToken()):this.peekCodePoint(0)===Ee?(this.consumeCodePoint(),{type:19,value:A}):{type:20,value:A}},e.prototype.consumeUrlToken=function(){const A=[];if(this.consumeWhiteSpace(),this.peekCodePoint(0)===sA)return{type:22,value:""};const n=this.peekCodePoint(0);if(n===de||n===he){const t=this.consumeStringToken(this.consumeCodePoint());return t.type===0&&(this.consumeWhiteSpace(),this.peekCodePoint(0)===sA||this.peekCodePoint(0)===jA)?(this.consumeCodePoint(),{type:22,value:t.value}):(this.consumeBadUrlRemnants(),ye)}for(;;){const t=this.consumeCodePoint();if(t===sA||t===jA)return{type:22,value:O.apply(void 0,A)};if(pe(t))return this.consumeWhiteSpace(),this.peekCodePoint(0)===sA||this.peekCodePoint(0)===jA?(this.consumeCodePoint(),{type:22,value:O.apply(void 0,A)}):(this.consumeBadUrlRemnants(),ye);if(t===he||t===de||t===Ee||IB(t))return this.consumeBadUrlRemnants(),ye;if(t===se)if(UA(t,this.peekCodePoint(0)))A.push(this.consumeEscapedCodePoint());else return this.consumeBadUrlRemnants(),ye;else A.push(t)}},e.prototype.consumeWhiteSpace=function(){for(;pe(this.peekCodePoint(0));)this.consumeCodePoint()},e.prototype.consumeBadUrlRemnants=function(){for(;;){const A=this.consumeCodePoint();if(A===jA||A===sA)return;UA(A,this.peekCodePoint(0))&&this.consumeEscapedCodePoint()}},e.prototype.consumeStringSlice=function(A){let t="";for(;A>0;){const r=Math.min(5e4,A);t+=O.apply(void 0,this._value.splice(0,r)),A-=r}return this._value.shift(),t},e.prototype.consumeStringToken=function(A){let n="",t=0;do{const r=this._value[t];if(r===sA||r===void 0||r===A)return n+=this.consumeStringSlice(t),{type:0,value:n};if(r===Ge)return this._value.splice(0,t),RB;if(r===se){const s=this._value[t+1];s!==sA&&s!==void 0&&(s===Ge?(n+=this.consumeStringSlice(t),t=-1,this._value.shift()):UA(r,s)&&(n+=this.consumeStringSlice(t),n+=O(this.consumeEscapedCodePoint()),t=-1))}t++}while(!0)},e.prototype.consumeNumber=function(){const A=[];let n=we,t=this.peekCodePoint(0);for((t===xA||t===W)&&A.push(this.consumeCodePoint());X(this.peekCodePoint(0));)A.push(this.consumeCodePoint());t=this.peekCodePoint(0);let r=this.peekCodePoint(1);if(t===Qe&&X(r))for(A.push(this.consumeCodePoint(),this.consumeCodePoint()),n=wn;X(this.peekCodePoint(0));)A.push(this.consumeCodePoint());t=this.peekCodePoint(0),r=this.peekCodePoint(1);const s=this.peekCodePoint(2);if((t===Qr||t===cr)&&((r===xA||r===W)&&X(s)||X(r)))for(A.push(this.consumeCodePoint(),this.consumeCodePoint()),n=wn;X(this.peekCodePoint(0));)A.push(this.consumeCodePoint());return[yB(A),n]},e.prototype.consumeNumericToken=function(){const A=this.consumeNumber(),n=A[0],t=A[1],r=this.peekCodePoint(0),s=this.peekCodePoint(1),B=this.peekCodePoint(2);if(Ie(r,s,B)){const o=this.consumeName();return{type:15,number:n,flags:t,unit:o}}return r===Ys?(this.consumeCodePoint(),{type:16,number:n,flags:t}):{type:17,number:n,flags:t}},e.prototype.consumeEscapedCodePoint=function(){const A=this.consumeCodePoint();if(GA(A)){let n=O(A);for(;GA(this.peekCodePoint(0))&&n.length<6;)n+=O(this.consumeCodePoint());pe(this.peekCodePoint(0))&&this.consumeCodePoint();const t=parseInt(n,16);return t===0||hB(t)||t>1114111?Cn:t}return A===sA?Cn:A},e.prototype.consumeName=function(){let A="";for(;;){const n=this.consumeCodePoint();if(fn(n))A+=O(n);else if(UA(n,this.peekCodePoint(0)))A+=O(this.consumeEscapedCodePoint());else return this.reconsumeCodePoint(n),A}},e}(),lr=function(){function e(A){this._tokens=A}return e.create=function(A){const n=new wr;return n.write(A),new e(n.read())},e.parseValue=function(A){return e.create(A).parseComponentValue()},e.parseValues=function(A){return e.create(A).parseComponentValues()},e.prototype.parseComponentValue=function(){let A=this.consumeToken();for(;A.type===31;)A=this.consumeToken();if(A.type===32)throw new SyntaxError("Error parsing CSS component value, unexpected EOF");this.reconsumeToken(A);const n=this.consumeComponentValue();do A=this.consumeToken();while(A.type===31);if(A.type===32)return n;throw new SyntaxError("Error parsing CSS component value, multiple values found when expecting only one")},e.prototype.parseComponentValues=function(){const A=[];for(;;){const n=this.consumeComponentValue();if(n.type===32)return A;A.push(n),A.push()}},e.prototype.consumeComponentValue=function(){const A=this.consumeToken();switch(A.type){case 11:case 28:case 2:return this.consumeSimpleBlock(A.type);case 19:return this.consumeFunction(A)}return A},e.prototype.consumeSimpleBlock=function(A){const n={type:A,values:[]};let t=this.consumeToken();for(;;){if(t.type===32||YB(t,A))return n;this.reconsumeToken(t),n.values.push(this.consumeComponentValue()),t=this.consumeToken()}},e.prototype.consumeFunction=function(A){const n={name:A.value,values:[],type:18};for(;;){const t=this.consumeToken();if(t.type===32||t.type===3)return n;this.reconsumeToken(t),n.values.push(this.consumeComponentValue())}},e.prototype.consumeToken=function(){const A=this._tokens.shift();return typeof A>"u"?Mt:A},e.prototype.reconsumeToken=function(A){this._tokens.unshift(A)},e}(),le=function(e){return e.type===15},JA=function(e){return e.type===17},b=function(e){return e.type===20},JB=function(e){return e.type===0},Gt=function(e,A){return b(e)&&e.value===A},ur=function(e){return e.type!==31},kA=function(e){return e.type!==31&&e.type!==4},BA=function(e){const A=[];let n=[];return e.forEach(function(t){if(t.type===4){if(n.length===0)throw new Error("Error parsing function args, zero tokens for arg");A.push(n),n=[];return}t.type!==31&&n.push(t)}),n.length&&A.push(n),A};var YB=function(e,A){return A===11&&e.type===12||A===28&&e.type===29?!0:A===2&&e.type===3};const pA=function(e){return e.type===17||e.type===15},G=function(e){return e.type===16||pA(e)},Cr=function(e){return e.length>1?[e[0],e[1]]:[e[0]]},v={type:17,number:0,flags:we},Wt={type:16,number:50,flags:we},hA={type:16,number:100,flags:we},te=function(e,A,n){const t=e[0],r=e[1];return[x(t,A),x(typeof r<"u"?r:t,n)]};var x=function(e,A){if(e.type===16)return e.number/100*A;if(le(e))switch(e.unit){case"rem":case"em":return 16*e.number;case"px":default:return e.number}return e.number};const fr="deg",Ur="grad",Fr="rad",hr="turn",Ze={name:"angle",parse:function(e,A){if(A.type===15)switch(A.unit){case fr:return Math.PI*A.number/180;case Ur:return Math.PI/200*A.number;case Fr:return A.number;case hr:return Math.PI*2*A.number}throw new Error("Unsupported angle type")}},dr=function(e){return e.type===15&&(e.unit===fr||e.unit===Ur||e.unit===Fr||e.unit===hr)},Er=function(e){switch(e.filter(b).map(function(n){return n.value}).join(" ")){case"to bottom right":case"to right bottom":case"left top":case"top left":return[v,v];case"to top":case"bottom":return AA(0);case"to bottom left":case"to left bottom":case"right top":case"top right":return[v,hA];case"to right":case"left":return AA(90);case"to top left":case"to left top":case"right bottom":case"bottom right":return[hA,hA];case"to bottom":case"top":return AA(180);case"to top right":case"to right top":case"left bottom":case"bottom left":return[hA,v];case"to left":case"right":return AA(270)}return 0};var AA=function(e){return Math.PI*e/180};const EA={name:"color",parse:function(e,A){if(A.type===18){const B=WB[A.name];if(typeof B>"u")throw new Error('Attempting to parse an unsupported color function "'+A.name+'"');return B(e,A.values)}if(A.type===5){if(A.value.length===3){var n=A.value.substring(0,1),t=A.value.substring(1,2),r=A.value.substring(2,3);return dA(parseInt(n+n,16),parseInt(t+t,16),parseInt(r+r,16),1)}if(A.value.length===4){var n=A.value.substring(0,1),t=A.value.substring(1,2),r=A.value.substring(2,3),s=A.value.substring(3,4);return dA(parseInt(n+n,16),parseInt(t+t,16),parseInt(r+r,16),parseInt(s+s,16)/255)}if(A.value.length===6){var n=A.value.substring(0,2),t=A.value.substring(2,4),r=A.value.substring(4,6);return dA(parseInt(n,16),parseInt(t,16),parseInt(r,16),1)}if(A.value.length===8){var n=A.value.substring(0,2),t=A.value.substring(2,4),r=A.value.substring(4,6),s=A.value.substring(6,8);return dA(parseInt(n,16),parseInt(t,16),parseInt(r,16),parseInt(s,16)/255)}}if(A.type===20){const B=gA[A.value.toUpperCase()];if(typeof B<"u")return B}return gA.TRANSPARENT}},HA=function(e){return(255&e)===0},V=function(e){const A=255&e,n=255&e>>8,t=255&e>>16,r=255&e>>24;return A<255?"rgba("+r+","+t+","+n+","+A/255+")":"rgb("+r+","+t+","+n+")"};var dA=function(e,A,n,t){return(e<<24|A<<16|n<<8|Math.round(t*255)<<0)>>>0};const Un=function(e,A){if(e.type===17)return e.number;if(e.type===16){const n=A===3?1:255;return A===3?e.number/100*n:Math.round(e.number/100*n)}return 0},Fn=function(e,A){const n=A.filter(kA);if(n.length===3){var t=n.map(Un),r=t[0],s=t[1],B=t[2];return dA(r,s,B,1)}if(n.length===4){var o=n.map(Un),r=o[0],s=o[1],B=o[2],i=o[3];return dA(r,s,B,i)}return 0};function at(e,A,n){return n<0&&(n+=1),n>=1&&(n-=1),n<1/6?(A-e)*n*6+e:n<1/2?A:n<2/3?(A-e)*6*(2/3-n)+e:e}const hn=function(e,A){const n=A.filter(kA),t=n[0],r=n[1],s=n[2],B=n[3],o=(t.type===17?AA(t.number):Ze.parse(e,t))/(Math.PI*2),i=G(r)?r.number/100:0,c=G(s)?s.number/100:0,a=typeof B<"u"&&G(B)?x(B,1):1;if(i===0)return dA(c*255,c*255,c*255,1);const Q=c<=.5?c*(i+1):c+i-c*i,g=c*2-Q,l=at(g,Q,o+1/3),w=at(g,Q,o),U=at(g,Q,o-1/3);return dA(l*255,w*255,U*255,a)};var WB={hsl:hn,hsla:hn,rgb:Fn,rgba:Fn};const Be=function(e,A){return EA.parse(e,lr.create(A).parseComponentValue())};var gA={ALICEBLUE:4042850303,ANTIQUEWHITE:4209760255,AQUA:16777215,AQUAMARINE:2147472639,AZURE:4043309055,BEIGE:4126530815,BISQUE:4293182719,BLACK:255,BLANCHEDALMOND:4293643775,BLUE:65535,BLUEVIOLET:2318131967,BROWN:2771004159,BURLYWOOD:3736635391,CADETBLUE:1604231423,CHARTREUSE:2147418367,CHOCOLATE:3530104575,CORAL:4286533887,CORNFLOWERBLUE:1687547391,CORNSILK:4294499583,CRIMSON:3692313855,CYAN:16777215,DARKBLUE:35839,DARKCYAN:9145343,DARKGOLDENROD:3095837695,DARKGRAY:2846468607,DARKGREEN:6553855,DARKGREY:2846468607,DARKKHAKI:3182914559,DARKMAGENTA:2332068863,DARKOLIVEGREEN:1433087999,DARKORANGE:4287365375,DARKORCHID:2570243327,DARKRED:2332033279,DARKSALMON:3918953215,DARKSEAGREEN:2411499519,DARKSLATEBLUE:1211993087,DARKSLATEGRAY:793726975,DARKSLATEGREY:793726975,DARKTURQUOISE:13554175,DARKVIOLET:2483082239,DEEPPINK:4279538687,DEEPSKYBLUE:12582911,DIMGRAY:1768516095,DIMGREY:1768516095,DODGERBLUE:512819199,FIREBRICK:2988581631,FLORALWHITE:4294635775,FORESTGREEN:579543807,FUCHSIA:4278255615,GAINSBORO:3705462015,GHOSTWHITE:4177068031,GOLD:4292280575,GOLDENROD:3668254975,GRAY:2155905279,GREEN:8388863,GREENYELLOW:2919182335,GREY:2155905279,HONEYDEW:4043305215,HOTPINK:4285117695,INDIANRED:3445382399,INDIGO:1258324735,IVORY:4294963455,KHAKI:4041641215,LAVENDER:3873897215,LAVENDERBLUSH:4293981695,LAWNGREEN:2096890111,LEMONCHIFFON:4294626815,LIGHTBLUE:2916673279,LIGHTCORAL:4034953471,LIGHTCYAN:3774873599,LIGHTGOLDENRODYELLOW:4210742015,LIGHTGRAY:3553874943,LIGHTGREEN:2431553791,LIGHTGREY:3553874943,LIGHTPINK:4290167295,LIGHTSALMON:4288707327,LIGHTSEAGREEN:548580095,LIGHTSKYBLUE:2278488831,LIGHTSLATEGRAY:2005441023,LIGHTSLATEGREY:2005441023,LIGHTSTEELBLUE:2965692159,LIGHTYELLOW:4294959359,LIME:16711935,LIMEGREEN:852308735,LINEN:4210091775,MAGENTA:4278255615,MAROON:2147483903,MEDIUMAQUAMARINE:1724754687,MEDIUMBLUE:52735,MEDIUMORCHID:3126187007,MEDIUMPURPLE:2473647103,MEDIUMSEAGREEN:1018393087,MEDIUMSLATEBLUE:2070474495,MEDIUMSPRINGGREEN:16423679,MEDIUMTURQUOISE:1221709055,MEDIUMVIOLETRED:3340076543,MIDNIGHTBLUE:421097727,MINTCREAM:4127193855,MISTYROSE:4293190143,MOCCASIN:4293178879,NAVAJOWHITE:4292783615,NAVY:33023,OLDLACE:4260751103,OLIVE:2155872511,OLIVEDRAB:1804477439,ORANGE:4289003775,ORANGERED:4282712319,ORCHID:3664828159,PALEGOLDENROD:4008225535,PALEGREEN:2566625535,PALETURQUOISE:2951671551,PALEVIOLETRED:3681588223,PAPAYAWHIP:4293907967,PEACHPUFF:4292524543,PERU:3448061951,PINK:4290825215,PLUM:3718307327,POWDERBLUE:2967529215,PURPLE:2147516671,REBECCAPURPLE:1714657791,RED:4278190335,ROSYBROWN:3163525119,ROYALBLUE:1097458175,SADDLEBROWN:2336560127,SALMON:4202722047,SANDYBROWN:4104413439,SEAGREEN:780883967,SEASHELL:4294307583,SIENNA:2689740287,SILVER:3233857791,SKYBLUE:2278484991,SLATEBLUE:1784335871,SLATEGRAY:1887473919,SLATEGREY:1887473919,SNOW:4294638335,SPRINGGREEN:16744447,STEELBLUE:1182971135,TAN:3535047935,TEAL:8421631,THISTLE:3636451583,TOMATO:4284696575,TRANSPARENT:0,TURQUOISE:1088475391,VIOLET:4001558271,WHEAT:4125012991,WHITE:4294967295,WHITESMOKE:4126537215,YELLOW:4294902015,YELLOWGREEN:2597139199};const ZB={name:"background-clip",initialValue:"border-box",prefix:!1,type:1,parse:function(e,A){return A.map(function(n){if(b(n))switch(n.value){case"padding-box":return 1;case"content-box":return 2}return 0})}},qB={name:"background-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},qe=function(e,A){const n=EA.parse(e,A[0]),t=A[1];return t&&G(t)?{color:n,stop:t}:{color:n,stop:null}},dn=function(e,A){const n=e[0],t=e[e.length-1];n.stop===null&&(n.stop=v),t.stop===null&&(t.stop=hA);const r=[];let s=0;for(var B=0;B<e.length;B++){const i=e[B].stop;if(i!==null){const c=x(i,A);c>s?r.push(c):r.push(s),s=c}else r.push(null)}let o=null;for(var B=0;B<r.length;B++){const c=r[B];if(c===null)o===null&&(o=B);else if(o!==null){const a=B-o,Q=r[o-1],g=(c-Q)/(a+1);for(let l=1;l<=a;l++)r[o+l-1]=g*l;o=null}}return e.map(function(i,c){return{color:i.color,stop:Math.max(Math.min(1,r[c]/A),0)}})},jB=function(e,A,n){const t=A/2,r=n/2,s=x(e[0],A)-t,B=r-x(e[1],n);return(Math.atan2(B,s)+Math.PI*2)%(Math.PI*2)},zB=function(e,A,n){const t=typeof e=="number"?e:jB(e,A,n),r=Math.abs(A*Math.sin(t))+Math.abs(n*Math.cos(t)),s=A/2,B=n/2,o=r/2,i=Math.sin(t-Math.PI/2)*o,c=Math.cos(t-Math.PI/2)*o;return[r,s-c,s+c,B-i,B+i]},tA=function(e,A){return Math.sqrt(e*e+A*A)},En=function(e,A,n,t,r){return[[0,0],[0,A],[e,0],[e,A]].reduce(function(B,o){const i=o[0],c=o[1],a=tA(n-i,t-c);return(r?a<B.optimumDistance:a>B.optimumDistance)?{optimumCorner:o,optimumDistance:a}:B},{optimumDistance:r?1/0:-1/0,optimumCorner:null}).optimumCorner},$B=function(e,A,n,t,r){let s=0,B=0;switch(e.size){case 0:e.shape===0?s=B=Math.min(Math.abs(A),Math.abs(A-t),Math.abs(n),Math.abs(n-r)):e.shape===1&&(s=Math.min(Math.abs(A),Math.abs(A-t)),B=Math.min(Math.abs(n),Math.abs(n-r)));break;case 2:if(e.shape===0)s=B=Math.min(tA(A,n),tA(A,n-r),tA(A-t,n),tA(A-t,n-r));else if(e.shape===1){var o=Math.min(Math.abs(n),Math.abs(n-r))/Math.min(Math.abs(A),Math.abs(A-t)),i=En(t,r,A,n,!0),c=i[0],a=i[1];s=tA(c-A,(a-n)/o),B=o*s}break;case 1:e.shape===0?s=B=Math.max(Math.abs(A),Math.abs(A-t),Math.abs(n),Math.abs(n-r)):e.shape===1&&(s=Math.max(Math.abs(A),Math.abs(A-t)),B=Math.max(Math.abs(n),Math.abs(n-r)));break;case 3:if(e.shape===0)s=B=Math.max(tA(A,n),tA(A,n-r),tA(A-t,n),tA(A-t,n-r));else if(e.shape===1){var o=Math.max(Math.abs(n),Math.abs(n-r))/Math.max(Math.abs(A),Math.abs(A-t)),Q=En(t,r,A,n,!1),c=Q[0],a=Q[1];s=tA(c-A,(a-n)/o),B=o*s}break}return Array.isArray(e.size)&&(s=x(e.size[0],t),B=e.size.length===2?x(e.size[1],r):s),[s,B]},Ao=function(e,A){let n=AA(180);const t=[];return BA(A).forEach(function(r,s){if(s===0){const o=r[0];if(o.type===20&&o.value==="to"){n=Er(r);return}else if(dr(o)){n=Ze.parse(e,o);return}}const B=qe(e,r);t.push(B)}),{angle:n,stops:t,type:1}},me=function(e,A){let n=AA(180);const t=[];return BA(A).forEach(function(r,s){if(s===0){const o=r[0];if(o.type===20&&["top","left","right","bottom"].indexOf(o.value)!==-1){n=Er(r);return}else if(dr(o)){n=(Ze.parse(e,o)+AA(270))%AA(360);return}}const B=qe(e,r);t.push(B)}),{angle:n,stops:t,type:1}},eo=function(e,A){const n=AA(180),t=[];let r=1;const s=0,B=3,o=[];return BA(A).forEach(function(i,c){const a=i[0];if(c===0){if(b(a)&&a.value==="linear"){r=1;return}else if(b(a)&&a.value==="radial"){r=2;return}}if(a.type===18){if(a.name==="from"){var Q=EA.parse(e,a.values[0]);t.push({stop:v,color:Q})}else if(a.name==="to"){var Q=EA.parse(e,a.values[0]);t.push({stop:hA,color:Q})}else if(a.name==="color-stop"){const g=a.values.filter(kA);if(g.length===2){var Q=EA.parse(e,g[1]);const w=g[0];JA(w)&&t.push({stop:{type:16,number:w.number*100,flags:w.flags},color:Q})}}}}),r===1?{angle:(n+AA(180))%AA(360),stops:t,type:r}:{size:B,shape:s,stops:t,position:o,type:r}},Hr="closest-side",pr="farthest-side",Ir="closest-corner",yr="farthest-corner",mr="circle",Kr="ellipse",Lr="cover",br="contain",to=function(e,A){let n=0,t=3;const r=[],s=[];return BA(A).forEach(function(B,o){let i=!0;if(o===0){let c=!1;i=B.reduce(function(a,Q){if(c)if(b(Q))switch(Q.value){case"center":return s.push(Wt),a;case"top":case"left":return s.push(v),a;case"right":case"bottom":return s.push(hA),a}else(G(Q)||pA(Q))&&s.push(Q);else if(b(Q))switch(Q.value){case mr:return n=0,!1;case Kr:return n=1,!1;case"at":return c=!0,!1;case Hr:return t=0,!1;case Lr:case pr:return t=1,!1;case br:case Ir:return t=2,!1;case yr:return t=3,!1}else if(pA(Q)||G(Q))return Array.isArray(t)||(t=[]),t.push(Q),!1;return a},i)}if(i){const c=qe(e,B);r.push(c)}}),{size:t,shape:n,stops:r,position:s,type:2}},Ke=function(e,A){let n=0,t=3;const r=[],s=[];return BA(A).forEach(function(B,o){let i=!0;if(o===0?i=B.reduce(function(c,a){if(b(a))switch(a.value){case"center":return s.push(Wt),!1;case"top":case"left":return s.push(v),!1;case"right":case"bottom":return s.push(hA),!1}else if(G(a)||pA(a))return s.push(a),!1;return c},i):o===1&&(i=B.reduce(function(c,a){if(b(a))switch(a.value){case mr:return n=0,!1;case Kr:return n=1,!1;case br:case Hr:return t=0,!1;case pr:return t=1,!1;case Ir:return t=2,!1;case Lr:case yr:return t=3,!1}else if(pA(a)||G(a))return Array.isArray(t)||(t=[]),t.push(a),!1;return c},i)),i){const c=qe(e,B);r.push(c)}}),{size:t,shape:n,stops:r,position:s,type:2}},no=function(e){return e.type===1},ro=function(e){return e.type===2},Zt={name:"image",parse:function(e,A){if(A.type===22){const n={url:A.value,type:0};return e.cache.addImage(A.value),n}if(A.type===18){const n=xr[A.name];if(typeof n>"u")throw new Error('Attempting to parse an unsupported image function "'+A.name+'"');return n(e,A.values)}throw new Error("Unsupported image type "+A.type)}};function so(e){return!(e.type===20&&e.value==="none")&&(e.type!==18||!!xr[e.name])}var xr={"linear-gradient":Ao,"-moz-linear-gradient":me,"-ms-linear-gradient":me,"-o-linear-gradient":me,"-webkit-linear-gradient":me,"radial-gradient":to,"-moz-radial-gradient":Ke,"-ms-radial-gradient":Ke,"-o-radial-gradient":Ke,"-webkit-radial-gradient":Ke,"-webkit-gradient":eo};const Bo={name:"background-image",initialValue:"none",type:1,prefix:!1,parse:function(e,A){if(A.length===0)return[];const n=A[0];return n.type===20&&n.value==="none"?[]:A.filter(function(t){return kA(t)&&so(t)}).map(function(t){return Zt.parse(e,t)})}},oo={name:"background-origin",initialValue:"border-box",prefix:!1,type:1,parse:function(e,A){return A.map(function(n){if(b(n))switch(n.value){case"padding-box":return 1;case"content-box":return 2}return 0})}},io={name:"background-position",initialValue:"0% 0%",type:1,prefix:!1,parse:function(e,A){return BA(A).map(function(n){return n.filter(G)}).map(Cr)}},co={name:"background-repeat",initialValue:"repeat",prefix:!1,type:1,parse:function(e,A){return BA(A).map(function(n){return n.filter(b).map(function(t){return t.value}).join(" ")}).map(ao)}};var ao=function(e){switch(e){case"no-repeat":return 1;case"repeat-x":case"repeat no-repeat":return 2;case"repeat-y":case"no-repeat repeat":return 3;case"repeat":default:return 0}};let XA;(function(e){e.AUTO="auto",e.CONTAIN="contain",e.COVER="cover"})(XA||(XA={}));const Qo={name:"background-size",initialValue:"0",prefix:!1,type:1,parse:function(e,A){return BA(A).map(function(n){return n.filter(go)})}};var go=function(e){return b(e)||G(e)};const je=function(e){return{name:"border-"+e+"-color",initialValue:"transparent",prefix:!1,type:3,format:"color"}},wo=je("top"),lo=je("right"),uo=je("bottom"),Co=je("left"),ze=function(e){return{name:"border-radius-"+e,initialValue:"0 0",prefix:!1,type:1,parse:function(A,n){return Cr(n.filter(G))}}},fo=ze("top-left"),Uo=ze("top-right"),Fo=ze("bottom-right"),ho=ze("bottom-left"),$e=function(e){return{name:"border-"+e+"-style",initialValue:"solid",prefix:!1,type:2,parse:function(A,n){switch(n){case"none":return 0;case"dashed":return 2;case"dotted":return 3;case"double":return 4}return 1}}},Eo=$e("top"),Ho=$e("right"),po=$e("bottom"),Io=$e("left"),At=function(e){return{name:"border-"+e+"-width",initialValue:"0",type:0,prefix:!1,parse:function(A,n){return le(n)?n.number:0}}},yo=At("top"),mo=At("right"),Ko=At("bottom"),Lo=At("left"),bo={name:"color",initialValue:"transparent",prefix:!1,type:3,format:"color"},xo={name:"direction",initialValue:"ltr",prefix:!1,type:2,parse:function(e,A){switch(A){case"rtl":return 1;case"ltr":default:return 0}}},Do={name:"display",initialValue:"inline-block",prefix:!1,type:1,parse:function(e,A){return A.filter(b).reduce(function(n,t){return n|To(t.value)},0)}};var To=function(e){switch(e){case"block":case"-webkit-box":return 2;case"inline":return 4;case"run-in":return 8;case"flow":return 16;case"flow-root":return 32;case"table":return 64;case"flex":case"-webkit-flex":return 128;case"grid":case"-ms-grid":return 256;case"ruby":return 512;case"subgrid":return 1024;case"list-item":return 2048;case"table-row-group":return 4096;case"table-header-group":return 8192;case"table-footer-group":return 16384;case"table-row":return 32768;case"table-cell":return 65536;case"table-column-group":return 131072;case"table-column":return 262144;case"table-caption":return 524288;case"ruby-base":return 1048576;case"ruby-text":return 2097152;case"ruby-base-container":return 4194304;case"ruby-text-container":return 8388608;case"contents":return 16777216;case"inline-block":return 33554432;case"inline-list-item":return 67108864;case"inline-table":return 134217728;case"inline-flex":return 268435456;case"inline-grid":return 536870912}return 0};const So={name:"float",initialValue:"none",prefix:!1,type:2,parse:function(e,A){switch(A){case"left":return 1;case"right":return 2;case"inline-start":return 3;case"inline-end":return 4}return 0}},Oo={name:"letter-spacing",initialValue:"0",prefix:!1,type:0,parse:function(e,A){return A.type===20&&A.value==="normal"?0:A.type===17||A.type===15?A.number:0}};let Ve;(function(e){e.NORMAL="normal",e.STRICT="strict"})(Ve||(Ve={}));const Mo={name:"line-break",initialValue:"normal",prefix:!1,type:2,parse:function(e,A){switch(A){case"strict":return Ve.STRICT;case"normal":default:return Ve.NORMAL}}},Go={name:"line-height",initialValue:"normal",prefix:!1,type:4},Hn=function(e,A){return b(e)&&e.value==="normal"?1.2*A:e.type===17?A*e.number:G(e)?x(e,A):A},Ro={name:"list-style-image",initialValue:"none",type:0,prefix:!1,parse:function(e,A){return A.type===20&&A.value==="none"?null:Zt.parse(e,A)}},Vo={name:"list-style-position",initialValue:"outside",prefix:!1,type:2,parse:function(e,A){switch(A){case"inside":return 0;case"outside":default:return 1}}},Rt={name:"list-style-type",initialValue:"none",prefix:!1,type:2,parse:function(e,A){switch(A){case"disc":return 0;case"circle":return 1;case"square":return 2;case"decimal":return 3;case"cjk-decimal":return 4;case"decimal-leading-zero":return 5;case"lower-roman":return 6;case"upper-roman":return 7;case"lower-greek":return 8;case"lower-alpha":return 9;case"upper-alpha":return 10;case"arabic-indic":return 11;case"armenian":return 12;case"bengali":return 13;case"cambodian":return 14;case"cjk-earthly-branch":return 15;case"cjk-heavenly-stem":return 16;case"cjk-ideographic":return 17;case"devanagari":return 18;case"ethiopic-numeric":return 19;case"georgian":return 20;case"gujarati":return 21;case"gurmukhi":return 22;case"hebrew":return 22;case"hiragana":return 23;case"hiragana-iroha":return 24;case"japanese-formal":return 25;case"japanese-informal":return 26;case"kannada":return 27;case"katakana":return 28;case"katakana-iroha":return 29;case"khmer":return 30;case"korean-hangul-formal":return 31;case"korean-hanja-formal":return 32;case"korean-hanja-informal":return 33;case"lao":return 34;case"lower-armenian":return 35;case"malayalam":return 36;case"mongolian":return 37;case"myanmar":return 38;case"oriya":return 39;case"persian":return 40;case"simp-chinese-formal":return 41;case"simp-chinese-informal":return 42;case"tamil":return 43;case"telugu":return 44;case"thai":return 45;case"tibetan":return 46;case"trad-chinese-formal":return 47;case"trad-chinese-informal":return 48;case"upper-armenian":return 49;case"disclosure-open":return 50;case"disclosure-closed":return 51;case"none":default:return-1}}},et=function(e){return{name:"margin-"+e,initialValue:"0",prefix:!1,type:4}},No=et("top"),_o=et("right"),vo=et("bottom"),Po=et("left"),Xo={name:"overflow",initialValue:"visible",prefix:!1,type:1,parse:function(e,A){return A.filter(b).map(function(n){switch(n.value){case"hidden":return 1;case"scroll":return 2;case"clip":return 3;case"auto":return 4;case"visible":default:return 0}})}},ko={name:"overflow-wrap",initialValue:"normal",prefix:!1,type:2,parse:function(e,A){switch(A){case"break-word":return"break-word";case"normal":default:return"normal"}}},tt=function(e){return{name:"padding-"+e,initialValue:"0",prefix:!1,type:3,format:"length-percentage"}},Jo=tt("top"),Yo=tt("right"),Wo=tt("bottom"),Zo=tt("left"),qo={name:"text-align",initialValue:"left",prefix:!1,type:2,parse:function(e,A){switch(A){case"right":return 2;case"center":case"justify":return 1;case"left":default:return 0}}},jo={name:"position",initialValue:"static",prefix:!1,type:2,parse:function(e,A){switch(A){case"relative":return 1;case"absolute":return 2;case"fixed":return 3;case"sticky":return 4}return 0}},zo={name:"text-shadow",initialValue:"none",type:1,prefix:!1,parse:function(e,A){return A.length===1&&Gt(A[0],"none")?[]:BA(A).map(function(n){const t={color:gA.TRANSPARENT,offsetX:v,offsetY:v,blur:v};let r=0;for(let s=0;s<n.length;s++){const B=n[s];pA(B)?(r===0?t.offsetX=B:r===1?t.offsetY=B:t.blur=B,r++):t.color=EA.parse(e,B)}return t})}},$o={name:"text-transform",initialValue:"none",prefix:!1,type:2,parse:function(e,A){switch(A){case"uppercase":return 2;case"lowercase":return 1;case"capitalize":return 3}return 0}},Ai={name:"transform",initialValue:"none",prefix:!0,type:0,parse:function(e,A){if(A.type===20&&A.value==="none")return null;if(A.type===18){const n=ni[A.name];if(typeof n>"u")throw new Error('Attempting to parse an unsupported transform function "'+A.name+'"');return n(A.values)}return null}},ei=function(e){const A=e.filter(function(n){return n.type===17}).map(function(n){return n.number});return A.length===6?A:null},ti=function(e){const A=e.filter(function(i){return i.type===17}).map(function(i){return i.number}),n=A[0],t=A[1];A[2],A[3];const r=A[4],s=A[5];A[6],A[7],A[8],A[9],A[10],A[11];const B=A[12],o=A[13];return A[14],A[15],A.length===16?[n,t,r,s,B,o]:null};var ni={matrix:ei,matrix3d:ti};const pn={type:16,number:50,flags:we},ri=[pn,pn],si={name:"transform-origin",initialValue:"50% 50%",prefix:!0,type:1,parse:function(e,A){const n=A.filter(G);return n.length!==2?ri:[n[0],n[1]]}},Bi={name:"visible",initialValue:"none",prefix:!1,type:2,parse:function(e,A){switch(A){case"hidden":return 1;case"collapse":return 2;case"visible":default:return 0}}};let oe;(function(e){e.NORMAL="normal",e.BREAK_ALL="break-all",e.KEEP_ALL="keep-all"})(oe||(oe={}));const oi={name:"word-break",initialValue:"normal",prefix:!1,type:2,parse:function(e,A){switch(A){case"break-all":return oe.BREAK_ALL;case"keep-all":return oe.KEEP_ALL;case"normal":default:return oe.NORMAL}}},ii={name:"z-index",initialValue:"auto",prefix:!1,type:0,parse:function(e,A){if(A.type===20)return{auto:!0,order:0};if(JA(A))return{auto:!1,order:A.number};throw new Error("Invalid z-index number parsed")}},Dr={name:"time",parse:function(e,A){if(A.type===15)switch(A.unit.toLowerCase()){case"s":return 1e3*A.number;case"ms":return A.number}throw new Error("Unsupported time type")}},ci={name:"opacity",initialValue:"1",type:0,prefix:!1,parse:function(e,A){return JA(A)?A.number:1}},ai={name:"text-decoration-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},Qi={name:"text-decoration-line",initialValue:"none",prefix:!1,type:1,parse:function(e,A){return A.filter(b).map(function(n){switch(n.value){case"underline":return 1;case"overline":return 2;case"line-through":return 3;case"none":return 4}return 0}).filter(function(n){return n!==0})}},gi={name:"font-family",initialValue:"",prefix:!1,type:1,parse:function(e,A){const n=[],t=[];return A.forEach(function(r){switch(r.type){case 20:case 0:n.push(r.value);break;case 17:n.push(r.number.toString());break;case 4:t.push(n.join(" ")),n.length=0;break}}),n.length&&t.push(n.join(" ")),t.map(function(r){return r.indexOf(" ")===-1?r:"'"+r+"'"})}},wi={name:"font-size",initialValue:"0",prefix:!1,type:3,format:"length"},li={name:"font-weight",initialValue:"normal",type:0,prefix:!1,parse:function(e,A){if(JA(A))return A.number;if(b(A))switch(A.value){case"bold":return 700;case"normal":default:return 400}return 400}},ui={name:"font-variant",initialValue:"none",type:1,prefix:!1,parse:function(e,A){return A.filter(b).map(function(n){return n.value})}},Ci={name:"font-style",initialValue:"normal",prefix:!1,type:2,parse:function(e,A){switch(A){case"oblique":return"oblique";case"italic":return"italic";case"normal":default:return"normal"}}},R=function(e,A){return(e&A)!==0},fi={name:"content",initialValue:"none",type:1,prefix:!1,parse:function(e,A){if(A.length===0)return[];const n=A[0];return n.type===20&&n.value==="none"?[]:A}},Ui={name:"counter-increment",initialValue:"none",prefix:!0,type:1,parse:function(e,A){if(A.length===0)return null;const n=A[0];if(n.type===20&&n.value==="none")return null;const t=[],r=A.filter(ur);for(let s=0;s<r.length;s++){const B=r[s],o=r[s+1];if(B.type===20){const i=o&&JA(o)?o.number:1;t.push({counter:B.value,increment:i})}}return t}},Fi={name:"counter-reset",initialValue:"none",prefix:!0,type:1,parse:function(e,A){if(A.length===0)return[];const n=[],t=A.filter(ur);for(let r=0;r<t.length;r++){const s=t[r],B=t[r+1];if(b(s)&&s.value!=="none"){const o=B&&JA(B)?B.number:0;n.push({counter:s.value,reset:o})}}return n}},hi={name:"duration",initialValue:"0s",prefix:!1,type:1,parse:function(e,A){return A.filter(le).map(function(n){return Dr.parse(e,n)})}},di={name:"quotes",initialValue:"none",prefix:!0,type:1,parse:function(e,A){if(A.length===0)return null;const n=A[0];if(n.type===20&&n.value==="none")return null;const t=[],r=A.filter(JB);if(r.length%2!==0)return null;for(let s=0;s<r.length;s+=2){const B=r[s].value,o=r[s+1].value;t.push({open:B,close:o})}return t}},In=function(e,A,n){if(!e)return"";const t=e[Math.min(A,e.length-1)];return t?n?t.open:t.close:""},Ei={name:"box-shadow",initialValue:"none",type:1,prefix:!1,parse:function(e,A){return A.length===1&&Gt(A[0],"none")?[]:BA(A).map(function(n){const t={color:255,offsetX:v,offsetY:v,blur:v,spread:v,inset:!1};let r=0;for(let s=0;s<n.length;s++){const B=n[s];Gt(B,"inset")?t.inset=!0:pA(B)?(r===0?t.offsetX=B:r===1?t.offsetY=B:r===2?t.blur=B:t.spread=B,r++):t.color=EA.parse(e,B)}return t})}},Hi={name:"paint-order",initialValue:"normal",prefix:!1,type:1,parse:function(e,A){const n=[0,1,2],t=[];return A.filter(b).forEach(function(r){switch(r.value){case"stroke":t.push(1);break;case"fill":t.push(0);break;case"markers":t.push(2);break}}),n.forEach(function(r){t.indexOf(r)===-1&&t.push(r)}),t}},pi={name:"-webkit-text-stroke-color",initialValue:"currentcolor",prefix:!1,type:3,format:"color"},Ii={name:"-webkit-text-stroke-width",initialValue:"0",type:0,prefix:!1,parse:function(e,A){return le(A)?A.number:0}},yi=function(){function e(A,n){let t,r;this.animationDuration=f(A,hi,n.animationDuration),this.backgroundClip=f(A,ZB,n.backgroundClip),this.backgroundColor=f(A,qB,n.backgroundColor),this.backgroundImage=f(A,Bo,n.backgroundImage),this.backgroundOrigin=f(A,oo,n.backgroundOrigin),this.backgroundPosition=f(A,io,n.backgroundPosition),this.backgroundRepeat=f(A,co,n.backgroundRepeat),this.backgroundSize=f(A,Qo,n.backgroundSize),this.borderTopColor=f(A,wo,n.borderTopColor),this.borderRightColor=f(A,lo,n.borderRightColor),this.borderBottomColor=f(A,uo,n.borderBottomColor),this.borderLeftColor=f(A,Co,n.borderLeftColor),this.borderTopLeftRadius=f(A,fo,n.borderTopLeftRadius),this.borderTopRightRadius=f(A,Uo,n.borderTopRightRadius),this.borderBottomRightRadius=f(A,Fo,n.borderBottomRightRadius),this.borderBottomLeftRadius=f(A,ho,n.borderBottomLeftRadius),this.borderTopStyle=f(A,Eo,n.borderTopStyle),this.borderRightStyle=f(A,Ho,n.borderRightStyle),this.borderBottomStyle=f(A,po,n.borderBottomStyle),this.borderLeftStyle=f(A,Io,n.borderLeftStyle),this.borderTopWidth=f(A,yo,n.borderTopWidth),this.borderRightWidth=f(A,mo,n.borderRightWidth),this.borderBottomWidth=f(A,Ko,n.borderBottomWidth),this.borderLeftWidth=f(A,Lo,n.borderLeftWidth),this.boxShadow=f(A,Ei,n.boxShadow),this.color=f(A,bo,n.color),this.direction=f(A,xo,n.direction),this.display=f(A,Do,n.display),this.float=f(A,So,n.cssFloat),this.fontFamily=f(A,gi,n.fontFamily),this.fontSize=f(A,wi,n.fontSize),this.fontStyle=f(A,Ci,n.fontStyle),this.fontVariant=f(A,ui,n.fontVariant),this.fontWeight=f(A,li,n.fontWeight),this.letterSpacing=f(A,Oo,n.letterSpacing),this.lineBreak=f(A,Mo,n.lineBreak),this.lineHeight=f(A,Go,n.lineHeight),this.listStyleImage=f(A,Ro,n.listStyleImage),this.listStylePosition=f(A,Vo,n.listStylePosition),this.listStyleType=f(A,Rt,n.listStyleType),this.marginTop=f(A,No,n.marginTop),this.marginRight=f(A,_o,n.marginRight),this.marginBottom=f(A,vo,n.marginBottom),this.marginLeft=f(A,Po,n.marginLeft),this.opacity=f(A,ci,n.opacity);const s=f(A,Xo,n.overflow);this.overflowX=s[0],this.overflowY=s[s.length>1?1:0],this.overflowWrap=f(A,ko,n.overflowWrap),this.paddingTop=f(A,Jo,n.paddingTop),this.paddingRight=f(A,Yo,n.paddingRight),this.paddingBottom=f(A,Wo,n.paddingBottom),this.paddingLeft=f(A,Zo,n.paddingLeft),this.paintOrder=f(A,Hi,n.paintOrder),this.position=f(A,jo,n.position),this.textAlign=f(A,qo,n.textAlign),this.textDecorationColor=f(A,ai,(t=n.textDecorationColor)!==null&&t!==void 0?t:n.color),this.textDecorationLine=f(A,Qi,(r=n.textDecorationLine)!==null&&r!==void 0?r:n.textDecoration),this.textShadow=f(A,zo,n.textShadow),this.textTransform=f(A,$o,n.textTransform),this.transform=f(A,Ai,n.transform),this.transformOrigin=f(A,si,n.transformOrigin),this.visibility=f(A,Bi,n.visibility),this.webkitTextStrokeColor=f(A,pi,n.webkitTextStrokeColor),this.webkitTextStrokeWidth=f(A,Ii,n.webkitTextStrokeWidth),this.wordBreak=f(A,oi,n.wordBreak),this.zIndex=f(A,ii,n.zIndex)}return e.prototype.isVisible=function(){return this.display>0&&this.opacity>0&&this.visibility===0},e.prototype.isTransparent=function(){return HA(this.backgroundColor)},e.prototype.isTransformed=function(){return this.transform!==null},e.prototype.isPositioned=function(){return this.position!==0},e.prototype.isPositionedWithZIndex=function(){return this.isPositioned()&&!this.zIndex.auto},e.prototype.isFloating=function(){return this.float!==0},e.prototype.isInlineLevel=function(){return R(this.display,4)||R(this.display,33554432)||R(this.display,268435456)||R(this.display,536870912)||R(this.display,67108864)||R(this.display,134217728)},e}(),mi=function(){function e(A,n){this.content=f(A,fi,n.content),this.quotes=f(A,di,n.quotes)}return e}(),yn=function(){function e(A,n){this.counterIncrement=f(A,Ui,n.counterIncrement),this.counterReset=f(A,Fi,n.counterReset)}return e}();var f=function(e,A,n){const t=new wr,r=n!==null&&typeof n<"u"?n.toString():A.initialValue;t.write(r);const s=new lr(t.read());switch(A.type){case 2:var B=s.parseComponentValue();return A.parse(e,b(B)?B.value:A.initialValue);case 0:return A.parse(e,s.parseComponentValue());case 1:return A.parse(e,s.parseComponentValues());case 4:return s.parseComponentValue();case 3:switch(A.format){case"angle":return Ze.parse(e,s.parseComponentValue());case"color":return EA.parse(e,s.parseComponentValue());case"image":return Zt.parse(e,s.parseComponentValue());case"length":var o=s.parseComponentValue();return pA(o)?o:v;case"length-percentage":var i=s.parseComponentValue();return G(i)?i:v;case"time":return Dr.parse(e,s.parseComponentValue())}break}};const Ki="data-html2canvas-debug",Li=function(e){switch(e.getAttribute(Ki)){case"all":return 1;case"clone":return 2;case"parse":return 3;case"render":return 4;default:return 0}},Vt=function(e,A){const n=Li(e);return n===1||A===n},oA=function(){function e(A,n){if(this.context=A,this.textNodes=[],this.elements=[],this.flags=0,Vt(n,3))debugger;this.styles=new yi(A,window.getComputedStyle(n,null)),vt(n)&&(this.styles.animationDuration.some(function(t){return t>0})&&(n.style.animationDuration="0s"),this.styles.transform!==null&&(n.style.transform="none")),this.bounds=Ye(this.context,n),Vt(n,4)&&(this.flags|=16)}return e}(),bi="AAAAAAAAAAAAEA4AGBkAAFAaAAACAAAAAAAIABAAGAAwADgACAAQAAgAEAAIABAACAAQAAgAEAAIABAACAAQAAgAEAAIABAAQABIAEQATAAIABAACAAQAAgAEAAIABAAVABcAAgAEAAIABAACAAQAGAAaABwAHgAgACIAI4AlgAIABAAmwCjAKgAsAC2AL4AvQDFAMoA0gBPAVYBWgEIAAgACACMANoAYgFkAWwBdAF8AX0BhQGNAZUBlgGeAaMBlQGWAasBswF8AbsBwwF0AcsBYwHTAQgA2wG/AOMBdAF8AekB8QF0AfkB+wHiAHQBfAEIAAMC5gQIAAsCEgIIAAgAFgIeAggAIgIpAggAMQI5AkACygEIAAgASAJQAlgCYAIIAAgACAAKBQoFCgUTBRMFGQUrBSsFCAAIAAgACAAIAAgACAAIAAgACABdAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACABoAmgCrwGvAQgAbgJ2AggAHgEIAAgACADnAXsCCAAIAAgAgwIIAAgACAAIAAgACACKAggAkQKZAggAPADJAAgAoQKkAqwCsgK6AsICCADJAggA0AIIAAgACAAIANYC3gIIAAgACAAIAAgACABAAOYCCAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAkASoB+QIEAAgACAA8AEMCCABCBQgACABJBVAFCAAIAAgACAAIAAgACAAIAAgACABTBVoFCAAIAFoFCABfBWUFCAAIAAgACAAIAAgAbQUIAAgACAAIAAgACABzBXsFfQWFBYoFigWKBZEFigWKBYoFmAWfBaYFrgWxBbkFCAAIAAgACAAIAAgACAAIAAgACAAIAMEFCAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAMgFCADQBQgACAAIAAgACAAIAAgACAAIAAgACAAIAO4CCAAIAAgAiQAIAAgACABAAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAD0AggACAD8AggACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIANYFCAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAMDvwAIAAgAJAIIAAgACAAIAAgACAAIAAgACwMTAwgACAB9BOsEGwMjAwgAKwMyAwsFYgE3A/MEPwMIAEUDTQNRAwgAWQOsAGEDCAAIAAgACAAIAAgACABpAzQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFIQUoBSwFCAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACABtAwgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACABMAEwACAAIAAgACAAIABgACAAIAAgACAC/AAgACAAyAQgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACACAAIAAwAAgACAAIAAgACAAIAAgACAAIAAAARABIAAgACAAIABQASAAIAAgAIABwAEAAjgCIABsAqAC2AL0AigDQAtwC+IJIQqVAZUBWQqVAZUBlQGVAZUBlQGrC5UBlQGVAZUBlQGVAZUBlQGVAXsKlQGVAbAK6wsrDGUMpQzlDJUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAfAKAAuZA64AtwCJALoC6ADwAAgAuACgA/oEpgO6AqsD+AAIAAgAswMIAAgACAAIAIkAuwP5AfsBwwPLAwgACAAIAAgACADRA9kDCAAIAOED6QMIAAgACAAIAAgACADuA/YDCAAIAP4DyQAIAAgABgQIAAgAXQAOBAgACAAIAAgACAAIABMECAAIAAgACAAIAAgACAD8AAQBCAAIAAgAGgQiBCoECAExBAgAEAEIAAgACAAIAAgACAAIAAgACAAIAAgACAA4BAgACABABEYECAAIAAgATAQYAQgAVAQIAAgACAAIAAgACAAIAAgACAAIAFoECAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgAOQEIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAB+BAcACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAEABhgSMBAgACAAIAAgAlAQIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAwAEAAQABAADAAMAAwADAAQABAAEAAQABAAEAAQABHATAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgAdQMIAAgACAAIAAgACAAIAMkACAAIAAgAfQMIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACACFA4kDCAAIAAgACAAIAOcBCAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAIcDCAAIAAgACAAIAAgACAAIAAgACAAIAJEDCAAIAAgACADFAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACABgBAgAZgQIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgAbAQCBXIECAAIAHkECAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACABAAJwEQACjBKoEsgQIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAC6BMIECAAIAAgACAAIAAgACABmBAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgAxwQIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAGYECAAIAAgAzgQIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgAigWKBYoFigWKBYoFigWKBd0FXwUIAOIF6gXxBYoF3gT5BQAGCAaKBYoFigWKBYoFigWKBYoFigWKBYoFigXWBIoFigWKBYoFigWKBYoFigWKBYsFEAaKBYoFigWKBYoFigWKBRQGCACKBYoFigWKBQgACAAIANEECAAIABgGigUgBggAJgYIAC4GMwaKBYoF0wQ3Bj4GigWKBYoFigWKBYoFigWKBYoFigWKBYoFigUIAAgACAAIAAgACAAIAAgAigWKBYoFigWKBYoFigWKBYoFigWKBYoFigWKBYoFigWKBYoFigWKBYoFigWKBYoFigWKBYoFigWKBYoFigWLBf///////wQABAAEAAQABAAEAAQABAAEAAQAAwAEAAQAAgAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAAAAAAAAAAAAAAAAAAAAAAAAAOAAAAAAAAAAQADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUABQAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAAAAUAAAAFAAUAAAAFAAUAAAAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAEAAQABAAEAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUABQAFAAUABQAFAAUABQAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUABQAFAAUAAQAAAAUABQAFAAUABQAFAAAAAAAFAAUAAAAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAUABQAFAAUABQAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAFAAAAAAAFAAUAAQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABwAFAAUABQAFAAAABwAHAAcAAAAHAAcABwAFAAEAAAAAAAAAAAAAAAAAAAAAAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHAAcABwAFAAUABQAFAAcABwAFAAUAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHAAAAAQABAAAAAAAAAAAAAAAFAAUABQAFAAAABwAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAHAAcABwAHAAcAAAAHAAcAAAAAAAUABQAHAAUAAQAHAAEABwAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUABQAFAAUABwABAAUABQAFAAUAAAAAAAAAAAAAAAEAAQABAAEAAQABAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABwAFAAUAAAAAAAAAAAAAAAAABQAFAAUABQAFAAUAAQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQABQANAAQABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQABAAEAAQABAAEAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAEAAQABAAEAAQABAAEAAQABAAEAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAQABAAEAAQABAAEAAQABAAAAAAAAAAAAAAAAAAAAAAABQAHAAUABQAFAAAAAAAAAAcABQAFAAUABQAFAAQABAAEAAQABAAEAAQABAAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUAAAAFAAUABQAFAAUAAAAFAAUABQAAAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAAAAAAAAAAAAUABQAFAAcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAHAAUAAAAHAAcABwAFAAUABQAFAAUABQAFAAUABwAHAAcABwAFAAcABwAAAAUABQAFAAUABQAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABwAHAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAUABwAHAAUABQAFAAUAAAAAAAcABwAAAAAABwAHAAUAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAAABQAFAAcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAAABwAHAAcABQAFAAAAAAAAAAAABQAFAAAAAAAFAAUABQAAAAAAAAAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAAAAAAAAAFAAAAAAAAAAAAAAAAAAAAAAAAAAAABwAFAAUABQAFAAUAAAAFAAUABwAAAAcABwAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUAAAAFAAUABwAFAAUABQAFAAAAAAAHAAcAAAAAAAcABwAFAAAAAAAAAAAAAAAAAAAABQAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAcABwAAAAAAAAAHAAcABwAAAAcABwAHAAUAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAAAAAAAAAAAAAAAAAAABQAHAAcABwAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABwAHAAcABwAAAAUABQAFAAAABQAFAAUABQAAAAAAAAAAAAAAAAAAAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAcABQAHAAcABQAHAAcAAAAFAAcABwAAAAcABwAFAAUAAAAAAAAAAAAAAAAAAAAFAAUAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAcABwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAAAAUABwAAAAAAAAAAAAAAAAAAAAAAAAAAAAUAAAAAAAAAAAAFAAcABwAFAAUABQAAAAUAAAAHAAcABwAHAAcABwAHAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUAAAAHAAUABQAFAAUABQAFAAUAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAAABwAFAAUABQAFAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUABQAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAUAAAAFAAAAAAAAAAAABwAHAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABwAFAAUABQAFAAUAAAAFAAUAAAAAAAAAAAAAAAUABQAFAAUABQAFAAUABQAFAAUABQAAAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUABwAFAAUABQAFAAUABQAAAAUABQAHAAcABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHAAcABQAFAAAAAAAAAAAABQAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAcABQAFAAAAAAAAAAAAAAAAAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAHAAUABQAFAAUABQAFAAUABwAHAAcABwAHAAcABwAHAAUABwAHAAUABQAFAAUABQAFAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUABwAHAAcABwAFAAUABwAHAAcAAAAAAAAAAAAHAAcABQAHAAcABwAHAAcABwAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAcABwAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcABQAHAAUABQAFAAUABQAFAAUAAAAFAAAABQAAAAAABQAFAAUABQAFAAUABQAFAAcABwAHAAcABwAHAAUABQAFAAUABQAFAAUABQAFAAUAAAAAAAUABQAFAAUABQAHAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUABQAFAAUABwAFAAcABwAHAAcABwAFAAcABwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHAAUABQAFAAUABwAHAAUABQAHAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAcABQAFAAcABwAHAAUABwAFAAUABQAHAAcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABwAHAAcABwAHAAcABwAHAAUABQAFAAUABQAFAAUABQAHAAcABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUAAAAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAcABQAFAAUABQAFAAUABQAAAAAAAAAAAAUAAAAAAAAAAAAAAAAABQAAAAAABwAFAAUAAAAAAAAAAAAAAAAABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAAABQAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUABQAFAAUADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUAAAAFAAUABQAFAAUABQAFAAUABQAFAAAAAAAAAAAABQAAAAAAAAAFAAAAAAAAAAAABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABwAHAAUABQAHAAAAAAAAAAAABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcABwAHAAcABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUAAAAAAAAAAAAAAAAABQAFAAUABQAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUABQAFAAUABQAFAAUABQAHAAcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAcABwAFAAUABQAFAAcABwAFAAUABwAHAAAAAAAAAAAAAAAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUABQAFAAcABwAFAAUABwAHAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAAAAAAAAAAAAAAAAAAAAAAFAAcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUAAAAFAAUABQAAAAAABQAFAAAAAAAAAAAAAAAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcABQAFAAcABwAAAAAAAAAAAAAABwAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcABwAFAAcABwAFAAcABwAAAAcABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUABQAAAAAAAAAAAAAAAAAFAAUABQAAAAUABQAAAAAAAAAAAAAABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAAAAAAAAAAAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcABQAHAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAUABwAFAAUABQAFAAUABQAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABwAHAAcABQAFAAUABQAFAAUABQAFAAUABwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHAAcABwAFAAUABQAHAAcABQAHAAUABQAAAAAAAAAAAAAAAAAFAAAABwAHAAcABQAFAAUABQAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABwAHAAcABwAAAAAABwAHAAAAAAAHAAcABwAAAAAAAAAAAAAAAAAAAAAAAAAFAAAAAAAAAAAAAAAAAAAAAAAAAAAABwAHAAAAAAAFAAUABQAFAAUABQAFAAAAAAAAAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHAAcABwAFAAUABQAFAAUABQAFAAUABwAHAAUABQAFAAcABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAHAAcABQAFAAUABQAFAAUABwAFAAcABwAFAAcABQAFAAcABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAHAAcABQAFAAUABQAAAAAABwAHAAcABwAFAAUABwAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcABwAHAAUABQAFAAUABQAFAAUABQAHAAcABQAHAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABwAFAAcABwAFAAUABQAFAAUABQAHAAUAAAAAAAAAAAAAAAAAAAAAAAcABwAFAAUABQAFAAcABQAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHAAcABwAFAAUABQAFAAUABQAFAAUABQAHAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHAAcABwAFAAUABQAFAAAAAAAFAAUABwAHAAcABwAFAAAAAAAAAAcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAUABQAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUABQAFAAUABwAHAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcABQAFAAUABQAFAAUABQAAAAUABQAFAAUABQAFAAcABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAAAAHAAUABQAFAAUABQAFAAUABwAFAAUABwAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUABQAFAAUAAAAAAAAABQAAAAUABQAAAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAHAAcABwAHAAcAAAAFAAUAAAAHAAcABQAHAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABwAHAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAAAAAAAAAAAAAAAAAAABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcABwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAAAAUABQAFAAAAAAAFAAUABQAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAAAAAAAAAAABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAFAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUABQAAAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUABQAFAAUABQAAAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAAAAAABQAFAAUABQAFAAUABQAAAAUABQAAAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAFAAUABQAFAAUADgAOAA4ADgAOAA4ADwAPAA8ADwAPAA8ADwAPAA8ADwAPAA8ADwAPAA8ADwAPAA8ADwAPAA8ADwAPAA8ADwAPAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcABwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABwAHAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAAAAAAAAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAKAAoACgAKAAoACgAKAAoACgAKAAoACgAKAAoACgAKAAoACgAKAAoACgAKAAoACgAMAAwADAAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkAAAAAAAAAAAAKAAoACgAKAAoACgAKAAoACgAKAAoACgAKAAoACgAKAAoACgAKAAoACgAKAAoACgAKAAoACgAKAAoACgAKAAoACgAAAAAAAAAAAAsADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwACwAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAAAAAADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA4AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAA4ADgAOAA4ADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA4ADgAAAAAAAAAAAAAAAAAAAAAADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAOAA4ADgAOAA4ADgAOAA4ADgAOAAAAAAAAAAAADgAOAA4AAAAAAAAAAAAAAAAAAAAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAOAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAAAAAAAAAAAAAAAAAAAAAAAAAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAOAA4ADgAAAA4ADgAOAA4ADgAOAAAADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4AAAAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4AAAAAAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAAAA4AAAAOAAAAAAAAAAAAAAAAAA4AAAAAAAAAAAAAAAAADgAAAAAAAAAAAAAAAAAAAAAAAAAAAA4ADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAAAAAADgAAAAAAAAAAAA4AAAAOAAAAAAAAAAAADgAOAA4AAAAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAA4ADgAOAA4AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAA4ADgAAAAAAAAAAAAAAAAAAAAAAAAAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAA4AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA4ADgAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAAAAAAAAAAAA4AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAAAADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAA4ADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA4ADgAOAA4ADgAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA4ADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAAAAAADgAOAA4ADgAOAA4ADgAOAA4ADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAAAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA4AAAAAAA4ADgAOAA4ADgAOAA4ADgAOAAAADgAOAA4ADgAAAAAAAAAAAAAAAAAAAAAAAAAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4AAAAAAAAAAAAAAAAADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAA4ADgAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAOAA4ADgAOAA4ADgAOAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAOAA4ADgAOAA4AAAAAAAAAAAAAAAAAAAAAAA4ADgAOAA4ADgAOAA4ADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4AAAAOAA4ADgAOAA4ADgAAAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4AAAAAAAAAAAA=",mn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",ne=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(let e=0;e<mn.length;e++)ne[mn.charCodeAt(e)]=e;const xi=function(e){let A=e.length*.75,n=e.length,t,r=0,s,B,o,i;e[e.length-1]==="="&&(A--,e[e.length-2]==="="&&A--);const c=typeof ArrayBuffer<"u"&&typeof Uint8Array<"u"&&typeof Uint8Array.prototype.slice<"u"?new ArrayBuffer(A):new Array(A),a=Array.isArray(c)?c:new Uint8Array(c);for(t=0;t<n;t+=4)s=ne[e.charCodeAt(t)],B=ne[e.charCodeAt(t+1)],o=ne[e.charCodeAt(t+2)],i=ne[e.charCodeAt(t+3)],a[r++]=s<<2|B>>4,a[r++]=(B&15)<<4|o>>2,a[r++]=(o&3)<<6|i&63;return c},Di=function(e){const A=e.length,n=[];for(let t=0;t<A;t+=2)n.push(e[t+1]<<8|e[t]);return n},Ti=function(e){const A=e.length,n=[];for(let t=0;t<A;t+=4)n.push(e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]);return n},TA=5,qt=11,Qt=2,Si=qt-TA,Tr=65536>>TA,Oi=1<<TA,gt=Oi-1,Mi=1024>>TA,Gi=Tr+Mi,Ri=Gi,Vi=32,Ni=Ri+Vi,_i=65536>>qt,vi=1<<Si,Pi=vi-1,Kn=function(e,A,n){return e.slice?e.slice(A,n):new Uint16Array(Array.prototype.slice.call(e,A,n))},Xi=function(e,A,n){return e.slice?e.slice(A,n):new Uint32Array(Array.prototype.slice.call(e,A,n))},ki=function(e,A){const n=xi(e),t=Array.isArray(n)?Ti(n):new Uint32Array(n),r=Array.isArray(n)?Di(n):new Uint16Array(n),s=24,B=Kn(r,s/2,t[4]/2),o=t[5]===2?Kn(r,(s+t[4])/2):Xi(t,Math.ceil((s+t[4])/4));return new Ji(t[0],t[1],t[2],t[3],B,o)};var Ji=function(){function e(A,n,t,r,s,B){this.initialValue=A,this.errorValue=n,this.highStart=t,this.highValueIndex=r,this.index=s,this.data=B}return e.prototype.get=function(A){let n;if(A>=0){if(A<55296||A>56319&&A<=65535)return n=this.index[A>>TA],n=(n<<Qt)+(A&gt),this.data[n];if(A<=65535)return n=this.index[Tr+(A-55296>>TA)],n=(n<<Qt)+(A&gt),this.data[n];if(A<this.highStart)return n=Ni-_i+(A>>qt),n=this.index[n],n+=A>>TA&Pi,n=this.index[n],n=(n<<Qt)+(A&gt),this.data[n];if(A<=1114111)return this.data[this.highValueIndex]}return this.errorValue},e}();const Ln="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Yi=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(let e=0;e<Ln.length;e++)Yi[Ln.charCodeAt(e)]=e;const Wi=1,wt=2,lt=3,bn=4,xn=5,Zi=7,Dn=8,ut=9,Ct=10,Tn=11,Sn=12,On=13,Mn=14,ft=15,qi=function(e){const A=[];let n=0;const t=e.length;for(;n<t;){const r=e.charCodeAt(n++);if(r>=55296&&r<=56319&&n<t){const s=e.charCodeAt(n++);(s&64512)===56320?A.push(((r&1023)<<10)+(s&1023)+65536):(A.push(r),n--)}else A.push(r)}return A},ji=function(){const e=[];for(let s=0;s<arguments.length;s++)e[s]=arguments[s];if(String.fromCodePoint)return String.fromCodePoint.apply(String,e);const A=e.length;if(!A)return"";const n=[];let t=-1,r="";for(;++t<A;){let s=e[t];s<=65535?n.push(s):(s-=65536,n.push((s>>10)+55296,s%1024+56320)),(t+1===A||n.length>16384)&&(r+=String.fromCharCode.apply(String,n),n.length=0)}return r},zi=ki(bi),z="×",Ut="÷",$i=function(e){return zi.get(e)},Ac=function(e,A,n){let t=n-2,r=A[t];const s=A[n-1],B=A[n];if(s===wt&&B===lt)return z;if(s===wt||s===lt||s===bn||B===wt||B===lt||B===bn)return Ut;if(s===Dn&&[Dn,ut,Tn,Sn].indexOf(B)!==-1||(s===Tn||s===ut)&&(B===ut||B===Ct)||(s===Sn||s===Ct)&&B===Ct||B===On||B===xn||B===Zi||s===Wi)return z;if(s===On&&B===Mn){for(;r===xn;)r=A[--t];if(r===Mn)return z}if(s===ft&&B===ft){let o=0;for(;r===ft;)o++,r=A[--t];if(o%2===0)return z}return Ut},ec=function(e){const A=qi(e),n=A.length;let t=0,r=0;const s=A.map($i);return{next:function(){if(t>=n)return{done:!0,value:null};let B=z;for(;t<n&&(B=Ac(A,s,++t))===z;);if(B!==z||t===n){const o=ji.apply(null,A.slice(r,t));return r=t,{value:o,done:!1}}return{done:!0,value:null}}}},tc=function(e){const A=ec(e),n=[];let t;for(;!(t=A.next()).done;)t.value&&n.push(t.value.slice());return n},nc=function(e){if(e.createRange){const n=e.createRange();if(n.getBoundingClientRect){const t=e.createElement("boundtest");t.style.height="123px",t.style.display="block",e.body.appendChild(t),n.selectNode(t);const r=n.getBoundingClientRect(),s=Math.round(r.height);if(e.body.removeChild(t),s===123)return!0}}return!1},rc=function(e){const A=e.createElement("boundtest");A.style.width="50px",A.style.display="block",A.style.fontSize="12px",A.style.letterSpacing="0px",A.style.wordSpacing="0px",e.body.appendChild(A);const n=e.createRange();A.innerHTML=typeof"".repeat=="function"?"&#128104;".repeat(10):"";const t=A.firstChild,r=We(t.data).map(function(i){return O(i)});let s=0,B={};const o=r.every(function(i,c){n.setStart(t,s),n.setEnd(t,s+i.length);const a=n.getBoundingClientRect();s+=i.length;const Q=a.x>B.x||a.y>B.y;return B=a,c===0?!0:Q});return e.body.removeChild(A),o},sc=function(){return typeof new Image().crossOrigin<"u"},Bc=function(){return typeof new XMLHttpRequest().responseType=="string"},oc=function(e){const A=new Image,n=e.createElement("canvas"),t=n.getContext("2d");if(!t)return!1;A.src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg'></svg>";try{t.drawImage(A,0,0),n.toDataURL()}catch{return!1}return!0},Gn=function(e){return e[0]===0&&e[1]===255&&e[2]===0&&e[3]===255},ic=function(e){const A=e.createElement("canvas"),n=100;A.width=n,A.height=n;const t=A.getContext("2d");if(!t)return Promise.reject(!1);t.fillStyle="rgb(0, 255, 0)",t.fillRect(0,0,n,n);const r=new Image,s=A.toDataURL();r.src=s;const B=Nt(n,n,0,0,r);return t.fillStyle="red",t.fillRect(0,0,n,n),Rn(B).then(function(o){t.drawImage(o,0,0);const i=t.getImageData(0,0,n,n).data;t.fillStyle="red",t.fillRect(0,0,n,n);const c=e.createElement("div");return c.style.backgroundImage="url("+s+")",c.style.height=n+"px",Gn(i)?Rn(Nt(n,n,0,0,c)):Promise.reject(!1)}).then(function(o){return t.drawImage(o,0,0),Gn(t.getImageData(0,0,n,n).data)}).catch(function(){return!1})};var Nt=function(e,A,n,t,r){const s="http://www.w3.org/2000/svg",B=document.createElementNS(s,"svg"),o=document.createElementNS(s,"foreignObject");return B.setAttributeNS(null,"width",e.toString()),B.setAttributeNS(null,"height",A.toString()),o.setAttributeNS(null,"width","100%"),o.setAttributeNS(null,"height","100%"),o.setAttributeNS(null,"x",n.toString()),o.setAttributeNS(null,"y",t.toString()),o.setAttributeNS(null,"externalResourcesRequired","true"),B.appendChild(o),o.appendChild(r),B},Rn=function(e){return new Promise(function(A,n){const t=new Image;t.onload=function(){return A(t)},t.onerror=n,t.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent(new XMLSerializer().serializeToString(e))})},_={get SUPPORT_RANGE_BOUNDS(){const e=nc(document);return Object.defineProperty(_,"SUPPORT_RANGE_BOUNDS",{value:e}),e},get SUPPORT_WORD_BREAKING(){const e=_.SUPPORT_RANGE_BOUNDS&&rc(document);return Object.defineProperty(_,"SUPPORT_WORD_BREAKING",{value:e}),e},get SUPPORT_SVG_DRAWING(){const e=oc(document);return Object.defineProperty(_,"SUPPORT_SVG_DRAWING",{value:e}),e},get SUPPORT_FOREIGNOBJECT_DRAWING(){const e=typeof Array.from=="function"&&typeof window.fetch=="function"?ic(document):Promise.resolve(!1);return Object.defineProperty(_,"SUPPORT_FOREIGNOBJECT_DRAWING",{value:e}),e},get SUPPORT_CORS_IMAGES(){const e=sc();return Object.defineProperty(_,"SUPPORT_CORS_IMAGES",{value:e}),e},get SUPPORT_RESPONSE_TYPE(){const e=Bc();return Object.defineProperty(_,"SUPPORT_RESPONSE_TYPE",{value:e}),e},get SUPPORT_CORS_XHR(){const e="withCredentials"in new XMLHttpRequest;return Object.defineProperty(_,"SUPPORT_CORS_XHR",{value:e}),e},get SUPPORT_NATIVE_TEXT_SEGMENTATION(){const e=!!(typeof Intl<"u"&&Intl.Segmenter);return Object.defineProperty(_,"SUPPORT_NATIVE_TEXT_SEGMENTATION",{value:e}),e}};const ie=function(){function e(A,n){this.text=A,this.bounds=n}return e}(),cc=function(e,A,n,t){const r=gc(A,n),s=[];let B=0;return r.forEach(function(o){if(n.textDecorationLine.length||o.trim().length>0)if(_.SUPPORT_RANGE_BOUNDS){const i=Vn(t,B,o.length).getClientRects();if(i.length>1){const c=jt(o);let a=0;c.forEach(function(Q){s.push(new ie(Q,wA.fromDOMRectList(e,Vn(t,a+B,Q.length).getClientRects()))),a+=Q.length})}else s.push(new ie(o,wA.fromDOMRectList(e,i)))}else{const i=t.splitText(o.length);s.push(new ie(o,ac(e,t))),t=i}else _.SUPPORT_RANGE_BOUNDS||(t=t.splitText(o.length));B+=o.length}),s};var ac=function(e,A){const n=A.ownerDocument;if(n){const t=n.createElement("html2canvaswrapper");t.appendChild(A.cloneNode(!0));const r=A.parentNode;if(r){r.replaceChild(t,A);const s=Ye(e,t);return t.firstChild&&r.replaceChild(t.firstChild,t),s}}return wA.EMPTY},Vn=function(e,A,n){const t=e.ownerDocument;if(!t)throw new Error("Node has no owner document");const r=t.createRange();return r.setStart(e,A),r.setEnd(e,A+n),r},jt=function(e){if(_.SUPPORT_NATIVE_TEXT_SEGMENTATION){const A=new Intl.Segmenter(void 0,{granularity:"grapheme"});return Array.from(A.segment(e)).map(function(n){return n.segment})}return tc(e)};const Qc=function(e,A){if(_.SUPPORT_NATIVE_TEXT_SEGMENTATION){const n=new Intl.Segmenter(void 0,{granularity:"word"});return Array.from(n.segment(e)).map(function(t){return t.segment})}return lc(e,A)};var gc=function(e,A){return A.letterSpacing!==0?jt(e):Qc(e,A)};const wc=[32,160,4961,65792,65793,4153,4241];var lc=function(e,A){const n=Ns(e,{lineBreak:A.lineBreak,wordBreak:A.overflowWrap==="break-word"?"break-word":A.wordBreak}),t=[];let r;const s=function(){if(r.value){const B=r.value.slice(),o=We(B);let i="";o.forEach(function(c){wc.indexOf(c)===-1?i+=O(c):(i.length&&t.push(i),t.push(O(c)),i="")}),i.length&&t.push(i)}};for(;!(r=n.next()).done;)s();return t};const uc=function(){function e(A,n,t){this.text=Cc(n.data,t.textTransform),this.textBounds=cc(A,this.text,t,n)}return e}();var Cc=function(e,A){switch(A){case 1:return e.toLowerCase();case 3:return e.replace(fc,Uc);case 2:return e.toUpperCase();default:return e}},fc=/(^|\s|:|-|\(|\))([a-z])/g,Uc=function(e,A,n){return e.length>0?A+n.toUpperCase():e};const Sr=function(e){nA(A,e);function A(n,t){const r=e.call(this,n,t)||this;return r.src=t.currentSrc||t.src,r.intrinsicWidth=t.naturalWidth,r.intrinsicHeight=t.naturalHeight,r.context.cache.addImage(r.src),r}return A}(oA),Or=function(e){nA(A,e);function A(n,t){const r=e.call(this,n,t)||this;return r.canvas=t,r.intrinsicWidth=t.width,r.intrinsicHeight=t.height,r}return A}(oA),Mr=function(e){nA(A,e);function A(n,t){const r=e.call(this,n,t)||this,s=new XMLSerializer,B=Ye(n,t);return t.setAttribute("width",B.width+"px"),t.setAttribute("height",B.height+"px"),r.svg="data:image/svg+xml,"+encodeURIComponent(s.serializeToString(t)),r.intrinsicWidth=t.width.baseVal.value,r.intrinsicHeight=t.height.baseVal.value,r.context.cache.addImage(r.svg),r}return A}(oA),Gr=function(e){nA(A,e);function A(n,t){const r=e.call(this,n,t)||this;return r.value=t.value,r}return A}(oA),_t=function(e){nA(A,e);function A(n,t){const r=e.call(this,n,t)||this;return r.start=t.start,r.reversed=typeof t.reversed=="boolean"&&t.reversed===!0,r}return A}(oA),Fc=[{type:15,flags:0,unit:"px",number:3}],hc=[{type:16,flags:0,number:50}],dc=function(e){return e.width>e.height?new wA(e.left+(e.width-e.height)/2,e.top,e.height,e.height):e.width<e.height?new wA(e.left,e.top+(e.height-e.width)/2,e.width,e.width):e},Ec=function(e){const A=e.type===Hc?new Array(e.value.length+1).join("•"):e.value;return A.length===0?e.placeholder||"":A},Ne="checkbox",_e="radio";var Hc="password";const Nn=707406591,zt=function(e){nA(A,e);function A(n,t){const r=e.call(this,n,t)||this;switch(r.type=t.type.toLowerCase(),r.checked=t.checked,r.value=Ec(t),(r.type===Ne||r.type===_e)&&(r.styles.backgroundColor=3739148031,r.styles.borderTopColor=r.styles.borderRightColor=r.styles.borderBottomColor=r.styles.borderLeftColor=2779096575,r.styles.borderTopWidth=r.styles.borderRightWidth=r.styles.borderBottomWidth=r.styles.borderLeftWidth=1,r.styles.borderTopStyle=r.styles.borderRightStyle=r.styles.borderBottomStyle=r.styles.borderLeftStyle=1,r.styles.backgroundClip=[0],r.styles.backgroundOrigin=[0],r.bounds=dc(r.bounds)),r.type){case Ne:r.styles.borderTopRightRadius=r.styles.borderTopLeftRadius=r.styles.borderBottomRightRadius=r.styles.borderBottomLeftRadius=Fc;break;case _e:r.styles.borderTopRightRadius=r.styles.borderTopLeftRadius=r.styles.borderBottomRightRadius=r.styles.borderBottomLeftRadius=hc;break}return r}return A}(oA),Rr=function(e){nA(A,e);function A(n,t){const r=e.call(this,n,t)||this,s=t.options[t.selectedIndex||0];return r.value=s&&s.text||"",r}return A}(oA),Vr=function(e){nA(A,e);function A(n,t){const r=e.call(this,n,t)||this;return r.value=t.value,r}return A}(oA),Nr=function(e){nA(A,e);function A(n,t){const r=e.call(this,n,t)||this;r.src=t.src,r.width=parseInt(t.width,10)||0,r.height=parseInt(t.height,10)||0,r.backgroundColor=r.styles.backgroundColor;try{if(t.contentWindow&&t.contentWindow.document&&t.contentWindow.document.documentElement){r.tree=vr(n,t.contentWindow.document.documentElement);const s=t.contentWindow.document.documentElement?Be(n,getComputedStyle(t.contentWindow.document.documentElement).backgroundColor):gA.TRANSPARENT,B=t.contentWindow.document.body?Be(n,getComputedStyle(t.contentWindow.document.body).backgroundColor):gA.TRANSPARENT;r.backgroundColor=HA(s)?HA(B)?r.styles.backgroundColor:B:s}}catch{}return r}return A}(oA),pc=["OL","UL","MENU"];var Oe=function(e,A,n,t){for(let r=A.firstChild,s=void 0;r;r=s)if(s=r.nextSibling,Pr(r)&&r.data.trim().length>0)n.textNodes.push(new uc(e,r,n.styles));else if(PA(r))if(Yr(r)&&r.assignedNodes)r.assignedNodes().forEach(function(B){return Oe(e,B,n,t)});else{const B=_r(e,r);B.styles.isVisible()&&(Ic(r,B,t)?B.flags|=4:yc(B.styles)&&(B.flags|=2),pc.indexOf(r.tagName)!==-1&&(B.flags|=8),n.elements.push(B),r.slot,r.shadowRoot?Oe(e,r.shadowRoot,B,t):!ve(r)&&!Xr(r)&&!Pe(r)&&Oe(e,r,B,t))}},_r=function(e,A){return Pt(A)?new Sr(e,A):kr(A)?new Or(e,A):Xr(A)?new Mr(e,A):mc(A)?new Gr(e,A):Kc(A)?new _t(e,A):Lc(A)?new zt(e,A):Pe(A)?new Rr(e,A):ve(A)?new Vr(e,A):Jr(A)?new Nr(e,A):new oA(e,A)},vr=function(e,A){const n=_r(e,A);return n.flags|=4,Oe(e,A,n,n),n},Ic=function(e,A,n){return A.styles.isPositionedWithZIndex()||A.styles.opacity<1||A.styles.isTransformed()||$t(e)&&n.styles.isTransparent()},yc=function(e){return e.isPositioned()||e.isFloating()},Pr=function(e){return e.nodeType===Node.TEXT_NODE},PA=function(e){return e.nodeType===Node.ELEMENT_NODE},vt=function(e){return PA(e)&&typeof e.style<"u"&&!Me(e)},Me=function(e){return typeof e.className=="object"},mc=function(e){return e.tagName==="LI"},Kc=function(e){return e.tagName==="OL"},Lc=function(e){return e.tagName==="INPUT"};const bc=function(e){return e.tagName==="HTML"};var Xr=function(e){return e.tagName==="svg"},$t=function(e){return e.tagName==="BODY"},kr=function(e){return e.tagName==="CANVAS"};const _n=function(e){return e.tagName==="VIDEO"};var Pt=function(e){return e.tagName==="IMG"},Jr=function(e){return e.tagName==="IFRAME"};const vn=function(e){return e.tagName==="STYLE"},xc=function(e){return e.tagName==="SCRIPT"};var ve=function(e){return e.tagName==="TEXTAREA"},Pe=function(e){return e.tagName==="SELECT"},Yr=function(e){return e.tagName==="SLOT"};const Pn=function(e){return e.tagName.indexOf("-")>0},Dc=function(){function e(){this.counters={}}return e.prototype.getCounterValue=function(A){const n=this.counters[A];return n&&n.length?n[n.length-1]:1},e.prototype.getCounterValues=function(A){const n=this.counters[A];return n||[]},e.prototype.pop=function(A){const n=this;A.forEach(function(t){return n.counters[t].pop()})},e.prototype.parse=function(A){const n=this,t=A.counterIncrement,r=A.counterReset;let s=!0;t!==null&&t.forEach(function(o){const i=n.counters[o.counter];i&&o.increment!==0&&(s=!1,i.length||i.push(1),i[Math.max(0,i.length-1)]+=o.increment)});const B=[];return s&&r.forEach(function(o){let i=n.counters[o.counter];B.push(o.counter),i||(i=n.counters[o.counter]=[]),i.push(o.reset)}),B},e}(),Xn={integers:[1e3,900,500,400,100,90,50,40,10,9,5,4,1],values:["M","CM","D","CD","C","XC","L","XL","X","IX","V","IV","I"]},kn={integers:[9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["Ք","Փ","Ւ","Ց","Ր","Տ","Վ","Ս","Ռ","Ջ","Պ","Չ","Ո","Շ","Ն","Յ","Մ","Ճ","Ղ","Ձ","Հ","Կ","Ծ","Խ","Լ","Ի","Ժ","Թ","Ը","Է","Զ","Ե","Դ","Գ","Բ","Ա"]},Tc={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,400,300,200,100,90,80,70,60,50,40,30,20,19,18,17,16,15,10,9,8,7,6,5,4,3,2,1],values:["י׳","ט׳","ח׳","ז׳","ו׳","ה׳","ד׳","ג׳","ב׳","א׳","ת","ש","ר","ק","צ","פ","ע","ס","נ","מ","ל","כ","יט","יח","יז","טז","טו","י","ט","ח","ז","ו","ה","ד","ג","ב","א"]},Sc={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["ჵ","ჰ","ჯ","ჴ","ხ","ჭ","წ","ძ","ც","ჩ","შ","ყ","ღ","ქ","ფ","ჳ","ტ","ს","რ","ჟ","პ","ო","ჲ","ნ","მ","ლ","კ","ი","თ","ჱ","ზ","ვ","ე","დ","გ","ბ","ა"]},RA=function(e,A,n,t,r,s){return e<A||e>n?ge(e,r,s.length>0):t.integers.reduce(function(B,o,i){for(;e>=o;)e-=o,B+=t.values[i];return B},"")+s},Wr=function(e,A,n,t){let r="";do n||e--,r=t(e)+r,e/=A;while(e*A>=A);return r},S=function(e,A,n,t,r){const s=n-A+1;return(e<0?"-":"")+(Wr(Math.abs(e),s,t,function(B){return O(Math.floor(B%s)+A)})+r)},LA=function(e,A,n){n===void 0&&(n=". ");const t=A.length;return Wr(Math.abs(e),t,!1,function(r){return A[Math.floor(r%t)]})+n},_A=1,CA=2,fA=4,re=8,QA=function(e,A,n,t,r,s){if(e<-9999||e>9999)return ge(e,4,r.length>0);let B=Math.abs(e),o=r;if(B===0)return A[0]+o;for(let i=0;B>0&&i<=4;i++){const c=B%10;c===0&&R(s,_A)&&o!==""?o=A[c]+o:c>1||c===1&&i===0||c===1&&i===1&&R(s,CA)||c===1&&i===1&&R(s,fA)&&e>100||c===1&&i>1&&R(s,re)?o=A[c]+(i>0?n[i-1]:"")+o:c===1&&i>0&&(o=n[i-1]+o),B=Math.floor(B/10)}return(e<0?t:"")+o},Jn="十百千萬",Yn="拾佰仟萬",Wn="マイナス",Ft="마이너스";var ge=function(e,A,n){const t=n?". ":"",r=n?"、":"",s=n?", ":"",B=n?" ":"";switch(A){case 0:return"•"+B;case 1:return"◦"+B;case 2:return"◾"+B;case 5:var o=S(e,48,57,!0,t);return o.length<4?"0"+o:o;case 4:return LA(e,"〇一二三四五六七八九",r);case 6:return RA(e,1,3999,Xn,3,t).toLowerCase();case 7:return RA(e,1,3999,Xn,3,t);case 8:return S(e,945,969,!1,t);case 9:return S(e,97,122,!1,t);case 10:return S(e,65,90,!1,t);case 11:return S(e,1632,1641,!0,t);case 12:case 49:return RA(e,1,9999,kn,3,t);case 35:return RA(e,1,9999,kn,3,t).toLowerCase();case 13:return S(e,2534,2543,!0,t);case 14:case 30:return S(e,6112,6121,!0,t);case 15:return LA(e,"子丑寅卯辰巳午未申酉戌亥",r);case 16:return LA(e,"甲乙丙丁戊己庚辛壬癸",r);case 17:case 48:return QA(e,"零一二三四五六七八九",Jn,"負",r,CA|fA|re);case 47:return QA(e,"零壹貳參肆伍陸柒捌玖",Yn,"負",r,_A|CA|fA|re);case 42:return QA(e,"零一二三四五六七八九",Jn,"负",r,CA|fA|re);case 41:return QA(e,"零壹贰叁肆伍陆柒捌玖",Yn,"负",r,_A|CA|fA|re);case 26:return QA(e,"〇一二三四五六七八九","十百千万",Wn,r,0);case 25:return QA(e,"零壱弐参四伍六七八九","拾百千万",Wn,r,_A|CA|fA);case 31:return QA(e,"영일이삼사오육칠팔구","십백천만",Ft,s,_A|CA|fA);case 33:return QA(e,"零一二三四五六七八九","十百千萬",Ft,s,0);case 32:return QA(e,"零壹貳參四五六七八九","拾百千",Ft,s,_A|CA|fA);case 18:return S(e,2406,2415,!0,t);case 20:return RA(e,1,19999,Sc,3,t);case 21:return S(e,2790,2799,!0,t);case 22:return S(e,2662,2671,!0,t);case 22:return RA(e,1,10999,Tc,3,t);case 23:return LA(e,"あいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもやゆよらりるれろわゐゑをん");case 24:return LA(e,"いろはにほへとちりぬるをわかよたれそつねならむうゐのおくやまけふこえてあさきゆめみしゑひもせす");case 27:return S(e,3302,3311,!0,t);case 28:return LA(e,"アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヰヱヲン",r);case 29:return LA(e,"イロハニホヘトチリヌルヲワカヨタレソツネナラムウヰノオクヤマケフコエテアサキユメミシヱヒモセス",r);case 34:return S(e,3792,3801,!0,t);case 37:return S(e,6160,6169,!0,t);case 38:return S(e,4160,4169,!0,t);case 39:return S(e,2918,2927,!0,t);case 40:return S(e,1776,1785,!0,t);case 43:return S(e,3046,3055,!0,t);case 44:return S(e,3174,3183,!0,t);case 45:return S(e,3664,3673,!0,t);case 46:return S(e,3872,3881,!0,t);case 3:default:return S(e,48,57,!0,t)}};const Zr="data-html2canvas-ignore",Zn=function(){function e(A,n,t){if(this.context=A,this.options=t,this.scrolledElements=[],this.referenceElement=n,this.counters=new Dc,this.quoteDepth=0,!n.ownerDocument)throw new Error("Cloned element does not have an owner document");this.documentElement=this.cloneNode(n.ownerDocument.documentElement,!1)}return e.prototype.toIFrame=function(A,n){const t=this,r=Oc(A,n);if(!r.contentWindow)return Promise.reject("Unable to find iframe window");const s=A.defaultView.pageXOffset,B=A.defaultView.pageYOffset,o=r.contentWindow,i=o.document,c=Rc(r).then(function(){return k(t,void 0,void 0,function(){let a,Q;return P(this,function(g){switch(g.label){case 0:return this.scrolledElements.forEach(vc),o&&(o.scrollTo(n.left,n.top),/(iPad|iPhone|iPod)/g.test(navigator.userAgent)&&(o.scrollY!==n.top||o.scrollX!==n.left)&&(this.context.logger.warn("Unable to restore scroll position for cloned document"),this.context.windowBounds=this.context.windowBounds.add(o.scrollX-n.left,o.scrollY-n.top,0,0))),a=this.options.onclone,Q=this.clonedReferenceElement,typeof Q>"u"?[2,Promise.reject("Error finding the "+this.referenceElement.nodeName+" in the cloned document")]:i.fonts&&i.fonts.ready?[4,i.fonts.ready]:[3,2];case 1:g.sent(),g.label=2;case 2:return/(AppleWebKit)/g.test(navigator.userAgent)?[4,Gc(i)]:[3,4];case 3:g.sent(),g.label=4;case 4:return typeof a=="function"?[2,Promise.resolve().then(function(){return a(i,Q)}).then(function(){return r})]:[2,r]}})})});return i.open(),i.write(Nc(document.doctype)+"<html></html>"),_c(this.referenceElement.ownerDocument,s,B),i.replaceChild(i.adoptNode(this.documentElement),i.documentElement),i.close(),c},e.prototype.createElementClone=function(A){if(Vt(A,2))debugger;if(kr(A))return this.createCanvasClone(A);if(_n(A))return this.createVideoClone(A);if(vn(A))return this.createStyleClone(A);const n=A.cloneNode(!1);return Pt(n)&&(Pt(A)&&A.currentSrc&&A.currentSrc!==A.src&&(n.src=A.currentSrc,n.srcset=""),n.loading==="lazy"&&(n.loading="eager")),Pn(n)?this.createCustomElementClone(n):n},e.prototype.createCustomElementClone=function(A){const n=document.createElement("html2canvascustomelement");return ht(A.style,n),n},e.prototype.createStyleClone=function(A){try{const n=A.sheet;if(n&&n.cssRules){const t=[].slice.call(n.cssRules,0).reduce(function(s,B){return B&&typeof B.cssText=="string"?s+B.cssText:s},""),r=A.cloneNode(!1);return r.textContent=t,r}}catch(n){if(this.context.logger.error("Unable to access cssRules property",n),n.name!=="SecurityError")throw n}return A.cloneNode(!1)},e.prototype.createCanvasClone=function(A){let n;if(this.options.inlineImages&&A.ownerDocument){const r=A.ownerDocument.createElement("img");try{return r.src=A.toDataURL(),r}catch{this.context.logger.info("Unable to inline canvas contents, canvas is tainted",A)}}const t=A.cloneNode(!1);try{t.width=A.width,t.height=A.height;const r=A.getContext("2d"),s=t.getContext("2d");if(s)if(!this.options.allowTaint&&r)s.putImageData(r.getImageData(0,0,A.width,A.height),0,0);else{const B=(n=A.getContext("webgl2"))!==null&&n!==void 0?n:A.getContext("webgl");if(B){const o=B.getContextAttributes();(o==null?void 0:o.preserveDrawingBuffer)===!1&&this.context.logger.warn("Unable to clone WebGL context as it has preserveDrawingBuffer=false",A)}s.drawImage(A,0,0)}return t}catch{this.context.logger.info("Unable to clone canvas as it is tainted",A)}return t},e.prototype.createVideoClone=function(A){const n=A.ownerDocument.createElement("canvas");n.width=A.offsetWidth,n.height=A.offsetHeight;const t=n.getContext("2d");try{return t&&(t.drawImage(A,0,0,n.width,n.height),this.options.allowTaint||t.getImageData(0,0,n.width,n.height)),n}catch{this.context.logger.info("Unable to clone video as it is tainted",A)}const r=A.ownerDocument.createElement("canvas");return r.width=A.offsetWidth,r.height=A.offsetHeight,r},e.prototype.appendChildNode=function(A,n,t){(!PA(n)||!xc(n)&&!n.hasAttribute(Zr)&&(typeof this.options.ignoreElements!="function"||!this.options.ignoreElements(n)))&&(!this.options.copyStyles||!PA(n)||!vn(n))&&A.appendChild(this.cloneNode(n,t))},e.prototype.cloneChildNodes=function(A,n,t){const r=this;for(let s=A.shadowRoot?A.shadowRoot.firstChild:A.firstChild;s;s=s.nextSibling)if(PA(s)&&Yr(s)&&typeof s.assignedNodes=="function"){const B=s.assignedNodes();B.length&&B.forEach(function(o){return r.appendChildNode(n,o,t)})}else this.appendChildNode(n,s,t)},e.prototype.cloneNode=function(A,n){if(Pr(A))return document.createTextNode(A.data);if(!A.ownerDocument)return A.cloneNode(!1);const t=A.ownerDocument.defaultView;if(t&&PA(A)&&(vt(A)||Me(A))){const r=this.createElementClone(A);r.style.transitionProperty="none";const s=t.getComputedStyle(A),B=t.getComputedStyle(A,":before"),o=t.getComputedStyle(A,":after");this.referenceElement===A&&vt(r)&&(this.clonedReferenceElement=r),$t(r)&&kc(r);const i=this.counters.parse(new yn(this.context,s)),c=this.resolvePseudoContent(A,r,B,ce.BEFORE);Pn(A)&&(n=!0),_n(A)||this.cloneChildNodes(A,r,n),c&&r.insertBefore(c,r.firstChild);const a=this.resolvePseudoContent(A,r,o,ce.AFTER);return a&&r.appendChild(a),this.counters.pop(i),(s&&(this.options.copyStyles||Me(A))&&!Jr(A)||n)&&ht(s,r),(A.scrollTop!==0||A.scrollLeft!==0)&&this.scrolledElements.push([r,A.scrollLeft,A.scrollTop]),(ve(A)||Pe(A))&&(ve(r)||Pe(r))&&(r.value=A.value),r}return A.cloneNode(!1)},e.prototype.resolvePseudoContent=function(A,n,t,r){const s=this;if(!t)return;const B=t.content,o=n.ownerDocument;if(!o||!B||B==="none"||B==="-moz-alt-content"||t.display==="none")return;this.counters.parse(new yn(this.context,t));const i=new mi(this.context,t),c=o.createElement("html2canvaspseudoelement");ht(t,c),i.content.forEach(function(Q){if(Q.type===0)c.appendChild(o.createTextNode(Q.value));else if(Q.type===22){const F=o.createElement("img");F.src=Q.value,F.style.opacity="1",c.appendChild(F)}else if(Q.type===18){if(Q.name==="attr"){const F=Q.values.filter(b);F.length&&c.appendChild(o.createTextNode(A.getAttribute(F[0].value)||""))}else if(Q.name==="counter"){var g=Q.values.filter(kA),l=g[0],w=g[1];if(l&&b(l)){const F=s.counters.getCounterValue(l.value),h=w&&b(w)?Rt.parse(s.context,w.value):3;c.appendChild(o.createTextNode(ge(F,h,!1)))}}else if(Q.name==="counters"){var U=Q.values.filter(kA),l=U[0],p=U[1],w=U[2];if(l&&b(l)){const T=s.counters.getCounterValues(l.value),m=w&&b(w)?Rt.parse(s.context,w.value):3,d=p&&p.type===0?p.value:"",C=T.map(function(I){return ge(I,m,!1)}).join(d);c.appendChild(o.createTextNode(C))}}}else if(Q.type===20)switch(Q.value){case"open-quote":c.appendChild(o.createTextNode(In(i.quotes,s.quoteDepth++,!0)));break;case"close-quote":c.appendChild(o.createTextNode(In(i.quotes,--s.quoteDepth,!1)));break;default:c.appendChild(o.createTextNode(Q.value))}}),c.className=Xt+" "+kt;const a=r===ce.BEFORE?" "+Xt:" "+kt;return Me(n)?n.className.baseValue+=a:n.className+=a,c},e.destroy=function(A){return A.parentNode?(A.parentNode.removeChild(A),!0):!1},e}();let ce;(function(e){e[e.BEFORE=0]="BEFORE",e[e.AFTER=1]="AFTER"})(ce||(ce={}));var Oc=function(e,A){const n=e.createElement("iframe");return n.className="html2canvas-container",n.style.visibility="hidden",n.style.position="fixed",n.style.left="-10000px",n.style.top="0px",n.style.border="0",n.width=A.width.toString(),n.height=A.height.toString(),n.scrolling="no",n.setAttribute(Zr,"true"),e.body.appendChild(n),n};const Mc=function(e){return new Promise(function(A){if(e.complete){A();return}if(!e.src){A();return}e.onload=A,e.onerror=A})};var Gc=function(e){return Promise.all([].slice.call(e.images,0).map(Mc))},Rc=function(e){return new Promise(function(A,n){const t=e.contentWindow;if(!t)return n("No window assigned for iframe");const r=t.document;t.onload=e.onload=function(){t.onload=e.onload=null;var s=rs(function(){r.body.childNodes.length>0&&r.readyState==="complete"&&(clearInterval(s),A(e))},50)}})};const Vc=["all","d","content"];var ht=function(e,A){for(let n=e.length-1;n>=0;n--){const t=e.item(n);Vc.indexOf(t)===-1&&A.style.setProperty(t,e.getPropertyValue(t))}return A},Nc=function(e){let A="";return e&&(A+="<!DOCTYPE ",e.name&&(A+=e.name),e.internalSubset&&(A+=e.internalSubset),e.publicId&&(A+='"'+e.publicId+'"'),e.systemId&&(A+='"'+e.systemId+'"'),A+=">"),A},_c=function(e,A,n){e&&e.defaultView&&(A!==e.defaultView.pageXOffset||n!==e.defaultView.pageYOffset)&&e.defaultView.scrollTo(A,n)},vc=function(e){const A=e[0],n=e[1],t=e[2];A.scrollLeft=n,A.scrollTop=t};const Pc=":before",Xc=":after";var Xt="___html2canvas___pseudoelement_before",kt="___html2canvas___pseudoelement_after";const qn=`{
    content: "" !important;
    display: none !important;
}`;var kc=function(e){Jc(e,"."+Xt+Pc+qn+`
         .`+kt+Xc+qn)},Jc=function(e,A){const n=e.ownerDocument;if(n){const t=n.createElement("style");t.textContent=A,e.appendChild(t)}};const qr=function(){function e(){}return e.getOrigin=function(A){const n=e._link;return n?(n.href=A,n.href=n.href,n.protocol+n.hostname+n.port):"about:blank"},e.isSameOrigin=function(A){return e.getOrigin(A)===e._origin},e.setContext=function(A){e._link=A.document.createElement("a"),e._origin=e.getOrigin(A.location.href)},e._origin="about:blank",e}(),Yc=function(){function e(A,n){this.context=A,this._options=n,this._cache={}}return e.prototype.addImage=function(A){const n=Promise.resolve();return this.has(A)||(Et(A)||jc(A))&&(this._cache[A]=this.loadImage(A)).catch(function(){}),n},e.prototype.match=function(A){return this._cache[A]},e.prototype.loadImage=function(A){return k(this,void 0,void 0,function(){let n,t,r,s;const B=this;return P(this,function(o){switch(o.label){case 0:return n=qr.isSameOrigin(A),t=!dt(A)&&this._options.useCORS===!0&&_.SUPPORT_CORS_IMAGES&&!n,r=!dt(A)&&!n&&!Et(A)&&typeof this._options.proxy=="string"&&_.SUPPORT_CORS_XHR&&!t,!n&&this._options.allowTaint===!1&&!dt(A)&&!Et(A)&&!r&&!t?[2]:(s=A,r?[4,this.proxy(s)]:[3,2]);case 1:s=o.sent(),o.label=2;case 2:return this.context.logger.debug("Added image "+A.substring(0,256)),[4,new Promise(function(i,c){const a=new Image;a.onload=function(){return i(a)},a.onerror=c,(zc(s)||t)&&(a.crossOrigin="anonymous"),a.src=s,a.complete===!0&&setTimeout(function(){return i(a)},500),B._options.imageTimeout>0&&setTimeout(function(){return c("Timed out ("+B._options.imageTimeout+"ms) loading image")},B._options.imageTimeout)})];case 3:return[2,o.sent()]}})})},e.prototype.has=function(A){return typeof this._cache[A]<"u"},e.prototype.keys=function(){return Promise.resolve(Object.keys(this._cache))},e.prototype.proxy=function(A){const n=this,t=this._options.proxy;if(!t)throw new Error("No proxy defined");const r=A.substring(0,256);return new Promise(function(s,B){const o=_.SUPPORT_RESPONSE_TYPE?"blob":"text",i=new XMLHttpRequest;i.onload=function(){if(i.status===200)if(o==="text")s(i.response);else{const a=new FileReader;a.addEventListener("load",function(){return s(a.result)},!1),a.addEventListener("error",function(Q){return B(Q)},!1),a.readAsDataURL(i.response)}else B("Failed to proxy resource "+r+" with status code "+i.status)},i.onerror=B;const c=t.indexOf("?")>-1?"&":"?";if(i.open("GET",""+t+c+"url="+encodeURIComponent(A)+"&responseType="+o),o!=="text"&&i instanceof XMLHttpRequest&&(i.responseType=o),n._options.imageTimeout){const a=n._options.imageTimeout;i.timeout=a,i.ontimeout=function(){return B("Timed out ("+a+"ms) proxying "+r)}}i.send()})},e}(),Wc=/^data:image\/svg\+xml/i,Zc=/^data:image\/.*;base64,/i,qc=/^data:image\/.*/i;var jc=function(e){return _.SUPPORT_SVG_DRAWING||!$c(e)},dt=function(e){return qc.test(e)},zc=function(e){return Zc.test(e)},Et=function(e){return e.substr(0,4)==="blob"},$c=function(e){return e.substr(-3).toLowerCase()==="svg"||Wc.test(e)};const u=function(){function e(A,n){this.type=0,this.x=A,this.y=n}return e.prototype.add=function(A,n){return new e(this.x+A,this.y+n)},e}(),VA=function(e,A,n){return new u(e.x+(A.x-e.x)*n,e.y+(A.y-e.y)*n)},Le=function(){function e(A,n,t,r){this.type=1,this.start=A,this.startControl=n,this.endControl=t,this.end=r}return e.prototype.subdivide=function(A,n){const t=VA(this.start,this.startControl,A),r=VA(this.startControl,this.endControl,A),s=VA(this.endControl,this.end,A),B=VA(t,r,A),o=VA(r,s,A),i=VA(B,o,A);return n?new e(this.start,t,B,i):new e(i,o,s,this.end)},e.prototype.add=function(A,n){return new e(this.start.add(A,n),this.startControl.add(A,n),this.endControl.add(A,n),this.end.add(A,n))},e.prototype.reverse=function(){return new e(this.end,this.endControl,this.startControl,this.start)},e}(),$=function(e){return e.type===1},Aa=function(){function e(A){const n=A.styles,t=A.bounds;let r=te(n.borderTopLeftRadius,t.width,t.height),s=r[0],B=r[1],o=te(n.borderTopRightRadius,t.width,t.height),i=o[0],c=o[1],a=te(n.borderBottomRightRadius,t.width,t.height),Q=a[0],g=a[1],l=te(n.borderBottomLeftRadius,t.width,t.height),w=l[0],U=l[1];const p=[];p.push((s+i)/t.width),p.push((w+Q)/t.width),p.push((B+U)/t.height),p.push((c+g)/t.height);const F=Math.max.apply(Math,p);F>1&&(s/=F,B/=F,i/=F,c/=F,Q/=F,g/=F,w/=F,U/=F);const h=t.width-i,T=t.height-g,m=t.width-Q,d=t.height-U,C=n.borderTopWidth,I=n.borderRightWidth,y=n.borderBottomWidth,E=n.borderLeftWidth,M=x(n.paddingTop,A.bounds.width),j=x(n.paddingRight,A.bounds.width),Z=x(n.paddingBottom,A.bounds.width),L=x(n.paddingLeft,A.bounds.width);this.topLeftBorderDoubleOuterBox=s>0||B>0?D(t.left+E/3,t.top+C/3,s-E/3,B-C/3,K.TOP_LEFT):new u(t.left+E/3,t.top+C/3),this.topRightBorderDoubleOuterBox=s>0||B>0?D(t.left+h,t.top+C/3,i-I/3,c-C/3,K.TOP_RIGHT):new u(t.left+t.width-I/3,t.top+C/3),this.bottomRightBorderDoubleOuterBox=Q>0||g>0?D(t.left+m,t.top+T,Q-I/3,g-y/3,K.BOTTOM_RIGHT):new u(t.left+t.width-I/3,t.top+t.height-y/3),this.bottomLeftBorderDoubleOuterBox=w>0||U>0?D(t.left+E/3,t.top+d,w-E/3,U-y/3,K.BOTTOM_LEFT):new u(t.left+E/3,t.top+t.height-y/3),this.topLeftBorderDoubleInnerBox=s>0||B>0?D(t.left+E*2/3,t.top+C*2/3,s-E*2/3,B-C*2/3,K.TOP_LEFT):new u(t.left+E*2/3,t.top+C*2/3),this.topRightBorderDoubleInnerBox=s>0||B>0?D(t.left+h,t.top+C*2/3,i-I*2/3,c-C*2/3,K.TOP_RIGHT):new u(t.left+t.width-I*2/3,t.top+C*2/3),this.bottomRightBorderDoubleInnerBox=Q>0||g>0?D(t.left+m,t.top+T,Q-I*2/3,g-y*2/3,K.BOTTOM_RIGHT):new u(t.left+t.width-I*2/3,t.top+t.height-y*2/3),this.bottomLeftBorderDoubleInnerBox=w>0||U>0?D(t.left+E*2/3,t.top+d,w-E*2/3,U-y*2/3,K.BOTTOM_LEFT):new u(t.left+E*2/3,t.top+t.height-y*2/3),this.topLeftBorderStroke=s>0||B>0?D(t.left+E/2,t.top+C/2,s-E/2,B-C/2,K.TOP_LEFT):new u(t.left+E/2,t.top+C/2),this.topRightBorderStroke=s>0||B>0?D(t.left+h,t.top+C/2,i-I/2,c-C/2,K.TOP_RIGHT):new u(t.left+t.width-I/2,t.top+C/2),this.bottomRightBorderStroke=Q>0||g>0?D(t.left+m,t.top+T,Q-I/2,g-y/2,K.BOTTOM_RIGHT):new u(t.left+t.width-I/2,t.top+t.height-y/2),this.bottomLeftBorderStroke=w>0||U>0?D(t.left+E/2,t.top+d,w-E/2,U-y/2,K.BOTTOM_LEFT):new u(t.left+E/2,t.top+t.height-y/2),this.topLeftBorderBox=s>0||B>0?D(t.left,t.top,s,B,K.TOP_LEFT):new u(t.left,t.top),this.topRightBorderBox=i>0||c>0?D(t.left+h,t.top,i,c,K.TOP_RIGHT):new u(t.left+t.width,t.top),this.bottomRightBorderBox=Q>0||g>0?D(t.left+m,t.top+T,Q,g,K.BOTTOM_RIGHT):new u(t.left+t.width,t.top+t.height),this.bottomLeftBorderBox=w>0||U>0?D(t.left,t.top+d,w,U,K.BOTTOM_LEFT):new u(t.left,t.top+t.height),this.topLeftPaddingBox=s>0||B>0?D(t.left+E,t.top+C,Math.max(0,s-E),Math.max(0,B-C),K.TOP_LEFT):new u(t.left+E,t.top+C),this.topRightPaddingBox=i>0||c>0?D(t.left+Math.min(h,t.width-I),t.top+C,h>t.width+I?0:Math.max(0,i-I),Math.max(0,c-C),K.TOP_RIGHT):new u(t.left+t.width-I,t.top+C),this.bottomRightPaddingBox=Q>0||g>0?D(t.left+Math.min(m,t.width-E),t.top+Math.min(T,t.height-y),Math.max(0,Q-I),Math.max(0,g-y),K.BOTTOM_RIGHT):new u(t.left+t.width-I,t.top+t.height-y),this.bottomLeftPaddingBox=w>0||U>0?D(t.left+E,t.top+Math.min(d,t.height-y),Math.max(0,w-E),Math.max(0,U-y),K.BOTTOM_LEFT):new u(t.left+E,t.top+t.height-y),this.topLeftContentBox=s>0||B>0?D(t.left+E+L,t.top+C+M,Math.max(0,s-(E+L)),Math.max(0,B-(C+M)),K.TOP_LEFT):new u(t.left+E+L,t.top+C+M),this.topRightContentBox=i>0||c>0?D(t.left+Math.min(h,t.width+E+L),t.top+C+M,h>t.width+E+L?0:i-E+L,c-(C+M),K.TOP_RIGHT):new u(t.left+t.width-(I+j),t.top+C+M),this.bottomRightContentBox=Q>0||g>0?D(t.left+Math.min(m,t.width-(E+L)),t.top+Math.min(T,t.height+C+M),Math.max(0,Q-(I+j)),g-(y+Z),K.BOTTOM_RIGHT):new u(t.left+t.width-(I+j),t.top+t.height-(y+Z)),this.bottomLeftContentBox=w>0||U>0?D(t.left+E+L,t.top+d,Math.max(0,w-(E+L)),U-(y+Z),K.BOTTOM_LEFT):new u(t.left+E+L,t.top+t.height-(y+Z))}return e}();let K;(function(e){e[e.TOP_LEFT=0]="TOP_LEFT",e[e.TOP_RIGHT=1]="TOP_RIGHT",e[e.BOTTOM_RIGHT=2]="BOTTOM_RIGHT",e[e.BOTTOM_LEFT=3]="BOTTOM_LEFT"})(K||(K={}));var D=function(e,A,n,t,r){const s=4*((Math.sqrt(2)-1)/3),B=n*s,o=t*s,i=e+n,c=A+t;switch(r){case K.TOP_LEFT:return new Le(new u(e,c),new u(e,c-o),new u(i-B,A),new u(i,A));case K.TOP_RIGHT:return new Le(new u(e,A),new u(e+B,A),new u(i,c-o),new u(i,c));case K.BOTTOM_RIGHT:return new Le(new u(i,A),new u(i,A+o),new u(e+B,c),new u(e,c));case K.BOTTOM_LEFT:default:return new Le(new u(i,c),new u(i-B,c),new u(e,A+o),new u(e,A))}};const Xe=function(e){return[e.topLeftBorderBox,e.topRightBorderBox,e.bottomRightBorderBox,e.bottomLeftBorderBox]},ea=function(e){return[e.topLeftContentBox,e.topRightContentBox,e.bottomRightContentBox,e.bottomLeftContentBox]},ke=function(e){return[e.topLeftPaddingBox,e.topRightPaddingBox,e.bottomRightPaddingBox,e.bottomLeftPaddingBox]},ta=function(){function e(A,n,t){this.offsetX=A,this.offsetY=n,this.matrix=t,this.type=0,this.target=6}return e}(),be=function(){function e(A,n){this.path=A,this.target=n,this.type=1}return e}(),na=function(){function e(A){this.opacity=A,this.type=2,this.target=6}return e}(),ra=function(e){return e.type===0},jr=function(e){return e.type===1},sa=function(e){return e.type===2},jn=function(e,A){return e.length===A.length?e.some(function(n,t){return n===A[t]}):!1},Ba=function(e,A,n,t,r){return e.map(function(s,B){switch(B){case 0:return s.add(A,n);case 1:return s.add(A+t,n);case 2:return s.add(A+t,n+r);case 3:return s.add(A,n+r)}return s})},zr=function(){function e(A){this.element=A,this.inlineLevel=[],this.nonInlineLevel=[],this.negativeZIndex=[],this.zeroOrAutoZIndexOrTransformedOrOpacity=[],this.positiveZIndex=[],this.nonPositionedFloats=[],this.nonPositionedInlineLevel=[]}return e}(),$r=function(){function e(A,n){if(this.container=A,this.parent=n,this.effects=[],this.curves=new Aa(this.container),this.container.styles.opacity<1&&this.effects.push(new na(this.container.styles.opacity)),this.container.styles.transform!==null){const t=this.container.bounds.left+this.container.styles.transformOrigin[0].number,r=this.container.bounds.top+this.container.styles.transformOrigin[1].number,s=this.container.styles.transform;this.effects.push(new ta(t,r,s))}if(this.container.styles.overflowX!==0){const t=Xe(this.curves),r=ke(this.curves);jn(t,r)?this.effects.push(new be(t,6)):(this.effects.push(new be(t,2)),this.effects.push(new be(r,4)))}}return e.prototype.getEffects=function(A){let n=[2,3].indexOf(this.container.styles.position)===-1,t=this.parent;const r=this.effects.slice(0);for(;t;){const s=t.effects.filter(function(B){return!jr(B)});if(n||t.container.styles.position!==0||!t.parent){if(r.unshift.apply(r,s),n=[2,3].indexOf(t.container.styles.position)===-1,t.container.styles.overflowX!==0){const B=Xe(t.curves),o=ke(t.curves);jn(B,o)||r.unshift(new be(o,6))}}else r.unshift.apply(r,s);t=t.parent}return r.filter(function(s){return R(s.target,A)})},e}();var Jt=function(e,A,n,t){e.container.elements.forEach(function(r){const s=R(r.flags,4),B=R(r.flags,2),o=new $r(r,e);R(r.styles.display,2048)&&t.push(o);const i=R(r.flags,8)?[]:t;if(s||B){const c=s||r.styles.isPositioned()?n:A,a=new zr(o);if(r.styles.isPositioned()||r.styles.opacity<1||r.styles.isTransformed()){const Q=r.styles.zIndex.order;if(Q<0){let g=0;c.negativeZIndex.some(function(l,w){return Q>l.element.container.styles.zIndex.order?(g=w,!1):g>0}),c.negativeZIndex.splice(g,0,a)}else if(Q>0){let g=0;c.positiveZIndex.some(function(l,w){return Q>=l.element.container.styles.zIndex.order?(g=w+1,!1):g>0}),c.positiveZIndex.splice(g,0,a)}else c.zeroOrAutoZIndexOrTransformedOrOpacity.push(a)}else r.styles.isFloating()?c.nonPositionedFloats.push(a):c.nonPositionedInlineLevel.push(a);Jt(o,a,s?a:n,i)}else r.styles.isInlineLevel()?A.inlineLevel.push(o):A.nonInlineLevel.push(o),Jt(o,A,n,i);R(r.flags,8)&&As(r,i)})},As=function(e,A){let n=e instanceof _t?e.start:1;const t=e instanceof _t?e.reversed:!1;for(let r=0;r<A.length;r++){const s=A[r];s.container instanceof Gr&&typeof s.container.value=="number"&&s.container.value!==0&&(n=s.container.value),s.listValue=ge(n,s.container.styles.listStyleType,!0),n+=t?-1:1}};const oa=function(e){const A=new $r(e,null),n=new zr(A),t=[];return Jt(A,n,n,t),As(A.container,t),n},zn=function(e,A){switch(A){case 0:return eA(e.topLeftBorderBox,e.topLeftPaddingBox,e.topRightBorderBox,e.topRightPaddingBox);case 1:return eA(e.topRightBorderBox,e.topRightPaddingBox,e.bottomRightBorderBox,e.bottomRightPaddingBox);case 2:return eA(e.bottomRightBorderBox,e.bottomRightPaddingBox,e.bottomLeftBorderBox,e.bottomLeftPaddingBox);case 3:default:return eA(e.bottomLeftBorderBox,e.bottomLeftPaddingBox,e.topLeftBorderBox,e.topLeftPaddingBox)}},ia=function(e,A){switch(A){case 0:return eA(e.topLeftBorderBox,e.topLeftBorderDoubleOuterBox,e.topRightBorderBox,e.topRightBorderDoubleOuterBox);case 1:return eA(e.topRightBorderBox,e.topRightBorderDoubleOuterBox,e.bottomRightBorderBox,e.bottomRightBorderDoubleOuterBox);case 2:return eA(e.bottomRightBorderBox,e.bottomRightBorderDoubleOuterBox,e.bottomLeftBorderBox,e.bottomLeftBorderDoubleOuterBox);case 3:default:return eA(e.bottomLeftBorderBox,e.bottomLeftBorderDoubleOuterBox,e.topLeftBorderBox,e.topLeftBorderDoubleOuterBox)}},ca=function(e,A){switch(A){case 0:return eA(e.topLeftBorderDoubleInnerBox,e.topLeftPaddingBox,e.topRightBorderDoubleInnerBox,e.topRightPaddingBox);case 1:return eA(e.topRightBorderDoubleInnerBox,e.topRightPaddingBox,e.bottomRightBorderDoubleInnerBox,e.bottomRightPaddingBox);case 2:return eA(e.bottomRightBorderDoubleInnerBox,e.bottomRightPaddingBox,e.bottomLeftBorderDoubleInnerBox,e.bottomLeftPaddingBox);case 3:default:return eA(e.bottomLeftBorderDoubleInnerBox,e.bottomLeftPaddingBox,e.topLeftBorderDoubleInnerBox,e.topLeftPaddingBox)}},aa=function(e,A){switch(A){case 0:return xe(e.topLeftBorderStroke,e.topRightBorderStroke);case 1:return xe(e.topRightBorderStroke,e.bottomRightBorderStroke);case 2:return xe(e.bottomRightBorderStroke,e.bottomLeftBorderStroke);case 3:default:return xe(e.bottomLeftBorderStroke,e.topLeftBorderStroke)}};var xe=function(e,A){const n=[];return $(e)?n.push(e.subdivide(.5,!1)):n.push(e),$(A)?n.push(A.subdivide(.5,!0)):n.push(A),n},eA=function(e,A,n,t){const r=[];return $(e)?r.push(e.subdivide(.5,!1)):r.push(e),$(n)?r.push(n.subdivide(.5,!0)):r.push(n),$(t)?r.push(t.subdivide(.5,!0).reverse()):r.push(t),$(A)?r.push(A.subdivide(.5,!1).reverse()):r.push(A),r};const es=function(e){const A=e.bounds,n=e.styles;return A.add(n.borderLeftWidth,n.borderTopWidth,-(n.borderRightWidth+n.borderLeftWidth),-(n.borderTopWidth+n.borderBottomWidth))},Je=function(e){const A=e.styles,n=e.bounds,t=x(A.paddingLeft,n.width),r=x(A.paddingRight,n.width),s=x(A.paddingTop,n.width),B=x(A.paddingBottom,n.width);return n.add(t+A.borderLeftWidth,s+A.borderTopWidth,-(A.borderRightWidth+A.borderLeftWidth+t+r),-(A.borderTopWidth+A.borderBottomWidth+s+B))},Qa=function(e,A){return e===0?A.bounds:e===2?Je(A):es(A)},ga=function(e,A){return e===0?A.bounds:e===2?Je(A):es(A)},Ht=function(e,A,n){const t=Qa(vA(e.styles.backgroundOrigin,A),e),r=ga(vA(e.styles.backgroundClip,A),e),s=wa(vA(e.styles.backgroundSize,A),n,t),B=s[0],o=s[1],i=te(vA(e.styles.backgroundPosition,A),t.width-B,t.height-o),c=la(vA(e.styles.backgroundRepeat,A),i,s,t,r),a=Math.round(t.left+i[0]),Q=Math.round(t.top+i[1]);return[c,a,Q,B,o]},NA=function(e){return b(e)&&e.value===XA.AUTO},De=function(e){return typeof e=="number"};var wa=function(e,A,n){const t=A[0],r=A[1],s=A[2],B=e[0],o=e[1];if(!B)return[0,0];if(G(B)&&o&&G(o))return[x(B,n.width),x(o,n.height)];const i=De(s);if(b(B)&&(B.value===XA.CONTAIN||B.value===XA.COVER))return De(s)?n.width/n.height<s!=(B.value===XA.COVER)?[n.width,n.width/s]:[n.height*s,n.height]:[n.width,n.height];const c=De(t),a=De(r),Q=c||a;if(NA(B)&&(!o||NA(o))){if(c&&a)return[t,r];if(!i&&!Q)return[n.width,n.height];if(Q&&i){const p=c?t:r*s,F=a?r:t/s;return[p,F]}const w=c?t:n.width,U=a?r:n.height;return[w,U]}if(i){let w=0,U=0;return G(B)?w=x(B,n.width):G(o)&&(U=x(o,n.height)),NA(B)?w=U*s:(!o||NA(o))&&(U=w/s),[w,U]}let g=null,l=null;if(G(B)?g=x(B,n.width):o&&G(o)&&(l=x(o,n.height)),g!==null&&(!o||NA(o))&&(l=c&&a?g/t*r:n.height),l!==null&&NA(B)&&(g=c&&a?l/r*t:n.width),g!==null&&l!==null)return[g,l];throw new Error("Unable to calculate background-size for element")},vA=function(e,A){const n=e[A];return typeof n>"u"?e[0]:n},la=function(e,A,n,t,r){const s=A[0],B=A[1],o=n[0],i=n[1];switch(e){case 2:return[new u(Math.round(t.left),Math.round(t.top+B)),new u(Math.round(t.left+t.width),Math.round(t.top+B)),new u(Math.round(t.left+t.width),Math.round(i+t.top+B)),new u(Math.round(t.left),Math.round(i+t.top+B))];case 3:return[new u(Math.round(t.left+s),Math.round(t.top)),new u(Math.round(t.left+s+o),Math.round(t.top)),new u(Math.round(t.left+s+o),Math.round(t.height+t.top)),new u(Math.round(t.left+s),Math.round(t.height+t.top))];case 1:return[new u(Math.round(t.left+s),Math.round(t.top+B)),new u(Math.round(t.left+s+o),Math.round(t.top+B)),new u(Math.round(t.left+s+o),Math.round(t.top+B+i)),new u(Math.round(t.left+s),Math.round(t.top+B+i))];default:return[new u(Math.round(r.left),Math.round(r.top)),new u(Math.round(r.left+r.width),Math.round(r.top)),new u(Math.round(r.left+r.width),Math.round(r.height+r.top)),new u(Math.round(r.left),Math.round(r.height+r.top))]}};const ua="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",$n="Hidden Text",Ca=function(){function e(A){this._data={},this._document=A}return e.prototype.parseMetrics=function(A,n){const t=this._document.createElement("div"),r=this._document.createElement("img"),s=this._document.createElement("span"),B=this._document.body;t.style.visibility="hidden",t.style.fontFamily=A,t.style.fontSize=n,t.style.margin="0",t.style.padding="0",t.style.whiteSpace="nowrap",B.appendChild(t),r.src=ua,r.width=1,r.height=1,r.style.margin="0",r.style.padding="0",r.style.verticalAlign="baseline",s.style.fontFamily=A,s.style.fontSize=n,s.style.margin="0",s.style.padding="0",s.appendChild(this._document.createTextNode($n)),t.appendChild(s),t.appendChild(r);const o=r.offsetTop-s.offsetTop+2;t.removeChild(s),t.appendChild(this._document.createTextNode($n)),t.style.lineHeight="normal",r.style.verticalAlign="super";const i=r.offsetTop-t.offsetTop+2;return B.removeChild(t),{baseline:o,middle:i}},e.prototype.getMetrics=function(A,n){const t=A+" "+n;return typeof this._data[t]>"u"&&(this._data[t]=this.parseMetrics(A,n)),this._data[t]},e}(),ts=function(){function e(A,n){this.context=A,this.options=n}return e}(),fa=1e4,Ua=function(e){nA(A,e);function A(n,t){const r=e.call(this,n,t)||this;return r._activeEffects=[],r.canvas=t.canvas?t.canvas:document.createElement("canvas"),r.ctx=r.canvas.getContext("2d"),t.canvas||(r.canvas.width=Math.floor(t.width*t.scale),r.canvas.height=Math.floor(t.height*t.scale),r.canvas.style.width=t.width+"px",r.canvas.style.height=t.height+"px"),r.fontMetrics=new Ca(document),r.ctx.scale(r.options.scale,r.options.scale),r.ctx.translate(-t.x,-t.y),r.ctx.textBaseline="bottom",r._activeEffects=[],r.context.logger.debug("Canvas renderer initialized ("+t.width+"x"+t.height+") with scale "+t.scale),r}return A.prototype.applyEffects=function(n){const t=this;for(;this._activeEffects.length;)this.popEffect();n.forEach(function(r){return t.applyEffect(r)})},A.prototype.applyEffect=function(n){this.ctx.save(),sa(n)&&(this.ctx.globalAlpha=n.opacity),ra(n)&&(this.ctx.translate(n.offsetX,n.offsetY),this.ctx.transform(n.matrix[0],n.matrix[1],n.matrix[2],n.matrix[3],n.matrix[4],n.matrix[5]),this.ctx.translate(-n.offsetX,-n.offsetY)),jr(n)&&(this.path(n.path),this.ctx.clip()),this._activeEffects.push(n)},A.prototype.popEffect=function(){this._activeEffects.pop(),this.ctx.restore()},A.prototype.renderStack=function(n){return k(this,void 0,void 0,function(){let t;return P(this,function(r){switch(r.label){case 0:return t=n.element.container.styles,t.isVisible()?[4,this.renderStackContent(n)]:[3,2];case 1:r.sent(),r.label=2;case 2:return[2]}})})},A.prototype.renderNode=function(n){return k(this,void 0,void 0,function(){return P(this,function(t){switch(t.label){case 0:if(R(n.container.flags,16))debugger;return n.container.styles.isVisible()?[4,this.renderNodeBackgroundAndBorders(n)]:[3,3];case 1:return t.sent(),[4,this.renderNodeContent(n)];case 2:t.sent(),t.label=3;case 3:return[2]}})})},A.prototype.renderTextWithLetterSpacing=function(n,t,r){const s=this;t===0?this.ctx.fillText(n.text,n.bounds.left,n.bounds.top+r):jt(n.text).reduce(function(o,i){return s.ctx.fillText(i,o,n.bounds.top+r),o+s.ctx.measureText(i).width},n.bounds.left)},A.prototype.createFontStyle=function(n){const t=n.fontVariant.filter(function(B){return B==="normal"||B==="small-caps"}).join(""),r=Ha(n.fontFamily).join(", "),s=le(n.fontSize)?""+n.fontSize.number+n.fontSize.unit:n.fontSize.number+"px";return[[n.fontStyle,t,n.fontWeight,s,r].join(" "),r,s]},A.prototype.renderTextNode=function(n,t){return k(this,void 0,void 0,function(){let r,s,B,o,i,c,a,Q;const g=this;return P(this,function(l){return r=this.createFontStyle(t),s=r[0],B=r[1],o=r[2],this.ctx.font=s,this.ctx.direction=t.direction===1?"rtl":"ltr",this.ctx.textAlign="left",this.ctx.textBaseline="ideographic",i=this.fontMetrics.getMetrics(B,o),c=i.baseline,a=i.middle,Q=t.paintOrder,n.textBounds.forEach(function(w){Q.forEach(function(U){switch(U){case 0:g.ctx.fillStyle=V(t.color),g.renderTextWithLetterSpacing(w,t.letterSpacing,c);var p=t.textShadow;p.length&&w.text.trim().length&&(p.slice(0).reverse().forEach(function(F){g.ctx.shadowColor=V(F.color),g.ctx.shadowOffsetX=F.offsetX.number*g.options.scale,g.ctx.shadowOffsetY=F.offsetY.number*g.options.scale,g.ctx.shadowBlur=F.blur.number,g.renderTextWithLetterSpacing(w,t.letterSpacing,c)}),g.ctx.shadowColor="",g.ctx.shadowOffsetX=0,g.ctx.shadowOffsetY=0,g.ctx.shadowBlur=0),t.textDecorationLine.length&&(g.ctx.fillStyle=V(t.textDecorationColor||t.color),t.textDecorationLine.forEach(function(F){switch(F){case 1:g.ctx.fillRect(w.bounds.left,Math.round(w.bounds.top+c),w.bounds.width,1);break;case 2:g.ctx.fillRect(w.bounds.left,Math.round(w.bounds.top),w.bounds.width,1);break;case 3:g.ctx.fillRect(w.bounds.left,Math.ceil(w.bounds.top+a),w.bounds.width,1);break}}));break;case 1:t.webkitTextStrokeWidth&&w.text.trim().length&&(g.ctx.strokeStyle=V(t.webkitTextStrokeColor),g.ctx.lineWidth=t.webkitTextStrokeWidth,g.ctx.lineJoin=window.chrome?"miter":"round",g.ctx.strokeText(w.text,w.bounds.left,w.bounds.top+c)),g.ctx.strokeStyle="",g.ctx.lineWidth=0,g.ctx.lineJoin="miter";break}})}),[2]})})},A.prototype.renderReplacedElement=function(n,t,r){if(r&&n.intrinsicWidth>0&&n.intrinsicHeight>0){const s=Je(n),B=ke(t);this.path(B),this.ctx.save(),this.ctx.clip(),this.ctx.drawImage(r,0,0,n.intrinsicWidth,n.intrinsicHeight,s.left,s.top,s.width,s.height),this.ctx.restore()}},A.prototype.renderNodeContent=function(n){return k(this,void 0,void 0,function(){var t,r,s,B,o,i,h,h,c,a,Q,g,m,l,w,d,U,p,F,h,T,m,d;return P(this,function(C){switch(C.label){case 0:this.applyEffects(n.getEffects(4)),t=n.container,r=n.curves,s=t.styles,B=0,o=t.textNodes,C.label=1;case 1:return B<o.length?(i=o[B],[4,this.renderTextNode(i,s)]):[3,4];case 2:C.sent(),C.label=3;case 3:return B++,[3,1];case 4:if(!(t instanceof Sr))return[3,8];C.label=5;case 5:return C.trys.push([5,7,,8]),[4,this.context.cache.match(t.src)];case 6:return h=C.sent(),this.renderReplacedElement(t,r,h),[3,8];case 7:return C.sent(),this.context.logger.error("Error loading image "+t.src),[3,8];case 8:if(t instanceof Or&&this.renderReplacedElement(t,r,t.canvas),!(t instanceof Mr))return[3,12];C.label=9;case 9:return C.trys.push([9,11,,12]),[4,this.context.cache.match(t.svg)];case 10:return h=C.sent(),this.renderReplacedElement(t,r,h),[3,12];case 11:return C.sent(),this.context.logger.error("Error loading svg "+t.svg.substring(0,255)),[3,12];case 12:return t instanceof Nr&&t.tree?(c=new A(this.context,{scale:this.options.scale,backgroundColor:t.backgroundColor,x:0,y:0,width:t.width,height:t.height}),[4,c.render(t.tree)]):[3,14];case 13:a=C.sent(),t.width&&t.height&&this.ctx.drawImage(a,0,0,t.width,t.height,t.bounds.left,t.bounds.top,t.bounds.width,t.bounds.height),C.label=14;case 14:if(t instanceof zt&&(Q=Math.min(t.bounds.width,t.bounds.height),t.type===Ne?t.checked&&(this.ctx.save(),this.path([new u(t.bounds.left+Q*.39363,t.bounds.top+Q*.79),new u(t.bounds.left+Q*.16,t.bounds.top+Q*.5549),new u(t.bounds.left+Q*.27347,t.bounds.top+Q*.44071),new u(t.bounds.left+Q*.39694,t.bounds.top+Q*.5649),new u(t.bounds.left+Q*.72983,t.bounds.top+Q*.23),new u(t.bounds.left+Q*.84,t.bounds.top+Q*.34085),new u(t.bounds.left+Q*.39363,t.bounds.top+Q*.79)]),this.ctx.fillStyle=V(Nn),this.ctx.fill(),this.ctx.restore()):t.type===_e&&t.checked&&(this.ctx.save(),this.ctx.beginPath(),this.ctx.arc(t.bounds.left+Q/2,t.bounds.top+Q/2,Q/4,0,Math.PI*2,!0),this.ctx.fillStyle=V(Nn),this.ctx.fill(),this.ctx.restore())),Fa(t)&&t.value.length){switch(g=this.createFontStyle(s),m=g[0],l=g[1],w=this.fontMetrics.getMetrics(m,l).baseline,this.ctx.font=m,this.ctx.fillStyle=V(s.color),this.ctx.textBaseline="alphabetic",this.ctx.textAlign=da(t.styles.textAlign),d=Je(t),U=0,t.styles.textAlign){case 1:U+=d.width/2;break;case 2:U+=d.width;break}p=d.add(U,0,0,-d.height/2+1),this.ctx.save(),this.path([new u(d.left,d.top),new u(d.left+d.width,d.top),new u(d.left+d.width,d.top+d.height),new u(d.left,d.top+d.height)]),this.ctx.clip(),this.renderTextWithLetterSpacing(new ie(t.value,p),s.letterSpacing,w),this.ctx.restore(),this.ctx.textBaseline="alphabetic",this.ctx.textAlign="left"}if(!R(t.styles.display,2048))return[3,20];if(t.styles.listStyleImage===null)return[3,19];if(F=t.styles.listStyleImage,F.type!==0)return[3,18];h=void 0,T=F.url,C.label=15;case 15:return C.trys.push([15,17,,18]),[4,this.context.cache.match(T)];case 16:return h=C.sent(),this.ctx.drawImage(h,t.bounds.left-(h.width+10),t.bounds.top),[3,18];case 17:return C.sent(),this.context.logger.error("Error loading list-style-image "+T),[3,18];case 18:return[3,20];case 19:n.listValue&&t.styles.listStyleType!==-1&&(m=this.createFontStyle(s)[0],this.ctx.font=m,this.ctx.fillStyle=V(s.color),this.ctx.textBaseline="middle",this.ctx.textAlign="right",d=new wA(t.bounds.left,t.bounds.top+x(t.styles.paddingTop,t.bounds.width),t.bounds.width,Hn(s.lineHeight,s.fontSize.number)/2+1),this.renderTextWithLetterSpacing(new ie(n.listValue,d),s.letterSpacing,Hn(s.lineHeight,s.fontSize.number)/2+2),this.ctx.textBaseline="bottom",this.ctx.textAlign="left"),C.label=20;case 20:return[2]}})})},A.prototype.renderStackContent=function(n){return k(this,void 0,void 0,function(){var t,r,F,s,B,F,o,i,F,c,a,F,Q,g,F,l,w,F,U,p,F;return P(this,function(h){switch(h.label){case 0:if(R(n.element.container.flags,16))debugger;return[4,this.renderNodeBackgroundAndBorders(n.element)];case 1:h.sent(),t=0,r=n.negativeZIndex,h.label=2;case 2:return t<r.length?(F=r[t],[4,this.renderStack(F)]):[3,5];case 3:h.sent(),h.label=4;case 4:return t++,[3,2];case 5:return[4,this.renderNodeContent(n.element)];case 6:h.sent(),s=0,B=n.nonInlineLevel,h.label=7;case 7:return s<B.length?(F=B[s],[4,this.renderNode(F)]):[3,10];case 8:h.sent(),h.label=9;case 9:return s++,[3,7];case 10:o=0,i=n.nonPositionedFloats,h.label=11;case 11:return o<i.length?(F=i[o],[4,this.renderStack(F)]):[3,14];case 12:h.sent(),h.label=13;case 13:return o++,[3,11];case 14:c=0,a=n.nonPositionedInlineLevel,h.label=15;case 15:return c<a.length?(F=a[c],[4,this.renderStack(F)]):[3,18];case 16:h.sent(),h.label=17;case 17:return c++,[3,15];case 18:Q=0,g=n.inlineLevel,h.label=19;case 19:return Q<g.length?(F=g[Q],[4,this.renderNode(F)]):[3,22];case 20:h.sent(),h.label=21;case 21:return Q++,[3,19];case 22:l=0,w=n.zeroOrAutoZIndexOrTransformedOrOpacity,h.label=23;case 23:return l<w.length?(F=w[l],[4,this.renderStack(F)]):[3,26];case 24:h.sent(),h.label=25;case 25:return l++,[3,23];case 26:U=0,p=n.positiveZIndex,h.label=27;case 27:return U<p.length?(F=p[U],[4,this.renderStack(F)]):[3,30];case 28:h.sent(),h.label=29;case 29:return U++,[3,27];case 30:return[2]}})})},A.prototype.mask=function(n){this.ctx.beginPath(),this.ctx.moveTo(0,0),this.ctx.lineTo(this.canvas.width,0),this.ctx.lineTo(this.canvas.width,this.canvas.height),this.ctx.lineTo(0,this.canvas.height),this.ctx.lineTo(0,0),this.formatPath(n.slice(0).reverse()),this.ctx.closePath()},A.prototype.path=function(n){this.ctx.beginPath(),this.formatPath(n),this.ctx.closePath()},A.prototype.formatPath=function(n){const t=this;n.forEach(function(r,s){const B=$(r)?r.start:r;s===0?t.ctx.moveTo(B.x,B.y):t.ctx.lineTo(B.x,B.y),$(r)&&t.ctx.bezierCurveTo(r.startControl.x,r.startControl.y,r.endControl.x,r.endControl.y,r.end.x,r.end.y)})},A.prototype.renderRepeat=function(n,t,r,s){this.path(n),this.ctx.fillStyle=t,this.ctx.translate(r,s),this.ctx.fill(),this.ctx.translate(-r,-s)},A.prototype.resizeImage=function(n,t,r){let s;if(n.width===t&&n.height===r)return n;const o=((s=this.canvas.ownerDocument)!==null&&s!==void 0?s:document).createElement("canvas");return o.width=Math.max(1,t),o.height=Math.max(1,r),o.getContext("2d").drawImage(n,0,0,n.width,n.height,0,0,t,r),o},A.prototype.renderBackgroundImage=function(n){return k(this,void 0,void 0,function(){let t,r,s,B,o,i;return P(this,function(c){switch(c.label){case 0:t=n.styles.backgroundImage.length-1,r=function(a){var Q,g,l,M,J,Y,L,N,y,w,M,J,Y,L,N,U,p,F,h,T,m,d,C,I,y,E,M,j,Z,L,N,lA,J,Y,IA,rA,uA,yA,mA,iA,KA,cA;return P(this,function(SA){switch(SA.label){case 0:if(a.type!==0)return[3,5];Q=void 0,g=a.url,SA.label=1;case 1:return SA.trys.push([1,3,,4]),[4,s.context.cache.match(g)];case 2:return Q=SA.sent(),[3,4];case 3:return SA.sent(),s.context.logger.error("Error loading background-image "+g),[3,4];case 4:return Q&&(l=Ht(n,t,[Q.width,Q.height,Q.width/Q.height]),M=l[0],J=l[1],Y=l[2],L=l[3],N=l[4],y=s.ctx.createPattern(s.resizeImage(Q,L,N),"repeat"),s.renderRepeat(M,y,J,Y)),[3,6];case 5:no(a)?(w=Ht(n,t,[null,null,null]),M=w[0],J=w[1],Y=w[2],L=w[3],N=w[4],U=zB(a.angle,L,N),p=U[0],F=U[1],h=U[2],T=U[3],m=U[4],d=document.createElement("canvas"),d.width=L,d.height=N,C=d.getContext("2d"),I=C.createLinearGradient(F,T,h,m),dn(a.stops,p).forEach(function(YA){return I.addColorStop(YA.stop,V(YA.color))}),C.fillStyle=I,C.fillRect(0,0,L,N),L>0&&N>0&&(y=s.ctx.createPattern(d,"repeat"),s.renderRepeat(M,y,J,Y))):ro(a)&&(E=Ht(n,t,[null,null,null]),M=E[0],j=E[1],Z=E[2],L=E[3],N=E[4],lA=a.position.length===0?[Wt]:a.position,J=x(lA[0],L),Y=x(lA[lA.length-1],N),IA=$B(a,J,Y,L,N),rA=IA[0],uA=IA[1],rA>0&&uA>0&&(yA=s.ctx.createRadialGradient(j+J,Z+Y,0,j+J,Z+Y,rA),dn(a.stops,rA*2).forEach(function(YA){return yA.addColorStop(YA.stop,V(YA.color))}),s.path(M),s.ctx.fillStyle=yA,rA!==uA?(mA=n.bounds.left+.5*n.bounds.width,iA=n.bounds.top+.5*n.bounds.height,KA=uA/rA,cA=1/KA,s.ctx.save(),s.ctx.translate(mA,iA),s.ctx.transform(1,0,0,KA,0,0),s.ctx.translate(-mA,-iA),s.ctx.fillRect(j,cA*(Z-iA)+iA,L,N*cA),s.ctx.restore()):s.ctx.fill())),SA.label=6;case 6:return t--,[2]}})},s=this,B=0,o=n.styles.backgroundImage.slice(0).reverse(),c.label=1;case 1:return B<o.length?(i=o[B],[5,r(i)]):[3,4];case 2:c.sent(),c.label=3;case 3:return B++,[3,1];case 4:return[2]}})})},A.prototype.renderSolidBorder=function(n,t,r){return k(this,void 0,void 0,function(){return P(this,function(s){return this.path(zn(r,t)),this.ctx.fillStyle=V(n),this.ctx.fill(),[2]})})},A.prototype.renderDoubleBorder=function(n,t,r,s){return k(this,void 0,void 0,function(){let B,o;return P(this,function(i){switch(i.label){case 0:return t<3?[4,this.renderSolidBorder(n,r,s)]:[3,2];case 1:return i.sent(),[2];case 2:return B=ia(s,r),this.path(B),this.ctx.fillStyle=V(n),this.ctx.fill(),o=ca(s,r),this.path(o),this.ctx.fill(),[2]}})})},A.prototype.renderNodeBackgroundAndBorders=function(n){return k(this,void 0,void 0,function(){let t,r,s,B,o,i,c,a;const Q=this;return P(this,function(g){switch(g.label){case 0:return this.applyEffects(n.getEffects(2)),t=n.container.styles,r=!HA(t.backgroundColor)||t.backgroundImage.length,s=[{style:t.borderTopStyle,color:t.borderTopColor,width:t.borderTopWidth},{style:t.borderRightStyle,color:t.borderRightColor,width:t.borderRightWidth},{style:t.borderBottomStyle,color:t.borderBottomColor,width:t.borderBottomWidth},{style:t.borderLeftStyle,color:t.borderLeftColor,width:t.borderLeftWidth}],B=ha(vA(t.backgroundClip,0),n.curves),r||t.boxShadow.length?(this.ctx.save(),this.path(B),this.ctx.clip(),HA(t.backgroundColor)||(this.ctx.fillStyle=V(t.backgroundColor),this.ctx.fill()),[4,this.renderBackgroundImage(n.container)]):[3,2];case 1:g.sent(),this.ctx.restore(),t.boxShadow.slice(0).reverse().forEach(function(l){Q.ctx.save();const w=Xe(n.curves),U=l.inset?0:fa,p=Ba(w,-U+(l.inset?1:-1)*l.spread.number,(l.inset?1:-1)*l.spread.number,l.spread.number*(l.inset?-2:2),l.spread.number*(l.inset?-2:2));l.inset?(Q.path(w),Q.ctx.clip(),Q.mask(p)):(Q.mask(w),Q.ctx.clip(),Q.path(p)),Q.ctx.shadowOffsetX=l.offsetX.number+U,Q.ctx.shadowOffsetY=l.offsetY.number,Q.ctx.shadowColor=V(l.color),Q.ctx.shadowBlur=l.blur.number,Q.ctx.fillStyle=l.inset?V(l.color):"rgba(0,0,0,1)",Q.ctx.fill(),Q.ctx.restore()}),g.label=2;case 2:o=0,i=0,c=s,g.label=3;case 3:return i<c.length?(a=c[i],a.style!==0&&!HA(a.color)&&a.width>0?a.style!==2?[3,5]:[4,this.renderDashedDottedBorder(a.color,a.width,o,n.curves,2)]:[3,11]):[3,13];case 4:return g.sent(),[3,11];case 5:return a.style!==3?[3,7]:[4,this.renderDashedDottedBorder(a.color,a.width,o,n.curves,3)];case 6:return g.sent(),[3,11];case 7:return a.style!==4?[3,9]:[4,this.renderDoubleBorder(a.color,a.width,o,n.curves)];case 8:return g.sent(),[3,11];case 9:return[4,this.renderSolidBorder(a.color,o,n.curves)];case 10:g.sent(),g.label=11;case 11:o++,g.label=12;case 12:return i++,[3,3];case 13:return[2]}})})},A.prototype.renderDashedDottedBorder=function(n,t,r,s,B){return k(this,void 0,void 0,function(){var o,i,c,a,Q,g,l,w,U,p,F,h,T,m,d,C,d,C;return P(this,function(I){return this.ctx.save(),o=aa(s,r),i=zn(s,r),B===2&&(this.path(i),this.ctx.clip()),$(i[0])?(c=i[0].start.x,a=i[0].start.y):(c=i[0].x,a=i[0].y),$(i[1])?(Q=i[1].end.x,g=i[1].end.y):(Q=i[1].x,g=i[1].y),r===0||r===2?l=Math.abs(c-Q):l=Math.abs(a-g),this.ctx.beginPath(),B===3?this.formatPath(o):this.formatPath(i.slice(0,2)),w=t<3?t*3:t*2,U=t<3?t*2:t,B===3&&(w=t,U=t),p=!0,l<=w*2?p=!1:l<=w*2+U?(F=l/(2*w+U),w*=F,U*=F):(h=Math.floor((l+U)/(w+U)),T=(l-h*w)/(h-1),m=(l-(h+1)*w)/h,U=m<=0||Math.abs(U-T)<Math.abs(U-m)?T:m),p&&(B===3?this.ctx.setLineDash([0,w+U]):this.ctx.setLineDash([w,U])),B===3?(this.ctx.lineCap="round",this.ctx.lineWidth=t):this.ctx.lineWidth=t*2+1.1,this.ctx.strokeStyle=V(n),this.ctx.stroke(),this.ctx.setLineDash([]),B===2&&($(i[0])&&(d=i[3],C=i[0],this.ctx.beginPath(),this.formatPath([new u(d.end.x,d.end.y),new u(C.start.x,C.start.y)]),this.ctx.stroke()),$(i[1])&&(d=i[1],C=i[2],this.ctx.beginPath(),this.formatPath([new u(d.end.x,d.end.y),new u(C.start.x,C.start.y)]),this.ctx.stroke())),this.ctx.restore(),[2]})})},A.prototype.render=function(n){return k(this,void 0,void 0,function(){let t;return P(this,function(r){switch(r.label){case 0:return this.options.backgroundColor&&(this.ctx.fillStyle=V(this.options.backgroundColor),this.ctx.fillRect(this.options.x,this.options.y,this.options.width,this.options.height)),t=oa(n),[4,this.renderStack(t)];case 1:return r.sent(),this.applyEffects([]),[2,this.canvas]}})})},A}(ts);var Fa=function(e){return e instanceof Vr||e instanceof Rr?!0:e instanceof zt&&e.type!==_e&&e.type!==Ne},ha=function(e,A){switch(e){case 0:return Xe(A);case 2:return ea(A);case 1:default:return ke(A)}},da=function(e){switch(e){case 1:return"center";case 2:return"right";case 0:default:return"left"}};const Ea=["-apple-system","system-ui"];var Ha=function(e){return/iPhone OS 15_(0|1)/.test(window.navigator.userAgent)?e.filter(function(A){return Ea.indexOf(A)===-1}):e};const pa=function(e){nA(A,e);function A(n,t){const r=e.call(this,n,t)||this;return r.canvas=t.canvas?t.canvas:document.createElement("canvas"),r.ctx=r.canvas.getContext("2d"),r.options=t,r.canvas.width=Math.floor(t.width*t.scale),r.canvas.height=Math.floor(t.height*t.scale),r.canvas.style.width=t.width+"px",r.canvas.style.height=t.height+"px",r.ctx.scale(r.options.scale,r.options.scale),r.ctx.translate(-t.x,-t.y),r.context.logger.debug("EXPERIMENTAL ForeignObject renderer initialized ("+t.width+"x"+t.height+" at "+t.x+","+t.y+") with scale "+t.scale),r}return A.prototype.render=function(n){return k(this,void 0,void 0,function(){let t,r;return P(this,function(s){switch(s.label){case 0:return t=Nt(this.options.width*this.options.scale,this.options.height*this.options.scale,this.options.scale,this.options.scale,n),[4,Ia(t)];case 1:return r=s.sent(),this.options.backgroundColor&&(this.ctx.fillStyle=V(this.options.backgroundColor),this.ctx.fillRect(0,0,this.options.width*this.options.scale,this.options.height*this.options.scale)),this.ctx.drawImage(r,-this.options.x*this.options.scale,-this.options.y*this.options.scale),[2,this.canvas]}})})},A}(ts);var Ia=function(e){return new Promise(function(A,n){const t=new Image;t.onload=function(){A(t)},t.onerror=n,t.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent(new XMLSerializer().serializeToString(e))})};const ya=function(){function e(A){const n=A.id,t=A.enabled;this.id=n,this.enabled=t,this.start=Date.now()}return e.prototype.debug=function(){const A=[];for(let n=0;n<arguments.length;n++)A[n]=arguments[n];this.enabled&&(typeof window<"u"&&window.console&&typeof console.debug=="function"?console.debug.apply(console,ue([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},e.prototype.getTime=function(){return Date.now()-this.start},e.prototype.info=function(){const A=[];for(let n=0;n<arguments.length;n++)A[n]=arguments[n];this.enabled&&typeof window<"u"&&window.console&&typeof console.info=="function"&&console.info.apply(console,ue([this.id,this.getTime()+"ms"],A))},e.prototype.warn=function(){const A=[];for(let n=0;n<arguments.length;n++)A[n]=arguments[n];this.enabled&&(typeof window<"u"&&window.console&&typeof console.warn=="function"?console.warn.apply(console,ue([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},e.prototype.error=function(){const A=[];for(let n=0;n<arguments.length;n++)A[n]=arguments[n];this.enabled&&(typeof window<"u"&&window.console&&typeof console.error=="function"?console.error.apply(console,ue([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},e.instances={},e}(),ma=function(){function e(A,n){let t;this.windowBounds=n,this.instanceName="#"+e.instanceCount++,this.logger=new ya({id:this.instanceName,enabled:A.logging}),this.cache=(t=A.cache)!==null&&t!==void 0?t:new Yc(this,A)}return e.instanceCount=1,e}(),Ka=function(e,A){return A===void 0&&(A={}),La(e,A)};typeof window<"u"&&qr.setContext(window);var La=function(e,A){return k(void 0,void 0,void 0,function(){var n,t,r,s,B,o,i,c,a,Q,g,l,w,U,p,F,h,T,m,d,I,C,I;let y,E,M,j,Z,L,N,lA,J,Y,IA,rA,uA,yA,mA,iA,KA;return P(this,function(cA){switch(cA.label){case 0:if(!e||typeof e!="object")return[2,Promise.reject("Invalid element provided as first argument")];if(n=e.ownerDocument,!n)throw new Error("Element is not attached to a Document");if(t=n.defaultView,!t)throw new Error("Document is not attached to a Window");return r={allowTaint:(y=A.allowTaint)!==null&&y!==void 0?y:!1,imageTimeout:(E=A.imageTimeout)!==null&&E!==void 0?E:15e3,proxy:A.proxy,useCORS:(M=A.useCORS)!==null&&M!==void 0?M:!1},s=It({logging:(j=A.logging)!==null&&j!==void 0?j:!0,cache:A.cache},r),B={windowWidth:(Z=A.windowWidth)!==null&&Z!==void 0?Z:t.innerWidth,windowHeight:(L=A.windowHeight)!==null&&L!==void 0?L:t.innerHeight,scrollX:(N=A.scrollX)!==null&&N!==void 0?N:t.pageXOffset,scrollY:(lA=A.scrollY)!==null&&lA!==void 0?lA:t.pageYOffset},o=new wA(B.scrollX,B.scrollY,B.windowWidth,B.windowHeight),i=new ma(s,o),c=(J=A.foreignObjectRendering)!==null&&J!==void 0?J:!1,a={allowTaint:(Y=A.allowTaint)!==null&&Y!==void 0?Y:!1,onclone:A.onclone,ignoreElements:A.ignoreElements,inlineImages:c,copyStyles:c},i.logger.debug("Starting document clone with size "+o.width+"x"+o.height+" scrolled to "+-o.left+","+-o.top),Q=new Zn(i,e,a),g=Q.clonedReferenceElement,g?[4,Q.toIFrame(n,o)]:[2,Promise.reject("Unable to find element in cloned iframe")];case 1:return l=cA.sent(),w=$t(g)||bc(g)?ss(g.ownerDocument):Ye(i,g),U=w.width,p=w.height,F=w.left,h=w.top,T=ba(i,g,A.backgroundColor),m={canvas:A.canvas,backgroundColor:T,scale:(rA=(IA=A.scale)!==null&&IA!==void 0?IA:t.devicePixelRatio)!==null&&rA!==void 0?rA:1,x:((uA=A.x)!==null&&uA!==void 0?uA:0)+F,y:((yA=A.y)!==null&&yA!==void 0?yA:0)+h,width:(mA=A.width)!==null&&mA!==void 0?mA:Math.ceil(U),height:(iA=A.height)!==null&&iA!==void 0?iA:Math.ceil(p)},c?(i.logger.debug("Document cloned, using foreign object rendering"),I=new pa(i,m),[4,I.render(g)]):[3,3];case 2:return d=cA.sent(),[3,5];case 3:return i.logger.debug("Document cloned, element located at "+F+","+h+" with size "+U+"x"+p+" using computed rendering"),i.logger.debug("Starting DOM parsing"),C=vr(i,g),T===C.styles.backgroundColor&&(C.styles.backgroundColor=gA.TRANSPARENT),i.logger.debug("Starting renderer for element at "+m.x+","+m.y+" with size "+m.width+"x"+m.height),I=new Ua(i,m),[4,I.render(C)];case 4:d=cA.sent(),cA.label=5;case 5:return(!((KA=A.removeContainer)!==null&&KA!==void 0)||KA)&&(Zn.destroy(l)||i.logger.error("Cannot detach cloned iframe as it is not in the DOM anymore")),i.logger.debug("Finished rendering"),[2,d]}})})},ba=function(e,A,n){const t=A.ownerDocument,r=t.documentElement?Be(e,getComputedStyle(t.documentElement).backgroundColor):gA.TRANSPARENT,s=t.body?Be(e,getComputedStyle(t.body).backgroundColor):gA.TRANSPARENT,B=typeof n=="string"?Be(e,n):n===null?gA.TRANSPARENT:4294967295;return A===t.documentElement?HA(r)?HA(s)?B:s:r:B};const Ta=(e,A)=>{const n=new Image;n.setAttribute("crossOrigin","anonymous"),n.onload=function(){const t=document.createElement("canvas");t.width=n.width,t.height=n.height,t.getContext("2d").drawImage(n,0,0,n.width,n.height);const s=t.toDataURL("image/png"),B=document.createElement("a"),o=new MouseEvent("click");B.download=A||new Date().getTime()+"",B.href=s,B.dispatchEvent(o)},n.src=e},Sa=async(e,A,n)=>{try{const{type:t="png",name:r="file"}=A||{},B=(await Ka(e,{useCORS:!0,backgroundColor:"transparent",...n})).toDataURL(`image/${t}`);xa(B,r)}catch(t){throw ns.msgError("下载失败，请重试"),new Error(t)}},xa=(e,A)=>{const n=document.createElement("a");document.body.appendChild(n),n.href=e,n.download=A,n.target="_blank",n.click(),n.remove()};export{Ta as a,xa as b,Sa as d,rs as s};
