import{E as g}from"./BaKauqEV.js";import{E as f}from"./BGIqOOM2.js";import{j as d,b as y,cW as s}from"./DkNxoV1Z.js";/* empty css        *//* empty css        */import E from"./CIYI4QnH.js";import{_ as I}from"./BfAhB6f0.js";import h from"./B_JTsW8J.js";import{_ as x}from"./DMaow0_q.js";import{_ as T}from"./BgpUw85V.js";import{l as w,m as L,c as k,M as t,a3 as p,a0 as B,u as o,y as O,O as m,N as P,Z as N,a7 as i}from"./uahP8ofS.js";const D={class:"flex"},b={key:0},v={class:"flex-1 text-tx-primary flex flex-col w-[400px]"},j=w({__name:"index",setup(M){const e=d(),n=y(),l=L({get(){return e.showLogin},set(a){e.showLogin=a}});return k(()=>e.showLogin,a=>{a||(e.temToken=null)}),(a,r)=>{const c=g,u=f;return t(),p(u,{modelValue:o(l),"onUpdate:modelValue":r[0]||(r[0]=_=>O(l)?l.value=_:null),width:"auto",class:"login-popup","append-to-body":"","show-close":o(e).loginPopupType!==o(s).BIND_MOBILE,"close-on-click-modal":!1,style:{"border-radius":"16px",overflow:"hidden",padding:"0"}},{default:B(()=>[m("div",D,[o(n).getWebsiteConfig.pc_login_image&&o(e).loginPopupType==o(s).LOGIN&&!o(n).isMobile?(t(),P("div",b,[N(c,{class:"w-[320px] h-full",fit:"cover",src:o(n).getWebsiteConfig.pc_login_image},null,8,["src"])])):i("",!0),m("div",v,[o(e).loginPopupType==o(s).LOGIN?(t(),p(E,{key:0})):i("",!0),o(e).loginPopupType==o(s).FORGOT_PWD_MAILBOX||o(e).loginPopupType==o(s).FORGOT_PWD_MOBILE?(t(),p(I,{key:1})):i("",!0),o(e).loginPopupType==o(s).REGISTER?(t(),p(h,{key:2})):i("",!0),o(e).loginPopupType==o(s).BIND_MOBILE?(t(),p(x,{key:3})):i("",!0),o(e).loginPopupType==o(s).BIND_WEIXIN?(t(),p(T,{key:4})):i("",!0)])])]),_:1},8,["modelValue","show-close"])}}});export{j as _};
