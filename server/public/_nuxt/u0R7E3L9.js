import{_ as q}from"./BAooD3NP.js";import{_ as C}from"./DmVakCHI.js";import{E}from"./2KyQdWL7.js";import{E as B}from"./DL-C_KWg.js";import{_ as R}from"./DLBZzw-4.js";import{a as L,_ as z}from"./C12kmceL.js";/* empty css        *//* empty css        */import{u as F}from"./CM_SPFYb.js";import{F as I}from"./BXWYK723.js";import{l as S,ak as V,m as u,M as i,N as x,Z as e,a0 as a,O as t,_ as A,aq as D,a3 as f,a5 as h,X as b,u as r,a7 as O}from"./uahP8ofS.js";import"./DlAUqK2U.js";import"./B5_1O_mx.js";import"./CeHUJVAt.js";import"./i9Efl4hL.js";import"./DP4-20kY.js";import"./oOqAltFp.js";import"./Cq-dlMe8.js";/* empty css        */import"./sUoOAzN4.js";import"./BJBjrpCs.js";import"./DWZt4P8F.js";import"./Cpg3PDWZ.js";import"./BnhkBVfO.js";import"./BD9OgGux.js";import"./DCTLXrZ8.js";/* empty css        */import"./Cj1QjIv2.js";import"./-Ctiqued.js";import"./loTdDtpJ.js";import"./DwFObZc_.js";import"./DQUFgXGm.js";import"./DxfcmQ5j.js";import"./RSfPJsXe.js";import"./CWOFGv9Q.js";import"./D0NTNmoy.js";import"./DH3GNTka.js";import"./Cr9cZ-Xs.js";import"./DHsrbrOc.js";import"./BxhsAhDB.js";import"./CcLFL5SG.js";import"./BWh_1kQN.js";import"./BQBYw5b-.js";import"./DUerO_fc.js";import"./CyuOY8IG.js";import"./ic_lduNt.js";import"./Ddd8PmxP.js";import"./CcdExHMo.js";const X={class:"h-full flex"},Z={class:"p-4 h-full"},$={class:"w-[300px] h-full flex flex-col bg-body rounded-lg"},M={class:"p-[15px]"},T={class:"flex items-center"},j={class:"flex-1 min-h-0"},G={class:"px-[15px]"},H={class:"flex-1 min-w-0 ml-[15px]"},J={class:"line-clamp-1 text-xl font-medium"},K={class:"flex-1 min-w-0 pr-4 py-4"},P={class:"bg-body rounded-[10px] h-full"},Gt=S({__name:"chat",async setup(Q){let s,p;const y=L(),{data:l}=([s,p]=V(()=>F(()=>I(),{default(){return[]},lazy:!0},"$X7Lic9ZOFl")),s=await s,p(),s),m=u(()=>y.query.id),n=u(()=>l.value.find(c=>c.id===Number(m.value))||{});return(c,d)=>{const v=q,_=C,g=E,w=B,k=R,N=z;return i(),x("div",null,[e(N,{name:"default"},{default:a(()=>[t("div",X,[t("div",Z,[t("div",$,[t("div",M,[t("div",T,[e(_,{class:"flex bg-body p-[5px] text-bold rounded-[50%] text-primary shadow-light",to:"/robot_square",replace:!0},{default:a(()=>[e(v,{name:"el-icon-Back",size:18})]),_:1}),d[0]||(d[0]=t("div",{class:"text-xl flex-1 min-w-0 ml-[10px]"}," 智能体广场 ",-1))])]),t("div",j,[e(w,null,{default:a(()=>[t("div",G,[(i(!0),x(A,null,D(r(l),o=>(i(),f(_,{key:o.id,to:{path:"",query:{id:o.id}},class:b(["flex mb-[15px] rounded-[10px] px-[15px] py-[10px] items-center border border-br-light bg-body",{"text-white !border-primary !bg-primary":r(m)==o.id}]),replace:!0},{default:a(()=>[e(g,{class:"w-[50px] h-[50px] rounded-[50%]",src:o.image,alt:""},null,8,["src"]),t("div",H,[t("div",J,h(o.name),1),t("div",{class:b(["line-clamp-1 mt-[4px] text-tx-secondary",{"!text-white":r(m)==o.id}])},h(o.intro),3)])]),_:2},1032,["to","class"]))),128))])]),_:1})])])]),t("div",K,[t("div",P,[r(n).id?(i(),f(k,{key:0,"robot-id":r(n).robot_id,"square-id":r(n).id},null,8,["robot-id","square-id"])):O("",!0)])])])]),_:1})])}}});export{Gt as default};
