import{E as U}from"./1tbF8VpO.js";import{_ as O}from"./D5KDMvDa.js";import{E as q}from"./C0rtsRzX.js";import{E as H}from"./DfZUM0y5.js";import{E as G}from"./C4e59zsN.js";import{E as J}from"./1_iFSP2P.js";import{E as Z}from"./BPWdBU3q.js";import{E as K}from"./Dg2XwvBU.js";import{_ as Q}from"./4zNM63n-.js";import{b as W,_ as X,v as Y,f as S,d as tt}from"./C3HqF-ve.js";/* empty css        */import"./l0sNRNKZ.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import{u as et}from"./67xbGseh.js";import{u as ot}from"./CcPlX2kz.js";import{d as st}from"./CqdZBCti.js";import{a as at,d as lt,b as nt}from"./R2n930gq.js";import{useAiPPTStore as rt}from"./DTLzBf5g.js";import{l as it,b as ct,r as dt,j as pt,ar as ut,k as mt,ah as ft,M as r,N as u,Z as s,a0 as n,O as o,a6 as w,u as a,y as C,aa as _t,a1 as P,_ as bt,aq as vt,a7 as x,a4 as _}from"./Dp9aCaJ6.js";import{_ as gt}from"./DlAUqK2U.js";import"./DCTLXrZ8.js";import"./Df5PDZbu.js";import"./B2dGhy6n.js";import"./DH7aY8gH.js";import"./Ded2KV7I.js";import"./9Bti1uB6.js";import"./rQAzgu1x.js";import"./BkngO3As.js";import"./Cv6HhfEG.js";import"./Bpcqu5bh.js";import"./B8ufs3zL.js";import"./dogXonFS.js";/* empty css        */import"./DOw7ocGw.js";const xt={class:"h-full p-4"},kt={class:"bg-body h-full flex-1 rounded-[12px] p-4 flex flex-col gap-4 relative"},ht={class:"sticky top-0"},yt={class:"mt-4",style:{"--el-border-radius-base":"12px"}},wt={class:"grid grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-4"},Pt={class:"flex justify-between"},Et={key:0,class:"flex items-center"},Tt=["onClick"],St=["onClick"],Ct=["onClick"],Vt={class:"relative flex-1"},Lt={key:1,class:"w-full pb-[56%]"},$t={class:"w-full h-full px-4 flex flex-col justify-center items-center absolute left-0 top-0"},jt=["src"],Nt={class:"text-xs text-[#1e2f44] dark:text-white line-clamp-2 w-full break-all text-center"},Rt={key:2,class:"ppt-loading w-full h-0 rounded pb-[56%]"},Bt={class:"absolute inset-0 flex flex-col justify-center"},Ft={class:"w-full box-border"},It={class:"line-clamp-1"},zt={class:"flex justify-between items-center"},At={class:"text-[#8794A3]"},Dt={class:"flex justify-center"},Mt=it({__name:"history",setup(Ut){const V=W(),b=rt(),E={1:{label:"生成中",type:"warning"},3:{label:"生成失败",type:"danger"},2:{label:"生成成功",type:"success"}},c=ct(-1),L=[{label:"全部",value:-1},{label:"生成中",value:1},{label:"生成成功",value:2},{label:"生成失败",value:3}],$=async l=>{c.value=l,await h(),T()},{pager:d,getLists:k,resetPage:h}=et({fetchFun:at,params:dt({status:c}),afterFetch(){T()}}),j=async l=>{await S.confirm("确定重新生成？");const{type:t,cover_id:i,title:p,catalog:v,prompt:g}=l,m={type:t,prompt:g,cover_id:i,title:p,catalogs:JSON.parse(v||"[]")};await b.genPPTSubmit(m),c.value=-1,h()},y=pt(),T=async()=>{clearTimeout(y.value),d.lists.filter(t=>t.status===1||t.status===0).map(t=>t.id).length>0&&(y.value=setTimeout(()=>{h()},6e3))},{lockFn:N}=ot(async l=>{const{file_url:t}=await lt({id:l.id}),i=document.createElement("a");i.href=t,i.download=`${l.title}.pptx`,i.click()}),R=async l=>{await S.confirm("确定删除？"),await nt({id:l}),tt.success("删除成功"),k()};return ut(async()=>{await k()}),mt(()=>{clearTimeout(y.value)}),(l,t)=>{const i=U,p=O,v=ft("RouterLink"),g=q,m=H,B=G,F=J,I=Z,z=K,A=Q,D=X,M=Y;return r(),u("div",null,[s(D,{name:"default"},{default:n(()=>[o("div",xt,[o("div",kt,[o("div",ht,[t[3]||(t[3]=o("div",{class:"border-b border-b-[#eff0f2] dark:border-[#333333] pb-4 text-2xl font-medium"},[w(" 生成记录 "),o("span",{class:"text-xs ml-2 text-error"},"免费预览，满意再付费。（点击下载按钮即扣费）")],-1)),o("div",yt,[s(i,{class:"task-type !bg-[transparent]",modelValue:a(c),"onUpdate:modelValue":t[0]||(t[0]=e=>C(c)?c.value=e:null),options:L,onChange:$},null,8,["modelValue"])])]),_t((r(),P(z,{class:"ppt-result flex-1",ref:"scrollBarRef"},{default:n(()=>[o("div",null,[o("div",wt,[s(v,{to:"/ai_ppt",class:"rounded-[12px] min-h-[300px] p-4 flex flex-col justify-center items-center gap-2 border border-[#eff0f2] dark:border-[#333333]"},{default:n(()=>[s(p,{name:"el-icon-Plus",size:32}),t[4]||(t[4]=o("div",{class:"text-xl font-bold my-[12px]"}," 新建PPT ",-1)),t[5]||(t[5]=o("div",{class:"text-tx-secondary"}," 点击简单输入一个标题即可生成PPT ",-1))]),_:1}),(r(!0),u(bt,null,vt(a(d).lists,(e,Ot)=>(r(),u("div",{key:e.id,class:"rounded-[12px] p-4 flex flex-col gap-2 border border-[#eff0f2] dark:border-[#333333]"},[o("div",Pt,[s(g,{type:E[e.status].type,effect:"light"},{default:n(()=>[w(x(E[e.status].label),1)]),_:2},1032,["type"]),e.status!==1||e.status===0?(r(),u("div",Et,[e.status===2?(r(),P(m,{key:0,effect:"dark",content:`下载${e.pay_status?"":a(b).config.isVipFree?"(会员免费)":a(b).config.price>0?"-"+a(b).config.price+a(V).getTokenUnit:""}`,placement:"bottom"},{default:n(()=>[o("div",{onClick:f=>a(N)(e)},[s(p,{class:"cursor-pointer dark:hover:bg-[rgba(255,255,255,0.05)] hover:bg-[rgba(0,0,0,0.05)] rounded-md p-1 box-content",name:"el-icon-Download",size:"18",color:"#556477"})],8,Tt)]),_:2},1032,["content"])):_("",!0),s(m,{effect:"dark",content:"重新生成",placement:"bottom"},{default:n(()=>[o("div",{onClick:f=>j(e)},[s(p,{class:"cursor-pointer dark:hover:bg-[rgba(255,255,255,0.05)] hover:bg-[rgba(0,0,0,0.05)] rounded-md p-1 box-content",name:"el-icon-RefreshRight",size:"18",color:"#556477"})],8,St)]),_:2},1024),s(m,{effect:"dark",content:"删除",placement:"bottom"},{default:n(()=>[o("div",{onClick:f=>R(e.id)},[s(p,{class:"cursor-pointer dark:hover:bg-[rgba(255,255,255,0.05)] hover:bg-[rgba(0,0,0,0.05)] rounded-md p-1 box-content",name:"el-icon-Delete",size:"18",color:"#556477"})],8,Ct)]),_:2},1024)])):_("",!0)]),o("div",Vt,[e.status===2?(r(),P(v,{key:0,to:{path:"/ai_ppt/detail",query:{id:e.id}},class:"w-full pb-[56%] block h-0 cursor-pointer"},{default:n(()=>{var f;return[s(B,{class:"rounded absolute inset-0",src:(f=e.preview)==null?void 0:f[0]},null,8,["src"])]}),_:2},1032,["to"])):_("",!0),e.status===3?(r(),u("div",Lt,[o("div",$t,[o("img",{class:"w-[200px] mb-4",src:a(st),alt:"生成失败"},null,8,jt),t[6]||(t[6]=o("div",{class:"my-[10px]"}," 生成失败 ",-1)),o("div",Nt," 错误信息："+x(e.fail_reason),1)])])):_("",!0),e.status===0||e.status===1?(r(),u("div",Rt,[o("div",Bt,[s(F,{rows:4,animated:""})])])):_("",!0)]),s(I,{placement:"bottom",title:"提示词","show-arrow":!1,transition:"custom-popover",width:"300px",trigger:"hover",content:e.prompt},{reference:n(()=>[o("div",Ft,[o("div",It,x(e.prompt),1)])]),_:2},1032,["content"]),o("div",zt,[o("span",At,x(e.create_time),1),s(g,null,{default:n(()=>t[7]||(t[7]=[w("AI PPT")])),_:1})])]))),128))])])]),_:1})),[[M,a(d).loading]]),o("div",Dt,[s(A,{modelValue:a(d),"onUpdate:modelValue":t[1]||(t[1]=e=>C(d)?d.value=e:null),onChange:t[2]||(t[2]=e=>a(k)())},null,8,["modelValue"])])])])]),_:1})])}}}),Se=gt(Mt,[["__scopeId","data-v-e32d743b"]]);export{Se as default};
