import{_ as m}from"./mEICgmiO.js";import{_ as p}from"./BL33rPcJ.js";import{b as s}from"./C3HqF-ve.js";import{l as a,M as l,N as n,Z as t,X as c,u as r,O as i,V as _}from"./Dp9aCaJ6.js";import{_ as d}from"./DlAUqK2U.js";import"./CLzx4hRx.js";import"./CN8DIg3d.js";import"./D5KDMvDa.js";import"./ojqhkWRu.js";/* empty css        */import"./DQdDW2bP.js";import"./BgprUsAe.js";import"./BPWdBU3q.js";import"./DfZUM0y5.js";import"./DCTLXrZ8.js";import"./Ded2KV7I.js";import"./Dg2XwvBU.js";import"./9Bti1uB6.js";/* empty css        *//* empty css        */import"./BG9of9uB.js";import"./gtzK5o60.js";import"./C84qoid8.js";import"./C1rq4dac.js";import"./C4e59zsN.js";import"./Df5PDZbu.js";import"./B2dGhy6n.js";import"./DH7aY8gH.js";/* empty css        */import"./BGmmfkI4.js";import"./BE7GAo-z.js";import"./DPg16PXw.js";import"./lr21KhUC.js";import"./D0Qj8Spo.js";/* empty css        */import"./DP2rzg_V.js";import"./CcPlX2kz.js";const f={class:"layout-header h-full flex items-center"},u={class:"flex-1 min-w-0"},g={class:""},h=a({__name:"index",setup(x){const o=s();return(e,v)=>(l(),n("div",f,[t(m,{class:c("mr-[50px]"),logo:r(o).getWebsiteConfig.pc_logo,title:r(o).getWebsiteConfig.pc_name},null,8,["logo","title"]),i("div",u,[i("div",g,[_(e.$slots,"default",{},void 0,!0)])]),t(p,{class:"ml-auto"})]))}}),io=d(h,[["__scopeId","data-v-a4fd1a58"]]);export{io as default};
