import{_ as k}from"./D_DReIpd.js";import{_ as x}from"./D5KDMvDa.js";import{E as b}from"./BGmmfkI4.js";import{E as C}from"./Dg2XwvBU.js";import{cn as w}from"./C3HqF-ve.js";/* empty css        */import{m as o}from"./D1PpnLei.js";import{_ as E}from"./C5LOqlzt.js";import{l as V,c as B,M as l,N as s,Z as r,a0 as L,u as c,_ as $,aq as z,a1 as N,O as n,X as F,a7 as M}from"./Dp9aCaJ6.js";const S={key:0,class:"grid grid-cols-2 gap-4"},j=["onClick"],q={class:"relative rounded-[12px] overflow-hidden cursor-pointer"},A={class:"text-hidden-2 text-center"},Q=V({__name:"sd-lora",props:{modelValue:{type:Array,default:[]}},emits:["update:modelValue"],setup(_,{emit:d}){const p=d,u=_;B(()=>o,()=>{i("clear")});const{modelValue:e}=w(u,p),i=a=>{a==="clear"?e.value=[]:e.value.includes(o.value[a].model_name)?e.value=e.value.filter(m=>m!==o.value[a].model_name):e.value.push(o.value[a].model_name)};return(a,m)=>{const f=k,v=x,h=b,g=C;return l(),s("div",null,[r(E,{title:"微调模型",tips:"在基础模型上叠加微调模型，让画面更细腻更可控"}),r(g,{"max-height":"360px"},{default:L(()=>[c(o).length>0?(l(),s("div",S,[(l(!0),s($,null,z(c(o),(t,y)=>(l(),s("div",{key:t.id,class:"flex flex-col gap-2",onClick:D=>i(y)},[n("div",q,[r(f,{class:"rounded-[12px] overflow-hidden w-auto h-full bg-[var(--el-bg-color-page)]",src:t.cover,fit:"cover",ratio:[144,100]},null,8,["src"]),n("div",{class:F(["absolute top-0 left-0 bg-[rgba(0,0,0,0.4)] w-full h-full flex justify-center items-center transition-opacity opacity-0",{"opacity-100":c(e).includes(t.model_name)}])},[r(v,{name:"el-icon-CircleCheckFilled",size:20,color:"#fff"})],2)]),n("div",A,M(t.title||t.model_name),1)],8,j))),128))])):(l(),N(h,{key:1,description:"暂无关联的微调模型","image-size":50}))]),_:1})])}}});export{Q as _};
