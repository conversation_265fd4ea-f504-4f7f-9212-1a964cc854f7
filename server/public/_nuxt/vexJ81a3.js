import{_ as h}from"./D5KDMvDa.js";import{E as x,a as v}from"./DCyfTujX.js";import{a as E,cm as b,_ as g}from"./C3HqF-ve.js";/* empty css        */import"./l0sNRNKZ.js";/* empty css        */import{l as u,m as y,M as s,N as l,Z as e,a0 as o,_ as L,aq as N,a1 as d,O as m,a7 as $,u as k}from"./Dp9aCaJ6.js";import{_ as B}from"./DlAUqK2U.js";import{E as P}from"./Dg2XwvBU.js";/* empty css        */import"./BPXDi9MM.js";import"./DfZUM0y5.js";import"./DCTLXrZ8.js";import"./dogXonFS.js";const S={class:"tab-list"},w={class:"menu-icon"},C={class:"mt-[10px] text-sm"},I=u({__name:"index",props:{navList:{}},setup(f){const t=E(),i=y(()=>{const a=t.path==="/"?t.path:t.path.replace(/\/$/,"");return t.meta.activePath||a});return(a,p)=>{const _=h,c=x,r=v;return s(),l("div",S,[e(r,{"default-active":k(i),router:"",style:{border:"none"}},{default:o(()=>[(s(!0),l(L,null,N(a.navList,n=>(s(),d(c,{key:n.path,index:n.path},{default:o(()=>[m("span",w,[e(_,{size:20,name:n.icon},null,8,["name"])]),m("span",C,$(n.name),1)]),_:2},1032,["index"]))),128))]),_:1},8,["default-active"])])}}}),M=B(I,[["__scopeId","data-v-317921ce"]]),V={class:"bg-white flex flex-col h-full w-[150px] text-tx-primary tab-list"},T=u({__name:"aside",setup(f){const t=[{name:"视频合成",icon:"el-icon-VideoCamera",path:"/digital_human/aside/video_compositing"}];return(i,a)=>{const p=M,_=P,c=b,r=g;return s(),d(r,{name:"default"},{panel:o(()=>[m("div",V,[e(_,null,{default:o(()=>[e(p,{"nav-list":t})]),_:1})])]),default:o(()=>[e(c)]),_:1})}}});export{T as default};
