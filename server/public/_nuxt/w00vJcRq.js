import{_ as n}from"./PVV9uz0C.js";import{_ as i}from"./BYwQPQUw.js";import{cV as m}from"./DGzblORL.js";import{l as s,M as p,N as c,Z as t}from"./uahP8ofS.js";import"./DlAUqK2U.js";import"./BDfwbB-e.js";import"./Dt1L8u-1.js";import"./DCTLXrZ8.js";import"./DfTiOcpk.js";import"./CY5Ghzht.js";import"./D1hZ5aQ6.js";import"./C3CHD-GI.js";import"./CvVSD7f9.js";import"./BD9OgGux.js";const $=s({__name:"index",setup(a){const e=m(),o=()=>{e.setSetting({key:"showDrawer",value:!0})};return(_,l)=>{const r=n;return p(),c("div",{class:"setting flex cursor-pointer h-full items-center pl-2",onClick:o},[t(r,{size:20,name:"local-icon-dianpu_fengge"}),t(i)])}}});export{$ as default};
