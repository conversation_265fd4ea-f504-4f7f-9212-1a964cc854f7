import{e as v,o as C,p as P}from"./C9xud4Fy.js";import{P as R}from"./CWneeePX.js";import"./DP2rzg_V.js";/* empty css        */import{m as V}from"./CzIVhTAp.js";import{l as b,j as k,b as w,M as x,a3 as y,a0 as n,Z as a,u as m}from"./uahP8ofS.js";const M=b({__name:"renamePop",emits:["success","close"],setup(E,{expose:p,emit:r}){const s=k(),u=r,e=w({name:"",fd_id:-1}),c=async()=>{await V({...e.value}),u("success"),s.value.close()};return p({open:t=>{s.value.open(),e.value.fd_id=t}}),(t,o)=>{const i=v,f=C,_=P,d=R;return x(),y(d,{title:"重命名",ref_key:"popRef",ref:s,async:"",onConfirm:c,onClose:o[1]||(o[1]=l=>t.$emit("close"))},{default:n(()=>[a(_,null,{default:n(()=>[a(f,{label:"文件名称",class:"is-required"},{default:n(()=>[a(i,{placeholder:"请输入文件名称",modelValue:m(e).name,"onUpdate:modelValue":o[0]||(o[0]=l=>m(e).name=l)},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},512)}}});export{M as _};
