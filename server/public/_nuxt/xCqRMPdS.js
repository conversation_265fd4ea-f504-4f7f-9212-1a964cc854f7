import{E as v}from"./BFLg3Y4S.js";import"./DGzblORL.js";/* empty css        */import{_ as k}from"./D4eYbUAL.js";import B from"./Df6t9BsD.js";import{_ as N}from"./shjgwA5A.js";import{l as y,b as s,F as I,M as a,N as b,u as o,a3 as i,a7 as l,a0 as c,O as p,Z as f}from"./uahP8ofS.js";const E={class:"h-full"},S={class:"p-main"},x={class:"p-main"},D=y({__name:"data-study",props:{id:{type:Number,default:0}},setup(n){const m=s(0),r=s(""),e=s(1),_=(d,t)=>{m.value=d,r.value=t,e.value=3};return I(()=>{e.value=1}),(d,t)=>{const u=v;return a(),b("div",E,[o(e)==1?(a(),i(k,{key:0,class:"h-full",onToImport:t[0]||(t[0]=C=>e.value=2),onToItemList:_,id:n.id},null,8,["id"])):l("",!0),o(e)==2?(a(),i(u,{key:1},{default:c(()=>[p("div",S,[f(B,{id:n.id,onBack:t[1]||(t[1]=()=>{e.value=1})},null,8,["id"])])]),_:1})):l("",!0),o(e)==3?(a(),i(u,{key:2},{default:c(()=>[p("div",x,[f(N,{onBack:t[2]||(t[2]=()=>{e.value=1}),"item-id":o(m),"item-name":o(r)},null,8,["item-id","item-name"])])]),_:1})):l("",!0)])}}});export{D as _};
