import{E as T}from"./DnE_5Yhr.js";import{l as j,j as N,b as B,E as A}from"./D726nzJl.js";import{E as H,a as I,b as U}from"./DcfReECr.js";import{W as J}from"./DhU28mPY.js";import{E as W}from"./BOaJ3oTb.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import{u as z}from"./CEZIOyk0.js";import{l as O,ak as F,M as d,N as f,u,Z as l,a0 as n,O as s,a1 as x,a6 as i,a4 as g,a7 as b,a9 as K}from"./Dp9aCaJ6.js";import{d as R,e as Z,f as q}from"./DjwCd26w.js";import{u as G}from"./BXMCmyua.js";import{E as Q}from"./XuAfFgEq.js";import"./CBTxQWUq.js";import"./DcsXcc99.js";import"./D12SYOqE.js";import"./GbXQDMM-.js";import"./DCTLXrZ8.js";import"./DxoS9eIh.js";import"./9Bti1uB6.js";import"./DlAUqK2U.js";const X={class:"task-reward flex flex-col min-h-0 h-full bg-body rounded-[12px]"},Y={key:0,class:"flex flex-wrap cursor-pointer pr-[20px]"},ee={class:"p-[30px] bg-page rounded-[12px]"},te={class:"flex justify-between"},ae={key:0},oe={class:"text-xl font-medium mt-[20px]"},ne=["innerHTML"],se={class:"text-base text-tx-secondary mt-[5px]"},re=["innerHTML"],de={key:1,class:"flex items-center justify-center h-full"},Te=O({__name:"task_reward",async setup(le){let _,h;const{copy:E}=G(),p=j(),v=N(),{getTokenUnit:V,config:w,getImageUrl:ie}=B(),o={1:{num:"0/1",btn_text:"立即签到",desc:"",payload:"每天签到，可获得"},2:{num:"0/10",btn_text:"复制链接",desc:"",payload:"邀请1人，可获得"},3:{num:"0/3",btn_text:"复制链接",desc:"",payload:"分享1次，可获得"},4:{num:"0/4",btn_text:"立即分享",desc:"",payload:"分享1次，可获得"},5:{num:"0/3",btn_text:"立即分享",desc:"",payload:"分享1次，可获得"},6:{num:"0/3",btn_text:"立即分享",desc:"",payload:"分享1次，可获得"},7:{num:"0/3",btn_text:"立即分享",desc:"",payload:"分享1次，可获得"}},C={4e3:{rowPerView:6},2e3:{rowPerView:5},1800:{rowPerView:4},1600:{rowPerView:4},1440:{rowPerView:4},1360:{rowPerView:3},1280:{rowPerView:3},1024:{rowPerView:3}},{data:k,refresh:D}=([_,h]=F(()=>z(()=>R(),{default(){return[]},lazy:!0,transform:r=>JSON.parse(r.data).filter(e=>{var c,m,y;return e.type===2||e.type===3?o[e.type].btn_text="复制链接":e.data.num>=e.data.day_num&&(o[e.type].btn_text=e.type===1?"已签到":"已分享"),o[e.type].num=`${(c=e.data)==null?void 0:c.num}/${(m=e.data)==null?void 0:m.day_num}`,o[e.type].desc=`${o[e.type].payload}${(y=e.data)==null?void 0:y.one_award}${V}`,e.data.is_open===1})},"$BKJAxy6eS7")),_=await _,h(),_),M=async r=>{switch(r){case 1:await q(),v.getUser(),D();break;case 2:case 3:Z().then(({share_id:t})=>{E(`点击下方专属邀请链接，完成新用户注册，即可获得奖励！
${w.current_domain}/?share_id=${t}`)});break;case 4:break;case 5:await p.push("/video");break;case 6:await p.push("/music");break;case 7:await p.push("/application/layout/robot");break}},P=async r=>{r==="mj"?await p.push("/draw/mj"):r==="dalle"&&await p.push("/draw/dalle")};return(r,t)=>{const e=T,c=A,m=H,y=I,$=U,L=J,S=W;return d(),f("div",X,[u(k).length?(d(),f("div",Y,[l(L,{ref:"waterFull",delay:100,list:u(k),width:364,gutter:20,animationDelay:0,animationDuration:0,backgroundColor:"none",animationPrefix:"none",animated:"none",animationEffect:"none",breakpoints:C},{item:n(({item:a})=>[s("div",ee,[s("div",te,[l(e,{src:a.image,class:"w-[70px] h-[70px] rounded-[12px]"},null,8,["src"]),a.type===4?(d(),f("div",ae,[l($,{onCommand:P},{dropdown:n(()=>[l(y,null,{default:n(()=>[u(w).switch.mj_status?(d(),x(m,{key:0,command:"mj"},{default:n(()=>t[1]||(t[1]=[i(" 去分享 MJ ")])),_:1})):g("",!0),u(w).switch.dalle3_status?(d(),x(m,{key:1,command:"dalle"},{default:n(()=>t[2]||(t[2]=[i(" 去分享 DALLE ")])),_:1})):g("",!0)]),_:1})]),default:n(()=>[l(c,{style:{"--el-button-bg-color":"#4a92ff"},class:"!border-none",type:"primary"},{default:n(()=>[i(b(o[a.type].btn_text)+" ",1),t[0]||(t[0]=s("i",{class:"el-icon-arrow-down el-icon--right"},null,-1))]),_:2},1024)]),_:2},1024)])):(d(),x(c,{key:1,style:{"--el-button-bg-color":"#4a92ff"},class:"!border-none",type:"primary",disabled:a.data.num>=a.data.day_num,onClick:K(pe=>M(a.type),["stop"])},{default:n(()=>[i(b(o[a.type].btn_text),1)]),_:2},1032,["disabled","onClick"]))]),s("div",null,[s("div",oe,[i(b(a.customName||a.name)+" (",1),s("span",{innerHTML:o[a.type].num},null,8,ne),t[3]||(t[3]=i(") "))]),s("div",se,[s("div",{innerHTML:o[a.type].desc},null,8,re)])])])]),_:1},8,["list"])])):(d(),f("div",de,[l(S,{image:u(Q),"image-size":250,description:"暂无任务奖励"},null,8,["image"])]))])}}});export{Te as default};
