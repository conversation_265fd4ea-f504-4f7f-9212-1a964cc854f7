import{E as x,a as g}from"./NEhbE7vl.js";import{a as k}from"./ClNUxNV9.js";import{b as v}from"./BNnETjxs.js";import y from"./DAKepvAG.js";import{l as w,r as B,j as U,b as V,M as o,N as i,O as D,Z as E,a0 as s,_ as S,aq as h,u as a,a1 as n,a3 as C}from"./Dp9aCaJ6.js";import{_ as N}from"./DlAUqK2U.js";import"./Cv6HhfEG.js";import"./DZESUSa0.js";import"./B6IIPh85.js";import"./D6j_GvJh.js";import"./Tedtu6ac.js";import"./DCTLXrZ8.js";import"./BYYVzU6A.js";import"./DH3BuQAR.js";import"./9Bti1uB6.js";import"./DIFvMdL6.js";import"./Cd8UtlNR.js";import"./B1qK8f0i.js";import"./Cr7puf4F.js";import"./zRTrVFrw.js";import"./BbPTMigZ.js";import"./DkqMgBWM.js";import"./CQXeYJFv.js";import"./DMVjPTRc.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./qQcR97T8.js";import"./C0R3uvOr.js";/* empty css        */import"./DCJ0qFa1.js";import"./D9R3MaZj.js";import"./CqanTtdS.js";import"./CxSV922q.js";import"./CeZA--ML.js";import"./DP2rzg_V.js";/* empty css        *//* empty css        */const q={class:"p-main flex h-full flex-col"},R=w({__name:"setUp",emits:["update"],setup(T,{emit:l}){const c=l,d=k(),r=B({current:"baseSetting",lists:[{type:"baseSetting",name:"基础信息",component:U(y)}]}),m=V({}),p=async()=>{m.value=await v({id:d.query.id})},u=()=>{p(),c("update")};return p(),(j,e)=>{const _=g,f=x;return o(),i("div",q,[e[1]||(e[1]=D("div",{class:"text-xl font-medium"},"知识库设置",-1)),E(f,{class:"flex-1 min-h-0",modelValue:a(r).current,"onUpdate:modelValue":e[0]||(e[0]=t=>a(r).current=t)},{default:s(()=>[(o(!0),i(S,null,h(a(r).lists,(t,b)=>(o(),n(_,{label:t.name,name:t.type,key:b},{default:s(()=>[(o(),n(C(t.component),{data:a(m),onUpdate:u},null,40,["data"]))]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])])}}}),yt=N(R,[["__scopeId","data-v-4da5484e"]]);export{yt as default};
