import{_ as Y}from"./PVV9uz0C.js";import{E as ee,a as te}from"./0Hk8YYNq.js";import{K as q,M as S,O as F,P as K,X as x,a4 as w,Z as D,e as oe,E as ae}from"./DGzblORL.js";import{l as $,m as y,q as se,M as v,a3 as V,a0 as m,X as L,u as n,a2 as M,a6 as A,V as z,i as le,J as ne,j as ie,b as g,c as ce,O as r,Z as p,y as O,N as R,aq as U,_ as I,a7 as C}from"./uahP8ofS.js";import{_ as re}from"./BlIitN2B.js";import{_ as ue}from"./zrSMfP_W.js";import{P as de}from"./2Cj0Rm6F.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import{u as pe}from"./BfGcwPP1.js";import{n as me,o as fe,p as _e,q as ve}from"./BNnETjxs.js";import{_ as ye}from"./DlAUqK2U.js";import"./Dt1L8u-1.js";import"./DCTLXrZ8.js";import"./BFLg3Y4S.js";import"./Cg6fN0Zt.js";import"./HrsfEhzV.js";import"./SdKmR7yR.js";import"./D1hZ5aQ6.js";import"./CSRQmB5h.js";import"./CY5Ghzht.js";import"./Dra4U1Cn.js";import"./BcwgFr80.js";import"./D3C91Y55.js";import"./CvVSD7f9.js";import"./BD9OgGux.js";/* empty css        */import"./fq5Fxud8.js";import"./BXbPhtux.js";import"./Cpg3PDWZ.js";const J=Symbol("rowContextKey"),he=["start","center","end","space-around","space-between","space-evenly"],be=["top","middle","bottom"],ge=q({tag:{type:String,default:"div"},gutter:{type:Number,default:0},justify:{type:String,values:he,default:"start"},align:{type:String,values:be}}),xe=$({name:"ElRow"}),we=$({...xe,props:ge,setup(N){const s=N,u=S("row"),i=y(()=>s.gutter);se(J,{gutter:i});const h=y(()=>{const e={};return s.gutter&&(e.marginRight=e.marginLeft=`-${s.gutter/2}px`),e}),b=y(()=>[u.b(),u.is(`justify-${s.justify}`,s.justify!=="start"),u.is(`align-${s.align}`,!!s.align)]);return(e,c)=>(v(),V(A(e.tag),{class:L(n(b)),style:M(n(h))},{default:m(()=>[z(e.$slots,"default")]),_:3},8,["class","style"]))}});var Ve=F(we,[["__file","row.vue"]]);const $e=K(Ve),Ne=q({tag:{type:String,default:"div"},span:{type:Number,default:24},offset:{type:Number,default:0},pull:{type:Number,default:0},push:{type:Number,default:0},xs:{type:x([Number,Object]),default:()=>w({})},sm:{type:x([Number,Object]),default:()=>w({})},md:{type:x([Number,Object]),default:()=>w({})},lg:{type:x([Number,Object]),default:()=>w({})},xl:{type:x([Number,Object]),default:()=>w({})}}),Ee=$({name:"ElCol"}),Ce=$({...Ee,props:Ne,setup(N){const s=N,{gutter:u}=le(J,{gutter:y(()=>0)}),i=S("col"),h=y(()=>{const e={};return u.value&&(e.paddingLeft=e.paddingRight=`${u.value/2}px`),e}),b=y(()=>{const e=[];return["span","offset","pull","push"].forEach(l=>{const d=s[l];D(d)&&(l==="span"?e.push(i.b(`${s[l]}`)):d>0&&e.push(i.b(`${l}-${s[l]}`)))}),["xs","sm","md","lg","xl"].forEach(l=>{D(s[l])?e.push(i.b(`${l}-${s[l]}`)):ne(s[l])&&Object.entries(s[l]).forEach(([d,E])=>{e.push(d!=="span"?i.b(`${l}-${d}-${E}`):i.b(`${l}-${E}`))})}),u.value&&e.push(i.is("guttered")),[i.b(),e]});return(e,c)=>(v(),V(A(e.tag),{class:L(n(b)),style:M(n(h))},{default:m(()=>[z(e.$slots,"default")]),_:3},8,["class","style"]))}});var ke=F(Ce,[["__file","col.vue"]]);const Be=K(ke),je={class:"important-notice mb-4"},Pe={class:"notice-header"},De={class:"example-tip mb-4"},Oe={class:"tip-header"},Re={class:"mb-4"},Ue={class:"grid grid-cols-2 gap-x-[20px]"};const Ie=$({__name:"editPop",emits:["success","close"],setup(N,{expose:s,emit:u}){const i=u,h=ie(),b=g(""),e=g({kb_id:"",fd_id:"",question:"",answer:"",files:[],images:[],video:[],uuid:""}),c=g(""),f=g(""),l=g([]),d=g([]),E=y(()=>{if(!c.value)return[];const o=d.value.find(t=>t.id===c.value);return o?o.examples:[]}),T=()=>{f.value=""},X=()=>{const o=d.value.find(t=>t.id===c.value);if(o){const t=o.examples.find(_=>_.id===f.value);t&&(e.value.question=t.question,e.value.answer=t.answer)}},Z=async()=>{try{const o=await me();d.value=o,l.value=o.map(t=>({id:t.id,name:t.name}))}catch(o){console.error("获取示例库数据失败:",o)}};ce(b,o=>{e.value.video=[{url:o,name:""}]});const{lockFn:W}=pe(async()=>{e.value.uuid?await fe({...e.value}):await _e({...e.value}),i("success")}),G=async()=>{var t;const o=await ve({uuid:e.value.uuid});Object.keys(e.value).map(_=>{e.value[_]=o[_]}),b.value=((t=o.video[0])==null?void 0:t.url)||""};return s({open:o=>{h.value.open(),c.value="",f.value="",[e.value.kb_id,e.value.fd_id,e.value.uuid]=[o.kb_id,o.fd_id,o.uuid||""],o.hasOwnProperty("uuid")&&G(),Z()}}),(o,t)=>{const _=Y,k=ee,B=te,j=Be,H=$e,P=oe,Se=re,Fe=ue,Ke=ae,Q=de;return v(),V(Q,{ref_key:"popRef",ref:h,title:"录入数据",width:"800px",async:"",onConfirm:n(W),onClose:t[7]||(t[7]=a=>o.$emit("close"))},{default:m(()=>[r("div",null,[r("div",je,[r("div",Pe,[p(_,{name:"el-icon-Warning",color:"#e6a23c",size:"18"}),t[8]||(t[8]=r("span",{class:"notice-title"},"⚠️ 重要提示",-1))]),t[9]||(t[9]=r("div",{class:"notice-content"}," 为保护您的信息安全，请勿录入如姓名、身份证号、电话等个人隐私信息及任何工作秘密、商业秘密。本平台在未获得您同意的情况下，不会使用您的数据进行模型训练。 ",-1))]),r("div",De,[r("div",Oe,[p(_,{name:"el-icon-Lightbulb",color:"#409EFF",size:"18"}),t[10]||(t[10]=r("span",{class:"tip-title"},"💡 小贴士",-1))]),t[11]||(t[11]=r("div",{class:"tip-content"}," 可以试试直接选择示例，快速掌握知识库使用方法，成为AI达人 ",-1))]),r("div",Re,[p(H,{gutter:20},{default:m(()=>[p(j,{span:12},{default:m(()=>[p(B,{modelValue:n(c),"onUpdate:modelValue":t[0]||(t[0]=a=>O(c)?c.value=a:null),placeholder:"请选择示例类别",clearable:"",class:"w-full",onChange:T},{default:m(()=>[(v(!0),R(I,null,U(n(l),a=>(v(),V(k,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),p(j,{span:12},{default:m(()=>[p(B,{modelValue:n(f),"onUpdate:modelValue":t[1]||(t[1]=a=>O(f)?f.value=a:null),placeholder:"请选择具体示例",clearable:"",class:"w-full",disabled:!n(c),onChange:X,filterable:""},{default:m(()=>[(v(!0),R(I,null,U(n(E),a=>(v(),V(k,{key:a.id,label:a.title,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),r("div",Ue,[p(P,{modelValue:n(e).question,"onUpdate:modelValue":t[2]||(t[2]=a=>n(e).question=a),type:"textarea",placeholder:"请输入文档内容，你可以理解为提问的问题（必填）",rows:"10"},null,8,["modelValue"]),p(P,{modelValue:n(e).answer,"onUpdate:modelValue":t[3]||(t[3]=a=>n(e).answer=a),type:"textarea",placeholder:"请填入补充内容，你可以理解为问题的答案",rows:"10"},null,8,["modelValue"])]),C("",!0),C("",!0),C("",!0)])]),_:1},8,["onConfirm"])}}}),bt=ye(Ie,[["__scopeId","data-v-ba361af2"]]);export{bt as default};
