import{cn as c}from"./ClNUxNV9.js";import{_ as u}from"./Dr3yaPao.js";import{l as d,M as a,N as o,Z as m,O as s,_,aq as v,X as f,u as y,a7 as x}from"./Dp9aCaJ6.js";import{_ as V}from"./DlAUqK2U.js";import"./B6IIPh85.js";import"./D6j_GvJh.js";import"./Tedtu6ac.js";import"./DCTLXrZ8.js";import"./BYYVzU6A.js";import"./DH3BuQAR.js";import"./9Bti1uB6.js";/* empty css        *//* empty css        */const h={class:"mt-[15px]"},k={class:"mt-[10px]"},q=["onClick"],C=d({__name:"dalle-picture-quality",props:{modelValue:{default:{version:"",style:"standard"}}},emits:["update:modelValue"],setup(l,{emit:r}){const i=r,n=l,{modelValue:t}=c(n,i),p=[{value:"standard",label:"标准"},{value:"hd",label:"HD-高清"}];return t.value="standard",(b,B)=>(a(),o("div",h,[m(u,{title:"图片质量",tips:"",required:""}),s("div",k,[(a(),o(_,null,v(p,e=>s("div",{key:e.value,class:f(["picture-quality-option rounded-[12px]",{"picture-quality-option__active":e.value===y(t)}]),onClick:D=>t.value=e.value},x(e.label),11,q)),64))])]))}}),S=V(C,[["__scopeId","data-v-cf6c91f6"]]);export{S as default};
