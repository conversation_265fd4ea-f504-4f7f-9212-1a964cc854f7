import{a as j,E as k}from"./DFv4Fkzk.js";import{co as w}from"./C12kmceL.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import{d as t,f as n}from"./01DN-8Sp.js";import{_ as x}from"./CjTFVlnC.js";import{l as y,c as B,u as l,M as p,N as c,Z as d,O as E,a0 as N,y as b,a7 as g,_ as h,aq as C,a3 as M}from"./uahP8ofS.js";import{_ as O}from"./DlAUqK2U.js";import"./CBI7ecvA.js";import"./DCTLXrZ8.js";import"./DL-C_KWg.js";import"./Cq-dlMe8.js";import"./HrsfEhzV.js";import"./DHsrbrOc.js";import"./CeHUJVAt.js";import"./B167I4fC.js";import"./D8Q6oBc7.js";import"./CZC_C7nT.js";import"./CgMcgcz-.js";import"./tONJIxwY.js";import"./BAooD3NP.js";import"./BQyknIqG.js";import"./Cfg4O2ZN.js";import"./BD9OgGux.js";/* empty css        */const q={key:0,class:"mj-version"},z=y({__name:"mj-version",props:{modelValue:{default:""}},emits:["update:modelValue"],setup(_,{emit:f}){const v=f,V=_,{modelValue:e}=w(V,v);return B(()=>{var o;return[n.value.draw_model,(o=t.value)==null?void 0:o.mj_version]},([o,s])=>{var r,a;(r=t.value)!=null&&r.mj_version&&e.value!==o&&(e.value=(a=t.value)==null?void 0:a.mj_version[n.value.draw_model][0])}),(o,s)=>{var u;const r=k,a=j;return(u=l(t))!=null&&u.mj_version?(p(),c("div",q,[d(x,{title:"版本选择",tips:"指定midjourney的渲染版本"}),E("div",null,[d(a,{modelValue:l(e),"onUpdate:modelValue":s[0]||(s[0]=m=>b(e)?e.value=m:null),placeholder:"请选择版本",class:"w-full mt-[8px]",size:"large"},{default:N(()=>{var m;return[(p(!0),c(h,null,C((m=l(t))==null?void 0:m.mj_version[l(n).draw_model],(i,D)=>(p(),M(r,{key:i,label:i,value:i},null,8,["label","value"]))),128))]}),_:1},8,["modelValue"])])])):g("",!0)}}}),ie=O(z,[["__scopeId","data-v-4b0030cc"]]);export{ie as default};
