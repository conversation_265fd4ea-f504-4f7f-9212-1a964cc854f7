import{E as d}from"./DHdG4KAO.js";import{_}from"./hW5rAJv-.js";import{b as f,c_ as h}from"./DkNxoV1Z.js";import{l as y,m as I,M as s,N as n,Z as i,a0 as r,u as t,O as S,a5 as k,a7 as N}from"./uahP8ofS.js";import{_ as g}from"./DlAUqK2U.js";import"./Dm0tUOTg.js";import"./C1DmYkVe.js";import"./DCTLXrZ8.js";import"./CZqEt49j.js";import"./BClAlNto.js";import"./BUe2SBq3.js";const B=["src"],b=["src"],q=y({__name:"menu-item",props:{item:{},path:{},isShowIcon:{type:[Number,Boolean]},isActive:{type:<PERSON>olean}},setup(c){const p=c,{getImageUrl:a}=f(),m=I(()=>{const e=p.item.link.query;try{const o=JSON.parse(e);return h(o)}catch{return e}});return(e,o)=>{const u=d,l=_;return s(),n("div",null,[i(l,{to:`${e.path}${t(m)?`?${t(m)}`:""}`},{default:r(()=>[i(u,{index:e.path},{title:r(()=>[S("span",null,k(e.item.name),1)]),default:r(()=>[e.isActive&&e.item.selected&&e.isShowIcon?(s(),n("img",{key:0,class:"menu-item-icon",src:t(a)(e.item.selected)},null,8,B)):e.item.unselected&&e.isShowIcon?(s(),n("img",{key:1,class:"menu-item-icon",src:t(a)(e.item.unselected)},null,8,b)):N("",!0)]),_:1},8,["index"])]),_:1},8,["to"])])}}}),J=g(q,[["__scopeId","data-v-eaa55001"]]);export{J as default};
