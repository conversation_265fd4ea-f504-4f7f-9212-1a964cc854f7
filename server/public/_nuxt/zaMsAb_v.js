import{_ as t}from"./CZEAFGa_.js";import{l as r,M as m,N as p,V as i,Z as e}from"./uahP8ofS.js";import"./BG-CgURr.js";import"./DGzblORL.js";import"./DHpYTbmv.js";import"./D1hZ5aQ6.js";import"./DfTiOcpk.js";import"./CvVSD7f9.js";import"./BD9OgGux.js";import"./DCTLXrZ8.js";/* empty css        *//* empty css        */import"./DtlaP_ij.js";import"./oiYfGNha.js";import"./HrsfEhzV.js";import"./BDY5qoU8.js";import"./D7Zbvkmx.js";import"./Cd4ETNBL.js";import"./0Hk8YYNq.js";import"./Dt1L8u-1.js";import"./BFLg3Y4S.js";import"./Cg6fN0Zt.js";import"./SdKmR7yR.js";import"./CSRQmB5h.js";import"./CY5Ghzht.js";import"./QB4cWjH_.js";import"./DlAUqK2U.js";/* empty css        *//* empty css        *//* empty css        *//* empty css        */import"./DP2rzg_V.js";/* empty css        */import"./BfGcwPP1.js";import"./Cz6JPCgt.js";import"./DNBnS2UK.js";import"./PVV9uz0C.js";import"./CgMcgcz-.js";import"./Cq2cs9uN.js";import"./BH2qV7qB.js";import"./BISY4fwB.js";import"./PUboszUG.js";const a={class:"layout-blank"},X=r({__name:"blank",setup(s){return(o,n)=>(m(),p("section",a,[i(o.$slots,"default"),e(t)]))}});export{X as default};
